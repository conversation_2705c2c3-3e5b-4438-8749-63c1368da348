# 批量文档处理接口使用说明（已修复版本v2）

## 🔧 修复内容
- **修复空指针异常**：正确初始化 `childTableNames` 为空列表
- **完善验证流程**：添加参数验证和自动配置逻辑
- **改进错误处理**：增加详细的错误分类和日志记录
- **流程一致性**：完全参照 `queryDynamicData` 的实现逻辑
- **🆕 修复JSON解析异常**：智能支持JSON对象和数组两种格式
- **🆕 增强调试能力**：添加JSON格式预览和详细解析日志
- **🆕 完善异常处理**：针对JSON解析的专项异常捕获和处理

## 🚀 JSON解析智能识别
系统现在能够自动识别和处理两种JSON格式：

### 格式1: JSON对象 `{...}`
```json
{
  "fd_3d7a5f5daa7a0a": "YXHT-20250605004",
  "fd_name": "营销合同数据",
  "fd_amount": 100000
}
```

### 格式2: JSON数组 `[{...}]`  
```json
[
  {
    "fd_3d7a5f5daa7a0a": "YXHT-20250605004", 
    "fd_name": "营销合同数据",
    "fd_amount": 100000
  }
]
```

**智能处理逻辑**：
- 检测到对象格式 → 直接解析
- 检测到数组格式 → 提取第一个元素作为文档数据
- 空数组 → 返回错误信息
- 无效格式 → 详细错误提示

## 接口地址
```
POST /costControl/YXHTUploadController/batchProcessDocuments
```

## 🚀 处理流程（已优化）
对每个单据号的处理步骤：
1. **文档类型识别** - 根据前缀判断调用哪个上传方法
2. **构建查询请求** - 初始化请求对象，设置自动配置模式
3. **参数验证** - 使用 `requestValidationService` 验证请求
4. **自动配置** - 调用 `buildDynamicConfigByDocumentNumber` 获取表结构配置
5. **配置验证** - 二次验证配置完成后的请求
6. **执行查询** - 调用 `dynamicDataService.queryDynamicData`
7. **🆕 智能JSON解析** - 自动识别和解析JSON对象/数组格式
8. **数据上传** - 根据文档类型调用相应的上传方法
9. **结果记录** - 记录处理结果和耗时统计

## 请求示例
```json
{
  "documentNumbers": [
    "YXHT-20250218001",
    "YXHT-20250227003",
    "YXHT-20250304006",
    "YXHT-20250328005",
    "YXHTFK-202504020005",
    "YXHTFK-202504020006",
    "YXHTFK-202504020007",
    "YXHTFK-202504080004",
    "YXHT-20250410001",
    "YXHT-20250417006",
    "YXHT-20250420001",
    "YXHT-20250423001",
    "YXHT-20250424001",
    "YXHT-20250430001",
    "YXHT-20250508005",
    "YXHT-20250508006",
    "YXHT-20250513007",
    "YXHT-20250515003",
    "YXHT-20250611004",
    "YXHT-20250605004",
    "YXHT-20250604007"
  ],
  "batchId": "batch-20250124-001",
  "timestamp": 1706097600000
}
```

## 🎯 单据类型映射（已确认）
```
YXHT-* → uploadYXHTCrm (CRM营销合同)
YXHTFK-* → uploadYXHT (营销合同)  
FD51AB/C-* → uploadFD51ABC (FD51ABC类型合同)
```

## 🔍 错误处理增强
- **参数验证失败** - 详细的参数错误信息
- **配置构建失败** - 不支持的单据号格式提示
- **配置验证失败** - 表结构配置错误信息
- **查询数据失败** - 数据库查询错误详情
- **🆕 JSON解析失败** - 具体的格式错误和解析异常信息
- **🆕 空数组处理** - 查询结果为空数组的专项提示
- **🆕 格式不匹配** - 非JSON格式数据的详细错误
- **上传失败** - 云简系统返回的具体错误
- **系统异常** - 捕获并记录所有未预期的异常

## ✅ 容错特性
- **单个失败不影响批量** - 某个文档处理失败不会中断整体流程
- **详细结果反馈** - 每个文档都有独立的处理结果
- **性能统计** - 记录每个文档和整体批次的处理耗时
- **日志追踪** - 完整的处理日志便于问题排查
- **🆕 JSON格式兼容** - 自动适配不同的JSON返回格式
- **🆕 调试信息** - JSON格式预览和解析详情

## 响应示例
```json
{
  "code": 200,
  "msg": "批量处理完成",
  "data": {
    "success": true,
    "totalProcessed": 21,
    "successCount": 20,
    "failureCount": 1,
    "batchId": "batch-20250124-001",
    "totalProcessingTime": 15000,
    "startTime": 1706097600000,
    "endTime": 1706097615000,
    "message": "批量处理完成，成功 20 个，失败 1 个",
    "results": [
      {
        "documentNumber": "YXHT-20250218001",
        "success": true,
        "uploadMethod": "uploadYXHTCrm",
        "message": "处理成功",
        "processingTime": 800,
        "cloudDocumentNumber": "YJ2025012400001"
      },
      {
        "documentNumber": "YXHT-JSON-ERROR",
        "success": false,
        "uploadMethod": "uploadYXHTCrm",
        "errorMessage": "JSON解析异常: illegal input, offset 1, char [",
        "processingTime": 50
      }
    ]
  }
}
```

## 🚦 使用限制
- **批量大小限制**：单次最多处理 100 个文档
- **超时设置**：每个文档处理超时 30 秒
- **并发控制**：顺序处理，避免数据库压力过大

## 🔧 调试信息
启用DEBUG日志级别可以看到：
- JSON格式预览信息
- 自动格式识别过程
- 数组/对象解析详情
- 详细的错误堆栈信息

现在可以安全地处理您的21个单据了！JSON解析异常已完全修复。