<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
        <span class="sub-title">彩色字体</span>
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4158829" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">收起</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">更多</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">信息化系统权限申请流程</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe652;</span>
                <div class="name">用车申请流程</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">差旅预定审批流程</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">考勤</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79c;</span>
                <div class="name">定位</div>
                <div class="code-name">&amp;#xe79c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">试产申请流程</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">大计划发布申请流程</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64b;</span>
                <div class="name">文件培训确认流程</div>
                <div class="code-name">&amp;#xe64b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64c;</span>
                <div class="name">物料主数据维护申请流程</div>
                <div class="code-name">&amp;#xe64c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64e;</span>
                <div class="name">合同审查盖章申请流程（制造交付）</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">AI工时填报子流程</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">请假审批流程</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">邮件组创建申请流程</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">盖章申请流程-非合同类（制造交付）</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">印章制发、停用、废止、换发审批流程</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">会议室下线流程</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">办公用品（低值易耗）领用流程</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65d;</span>
                <div class="name">出差海外签证申请流程</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">工牌、名片申请流程</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xf8db;</span>
                <div class="name">加班申请</div>
                <div class="code-name">&amp;#xf8db;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xf8e0;</span>
                <div class="name">补卡申请</div>
                <div class="code-name">&amp;#xf8e0;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: 
       url('iconfont.woff2?t=1689650860184') format('woff2'),
       url('iconfont.woff?t=1689650860184') format('woff'),
       url('iconfont.ttf?t=1689650860184') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-shouqi"></span>
            <div class="name">
              收起
            </div>
            <div class="code-name">.icon-shouqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo"></span>
            <div class="name">
              更多
            </div>
            <div class="code-name">.icon-gengduo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinxihuaxitongquanxianshenqingliucheng"></span>
            <div class="name">
              信息化系统权限申请流程
            </div>
            <div class="code-name">.icon-xinxihuaxitongquanxianshenqingliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yongcheshenqingliucheng"></span>
            <div class="name">
              用车申请流程
            </div>
            <div class="code-name">.icon-yongcheshenqingliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chalvyudingshenpiliucheng"></span>
            <div class="name">
              差旅预定审批流程
            </div>
            <div class="code-name">.icon-chalvyudingshenpiliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kaoqin"></span>
            <div class="name">
              考勤
            </div>
            <div class="code-name">.icon-kaoqin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingwei"></span>
            <div class="name">
              定位
            </div>
            <div class="code-name">.icon-dingwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shichanshenqingliucheng"></span>
            <div class="name">
              试产申请流程
            </div>
            <div class="code-name">.icon-shichanshenqingliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dajihuafabushenqingliucheng"></span>
            <div class="name">
              大计划发布申请流程
            </div>
            <div class="code-name">.icon-dajihuafabushenqingliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjianpeixunquerenliucheng"></span>
            <div class="name">
              文件培训确认流程
            </div>
            <div class="code-name">.icon-wenjianpeixunquerenliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wuliaozhushujuweihushenqingliucheng"></span>
            <div class="name">
              物料主数据维护申请流程
            </div>
            <div class="code-name">.icon-wuliaozhushujuweihushenqingliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hetongshenchagaizhangshenqingliuchengzhizaojiaofu"></span>
            <div class="name">
              合同审查盖章申请流程（制造交付）
            </div>
            <div class="code-name">.icon-hetongshenchagaizhangshenqingliuchengzhizaojiaofu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-AIgongshitianbaoziliucheng"></span>
            <div class="name">
              AI工时填报子流程
            </div>
            <div class="code-name">.icon-AIgongshitianbaoziliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingjiashenpiliucheng"></span>
            <div class="name">
              请假审批流程
            </div>
            <div class="code-name">.icon-qingjiashenpiliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youjianzuchuangjianshenqingliucheng"></span>
            <div class="name">
              邮件组创建申请流程
            </div>
            <div class="code-name">.icon-youjianzuchuangjianshenqingliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gaizhangshenqingliucheng-feihetongleizhizaojiaofu"></span>
            <div class="name">
              盖章申请流程-非合同类（制造交付）
            </div>
            <div class="code-name">.icon-gaizhangshenqingliucheng-feihetongleizhizaojiaofu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yinzhangzhifatingyongfeizhihuanfashenpiliucheng"></span>
            <div class="name">
              印章制发、停用、废止、换发审批流程
            </div>
            <div class="code-name">.icon-yinzhangzhifatingyongfeizhihuanfashenpiliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huiyishixiaxianliucheng"></span>
            <div class="name">
              会议室下线流程
            </div>
            <div class="code-name">.icon-huiyishixiaxianliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bangongyongpindizhiyihaolingyongliucheng"></span>
            <div class="name">
              办公用品（低值易耗）领用流程
            </div>
            <div class="code-name">.icon-bangongyongpindizhiyihaolingyongliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chuchahaiwaiqianzhengshenqingliucheng"></span>
            <div class="name">
              出差海外签证申请流程
            </div>
            <div class="code-name">.icon-chuchahaiwaiqianzhengshenqingliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongpaimingpianshenqingliucheng"></span>
            <div class="name">
              工牌、名片申请流程
            </div>
            <div class="code-name">.icon-gongpaimingpianshenqingliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiabanshenqing"></span>
            <div class="name">
              加班申请
            </div>
            <div class="code-name">.icon-jiabanshenqing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bukashenqing"></span>
            <div class="name">
              补卡申请
            </div>
            <div class="code-name">.icon-bukashenqing
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouqi"></use>
                </svg>
                <div class="name">收起</div>
                <div class="code-name">#icon-shouqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo"></use>
                </svg>
                <div class="name">更多</div>
                <div class="code-name">#icon-gengduo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinxihuaxitongquanxianshenqingliucheng"></use>
                </svg>
                <div class="name">信息化系统权限申请流程</div>
                <div class="code-name">#icon-xinxihuaxitongquanxianshenqingliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yongcheshenqingliucheng"></use>
                </svg>
                <div class="name">用车申请流程</div>
                <div class="code-name">#icon-yongcheshenqingliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chalvyudingshenpiliucheng"></use>
                </svg>
                <div class="name">差旅预定审批流程</div>
                <div class="code-name">#icon-chalvyudingshenpiliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kaoqin"></use>
                </svg>
                <div class="name">考勤</div>
                <div class="code-name">#icon-kaoqin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingwei"></use>
                </svg>
                <div class="name">定位</div>
                <div class="code-name">#icon-dingwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shichanshenqingliucheng"></use>
                </svg>
                <div class="name">试产申请流程</div>
                <div class="code-name">#icon-shichanshenqingliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dajihuafabushenqingliucheng"></use>
                </svg>
                <div class="name">大计划发布申请流程</div>
                <div class="code-name">#icon-dajihuafabushenqingliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjianpeixunquerenliucheng"></use>
                </svg>
                <div class="name">文件培训确认流程</div>
                <div class="code-name">#icon-wenjianpeixunquerenliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wuliaozhushujuweihushenqingliucheng"></use>
                </svg>
                <div class="name">物料主数据维护申请流程</div>
                <div class="code-name">#icon-wuliaozhushujuweihushenqingliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hetongshenchagaizhangshenqingliuchengzhizaojiaofu"></use>
                </svg>
                <div class="name">合同审查盖章申请流程（制造交付）</div>
                <div class="code-name">#icon-hetongshenchagaizhangshenqingliuchengzhizaojiaofu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-AIgongshitianbaoziliucheng"></use>
                </svg>
                <div class="name">AI工时填报子流程</div>
                <div class="code-name">#icon-AIgongshitianbaoziliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingjiashenpiliucheng"></use>
                </svg>
                <div class="name">请假审批流程</div>
                <div class="code-name">#icon-qingjiashenpiliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youjianzuchuangjianshenqingliucheng"></use>
                </svg>
                <div class="name">邮件组创建申请流程</div>
                <div class="code-name">#icon-youjianzuchuangjianshenqingliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gaizhangshenqingliucheng-feihetongleizhizaojiaofu"></use>
                </svg>
                <div class="name">盖章申请流程-非合同类（制造交付）</div>
                <div class="code-name">#icon-gaizhangshenqingliucheng-feihetongleizhizaojiaofu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yinzhangzhifatingyongfeizhihuanfashenpiliucheng"></use>
                </svg>
                <div class="name">印章制发、停用、废止、换发审批流程</div>
                <div class="code-name">#icon-yinzhangzhifatingyongfeizhihuanfashenpiliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huiyishixiaxianliucheng"></use>
                </svg>
                <div class="name">会议室下线流程</div>
                <div class="code-name">#icon-huiyishixiaxianliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bangongyongpindizhiyihaolingyongliucheng"></use>
                </svg>
                <div class="name">办公用品（低值易耗）领用流程</div>
                <div class="code-name">#icon-bangongyongpindizhiyihaolingyongliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chuchahaiwaiqianzhengshenqingliucheng"></use>
                </svg>
                <div class="name">出差海外签证申请流程</div>
                <div class="code-name">#icon-chuchahaiwaiqianzhengshenqingliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongpaimingpianshenqingliucheng"></use>
                </svg>
                <div class="name">工牌、名片申请流程</div>
                <div class="code-name">#icon-gongpaimingpianshenqingliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiabanshenqing"></use>
                </svg>
                <div class="name">加班申请</div>
                <div class="code-name">#icon-jiabanshenqing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bukashenqing"></use>
                </svg>
                <div class="name">补卡申请</div>
                <div class="code-name">#icon-bukashenqing</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
