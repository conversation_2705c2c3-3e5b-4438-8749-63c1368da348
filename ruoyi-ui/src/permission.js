import router from './router'
import store from './store'
import {Message, MessageBox} from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import {getToken} from '@/utils/auth'
import {isRelogin} from '@/utils/request'

NProgress.configure({showSpinner: false})

const whiteList = ['/login', '/register', '/sso']

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({path: '/'})
      NProgress.done()
    }
    else {
      if (store.getters.roles.length === 0) {
        isRelogin.show = true
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => {
          isRelogin.show = false
          store.dispatch('GenerateRoutes').then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            next({...to, replace: true}) // hack方法 确保addRoutes已完成
          })
        }).catch(err => {
          store.dispatch('LogOut').then(() => {
            Message.error(err)
            next({path: '/'})
          })
        })
      } else {
        if(to.path.includes('autoacct') && store.getters.autoacctFactory.length === 0){
          if(to.path.includes('config')){
            next()
          }else {
            MessageBox.confirm(
              '当所属工厂状态已过期，您可以继续留在该页面，或者选择工厂进入新界面',
              '系统提示',
              {
                confirmButtonText: '进入工厂选择界面',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
              isRelogin.show = false;
              location.href = '/autoacct/config';
            }).catch(() => {
              isRelogin.show = false;
            });
          }
        }
        else {
          next()
        }
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      // 正式环境访问时先判断OA是否登录，如果OA没有登录再重定向到登录页
      if (process.env.NODE_ENV !== 'development' && to.path === '/index' && Object.keys(to.query).length === 0){
        window.location.href = "https://sso.akesobio.com/sso/oauth2.0/authorize?client_id=1894e0087det411c01213f8f47c18723&redirect_uri=https%3A%2F%2Fdas.akesobio.com%2Fprod-api%2Flogin%3Fredirect%3Dindex%26response_type%3Dcode"
      }
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
