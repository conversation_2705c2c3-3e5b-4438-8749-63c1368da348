<template>
  <div>
    <div id="element">
      <img style="height: 40px" src="data:image/png;base64,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">
      <h3 style="text-align:center">人员入厂须知</h3>
      <p>
        1.<span>
        访客申请进入厂区，须提前联系康方接待人员，配合保安做好相应登记后，领取访客证，按要求佩戴好访客证，在康方接待人员陪同下方可进入。
      </span>
      </p>
      <p>
        2.<span>
               厂区停车在指定区域，请将车头朝驶出方向，禁止违规停车。
            </span>
      </p>
      <p>
        3.<span>
                全厂禁烟，吸烟者必须在康方指定的吸烟点吸烟。如有违反，违规访客和接待部门负责人分别罚款1000元。
            </span>
      </p>
      <p>
        4.<span>
                请注意保持工厂整洁干净，禁止乱扔垃圾和随地吐痰。
            </span>
      </p>
      <p>
        5.<span>
                全厂区域禁止摄影和拍照，摄影器材不得带入厂区，摄影拍照需要提前申请审批。
            </span>
      </p>
      <p>
        6.<span>
                易燃.易爆.有毒性等危险物品禁止带入厂区。
            </span>
      </p>
      <p>
        7.<span>
                不得带宠物进入厂区，未经批准，禁止携带小孩进入厂区。
            </span>
      </p>
      <p>
        8.<span>
                人员，车辆入厂必须配合康方保安人员，接受检查，引导与安排。
            </span>
      </p>
      <p>
        9.<span>
                工厂财物不能带出，如因工作或业务需要带离厂区，保安凭OA物资出门申请流程放行。
            </span>
      </p>
      <p>
        10.<span>
                人员入厂还须遵守康方其他规章制度和安全规定。
            </span>
      </p>
      <p>
        11.<span>
                当疏散警铃响起时，请遵循逃生路线疏散至大门前广场紧急集合点。
            </span>
      </p>
<!--      <p>-->
<!--        12.<span>-->
<!--                当疏散警铃响起时，请遵循逃生路线疏散至大门前广场紧急集合点。-->
<!--            </span>-->
<!--      </p>-->

      <div style="float:right;margin:20px 20px 0 0">
        <p>承诺人：<el-button type="primary" @click="popUpSignature">点击签名</el-button>
        <p></p>
        <p style="padding-left: 40px">{{signatureDate}}</p>
      </div>
      <div style="clear:right"></div>
    </div>
    <el-dialog :title="title" :visible.sync="openSignature" width="90%"  append-to-body>
<!--      解决横竖切换时窗口大小变换监听失败-->
        <div class="esign" v-if = "openSignature">
          <div class="esign-box">
            <Signature
              class="esignature"
              ref="esign"
              :width="width"
              :height="height"
              :isCrop="isCrop"
              :lineWidth="lineWidth"
              :lineColor="lineColor"
              :bgColor.sync="bgColor"
            />
          </div>
          <div class="esigh-btns">
            <button @click="handleReset">清空签名</button>
            <button @click="handleGenerate">签名确认</button>
          </div>
        </div>
    </el-dialog>
    <el-dialog :title="title" :visible.sync="open"  width="90%" append-to-body>
        <VuePdf :src="pdfSrc"/>
    </el-dialog>
  </div>
</template>

<script>
import VuePdf from "vue-pdf"
import {pdfAddImage} from '@/api/signature/signature'
import Signature from '@/components/Signature/index1'
import dayjs from "dayjs";
export default {
  name: "WorldHtml",
  components: {
    VuePdf,Signature
  },
created() {
  this.signatureDate = dayjs().format('YYYY 年 MM 月 DD 日')
  // this.signatureDate = '     年    月    日'
},
  data() {
    return {
      signatureDate:null,
      //线条粗细
      lineWidth: 10,
      lineColor: '#363e2d',
      bgColor: '',
      resultImg: '',
      isCrop: true,
      height:300,
      width:800,
      PDFurl:null,
      disabled:false,
      // 遮罩层
      loading: true,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      openSignature: false,
      open: false
    };
  },
  computed: {
    pdfSrc(){
      let src
      //处理pdfUrl返回
      if(this.PDFurl != null)
        src =  VuePdf.createLoadingTask({
          url: this.PDFurl,
          //引入pdf.js字体，templ
          cMapUrl: '/cmaps/',
          cMapPacked: true
        })
      return src ;

    }
    //省略其它
  },
  methods:{
    popUpSignature(){
      this.title = '签字板';
      this.openSignature = true;
      this.$refs.esign.reset()
      // this.$refs.esign.$_resizeHandler()
    },
    handleReset () {
      this.$refs.esign.reset()
    },
    handleGenerate () {
      this.title = "签名结果"
      this.loading = true;
      this.$refs.esign.generate().then(res => {
        this.resultImg = res
        pdfAddImage({base64String:res}).then(res =>{
          this.PDFurl = process.env.VUE_APP_BASE_API+'/SignatureController/returnPdfStream/'+res.id
          this.open = true
          this.openSignature = false
          this.loading = false
        }).catch(()=>{
          this.$refs.esign.reset()
          this.loading = false
        })
      }).catch(err => {
        alert(err)
        this.$refs.esign.reset()
        this.loading = false
      })
      this.handleReset()
    },
    start(){
      this.$refs.esign.signature = !this.$refs.esign.signature
    },
  }
}
</script>

<style scoped>
#element {
  border-radius: 8px;
  width: 600px;
  padding: 20px 40px;
  margin: 0 auto;
  background: -webkit-gradient(linear,left top,right top,color-stop(0,rgba(247,149,51,.1)),color-stop(15%,rgba(243,112,85,.1)),color-stop(30%,rgba(239,78,123,.1)),color-stop(44%,rgba(161,102,171,.1)),color-stop(58%,rgba(80,115,184,.1)),color-stop(72%,rgba(16,152,173,.1)),color-stop(86%,rgba(7,179,155,.1)),to(rgba(109,186,130,.1)));
  background: linear-gradient(90deg,rgba(247,149,51,.1),rgba(243,112,85,.1) 15%,rgba(239,78,123,.1) 30%,rgba(161,102,171,.1) 44%,rgba(80,115,184,.1) 58%,rgba(16,152,173,.1) 72%,rgba(7,179,155,.1) 86%,rgba(109,186,130,.1));
}

span {
  padding-left: 12px
}

p {
  font-size: 13px;
  line-height: 2.8;
}


.esign {
  max-width: 1000px;
  margin: auto;
  padding: 10px;
}
.esigh-btns button{
  color: #fff;
  height: 40px;
  width: 100px;
  font-size: 16px;
  margin-right: 10px;
  outline: none;
  border-radius: 4px;
  background: #F60;
  border: 1px solid transparent;
}
.esigh-btns button:active{
  color:#fff;
  box-shadow: 0px 0px 50px orangered inset;
}
.esigh-result {
  margin-top: 10px;
}
.esigh-result img {
  display: block;
  max-width: 100%;
  height: auto;
  border: 1px solid #ececee;
}
.esignature {
  margin: 10px 0;
  border: 2px solid #ccc;
}
</style>
