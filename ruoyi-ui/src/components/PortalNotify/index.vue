<template>
  <div class="container">
    <el-row :gutter="24">
      <el-col :span="2">
        <div class="avatar">
          <el-avatar :size="50" :src="notify.creatorAvatar" @error="errorHandler"></el-avatar>
        </div>
      </el-col>
      <el-col :span="22">
        <el-row class="subject">
          <el-tooltip :content="notify.subject" placement="top" popper-class="tool-tip" effect="light"
                      :open-delay="700">

            <div>
              <div class="done-tag" v-if="done">
                <el-tag type="success" size="mini" effect="plain">已处理</el-tag>
              </div>
              <div class="level1-tag" v-if="notify.level === 1">紧急</div>
              <div class="level2-tag" v-if="notify.level === 2">急</div>
              <span :class="notify.docRead === 1 ? 'subject-read' : ''"> {{ notify.subject }}</span>
            </div>
          </el-tooltip>
        </el-row>
        <el-row class="info">
          <div>{{ notify.creatorName }}</div>
          <div>{{ notify.createTime }}</div>
          <div class="module">{{ notify.moduleName }}</div>
          <div>{{ notify.processNode }}</div>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>

export default {
  name: "index",
  props: {
    done: {
      type: Boolean,
      require: true
    },
    notify: {
      type: Object,
      require: true
    },
  },
  methods: {
    errorHandler() {
      return true
    }
  },
  created() {
  }
}
</script>

<style scoped>
.container{
  cursor: pointer;
}

.avatar {
  padding-left: 15px;
}

::v-deep .el-avatar {
  border: none;
  background: #FFFFFF;
}

.subject {
  margin-left: 3px;
  font-size: 14px;
  color: black;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1; /* 控制显示的行数 */
  -webkit-box-orient: vertical;

  &:hover {
    color: #1890ff;
    cursor: pointer
  }
}

.done-tag {
  display: inline-block;
  padding-top: 2px;
  margin-right: 5px;
}

.level1-tag {
  margin-right: 3px;
  display: inline-block;
  color: white;
  position: relative;
  text-align: center;
  height: 17px;
  line-height: 17px;
  padding-left: 5px;
  padding-right: 10px;
  font-size: 10px;
  border: none;
  border-radius: 0;
  background-color: #ff5f5f;
}

.level2-tag {
  margin-right: 3px;
  display: inline-block;
  color: white;
  position: relative;
  text-align: center;
  height: 17px;
  line-height: 17px;
  padding-left: 5px;
  padding-right: 10px;
  font-size: 10px;
  border: none;
  border-radius: 0;
  background-color: #E6A23C;
}

.level1-tag::after,
.level2-tag::after {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border: 5px solid transparent;
  border-width: 8px 5px;
  border-right-color: #fff;
  position: absolute;
  right: 0;
  top: 0;
}

.subject-read {
  color: #808080;
  font-weight: normal;
}


.info {
  margin-top: 4px;
  margin-left: 5px;
  font-size: 11px;
  color: #808080;

  & > div {
    margin-right: 15px;
    display: inline-block;
  }

  .module {
    color: #2ec5a3;
  }
}


</style>
<style>
/* tool-tip最大宽度，在scope中不生效 */
.tool-tip {
  max-width: 80% !important;
}
</style>
