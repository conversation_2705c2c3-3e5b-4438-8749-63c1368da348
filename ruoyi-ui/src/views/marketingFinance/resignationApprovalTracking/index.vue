<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
<!--      <el-form-item label="当前审批节点" prop="currentApprovalNode">-->
<!--        <el-input-->
<!--          v-model="queryParams.currentApprovalNode"-->
<!--          placeholder="请输入当前审批节点名称"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData" border @selection-change="handleSelectionChange" :header-cell-style="{'text-align':'center'}">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="单据编号" align="center" prop="documentNum" min-width="180" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <a @click="handleClick(scope.row)" style="color: #00afff">
            {{ scope.row.documentNum }}
          </a>
        </template>
      </el-table-column>
      <el-table-column label="提交人" align="center" prop="submitterName" min-width="120" :show-overflow-tooltip="true" />
      <el-table-column label="提交日期" align="center" prop="submitDate" min-width="150" :show-overflow-tooltip="true" />
      <el-table-column label="当前审批节点" align="center" prop="currentApprovalNode" min-width="200" :show-overflow-tooltip="true" />
      <el-table-column label="当前审批人" align="center" prop="currentApprover" min-width="120" :show-overflow-tooltip="true" />
      <el-table-column label="滞留时间" align="center" prop="retentionTime" min-width="150" :show-overflow-tooltip="true" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listResignationApprovalTracking } from '@/api/marketingFinance/resignationApprovalTracking'

export default {
  name: 'ResignationApprovalTracking',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 查询参数
      queryParams: {
        currentApprovalNode: null,
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询离职审批追踪报表列表 */
    getList() {
      this.loading = true
      listResignationApprovalTracking(this.queryParams).then(res => {
        this.tableData = res.data.rows
        this.total = res.data.total
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.documentNum)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('/marketingFinance/resignationApprovalTracking/export', {
        ...this.queryParams
      }, `离职审批追踪报表_${new Date().getTime()}.xlsx`)
    },

    /** 查看详情 */
    handleClick(row) {
      // 这里可以添加查看详情的逻辑
      console.log('查看详情:', row)
      this.$message.info(`点击查看单据编号：${row.documentNum} 的详细信息`)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

/* 主内容容器 - 限制最大宽度，避免在宽屏上过度拉伸 */
.main-content-wrapper {
  max-width: 100%;
  margin: 0 auto;
  width: 100%;
}

/* 搜索筛选区域样式 */
.search-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px 20px;
  margin-bottom: 16px; /* 减少与表格区域的间距 */

  .el-form {
    margin-bottom: 0;

    .el-form-item {
      margin-bottom: 0;
      margin-right: 24px; /* 控制表单项之间的间距 */

      &.search-buttons {
        margin-left: 20px; /* 减少按钮区域的左边距 */
        margin-right: 0;
      }
    }
  }
}

/* 表格区域样式 */
.table-section {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
}

/* 表格工具栏 */
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafbfc;
  border-bottom: 1px solid #e9ecef;
  min-height: 48px;

  .toolbar-left {
    flex: 1;
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px; /* 控制导出按钮与右侧工具栏的间距 */

    .export-btn {
      margin-right: 8px;
    }
  }
}

/* 表格容器 */
.table-container {
  .main-table {
    width: 100%;

    /* 优化表格在不同屏幕尺寸下的显示 */
    @media (max-width: 1200px) {
      font-size: 13px;
    }
  }
}

/* 分页组件容器 */
.pagination-wrapper {
  padding: 16px;
  background: #fafbfc;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: center;
}

/* 响应式布局优化 */
@media (max-width: 768px) {
  .main-content-wrapper {
    max-width: 100%;
    padding: 0 8px;
  }

  .search-section {
    padding: 12px 16px;

    .el-form {
      .el-form-item {
        margin-right: 16px;

        &.search-buttons {
          margin-left: 0;
          margin-top: 8px;
        }
      }
    }
  }

  .table-toolbar {
    flex-direction: column;
    gap: 8px;

    .toolbar-right {
      width: 100%;
      justify-content: flex-end;
    }
  }
}

/* 优化小屏幕下的表格显示 */
@media (max-width: 992px) {
  .table-container .main-table {
    font-size: 12px;

    ::v-deep .el-table__cell {
      padding: 8px 0;
    }
  }
}
</style>
