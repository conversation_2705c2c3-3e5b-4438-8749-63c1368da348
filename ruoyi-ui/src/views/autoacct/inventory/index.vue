<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="128px">
      <el-form-item label="物料刷新时间">
        {{lastUpdateDate}}
      </el-form-item>
      <el-form-item label="行号" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入行号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工厂" prop="factory">
        <el-select v-model="queryParams.factoryList" multiple  placeholder="请选择活动区域">
          <el-option label="康方生物" value="1000"></el-option>
          <el-option label="康方药业" value="1050"></el-option>
          <el-option label="康方赛诺" value="1030"></el-option>
          <el-option label="康融广东" value="1060"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="物料" prop="material">
        <el-input
          v-model="queryParams.material"
          placeholder="请输入物料"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料描述" prop="materialDesc">
        <el-input
          v-model="queryParams.materialDesc"
          placeholder="请输入物料描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="包装规格" prop="packingSpecifications">
        <el-input
          v-model="queryParams.packingSpecifications"
          placeholder="请输入包装规格"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="批次" prop="batch">
        <el-input
          v-model="queryParams.batch"
          placeholder="请输入批次"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产批次" prop="productionBatches">
        <el-input
          v-model="queryParams.productionBatches"
          placeholder="请输入生产批次"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="非限制使用的库存" prop="nonRestrictedInventory">
        <el-input
          v-model="queryParams.nonRestrictedInventory"
          placeholder="请输入非限制使用的库存"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="基本计量单位" prop="basicUnit">
        <el-input
          v-model="queryParams.basicUnit"
          placeholder="请输入基本计量单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="质量检验" prop="qualityInspection">
        <el-input
          v-model="queryParams.qualityInspection"
          placeholder="请输入质量检验"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="已冻结" prop="frozen">
        <el-input
          v-model="queryParams.frozen"
          placeholder="请输入已冻结"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="货架寿命到期日" prop="shelfExpirationDate">
        <el-input
          v-model="queryParams.shelfExpirationDate"
          placeholder="请输入货架寿命到期日"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最近收货日期" prop="recentReceiptDate">
        <el-input
          v-model="queryParams.recentReceiptDate"
          placeholder="请输入最近收货日期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产日期" prop="manufactureDate">
        <el-input
          v-model="queryParams.manufactureDate"
          placeholder="请输入生产日期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="旧物料号" prop="oldMaterialNumber">
        <el-input
          v-model="queryParams.oldMaterialNumber"
          placeholder="请输入旧物料号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="存储地点" prop="storageLocation">
        <el-input
          v-model="queryParams.storageLocation"
          placeholder="请输入存储地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="仓储地点的描述" prop="storageLocationDesc">
        <el-input
          v-model="queryParams.storageLocationDesc"
          placeholder="请输入仓储地点的描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="原物料批次" prop="rawMaterialBatch">
        <el-input
          v-model="queryParams.rawMaterialBatch"
          placeholder="请输入原物料批次"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应商批次" prop="supplierLots">
        <el-input
          v-model="queryParams.supplierLots"
          placeholder="请输入供应商批次"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 系统提示：SAP库存数据现在通过定时任务自动更新，无需手动维护 -->
    <el-alert title="系统提示" type="info" :closable="false" show-icon class="mb8">
      此页面数据由系统定时任务自动更新（每日同步SAP库存数据），无需手动维护。如有疑问请联系系统管理员。
    </el-alert>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>


    <el-table v-loading="loading" :data="inventoryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="行号" align="center" prop="id" width="80" />
      <el-table-column label="工厂" align="center" prop="factory" />
      <el-table-column label="物料" align="center" prop="material" />
      <el-table-column label="物料描述" align="center" prop="materialDesc" :show-overflow-tooltip="true" />
      <el-table-column label="厂家" align="center" prop="manufacturers" />
      <el-table-column label="货号" align="center" prop="materialNumber" />
      <el-table-column label="包装规格" align="center" prop="packingSpecifications" />
      <el-table-column label="批次" align="center" prop="batch" />
      <el-table-column label="生产批次" align="center" prop="productionBatches" />
      <el-table-column label="非限制使用的库存" align="center" prop="nonRestrictedInventory" />
      <el-table-column label="基本计量单位" align="center" prop="basicUnit" />
      <el-table-column label="质量检验" align="center" prop="qualityInspection" />
      <el-table-column label="已冻结" align="center" prop="frozen" />
      <el-table-column label="货架寿命到期日" align="center" prop="shelfExpirationDate" />
      <el-table-column label="最近收货日期" align="center" prop="recentReceiptDate" />
      <el-table-column label="生产日期" align="center" prop="manufactureDate" />
      <el-table-column label="旧物料号" align="center" prop="oldMaterialNumber" />
      <el-table-column label="存储地点" align="center" prop="storageLocation" />
      <el-table-column label="仓储地点的描述" align="center" prop="storageLocationDesc" />
      <el-table-column label="原物料批次" align="center" prop="rawMaterialBatch" />
      <el-table-column label="供应商批次" align="center" prop="supplierLots" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改库存对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工厂" prop="factory">
          <el-input v-model="form.factory" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="物料" prop="material">
          <el-input v-model="form.material" placeholder="请输入物料" />
        </el-form-item>
        <el-form-item label="物料描述" prop="materialDesc">
          <el-input v-model="form.materialDesc" placeholder="请输入物料描述" />
        </el-form-item>
        <el-form-item label="包装规格" prop="packingSpecifications">
          <el-input v-model="form.packingSpecifications" placeholder="请输入包装规格" />
        </el-form-item>
        <el-form-item label="批次" prop="batch">
          <el-input v-model="form.batch" placeholder="请输入批次" />
        </el-form-item>
        <el-form-item label="生产批次" prop="productionBatches">
          <el-input v-model="form.productionBatches" placeholder="请输入生产批次" />
        </el-form-item>
        <el-form-item label="非限制使用的库存" prop="nonRestrictedInventory">
          <el-input v-model="form.nonRestrictedInventory" placeholder="请输入非限制使用的库存" />
        </el-form-item>
        <el-form-item label="基本计量单位" prop="basicUnit">
          <el-input v-model="form.basicUnit" placeholder="请输入基本计量单位" />
        </el-form-item>
        <el-form-item label="质量检验" prop="qualityInspection">
          <el-input v-model="form.qualityInspection" placeholder="请输入质量检验" />
        </el-form-item>
        <el-form-item label="已冻结" prop="frozen">
          <el-input v-model="form.frozen" placeholder="请输入已冻结" />
        </el-form-item>
        <el-form-item label="货架寿命到期日" prop="shelfExpirationDate">
          <el-input v-model="form.shelfExpirationDate" placeholder="请输入货架寿命到期日" />
        </el-form-item>
        <el-form-item label="最近收货日期" prop="recentReceiptDate">
          <el-input v-model="form.recentReceiptDate" placeholder="请输入最近收货日期" />
        </el-form-item>
        <el-form-item label="生产日期" prop="manufactureDate">
          <el-input v-model="form.manufactureDate" placeholder="请输入生产日期" />
        </el-form-item>
        <el-form-item label="旧物料号" prop="oldMaterialNumber">
          <el-input v-model="form.oldMaterialNumber" placeholder="请输入旧物料号" />
        </el-form-item>
        <el-form-item label="存储地点" prop="storageLocation">
          <el-input v-model="form.storageLocation" placeholder="请输入存储地点" />
        </el-form-item>
        <el-form-item label="仓储地点的描述" prop="storageLocationDesc">
          <el-input v-model="form.storageLocationDesc" placeholder="请输入仓储地点的描述" />
        </el-form-item>
        <el-form-item label="原物料批次" prop="rawMaterialBatch">
          <el-input v-model="form.rawMaterialBatch" placeholder="请输入原物料批次" />
        </el-form-item>
        <el-form-item label="供应商批次" prop="supplierLots">
          <el-input v-model="form.supplierLots" placeholder="请输入供应商批次" />
        </el-form-item>
        <el-form-item label="创建时间" prop="creationTime">
          <el-input v-model="form.creationTime" placeholder="请输入创建时间" />
        </el-form-item>
        <el-form-item label="创建人" prop="createdBy">
          <el-input v-model="form.createdBy" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updated">
          <el-input v-model="form.updated" placeholder="请输入更新时间" />
        </el-form-item>
        <el-form-item label="更新人" prop="updater">
          <el-input v-model="form.updater" placeholder="请输入更新人" />
        </el-form-item>
        <el-form-item label="删除时间" prop="deletionTime">
          <el-input v-model="form.deletionTime" placeholder="请输入删除时间" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>



    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport"/>
            是否根据行号更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listInventory, getInventory, delInventory, addInventory, updateInventory } from "@/api/autoacct/inventory";
import {getToken} from "@/utils/auth";
import {mapState} from "vuex";
import { getLastUpdateDate } from "@/api/monitor/jobLog";

export default {
  name: "Inventory",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 库存表格数据
      inventoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: null,
        factory: null,
        material: null,
        materialDesc: null,
        packingSpecifications: null,
        batch: null,
        productionBatches: null,
        nonRestrictedInventory: null,
        basicUnit: null,
        qualityInspection: null,
        frozen: null,
        shelfExpirationDate: null,
        recentReceiptDate: null,
        manufactureDate: null,
        oldMaterialNumber: null,
        storageLocation: null,
        storageLocationDesc: null,
        rawMaterialBatch: null,
        supplierLots: null,
        creationTime: null,
        createdBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null,
        factoryList:[]
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/autoacct/inventory/importData"
      },
      lastUpdateDate:null
    };
  },
  created() {
    this.queryParams.factoryList = this.user.autoacctFactory
    console.log(this.user.autoacctFactory)
    this.getList();
  },
  methods: {


    /** 查询库存列表 */
    getList() {
      this.loading = true;
      listInventory(this.queryParams).then(response => {
        this.inventoryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      getLastUpdateDate().then(response => {
        console.log(response.msg)
        this.lastUpdateDate = response.msg
        console.log(this.lastUpdateDate)
      })

    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        factory: null,
        material: null,
        materialDesc: null,
        packingSpecifications: null,
        batch: null,
        productionBatches: null,
        nonRestrictedInventory: null,
        basicUnit: null,
        qualityInspection: null,
        frozen: null,
        shelfExpirationDate: null,
        recentReceiptDate: null,
        manufactureDate: null,
        oldMaterialNumber: null,
        storageLocation: null,
        storageLocationDesc: null,
        rawMaterialBatch: null,
        supplierLots: null,
        creationTime: null,
        createdBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加库存";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getInventory(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改库存";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateInventory(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInventory(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除库存编号为"' + ids + '"的数据项？').then(function() {
        return delInventory(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('autoacct/inventory/export', {
        ...this.queryParams
      }, `inventory_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "sap库存测试数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('autoacct/inventory/importTemplate', {}, `sap库存测试数据导入模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }

  },
  computed:{
    ...mapState({user:state => state.user})
  }
};
</script>
