<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="行号" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入行号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工厂代码" prop="factoryCode">
        <el-select v-model="queryParams.factoryList" multiple  placeholder="请选择活动区域">
          <el-option label="康方生物" value="1000"></el-option>
          <el-option label="康方药业" value="1050"></el-option>
          <el-option label="康方赛诺" value="1030"></el-option>
          <el-option label="康融广东" value="1060"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="SAP物料编码" prop="materialCode">
        <el-input
          v-model="queryParams.materialCode"
          placeholder="请输入SAP物料编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="厂家" prop="factory">
        <el-input
          v-model="queryParams.factory"
          placeholder="请输入厂家"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="货号" prop="itemNumber">
        <el-input
          v-model="queryParams.itemNumber"
          placeholder="请输入货号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 系统提示：SAP采购未回数据现在通过定时任务自动更新，无需手动维护 -->
    <el-alert title="系统提示" type="info" :closable="false" show-icon class="mb8">
      此页面数据由系统定时任务自动更新（每日同步SAP数据），无需手动维护。如有疑问请联系系统管理员。
    </el-alert>

    <el-row :gutter="10" class="mb8">
      <!-- 以下手动操作按钮已禁用：数据现在通过定时任务自动更新（RyTask.refreshPurchaseNotReturn） -->
      <!--
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['autoacct:notreturn:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['autoacct:notreturn:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['autoacct:notreturn:remove']"
        >删除</el-button>
      </el-col>
      -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['autoacct:notreturn:export']"
        >导出</el-button>
      </el-col>
      <!-- 导入功能已禁用：数据现在通过定时任务自动更新 -->
      <!--
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
        >导入
        </el-button>
      </el-col>
      -->

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="notreturnList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="行号" align="center" prop="id" width="80" />
      <el-table-column label="工厂代码" align="center" prop="factoryCode" />
      <el-table-column label="物料编码" align="center" prop="material" />
      <el-table-column label="物料描述" align="center" prop="materialDesc" :show-overflow-tooltip="true" />
      <el-table-column label="厂家" align="center" prop="manufacturers" />
      <el-table-column label="货号" align="center" prop="articleNumber" />
      <el-table-column label="基本计量单位" align="center" prop="unit" />
      <el-table-column label="包装规格" align="center" prop="specification" :show-overflow-tooltip="true" />
      <el-table-column label="采购单位" align="center" prop="purchasingUnit" />
      <el-table-column label="转换系数" align="center" prop="purchasingUnitConversion" />
      <el-table-column label="按采购单位实际下单量" align="center" prop="purchasingUnitActualOrder" />
      <el-table-column label="计划要求到货时间" align="center" prop="planArrivalTime" >
        <template slot-scope="scope">
          <!-- 使用 formatter 格式化日期 -->
          <span>{{
              dayjs(scope.row.planArrivalTime).format('YYYY/MM/DD')
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="转换为基本单位未回数量" align="center" prop="convertUnitUnreturnedQuantity" />
      <!-- 操作列已禁用：数据现在通过定时任务自动更新，无需手动操作 -->
      <!--
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['autoacct:notreturn:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['autoacct:notreturn:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
      -->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改采购未回对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="50%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="180px">

        <el-form-item label="工厂代码" prop="factoryCode">
          <el-input v-model="form.factoryCode" placeholder="请输入工厂代码" />
        </el-form-item>
        <el-form-item label="物料描述" prop="materialDesc" >
          <el-input v-model="form.material"  disabled >
            <el-button slot="append" style="color: red"  icon="el-icon-edit"  @click="innerOpen2=true">点击选择</el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="物料描述" prop="materialDesc">
          <el-input v-model="form.materialDesc"  disabled />
        </el-form-item>
        <el-form-item label="厂家" prop="factory">
          <el-input v-model="form.factory" placeholder="请输入厂家" />
        </el-form-item>
        <el-form-item label="货号" prop="itemNumber">
          <el-input v-model="form.itemNumber" placeholder="请输入货号" />
        </el-form-item>
        <el-form-item label="基本计量单位" prop="unit">
          <el-input v-model="form.unit" placeholder="请输入基本单位" />
        </el-form-item>
        <el-form-item label="包装规格" prop="specification">
          <el-input v-model="form.specification" placeholder="请输入包装规格" />
        </el-form-item>
        <el-form-item label="采购单位" prop="purchasingUnit">
          <el-input v-model="form.purchasingUnit" placeholder="请输入采购单位" />
        </el-form-item>
        <el-form-item label="转换系数" prop="purchasingUnitConversion">
          <el-input v-model="form.purchasingUnitConversion" placeholder="请输入按采购单位转换系数" />
        </el-form-item>
        <el-form-item label="按采购单位实际下单量" prop="purchasingUnitActualOrder">
          <el-input v-model="form.purchasingUnitActualOrder" placeholder="请输入按采购单位实际下单量" />
        </el-form-item>
        <el-form-item label="计划要求到货时间" prop="planArrivalTime">
          <el-date-picker
            @change="watchDate"
            v-model="form.planArrivalTime"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
<!--          <el-input v-model="form.planArrivalTime" placeholder="请输入计划要求到货时间" />-->
        </el-form-item>
        <el-form-item label="转换为基本单位未回数量" prop="convertUnitUnreturnedQuantity">
          <el-input v-model="form.convertUnitUnreturnedQuantity" placeholder="请输入转换为基本单位未回数量" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>

      <el-dialog
        width="100%"
        title="物料表"
        :visible.sync="innerOpen2"
        append-to-body
      >
        <InventoryData @onEmitCheckValue="emitCheckInventoryData"></InventoryData>
      </el-dialog>
    </el-dialog>

    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
<!--          <div class="el-upload__tip" slot="tip">-->
<!--            <el-checkbox v-model="upload.updateSupport"/>-->
<!--            是否更新已经存在的用户数据-->
<!--          </div>-->
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listNotreturn, getNotreturn, delNotreturn, addNotreturn, updateNotreturn } from "@/api/autoacct/notreturn";
import InventoryData from "@/views/autoacct/components/InventoryData";
import {getToken} from "@/utils/auth";
import dayjs from "dayjs";
import {mapState} from "vuex";

export default {
  name: "Notreturn",
  components: {InventoryData},
  data() {
    return {
      dayjs:dayjs,
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/autoacct/notreturn/importData"
      },

      date:null,
      innerOpen2:false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购未回表格数据
      notreturnList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: null,
        materialCode: null,
        materialDesc: null,
        factory: null,
        itemNumber: null,
        unit: null,
        specification: null,
        purchasingUnit: null,
        purchasingUnitConversion: null,
        purchasingUnitActualOrder: null,
        planArrivalTime: null,
        convertUnitUnreturnedQuanti: null,
        creationTime: null,
        createdBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null,
        factoryCode: null,
        planDate: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.queryParams.factoryList = this.user.autoacctFactory

    this.getList();
  },
  methods: {
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('autoacct/notreturn/importTemplate', {}, `采购未回导入模板${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    watchDate(){
      let month = '' + (this.date.getMonth() + 1),
        day = '' + this.date.getDate(),
        year = this.date.getFullYear();
      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;
      this.form.planDate = [year, month].join('')
      // console.log([year, month, day].join(''))
    },
    emitCheckInventoryData(param){
      this.form.materialCode = param.id
      this.form.materialDesc = param.materialDesc
      this.form.material = param.material
      this.innerOpen2 = false
    },
    /** 查询采购未回列表 */
    getList() {
      this.loading = true;
      listNotreturn(this.queryParams).then(response => {
        this.notreturnList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        materialCode: null,
        materialDesc: null,
        factory: null,
        itemNumber: null,
        unit: null,
        specification: null,
        purchasingUnit: null,
        purchasingUnitConversion: null,
        purchasingUnitActualOrder: null,
        planArrivalTime: null,
        convertUnitUnreturnedQuanti: null,
        creationTime: null,
        createdBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null,
        factoryCode: null,
        planDate: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采购未回";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getNotreturn(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改采购未回";
      });
    },
    /** 提交按钮 */
    submitForm() {

      this.$confirm('此操作将永久修改该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateNotreturn(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addNotreturn(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      // this.$confirm('此操作将永久修改该数据, 是否继续?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
        const ids = row.id || this.ids;
        this.$modal.confirm('是否确认删除采购未回编号为"' + ids + '"的数据项？').then(function() {
          return delNotreturn(ids);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      // }).catch(() => {
      //   this.$message({
      //     type: 'info',
      //     message: '已取消'
      //   });
      // });

    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('autoacct/notreturn/export', {
        ...this.queryParams
      }, `notreturn_${new Date().getTime()}.xlsx`)
    }
  },
  computed:{
    ...mapState({user:state => state.user})
  }
};
</script>
