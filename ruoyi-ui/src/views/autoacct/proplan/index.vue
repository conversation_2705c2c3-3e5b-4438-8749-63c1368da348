<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="75px">
      <el-form-item label="行号" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入行号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工厂代码" prop="factoryCode">
        <el-select v-model="queryParams.factoryList" multiple  placeholder="请选择活动区域">
          <el-option label="康方生物" value="1000"></el-option>
          <el-option label="康方药业" value="1050"></el-option>
          <el-option label="康方赛诺" value="1030"></el-option>
          <el-option label="康融广东" value="1060"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="核算编码" prop="mpmCode">
        <el-input
          v-model="queryParams.mpmCode"
          placeholder="请输入核算代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产线" prop="productLine">
        <el-input
          v-model="queryParams.productLine"
          placeholder="请输入生产线"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产月份" prop="productMonth">
        <el-date-picker clearable
          v-model="queryParams.productMonth"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择生产月份">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['autoacct:proplan:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['autoacct:proplan:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['autoacct:proplan:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['autoacct:proplan:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
        >导入
        </el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="planList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="行号" align="center" prop="id" width="80" />
      <el-table-column label="工厂代码" align="center" prop="factoryCode" />
      <el-table-column label="核算编码" align="center" prop="mpmCode" />
      <el-table-column label="核算名称" align="center" prop="mpmName" />
      <el-table-column label="车间" align="center" prop="workshop" />
<!--      <el-table-column label="审核人员" align="center" prop="auditors" />-->
      <el-table-column label="生产线" align="center" prop="productLine" />
      <el-table-column label="生产日期" align="center" prop="productMonth" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.productMonth, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="生产批次" align="center" prop="producBatch" />
      <el-table-column label="生产状态" align="center" prop="productionState" >

        <template slot-scope="scope">
          <el-tag
            :type="productionStateTag(scope.row)"
            disable-transitions>{{productionStateValue(scope.row)}}
          </el-tag>
        </template>

      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['autoacct:proplan:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['autoacct:proplan:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改生产计划对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form  ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工厂代码" prop="producBatch">
          <el-input v-model="form.factoryCode" placeholder="请输入工厂代码" />
        </el-form-item>
        <el-form-item label="核算代码" prop="mpmCode">
          <el-input :disabled="true" v-model="form.mpmCode" placeholder="请输入核算代码">
            <el-button slot="append" style="color: red"  icon="el-icon-edit"  @click="innerOpen=true">点击选择</el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="核算名称" prop="mpmName">
          <el-input :disabled="true" v-model="form.mpmName" placeholder="请输入核算名称" />
        </el-form-item>
        <el-form-item label="车间" prop="workshop">
          <el-input v-model="form.workshop" placeholder="请输入车间" />
        </el-form-item>
        <el-form-item label="审核人员" prop="auditors">
          <el-input v-model="form.auditors" placeholder="请输入审核人员" />
        </el-form-item>
        <el-form-item label="生产线" prop="productLine">
          <el-input v-model="form.productLine" placeholder="请输入生产线" />
        </el-form-item>
        <el-form-item label="生产月份" prop="productMonth">
          <el-date-picker clearable
                          v-model="form.productMonth"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择生产月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="生产批次" prop="producBatch">
          <el-input v-model="form.producBatch" placeholder="请输入生产批次" />
        </el-form-item>

        <el-form-item label="生产状态" prop="productionState">
          <el-select v-model="form.productionState" placeholder="请选择">
            <el-option label="待完成" value="0"></el-option>
            <el-option label="已完成" value="1"></el-option>
          </el-select>

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>

      <el-dialog
        width="30%"
        title="核算编码表"
        :visible.sync="innerOpen"
        append-to-body
      >
        <MpmData @onEmitMPMCodes="onEmitMPMCodes"></MpmData>
      </el-dialog>
    </el-dialog>

    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport"/>
            是否根据行号更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listPlan, getPlan, delPlan, addPlan, updatePlan } from "@/api/autoacct/plan";
import MpmData from "@/views/autoacct/components/MpmData";
import {getToken} from "@/utils/auth";
import {mapState} from "vuex";

export default {
  name: "Plan",
  components: {MpmData},
  data() {
    return {
      productionStateOptions: [{
        value: '0',
        label: '待完成'
      }, {
        value: '1',
        label: '已完成'
      }],
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/autoacct/prodplan/importData"
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 生产计划表格数据
      planList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      innerOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: null,
        mpmCode: null,
        mpmName: null,
        workshop: null,
        auditors: null,
        creationTime: null,
        createBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null,
        productLine: null,
        productMonth: null,
        producBatch: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.queryParams.factoryList = this.user.autoacctFactory

    this.getList();
  },
  methods: {
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "生产计划导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('autoacct/prodplan/importTemplate', {}, `生产计划导入模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    onEmitMPMCodes(param1){
      this.form.mpmCode = param1.mpmCode
      this.form.mpmName = param1.mpmName
      this.innerOpen = false
    },
    /** 查询生产计划列表 */
    getList() {
      this.loading = true;
      listPlan(this.queryParams).then(response => {
        this.planList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mpmCode: null,
        mpmName: null,
        workshop: null,
        auditors: null,
        creationTime: null,
        createBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null,
        productLine: null,
        productMonth: null,
        producBatch: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {

      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加生产计划";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPlan(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改生产计划";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$confirm('此操作将永久修改该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updatePlan(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addPlan(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('此操作将永久修改该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = row.id || this.ids;
        this.$modal.confirm('是否确认删除生产计划编号为"' + ids + '"的数据项？').then(function() {
          return delPlan(ids);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消修改'
        });
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('autoacct/prodplan/export', {
        ...this.queryParams
      }, `生产计划_${new Date().getTime()}.xlsx`)
    },
    // 生产状态标签
    productionStateTag(row) {
      if (row.productionState === '0') {
        return 'warning'
      } else if (row.productionState === '1') {
        return 'success'
      } else {
        return 'info'
      }

    },
    productionStateValue(row){
      if (row.productionState === '0') {
        return '待完成'
      }else if (row.productionState === '1') {
        return '已完成'
      }
      return ''
    }

  },
  computed:{
    ...mapState({user:state => state.user})
  }
};
</script>
