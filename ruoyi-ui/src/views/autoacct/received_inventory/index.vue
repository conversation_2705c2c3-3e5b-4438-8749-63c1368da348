<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="工厂" prop="factory">
        <el-input
          v-model="queryParams.factory"
          placeholder="请输入工厂"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="物料" prop="material">-->
<!--        <el-input-->
<!--          v-model="queryParams.material"-->
<!--          placeholder="请输入物料"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="物料描述" prop="materialDesc">-->
<!--        <el-input-->
<!--          v-model="queryParams.materialDesc"-->
<!--          placeholder="请输入物料描述"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="包装规格" prop="packingSpecifications">-->
<!--        <el-input-->
<!--          v-model="queryParams.packingSpecifications"-->
<!--          placeholder="请输入包装规格"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="批次" prop="batch">-->
<!--        <el-input-->
<!--          v-model="queryParams.batch"-->
<!--          placeholder="请输入批次"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="生产批次" prop="productionBatches">-->
<!--        <el-input-->
<!--          v-model="queryParams.productionBatches"-->
<!--          placeholder="请输入生产批次"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="非限制使用的库存" prop="nonRestrictedInventory">-->
<!--        <el-input-->
<!--          v-model="queryParams.nonRestrictedInventory"-->
<!--          placeholder="请输入非限制使用的库存"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="基本计量单位" prop="basicUnit">-->
<!--        <el-input-->
<!--          v-model="queryParams.basicUnit"-->
<!--          placeholder="请输入基本计量单位"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="质量检验" prop="qualityInspection">-->
<!--        <el-input-->
<!--          v-model="queryParams.qualityInspection"-->
<!--          placeholder="请输入质量检验"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="已冻结" prop="frozen">-->
<!--        <el-input-->
<!--          v-model="queryParams.frozen"-->
<!--          placeholder="请输入已冻结"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="货架寿命到期日" prop="shelfExpirationDate">-->
<!--        <el-input-->
<!--          v-model="queryParams.shelfExpirationDate"-->
<!--          placeholder="请输入货架寿命到期日"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="最近收货日期" prop="recentReceiptDate">-->
<!--        <el-input-->
<!--          v-model="queryParams.recentReceiptDate"-->
<!--          placeholder="请输入最近收货日期"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="生产日期" prop="manufactureDate">-->
<!--        <el-input-->
<!--          v-model="queryParams.manufactureDate"-->
<!--          placeholder="请输入生产日期"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="旧物料号" prop="oldMaterialNumber">-->
<!--        <el-input-->
<!--          v-model="queryParams.oldMaterialNumber"-->
<!--          placeholder="请输入旧物料号"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="存储地点" prop="storageLocation">-->
<!--        <el-input-->
<!--          v-model="queryParams.storageLocation"-->
<!--          placeholder="请输入存储地点"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="仓储地点的描述" prop="storageLocationDesc">-->
<!--        <el-input-->
<!--          v-model="queryParams.storageLocationDesc"-->
<!--          placeholder="请输入仓储地点的描述"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="原物料批次" prop="rawMaterialBatch">-->
<!--        <el-input-->
<!--          v-model="queryParams.rawMaterialBatch"-->
<!--          placeholder="请输入原物料批次"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="供应商批次" prop="supplierLots">-->
<!--        <el-input-->
<!--          v-model="queryParams.supplierLots"-->
<!--          placeholder="请输入供应商批次"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="物料计划使用年月" prop="planDate">-->
<!--        <el-input-->
<!--          v-model="queryParams.planDate"-->
<!--          placeholder="请输入物料计划使用年月"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="厂家" prop="manufacturers">-->
<!--        <el-input-->
<!--          v-model="queryParams.manufacturers"-->
<!--          placeholder="请输入厂家"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="货号" prop="articleNumber">-->
<!--        <el-input-->
<!--          v-model="queryParams.articleNumber"-->
<!--          placeholder="请输入货号"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="物料标准名称" prop="materialStandardName">-->
<!--        <el-input-->
<!--          v-model="queryParams.materialStandardName"-->
<!--          placeholder="请输入物料标准名称"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="生产计划日期" prop="productMonth">-->
<!--        <el-date-picker clearable-->
<!--          v-model="queryParams.productMonth"-->
<!--          type="date"-->
<!--          value-format="yyyy-MM-dd"-->
<!--          placeholder="请选择生产计划日期">-->
<!--        </el-date-picker>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="生产计划mpm代码" prop="mpmCode">-->
<!--        <el-input-->
<!--          v-model="queryParams.mpmCode"-->
<!--          placeholder="请输入生产计划mpm代码"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 系统提示：SAP已领料库存数据现在通过定时任务自动更新，无需手动维护 -->
    <el-alert title="系统提示" type="info" :closable="false" show-icon class="mb8">
      此页面数据由系统定时任务自动更新（每日同步SAP已领料数据），无需手动维护。如有疑问请联系系统管理员。
    </el-alert>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['autoacct:received_inventory:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['autoacct:received_inventory:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['autoacct:received_inventory:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['autoacct:received_inventory:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="received_inventoryList" @selection-change="handleSelectionChange">

      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="主键id" align="center" prop="id" />-->
      <el-table-column label="领料单号" align="center" prop="orderNumber" />
      <el-table-column label="工厂" align="center" prop="factory" />
      <el-table-column label="物料" align="center" prop="material" />
      <el-table-column label="物料描述" align="center" prop="materialDesc" />
      <el-table-column label="包装规格" align="center" prop="packingSpecifications" />
      <el-table-column label="批次" align="center" prop="batch" />
      <el-table-column label="生产批次" align="center" prop="productionBatches" />
      <el-table-column label="非限制使用的库存" align="center" prop="nonRestrictedInventory" />
      <el-table-column label="基本计量单位" align="center" prop="basicUnit" />
      <el-table-column label="质量检验" align="center" prop="qualityInspection" />
      <el-table-column label="已冻结" align="center" prop="frozen" />
      <el-table-column label="货架寿命到期日" align="center" prop="shelfExpirationDate" />
      <el-table-column label="最近收货日期" align="center" prop="recentReceiptDate" />
      <el-table-column label="生产日期" align="center" prop="manufactureDate" />
      <el-table-column label="旧物料号" align="center" prop="oldMaterialNumber" />
      <el-table-column label="存储地点" align="center" prop="storageLocation" />
      <el-table-column label="仓储地点的描述" align="center" prop="storageLocationDesc" />
      <el-table-column label="原物料批次" align="center" prop="rawMaterialBatch" />
      <el-table-column label="供应商批次" align="center" prop="supplierLots" />
<!--      <el-table-column label="创建时间" align="center" prop="creationTime" />-->
<!--      <el-table-column label="创建人" align="center" prop="createdBy" />-->
<!--      <el-table-column label="更新时间" align="center" prop="updated" />-->
<!--      <el-table-column label="更新人" align="center" prop="updater" />-->
<!--      <el-table-column label="删除时间" align="center" prop="deletionTime" />-->
<!--      <el-table-column label="删除状态" align="center" prop="deleteStatus" />-->
      <el-table-column label="物料计划使用年月" align="center" prop="planDate" />
      <el-table-column label="厂家" align="center" prop="manufacturers" />
      <el-table-column label="货号" align="center" prop="articleNumber" />
      <el-table-column label="物料标准名称" align="center" prop="materialStandardName" />
<!--      <el-table-column label="生产计划日期" align="center" prop="productMonth" width="180">-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ parseTime(scope.row.productMonth, '{y}-{m}-{d}') }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="生产计划mpm代码" align="center" prop="mpmCode" />-->
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['autoacct:received_inventory:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['autoacct:received_inventory:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改已经领料库存对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工厂" prop="factory">
          <el-input v-model="form.factory" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="物料" prop="material">
          <el-input v-model="form.material" placeholder="请输入物料" />
        </el-form-item>
        <el-form-item label="物料描述" prop="materialDesc">
          <el-input v-model="form.materialDesc" placeholder="请输入物料描述" />
        </el-form-item>
        <el-form-item label="包装规格" prop="packingSpecifications">
          <el-input v-model="form.packingSpecifications" placeholder="请输入包装规格" />
        </el-form-item>
        <el-form-item label="批次" prop="batch">
          <el-input v-model="form.batch" placeholder="请输入批次" />
        </el-form-item>
        <el-form-item label="生产批次" prop="productionBatches">
          <el-input v-model="form.productionBatches" placeholder="请输入生产批次" />
        </el-form-item>
        <el-form-item label="非限制使用的库存" prop="nonRestrictedInventory">
          <el-input v-model="form.nonRestrictedInventory" placeholder="请输入非限制使用的库存" />
        </el-form-item>
        <el-form-item label="基本计量单位" prop="basicUnit">
          <el-input v-model="form.basicUnit" placeholder="请输入基本计量单位" />
        </el-form-item>
        <el-form-item label="质量检验" prop="qualityInspection">
          <el-input v-model="form.qualityInspection" placeholder="请输入质量检验" />
        </el-form-item>
        <el-form-item label="已冻结" prop="frozen">
          <el-input v-model="form.frozen" placeholder="请输入已冻结" />
        </el-form-item>
        <el-form-item label="货架寿命到期日" prop="shelfExpirationDate">
          <el-input v-model="form.shelfExpirationDate" placeholder="请输入货架寿命到期日" />
        </el-form-item>
        <el-form-item label="最近收货日期" prop="recentReceiptDate">
          <el-input v-model="form.recentReceiptDate" placeholder="请输入最近收货日期" />
        </el-form-item>
        <el-form-item label="生产日期" prop="manufactureDate">
          <el-input v-model="form.manufactureDate" placeholder="请输入生产日期" />
        </el-form-item>
        <el-form-item label="旧物料号" prop="oldMaterialNumber">
          <el-input v-model="form.oldMaterialNumber" placeholder="请输入旧物料号" />
        </el-form-item>
        <el-form-item label="存储地点" prop="storageLocation">
          <el-input v-model="form.storageLocation" placeholder="请输入存储地点" />
        </el-form-item>
        <el-form-item label="仓储地点的描述" prop="storageLocationDesc">
          <el-input v-model="form.storageLocationDesc" placeholder="请输入仓储地点的描述" />
        </el-form-item>
        <el-form-item label="原物料批次" prop="rawMaterialBatch">
          <el-input v-model="form.rawMaterialBatch" placeholder="请输入原物料批次" />
        </el-form-item>
        <el-form-item label="供应商批次" prop="supplierLots">
          <el-input v-model="form.supplierLots" placeholder="请输入供应商批次" />
        </el-form-item>
        <el-form-item label="创建时间" prop="creationTime">
          <el-input v-model="form.creationTime" placeholder="请输入创建时间" />
        </el-form-item>
        <el-form-item label="创建人" prop="createdBy">
          <el-input v-model="form.createdBy" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updated">
          <el-input v-model="form.updated" placeholder="请输入更新时间" />
        </el-form-item>
        <el-form-item label="更新人" prop="updater">
          <el-input v-model="form.updater" placeholder="请输入更新人" />
        </el-form-item>
        <el-form-item label="删除时间" prop="deletionTime">
          <el-input v-model="form.deletionTime" placeholder="请输入删除时间" />
        </el-form-item>
        <el-form-item label="物料计划使用年月" prop="planDate">
          <el-input v-model="form.planDate" placeholder="请输入物料计划使用年月" />
        </el-form-item>
        <el-form-item label="厂家" prop="manufacturers">
          <el-input v-model="form.manufacturers" placeholder="请输入厂家" />
        </el-form-item>
        <el-form-item label="货号" prop="articleNumber">
          <el-input v-model="form.articleNumber" placeholder="请输入货号" />
        </el-form-item>
        <el-form-item label="物料标准名称" prop="materialStandardName">
          <el-input v-model="form.materialStandardName" placeholder="请输入物料标准名称" />
        </el-form-item>
        <el-form-item label="生产计划日期" prop="productMonth">
          <el-date-picker clearable
            v-model="form.productMonth"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择生产计划日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="生产计划mpm代码" prop="mpmCode">
          <el-input v-model="form.mpmCode" placeholder="请输入生产计划mpm代码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listReceived_inventory, getReceived_inventory, delReceived_inventory, addReceived_inventory, updateReceived_inventory } from "@/api/autoacct/received_inventory";

export default {
  name: "Received_inventory",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 已经领料库存表格数据
      received_inventoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        factory: null,
        material: null,
        materialDesc: null,
        packingSpecifications: null,
        batch: null,
        productionBatches: null,
        nonRestrictedInventory: null,
        basicUnit: null,
        qualityInspection: null,
        frozen: null,
        shelfExpirationDate: null,
        recentReceiptDate: null,
        manufactureDate: null,
        oldMaterialNumber: null,
        storageLocation: null,
        storageLocationDesc: null,
        rawMaterialBatch: null,
        supplierLots: null,
        creationTime: null,
        createdBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null,
        planDate: null,
        manufacturers: null,
        articleNumber: null,
        materialStandardName: null,
        orderNumber: null
        // productMonth: null,
        // mpmCode: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询已经领料库存列表 */
    getList() {
      this.loading = true;
      listReceived_inventory(this.queryParams).then(response => {
        this.received_inventoryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        factory: null,
        material: null,
        materialDesc: null,
        packingSpecifications: null,
        batch: null,
        productionBatches: null,
        nonRestrictedInventory: null,
        basicUnit: null,
        qualityInspection: null,
        frozen: null,
        shelfExpirationDate: null,
        recentReceiptDate: null,
        manufactureDate: null,
        oldMaterialNumber: null,
        storageLocation: null,
        storageLocationDesc: null,
        rawMaterialBatch: null,
        supplierLots: null,
        creationTime: null,
        createdBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null,
        planDate: null,
        manufacturers: null,
        articleNumber: null,
        materialStandardName: null,
        productMonth: null,
        mpmCode: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加已经领料库存";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getReceived_inventory(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改已经领料库存";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateReceived_inventory(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReceived_inventory(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除已经领料库存编号为"' + ids + '"的数据项？').then(function() {
        return delReceived_inventory(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('autoacct/received_inventory/export', {
        ...this.queryParams
      }, `received_inventory_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
