<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="130px">
      <el-form-item label="行号" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入行号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工厂代码" prop="factoryCode">
<!--        <el-input-->
<!--          v-model="queryParams.factoryCode"-->
<!--          placeholder="请输入工厂代码"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
        <el-select v-model="queryParams.factoryList" multiple  placeholder="请选择活动区域">
          <el-option label="康方生物" value="1000"></el-option>
          <el-option label="康方药业" value="1050"></el-option>
          <el-option label="康方赛诺" value="1030"></el-option>
          <el-option label="康融广东" value="1060"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="MPM代码" prop="mpmCode">
        <el-input
          v-model="queryParams.mpmCode"
          placeholder="请输入MPM代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料代码" prop="material">
        <el-input
          v-model="queryParams.material"
          placeholder="请输入物料代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>

    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['autoacct:BOMrequire:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['autoacct:BOMrequire:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['autoacct:BOMrequire:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['autoacct:BOMrequire:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
        >导入
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="requireList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="行号" align="center" prop="id" width="80" />
      <el-table-column label="工厂代码" align="center" prop="factoryCode" />
      <el-table-column label="核算编码" align="center" prop="mpmCode" />
      <el-table-column label="核算名称" align="center" prop="mpmName" />
      <el-table-column label="生产工序" align="center" prop="productionProcess" />
      <el-table-column label="物料代码" align="center" prop="material" />
      <el-table-column label="批用量" align="center" prop="batchDosage" />
      <el-table-column label="物料描述" align="center" prop="materialDes" />
      <el-table-column label="基本计量单位" align="center" prop="units" />
      <el-table-column label="厂家" align="center" prop="manufacturers" />
      <el-table-column label="替代组" align="center" prop="alternativeGroup" />
      <el-table-column label= "优先级" align="center" prop="priority" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['autoacct:BOMrequire:edit']"
          >修改</el-button>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['autoacct:BOMrequire:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改BOM-物料需求对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="工厂代码" prop="factoryCode">
          <el-input :disabled="true" v-model="form.factoryCode" placeholder="请输入工厂代码" >
          </el-input>
        </el-form-item>
        <el-form-item label="核算代码" prop="mpmCode">
          <el-input :disabled="true" v-model="form.mpmCode" placeholder="请输入核算代码" >
            <el-button v-if="!value1" slot="append" style="color: red"  icon="el-icon-edit"  @click="innerOpen=true">点击选择</el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="核算名称" prop="mpmName">
          <el-input :disabled="true" v-model="form.mpmName" placeholder="请输入核算名称" />
        </el-form-item>
        <el-form-item label="生产工序" prop="productionProcess">
          <el-input v-model="form.productionProcess" placeholder="请输入生产工序" />
        </el-form-item>
<!--        <el-form-item label="组件SAP代码" prop="componentSapCode">-->
<!--          <el-input :disabled="true" v-model="form.componentSapCode" placeholder="请输入组件SAP代码" >-->
<!--          </el-input>-->
<!--        </el-form-item>-->
        <el-form-item label="物料代码”" prop="material">
          <el-input :disabled="true" v-model="form.material" placeholder="请输入物料代码">
            <el-button slot="append" style="color: red"  icon="el-icon-edit"  @click="innerOpen2=true">点击选择</el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="物料描述“" prop="materialDesc">
          <el-input :disabled="true" v-model="form.materialDesc" placeholder="请输入物料描述"/>
        </el-form-item>
        <el-form-item label="批用量" prop="batchDosage">
          <el-input v-model="form.batchDosage" placeholder="请输入批用量" />
        </el-form-item>
        <el-form-item label="替代组" prop="units">
          <el-input v-model="form.alternativeGroup" placeholder="替代组" />
        </el-form-item>
        <el-form-item label="优先级" prop="manufacturers">
          <el-input v-model="form.priority" placeholder="优先级" />

        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>

      <el-dialog
        width="50%"
        title="核算编码表"
        :visible.sync="innerOpen"
        append-to-body
      >
        <MpmData @onEmitMPMCodes="onEmitMPMCodes"></MpmData>
      </el-dialog>

      <el-dialog
        width="90%"
        title="生产计划表"
        :visible.sync="innerOpen1"
        append-to-body
      >
        <ProPlanData @emitCheckValue="emitCheckValue"></ProPlanData>
      </el-dialog>
      <el-dialog
        width="100%"
        title="物料表"
        :visible.sync="innerOpen2"
        append-to-body
      >
        <InventoryData @onEmitCheckValue="emitCheckInventoryData"></InventoryData>
      </el-dialog>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport"/>
            是否根据行号更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRequire, getRequire, delRequire, addRequire, updateRequire } from "@/api/autoacct/require";
import {getToken} from "@/utils/auth";
import MpmData from "@/views/autoacct/components/MpmData";
import ProPlanData from "@/views/autoacct/components/ProPlanData";
import InventoryData from "@/views/autoacct/components/InventoryData";
import {mapGetters, mapState} from "vuex";


export default {
  name: "Require",
  components: {InventoryData, ProPlanData, MpmData},
  data() {
    return {
      date:null,
      innerOpen1:false,
      innerOpen:false,
      innerOpen2:false,
      value1:null,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // BOM-物料需求表格数据
      requireList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: null,
        prodPlanId: null,
        mpmCode: null,
        mpmName: null,
        productionProcess: null,
        componentSapCode: null,
        componentDescription: null,
        units: null,
        batchDosage: null,
        materialPlanCodeCheck: null,
        creationTime: null,
        createdBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null,
        planDate: null,
        prodBatchValue: null,
        prodValue: null,
        alternativeGroup: null,
        priority: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/autoacct/BOMrequire/importData"
      }
    };
  },
  created() {
    this.queryParams.factoryList = this.user.autoacctFactory
    this.getList();
  },
  methods: {

    watchDate(){
      let month = '' + (this.date.getMonth() + 1),
        day = '' + this.date.getDate(),
        year = this.date.getFullYear();
      if (month.length < 2)
        month = '0' + month;
      // if (day.length < 2)
      //   day = '0' + day;
      this.form.planDate = [year, month].join('')
      // console.log([year, month, day].join(''))
    },

    emitCheckInventoryData(param){
      this.form.componentSapCode = param.id
      this.form.componentDescription = param.materialDesc
      this.form.material = param.material
      this.form.materialDesc = param.materialDesc

      this.innerOpen2 = false
    },
    emitCheckValue(param){
        this.form.prodPlanId = param.id
        this.form.mpmCode = param.mpmCode
        this.form.mpmName = param.mpmName

      this.innerOpen1 = false
    },
    onEmitMPMCodes(param1){
        // console.log(param1[0])
        this.form.mpmCode = param1.mpmCode
        this.form.mpmName = param1.mpmName
      this.innerOpen = false
    },
    /** 查询BOM-物料需求列表 */
    getList() {
      this.loading = true;
      listRequire(this.queryParams).then(response => {
        this.requireList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        prodPlanId: null,
        mpmCode: null,
        mpmName: null,
        productionProcess: null,
        componentSapCode: null,
        componentDescription: null,
        units: null,
        batchDosage: null,
        materialPlanCodeCheck: null,
        creationTime: null,
        createdBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null,
        planDate: null,
        prodBatchValue: null,
        prodValue: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加BOM-物料需求";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      console.log(row)
      this.reset();
      const id = row.id || this.ids
      getRequire(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改BOM-物料需求";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$confirm('是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateRequire(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addRequire(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
        // this.$message({
        //   type: 'success',
        //   message: '修改成功!'
        // });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消修改'
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('此操作将永久修改该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = row.id || this.ids;
        this.$modal.confirm('是否确认删除BOM-物料需求编号为"' + ids + '"的数据项？').then(function() {
          return delRequire(ids);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
        // this.$message({
        //   type: 'success',
        //   message: '成功!'
        // });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });

    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('autoacct/BOMrequire/export', {
        ...this.queryParams
      }, `Bom物料需求导出_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('autoacct/BOMrequire/importTemplate', {}, `BOM导入模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  },
  computed:{
    ...mapState({user:state => state.user})
  }
};
</script>
