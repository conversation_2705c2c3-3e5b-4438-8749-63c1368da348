# 更新操作响应处理优化说明

## 问题背景

在 `OaFormUploadService.java` 中，当调用费用报销头部更新接口时，API 返回的响应格式为：
```json
{"code":200000,"msg":"成功","bizId":"04a62f8c6d7d4ca6b524828a7636ca7f","data":0}
```

原有代码直接尝试从 `data.header.document_num` 提取单据号，但由于 `data` 字段为数字 `0` 而非对象，导致 `NullPointerException`。

## 修改方案

### 🎯 核心思路
区分**创建操作**和**更新操作**，采用不同的响应处理策略：

1. **更新操作**：当 API 响应成功但无法从响应中提取 `document_num` 时，直接使用 `existingDocumentNum`（原始单据号）
2. **创建操作**：必须从响应中获取新创建的 `document_num`，如果获取失败则抛出异常

### 🔧 实现逻辑

#### 1. 操作类型判断
```java
// 检查是否为更新操作（通过原始请求中是否包含单据号来判断）
String existingDocumentNum = jsonObject.getString("fd_3d7a5f5daa7a0a");
boolean isUpdateOperation = StringUtils.hasText(existingDocumentNum);
```

#### 2. API响应状态检查
```java
// 检查API响应是否成功
Integer resCode = resultObj.getInteger("resCode");
Integer code = resultObj.getInteger("code");
boolean isApiSuccess = (resCode != null && resCode == 200000) || (code != null && code == 200000);
```

#### 3. 分场景处理

##### 场景1: 更新操作 + API成功
```java
if (isApiSuccess && isUpdateOperation) {
    // 尝试从响应中提取document_num
    try {
        JSONObject data = resultObj.getJSONObject("data");
        if (data != null) {
            JSONObject header = data.getJSONObject("header");
            if (header != null) {
                documentNum = header.getString("document_num");
            }
        }
    } catch (Exception e) {
        log.debug("更新操作：无法从响应中提取document_num，这是正常情况: {}", e.getMessage());
    }
    
    // 如果响应中没有document_num，直接使用原始单据号
    if (documentNum == null) {
        documentNum = existingDocumentNum;
        log.info("更新操作成功，使用原始单据号作为返回值: {}", documentNum);
    } else {
        log.info("更新操作成功，从响应中获取到单据号: {}", documentNum);
    }
}
```

##### 场景2: 创建操作 + API成功
```java
else if (isApiSuccess && !isUpdateOperation) {
    // 创建操作必须从响应中获取document_num
    try {
        JSONObject data = resultObj.getJSONObject("data");
        if (data != null) {
            JSONObject header = data.getJSONObject("header");
            if (header != null) {
                documentNum = header.getString("document_num");
                log.info("创建操作成功，从响应中获取到单据号: {}", documentNum);
            }
        }
    } catch (Exception e) {
        log.error("创建操作：无法从响应中提取document_num，响应内容: {}", result);
        throw new RuntimeException("创建操作失败：无法从响应中获取单据号");
    }
    
    if (documentNum == null) {
        log.error("创建操作：响应中缺少document_num，响应: {}", result);
        throw new RuntimeException("创建操作失败：响应中缺少单据号");
    }
}
```

##### 场景3: API调用失败
```java
else {
    // API调用失败的情况
    log.error("API调用失败，响应: {}", result);
    throw new RuntimeException("API调用失败，请检查响应结果");
}
```

## 修改优势

### ✅ 解决的问题
1. **消除NullPointerException**：通过安全的空值检查和异常捕获
2. **智能处理更新操作**：当响应中 `data` 为数字时，自动使用原始单据号
3. **保持创建操作严格性**：创建操作仍然要求从响应中获取新单据号
4. **增强错误处理**：提供详细的日志信息和明确的错误提示

### 🎯 核心特性
1. **操作类型感知**：根据是否为更新操作采用不同策略
2. **响应格式兼容**：支持 `resCode` 和 `code` 两种状态码格式
3. **优雅降级**：更新操作时优先使用响应中的单据号，失败时使用原始单据号
4. **详细日志**：记录具体的处理路径便于调试

## 测试场景

### 🧪 更新操作测试
1. **响应包含完整结构**：`data.header.document_num` 存在
   - 预期：使用响应中的单据号
   - 日志：`更新操作成功，从响应中获取到单据号: {documentNum}`

2. **响应data为数字0**：如当前问题场景
   - 预期：使用原始单据号 `existingDocumentNum`
   - 日志：`更新操作成功，使用原始单据号作为返回值: {documentNum}`

3. **响应data为null或其他格式**：
   - 预期：使用原始单据号 `existingDocumentNum`
   - 日志：`更新操作成功，使用原始单据号作为返回值: {documentNum}`

### 🧪 创建操作测试
1. **响应包含完整结构**：`data.header.document_num` 存在
   - 预期：使用响应中的单据号
   - 日志：`创建操作成功，从响应中获取到单据号: {documentNum}`

2. **响应缺少document_num**：
   - 预期：抛出 `RuntimeException`
   - 日志：`创建操作：响应中缺少document_num`

### 🧪 API失败测试
1. **code/resCode != 200000**：
   - 预期：抛出 `RuntimeException`
   - 日志：`API调用失败，响应: {result}`

## 业务逻辑说明

### 📋 更新操作的合理性
对于更新操作，使用原始单据号作为返回值是合理的，因为：
1. **单据号不变**：更新操作不会改变单据的唯一标识
2. **操作确定性**：更新操作本身就知道要操作哪个单据
3. **API设计差异**：更新接口可能不返回完整的单据信息，只返回操作状态

### 📋 创建操作的严格性
对于创建操作，必须从响应中获取单据号，因为：
1. **新单据生成**：创建操作会生成新的单据号
2. **系统分配**：单据号由云简系统分配，客户端无法预知
3. **后续操作依赖**：返回的单据号用于后续的查询和操作

## 日志示例

### 成功场景
```
INFO  - 更新操作成功，使用原始单据号作为返回值: FSHA000000290
INFO  - 创建操作成功，从响应中获取到单据号: FSHA000000291
```

### 异常场景
```
ERROR - 创建操作：无法从响应中提取document_num，响应内容: {"code":200000,"data":0}
ERROR - API调用失败，响应: {"code":500000,"msg":"系统错误"}
```

通过这次优化，系统现在能够智能地处理不同类型的操作和响应格式，确保在各种场景下都能正确获取单据号，避免了NullPointerException的发生。
