package com.akesobio.report.clinical.service.impl;

import cn.hutool.core.date.DateUtil;
import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.common.utils.holiday.TianApiHolidayUtils;
import com.akesobio.report.clinical.domain.ClinicalFolder;
import com.akesobio.report.clinical.mapper.ClinicalFolderMapper;
import com.akesobio.report.clinical.service.ClinicalFolderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * ServiceImpl
 *
 * <AUTHOR> <PERSON>
 * @version 2003/04/01
 * @date 1956/09/12
 */
@Service
@DataSource(value = DataSourceType.EKP)
public class ClinicalFolderServiceImpl extends ServiceImpl<ClinicalFolderMapper, ClinicalFolder> implements ClinicalFolderService {
    @Autowired
    private ClinicalFolderMapper clinicalFolderMapper;

    @Override
    public List<ClinicalFolder> queryClinicalFolderList(ClinicalFolder o) {
        List<ClinicalFolder> list = clinicalFolderMapper.queryClinicalFolderList(o);
        for (ClinicalFolder e : list) {
            if (e.getApplicationDate() == null || e.getDispatchDate() == null) {
                e.setIsOverdue("");
            } else {
                int workingDays = TianApiHolidayUtils.calculateWorkingDays(e.getApplicationDate(), e.getDispatchDate());
                if (workingDays > 3)
                    e.setIsOverdue("是");
                else
                    e.setIsOverdue("否");
            }// CTA发起
            if (e.getCraBeginDate() == null || e.getCraEndDate() == null) {
                e.setIsOverduecra("");
            } else {
                int workingDays = TianApiHolidayUtils.calculateWorkingDays(e.getCraBeginDate(), e.getCraEndDate());
                if (workingDays > 5)
                    e.setIsOverduecra("是");
                else
                    e.setIsOverduecra("否");
            }// CRA
            if (e.getCtaBeginDate() == null || e.getCtaEndDate() == null) {
                e.setIsOverduecta("");
            } else {
                int workingDays = TianApiHolidayUtils.calculateWorkingDays(e.getCtaBeginDate(), e.getCtaEndDate());
                if (workingDays > 10)
                    e.setIsOverduecta("是");
                else
                    e.setIsOverduecta("否");
            }// CTA归档

        }
        return list;
    }
}