package com.akesobio.report.clinical.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * entity 实体类 外部系统权限管理申请
 *
 * <AUTHOR>
 * @version 2003/04/01
 * @date 1956/09/12
 */
@Data
@ExcelIgnoreUnannotated
public class ExternalSystemPermissions implements Serializable {
    private static final long serialVersionUID = 1L;

    private String link;//
    private String linkSuffix;//
    private String id;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("申请日期")
    private LocalDate applicationDate;//

    @ExcelProperty("单号")
    private String oddNumber;//

    @ExcelProperty("申请人")
    private String name;//

    @ExcelProperty("项目号")
    private String projectNumber;//

    @ExcelProperty("项目所属分部")
    private String projectBranch;//

    @ExcelProperty("申请外部系统")
    private String externalSystem;//

    @ExcelProperty("项目CTA")
    private String ctaName;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("项目组CTA处理日期")
    private LocalDate cta;//

    @ExcelProperty("申请人CRA复核开通情况")
    private String cra;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("系统确认开通/关闭日期")
    private LocalDate aOrDTime;//

    @ExcelProperty("当前节点")
    private String currentSession;//

    @ExcelProperty("当前处理人")
    private String currentProcessor;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("节点到达时间")
    private LocalDate arrivalTime;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("最新审批时间")
    private LocalDate lastDate;//

    @ExcelProperty("文档状态")
    private String documentStatus;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("PM审批到达时间")
    private LocalDate pmBeginDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("PM审批通过时间")
    private LocalDate pmEndDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("CTA处理到达时间")
    private LocalDate ctaBeginDate;//

    @ExcelProperty("是否超期")
    private String isOverdue;// 是否超期 3

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("CTA处理通过时间")
    private LocalDate ctaEndDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("申请人复核到达时间")
    private LocalDate applicantBeginDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("申请人复核通过时间")
    private LocalDate applicantEndDate;

    /**
     * 日期查询条件
     */
    private String startDate;
    private String endDate;
}