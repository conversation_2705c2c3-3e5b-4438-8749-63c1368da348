package com.akesobio.report.clinical.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * entity 实体类
 *
 * <AUTHOR>
 * @version 2003/04/01
 * @date 1956/09/12
 */
@Data
@ExcelIgnoreUnannotated
public class ClinicalFolder implements Serializable {
    private static final long serialVersionUID = 1L;

    private String link;//
    private String linkSuffix;//
    private String id;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("申请日期")
    private LocalDate applicationDate;//

    @ExcelProperty("单号")
    private String oddNumber;//

    @ExcelProperty("申请人")
    private String name;//

    @ExcelProperty("项目号")
    private String projectNumber;//

    @ExcelProperty("项目所属分部")
    private String projectBranch;//

    @ExcelProperty("申请事宜")
    private String matters;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("任务派遣日期")
    private LocalDate dispatchDate;//

    @ExcelProperty("是否超期")
    private String isOverdue;// 是否超期 3

    @ExcelProperty("授权人员")
    private String sqPerson;//

    @ExcelProperty("邮箱")
    private String email;//

    @ExcelProperty("授权人职务")
    private String sqPosition;//

    @ExcelProperty("授权人所属部门")
    private String sqDepartment;//

    @ExcelProperty("项目层面/中心层面")
    private String projectCenter;//

    @ExcelProperty("中心编号/中心名称")
    private String centerNumber;//

    @ExcelProperty("申请权限")
    private String sqPermission;//

    @ExcelProperty("CRA确认权限已开通")
    private String isPermission;//

    @ExcelProperty("CTA复核上传")
    private String cta;//

    @ExcelProperty("当前节点")
    private String currentSession;//

    @ExcelProperty("当前处理人")
    private String currentProcessor;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("节点到达时间")
    private LocalDate arrivalTime;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("最新审批时间")
    private LocalDate lastDate;//

    @ExcelProperty("文档状态")
    private String documentStatus;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("PM审批到达时间")
    private LocalDate pmBeginDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("PM审批通过时间")
    private LocalDate pmEndDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("文件夹负责人审批到达时间")
    private LocalDate wjjBeginDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("文件夹负责人审批通过时间")
    private LocalDate wjjEndDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("信息组审批到达时间")
    private LocalDate xxzBeginDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("信息组审批通过时间")
    private LocalDate xxzEndDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("CRA审批资质到达时间")
    private LocalDate craBeginDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("CRA审批资质通过时间")
    private LocalDate craEndDate;//

    @ExcelProperty("CRA是否超期")
    private String isOverduecra;// 是否超期 3

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("项目组CTA审核到达时间")
    private LocalDate ctaBeginDate;//

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("项目组CTA审核通过时间")
    private LocalDate ctaEndDate;//

    @ExcelProperty("CTA归档是否超期")
    private String isOverduecta;// 是否超期 3

    /**
     * 日期查询条件
     */
    private String startDate;
    private String endDate;
}