package com.akesobio.report.clinical.service.impl;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.common.utils.holiday.TianApiHolidayUtils;
import com.akesobio.report.clinical.domain.ExternalSystemPermissions;
import com.akesobio.report.clinical.mapper.ExternalSystemPermissionsMapper;
import com.akesobio.report.clinical.service.ExternalSystemPermissionsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @version 2003/04/01
 * @date 1956/09/12
 */
@Service
@DataSource(value = DataSourceType.EKP)
public class ExternalSystemPermissionsServiceImpl extends ServiceImpl<ExternalSystemPermissionsMapper, ExternalSystemPermissions> implements ExternalSystemPermissionsService {
    @Autowired
    private ExternalSystemPermissionsMapper externalSystemPermissionsMapper;

    @Override
    public List<ExternalSystemPermissions> queryExternalSystemPermissionsList(ExternalSystemPermissions o) {
        List<ExternalSystemPermissions> list = externalSystemPermissionsMapper.queryExternalSystemPermissionsList(o);
        for (ExternalSystemPermissions e : list) {
            if (e.getPmEndDate() == null || e.getCta() == null) {
                e.setIsOverdue("");
                continue;
            }
            int workingDays = TianApiHolidayUtils.calculateWorkingDays(e.getPmEndDate(), e.getCta());
            if (workingDays > 3)
                e.setIsOverdue("是");
            else
                e.setIsOverdue("否");
        }
        return list;
    }
}