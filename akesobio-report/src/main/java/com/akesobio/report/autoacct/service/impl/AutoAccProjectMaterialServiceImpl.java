package com.akesobio.report.autoacct.service.impl;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.autoacct.domain.AutoAccProjectMaterial;
import com.akesobio.report.autoacct.domain.MpmInfo;
import com.akesobio.report.autoacct.mapper.AutoAccProjectMaterialMapper;
import com.akesobio.report.autoacct.service.IAutoAccProjectMaterialService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class AutoAccProjectMaterialServiceImpl extends ServiceImpl<AutoAccProjectMaterialMapper, AutoAccProjectMaterial> implements IAutoAccProjectMaterialService {
    @Resource
    private AutoAccProjectMaterialMapper autoAccProjectMaterialMapper;

    @Override
    public String importData(List<AutoAccProjectMaterial> dataList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(dataList) || dataList.size() == 0)
        {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        int updateNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (AutoAccProjectMaterial autoAccProjectMaterial : dataList)
        {
            try
            {
                // 根据行号判断新增还是更新
                if (autoAccProjectMaterial.getId() != null && autoAccProjectMaterial.getId() > 0) {
                    // 更新操作
                    if (isUpdateSupport) {
                        AutoAccProjectMaterial existingRecord = getById(autoAccProjectMaterial.getId());
                        if (existingRecord != null) {
                            updateById(autoAccProjectMaterial);
                            updateNum++;
                            successMsg.append("<br/>" + updateNum + "、行号 " + autoAccProjectMaterial.getId() + " 更新成功");
                        } else {
                            failureNum++;
                            failureMsg.append("<br/>行号 " + autoAccProjectMaterial.getId() + " 对应的记录不存在");
                        }
                    } else {
                        failureNum++;
                        failureMsg.append("<br/>行号 " + autoAccProjectMaterial.getId() + " 的数据已存在且不允许更新");
                    }
                } else {
                    // 新增操作 - 直接保存
                    this.save(autoAccProjectMaterial);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、行号 " + autoAccProjectMaterial.getId() + " 导入成功");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、行号 " + autoAccProjectMaterial.getId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + (successNum + updateNum) + " 条，其中新增 " + successNum + " 条，更新 " + updateNum + " 条");
        }
        
        if (updateNum > 0) {
            successMsg.append("，更新 " + updateNum + " 条");
        }
        
        return successMsg.toString();
    }

    /**
     * 查询BOM功能模块
     *
     * @param id BOM功能模块主键
     * @return BOM功能模块
     */
    @Override
    public AutoAccProjectMaterial selectAutoAccProjectMaterialById(Long id)
    {
        return autoAccProjectMaterialMapper.selectAutoAccProjectMaterialById(id);
    }

    /**
     * 查询BOM功能模块列表
     *
     * @param autoAccProjectMaterial BOM功能模块
     * @return BOM功能模块
     */
    @Override
    public List<AutoAccProjectMaterial> selectAutoAccProjectMaterialList(AutoAccProjectMaterial autoAccProjectMaterial)
    {
        return autoAccProjectMaterialMapper.selectAutoAccProjectMaterialList(autoAccProjectMaterial);
    }

    /**
     * 新增BOM功能模块
     *
     * @param autoAccProjectMaterial BOM功能模块
     * @return 结果
     */
    @Override
    public int insertAutoAccProjectMaterial(AutoAccProjectMaterial autoAccProjectMaterial)
    {
        return autoAccProjectMaterialMapper.insertAutoAccProjectMaterial(autoAccProjectMaterial);
    }

    /**
     * 修改BOM功能模块
     *
     * @param autoAccProjectMaterial BOM功能模块
     * @return 结果
     */
    @Override
    public int updateAutoAccProjectMaterial(AutoAccProjectMaterial autoAccProjectMaterial)
    {
        return autoAccProjectMaterialMapper.updateAutoAccProjectMaterial(autoAccProjectMaterial);
    }

    /**
     * 批量删除BOM功能模块
     *
     * @param ids 需要删除的BOM功能模块主键
     * @return 结果
     */
    @Override
    public int deleteAutoAccProjectMaterialByIds(Long[] ids)
    {
        return autoAccProjectMaterialMapper.deleteAutoAccProjectMaterialByIds(ids);
    }

    /**
     * 删除BOM功能模块信息
     *
     * @param id BOM功能模块主键
     * @return 结果
     */
    @Override
    public int deleteAutoAccProjectMaterialById(Long id)
    {
        return autoAccProjectMaterialMapper.deleteAutoAccProjectMaterialById(id);
    }
}
