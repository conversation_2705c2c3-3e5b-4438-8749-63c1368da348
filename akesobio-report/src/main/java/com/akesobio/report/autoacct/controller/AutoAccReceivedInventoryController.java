package com.akesobio.report.autoacct.controller;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.autoacct.domain.AutoAccReceivedInventory;
import com.akesobio.report.autoacct.service.IAutoAccReceivedInventoryService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 已经领料库存Controller
 * 
 * <AUTHOR>
 * @date 2024-11-06
 */
@RestController
@RequestMapping("/autoacct/received_inventory")
public class AutoAccReceivedInventoryController extends BaseController
{
    @Resource
    private IAutoAccReceivedInventoryService autoAccReceivedInventoryService;

    /**
     * 查询已经领料库存列表
     */
    @PreAuthorize("@ss.hasPermi('autoacct:received_inventory:list')")
    @GetMapping("/list")
    public TableDataInfo list(AutoAccReceivedInventory autoAccReceivedInventory)
    {
        startPage();
        List<AutoAccReceivedInventory> list = autoAccReceivedInventoryService.selectAutoAccReceivedInventoryList(autoAccReceivedInventory);
        return getDataTable(list);
    }

    /**
     * 导出已经领料库存列表
     */
    @PreAuthorize("@ss.hasPermi('autoacct:received_inventory:export')")
    @Log(title = "已经领料库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AutoAccReceivedInventory autoAccReceivedInventory)
    {
        List<AutoAccReceivedInventory> list = autoAccReceivedInventoryService.selectAutoAccReceivedInventoryList(autoAccReceivedInventory);
        ExcelUtil<AutoAccReceivedInventory> util = new ExcelUtil<AutoAccReceivedInventory>(AutoAccReceivedInventory.class);
        util.exportExcel(response, list, "已经领料库存数据");
    }

    /**
     * 获取已经领料库存详细信息
     */
    @PreAuthorize("@ss.hasPermi('autoacct:received_inventory:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(autoAccReceivedInventoryService.selectAutoAccReceivedInventoryById(id));
    }

    /*
     * 已弃用：此功能现在通过定时任务自动处理
     * 相关定时任务：RyTask.refreshReceivedInventory()
     * 弃用日期：2025-01-31
     * 原因：SAP已领料库存数据现在通过API自动同步，手动操作可能导致数据不一致
     */
    @Deprecated
    @PreAuthorize("@ss.hasPermi('autoacct:received_inventory:add')")
    @Log(title = "已经领料库存", businessType = BusinessType.INSERT)
    // @PostMapping
    public AjaxResult add(@RequestBody AutoAccReceivedInventory autoAccReceivedInventory)
    {
        // 已禁用：数据现在通过定时任务自动更新
        /*
        return toAjax(autoAccReceivedInventoryService.insertAutoAccReceivedInventory(autoAccReceivedInventory));
        */
        return error("此功能已弃用，数据现在通过定时任务自动更新");
    }

    /*
     * 已弃用：此功能现在通过定时任务自动处理
     * 相关定时任务：RyTask.refreshReceivedInventory()
     * 弃用日期：2025-01-31
     * 原因：SAP已领料库存数据现在通过API自动同步，手动操作可能导致数据不一致
     */
    @Deprecated
    @PreAuthorize("@ss.hasPermi('autoacct:received_inventory:edit')")
    @Log(title = "已经领料库存", businessType = BusinessType.UPDATE)
    // @PutMapping
    public AjaxResult edit(@RequestBody AutoAccReceivedInventory autoAccReceivedInventory)
    {
        // 已禁用：数据现在通过定时任务自动更新
        /*
        return toAjax(autoAccReceivedInventoryService.updateAutoAccReceivedInventory(autoAccReceivedInventory));
        */
        return error("此功能已弃用，数据现在通过定时任务自动更新");
    }

    /*
     * 已弃用：此功能现在通过定时任务自动处理
     * 相关定时任务：RyTask.refreshReceivedInventory()
     * 弃用日期：2025-01-31
     * 原因：SAP已领料库存数据现在通过API自动同步，手动操作可能导致数据不一致
     */
    @Deprecated
    @PreAuthorize("@ss.hasPermi('autoacct:received_inventory:remove')")
    @Log(title = "已经领料库存", businessType = BusinessType.DELETE)
	// @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 已禁用：数据现在通过定时任务自动更新
        /*
        return toAjax(autoAccReceivedInventoryService.deleteAutoAccReceivedInventoryByIds(ids));
        */
        return error("此功能已弃用，数据现在通过定时任务自动更新");
    }
}
