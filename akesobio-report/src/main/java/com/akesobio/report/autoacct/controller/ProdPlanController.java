package com.akesobio.report.autoacct.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.utils.SecurityUtils;
import com.akesobio.report.autoacct.domain.AutoMaterialAcctInventory;
import com.akesobio.report.autoacct.domain.ProdPlan;
import com.akesobio.report.autoacct.service.IAutoMaterialAcctProdPlanService;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;

import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 生产计划Controller
 * 
 * <AUTHOR>
 * @date 2024-02-26
 */
@RestController
@RequestMapping("/autoacct/prodplan")
public class ProdPlanController extends BaseController
{
    @Autowired
    private IAutoMaterialAcctProdPlanService autoMaterialAcctProdPlanService;

    @Log(title = "生产计划导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<ProdPlan> util = new ExcelUtil<ProdPlan>(ProdPlan.class);
        List<ProdPlan> userList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        autoMaterialAcctProdPlanService.importProdPlan(userList, updateSupport, operName);
        String message =  "导入成功";
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<ProdPlan> util = new ExcelUtil<ProdPlan>(ProdPlan.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 查询生产计划列表
     */
    @PreAuthorize("@ss.hasPermi('autoacct:proplan:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProdPlan prodPlan)
    {
        startPage();
        List<ProdPlan> list = autoMaterialAcctProdPlanService.selectAutoMaterialAcctProdPlanList(prodPlan);
        return getDataTable(list);
    }

    /**
     * 导出生产计划列表
     */
    @PreAuthorize("@ss.hasPermi('autoacct:proplan:export')")
    @Log(title = "生产计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProdPlan prodPlan)
    {
        List<ProdPlan> list = autoMaterialAcctProdPlanService.selectAutoMaterialAcctProdPlanList(prodPlan);
        ExcelUtil<ProdPlan> util = new ExcelUtil<ProdPlan>(ProdPlan.class);
        util.exportExcel(response, list, "生产计划数据");
    }

    /**
     * 获取生产计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('autoacct:proplan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(autoMaterialAcctProdPlanService.selectAutoMaterialAcctProdPlanById(id));
    }

    /**
     * 新增生产计划
     */
    @PreAuthorize("@ss.hasPermi('autoacct:proplan:add')")
    @Log(title = "生产计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProdPlan prodPlan)
    {
        prodPlan.setCreatedBy(SecurityUtils.getUsername());
        prodPlan.setCreationTime(new Date().getTime());
        return toAjax(autoMaterialAcctProdPlanService.insertAutoMaterialAcctProdPlan(prodPlan));
    }

    /**
     * 修改生产计划
     */
    @PreAuthorize("@ss.hasPermi('autoacct:proplan:edit')")
    @Log(title = "生产计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProdPlan prodPlan)
    {
        prodPlan.setUpdater(SecurityUtils.getUsername());
        prodPlan.setUpdateTime(new Date());
        return toAjax(autoMaterialAcctProdPlanService.updateAutoMaterialAcctProdPlan(prodPlan));
    }

    /**
     * 删除生产计划
     */
    @PreAuthorize("@ss.hasPermi('autoacct:proplan:remove')")
    @Log(title = "生产计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(autoMaterialAcctProdPlanService.deleteAutoMaterialAcctProdPlanByIds(ids));
    }



}
