package com.akesobio.report.autoacct.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.autoacct.domain.MpmInfo;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctMpmInfoMapper;
import com.akesobio.report.autoacct.service.IAutoMaterialAcctMpmInfoService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * MPM编码Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class AutoMaterialAcctMpmInfoServiceImpl extends ServiceImpl<AutoMaterialAcctMpmInfoMapper, MpmInfo> implements IAutoMaterialAcctMpmInfoService
{
    @Resource
    private AutoMaterialAcctMpmInfoMapper autoMaterialAcctMpmInfoMapper;

    public String importData(List<MpmInfo> userList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(userList) || userList.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (MpmInfo user : userList)
        {
            try
            {
                LambdaQueryWrapper<MpmInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(MpmInfo::getMpmCode,user.getMpmCode());
                // 验证是否存
                int count = count(lambdaQueryWrapper);
                if (count<=0)
                {
                    save(user);
                    successMsg.append("<br/>" + successNum + "、mpm代码： " + user.getMpmCode() + " 导入成功");
                }
                else
                {

                    update(user,lambdaQueryWrapper);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、mpm代码： " + user.getMpmCode() + " 更新成功");
                }
//                else
//                {
//                    failureNum++;
//                    failureMsg.append("<br/>" + failureNum + "、mpm代码： " + user.getMpmCode() + " 已存在");
//                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、mpm代码： " + user.getMpmCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
    /**
     * 查询MPM编码
     * 
     * @param id MPM编码主键
     * @return MPM编码
     */
    @Override
    public MpmInfo selectAutoMaterialAcctMpmInfoById(Long id)
    {
        return autoMaterialAcctMpmInfoMapper.selectAutoMaterialAcctMpmInfoById(id);
    }

    /**
     * 查询MPM编码列表
     * 
     * @param mpmInfo MPM编码
     * @return MPM编码
     */
    @Override
    public List<MpmInfo> selectAutoMaterialAcctMpmInfoList(MpmInfo mpmInfo)
    {
        return autoMaterialAcctMpmInfoMapper.selectAutoMaterialAcctMpmInfoList(mpmInfo);
    }

    /**
     * 新增MPM编码
     * 
     * @param mpmInfo MPM编码
     * @return 结果
     */
    @Override
    public int insertAutoMaterialAcctMpmInfo(MpmInfo mpmInfo)
    {
        return autoMaterialAcctMpmInfoMapper.insertAutoMaterialAcctMpmInfo(mpmInfo);
    }

    /**
     * 修改MPM编码
     * 
     * @param mpmInfo MPM编码
     * @return 结果
     */
    @Override
    public int updateAutoMaterialAcctMpmInfo(MpmInfo mpmInfo)
    {

        return autoMaterialAcctMpmInfoMapper.updateAutoMaterialAcctMpmInfo(mpmInfo);
    }

    /**
     * 批量删除MPM编码
     * 
     * @param ids 需要删除的MPM编码主键
     * @return 结果
     */
    @Override
    public int deleteAutoMaterialAcctMpmInfoByIds(Long[] ids)
    {
        return autoMaterialAcctMpmInfoMapper.deleteAutoMaterialAcctMpmInfoByIds(ids);
    }

    /**
     * 删除MPM编码信息
     * 
     * @param id MPM编码主键
     * @return 结果
     */
    @Override
    public int deleteAutoMaterialAcctMpmInfoById(Long id)
    {
        return autoMaterialAcctMpmInfoMapper.deleteAutoMaterialAcctMpmInfoById(id);
    }
}
