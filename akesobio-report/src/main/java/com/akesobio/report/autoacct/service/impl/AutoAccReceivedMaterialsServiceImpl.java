package com.akesobio.report.autoacct.service.impl;

import java.util.List;
import java.util.ArrayList;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.common.utils.validation.BigDecimalValidationUtils.ValidationError;
import com.akesobio.report.autoacct.domain.MpmInfo;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctMpmInfoMapper;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctFactoryMapper;
import com.akesobio.report.autoacct.utils.AutoAcctValidationUtils;
import com.akesobio.report.autoacct.service.IAutoMaterialAcctMpmInfoService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.akesobio.report.autoacct.mapper.AutoAccReceivedMaterialsMapper;
import com.akesobio.report.autoacct.domain.AutoAccReceivedMaterials;
import com.akesobio.report.autoacct.service.IAutoAccReceivedMaterialsService;

import javax.annotation.Resource;

/**
 * 已领用工单物料Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-27
 */
@Service
public class AutoAccReceivedMaterialsServiceImpl  extends ServiceImpl<AutoAccReceivedMaterialsMapper, AutoAccReceivedMaterials> implements IAutoAccReceivedMaterialsService
{
    @Resource
    private AutoAccReceivedMaterialsMapper autoAccReceivedMaterialsMapper;
    
    @Resource
    private AutoMaterialAcctMpmInfoMapper autoMaterialAcctMpmInfoMapper;
    
    @Resource
    private AutoMaterialAcctFactoryMapper autoMaterialAcctFactoryMapper;

    @Override
    public String importData(List<AutoAccReceivedMaterials> dataList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(dataList) || dataList.size() == 0)
        {
            throw new ServiceException("导入数据不能为空！");
        }
        
        // 1. 预加载校验数据
        AutoAcctValidationUtils.ValidationData validationData = AutoAcctValidationUtils.loadValidationData(
            autoMaterialAcctFactoryMapper, null, autoMaterialAcctMpmInfoMapper);
        
        // 2. 批量校验所有数据
        List<ValidationError> validationErrors = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            AutoAccReceivedMaterials data = dataList.get(i);
            int rowIndex = i + 1;
            
            // 校验MPM代码
            ValidationError mpmError = AutoAcctValidationUtils.validateMpmCodeFast(
                data.getMpmCode(), validationData, "MPM代码", rowIndex);
            if (mpmError != null) {
                validationErrors.add(mpmError);
            }
            
            // 校验公司代码（工厂代码）
            ValidationError factoryError = AutoAcctValidationUtils.validateFactoryCodeFast(
                data.getFactoryCode(), validationData, "公司代码", rowIndex);
            if (factoryError != null) {
                validationErrors.add(factoryError);
            }
        }
        
        // 3. 如果有校验错误，抛出异常
        if (!validationErrors.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder("数据校验失败，错误如下：");
            for (ValidationError error : validationErrors) {
                errorMsg.append("<br/>第").append(error.getRowIndex()).append("行 ")
                       .append(error.getFieldName()).append("：").append(error.getErrorMessage());
            }
            throw new ServiceException(errorMsg.toString());
        }
        
        // 4. 执行导入操作
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (AutoAccReceivedMaterials data : dataList)
        {
            try
            {
                LambdaQueryWrapper<AutoAccReceivedMaterials> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(AutoAccReceivedMaterials::getOrderNumber,data.getOrderNumber());
                // 验证是否存在
                int count = count(lambdaQueryWrapper);
                if (count<=0)
                {
                    save(data);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、订单号： " + data.getOrderNumber() + " 导入成功");
                }
                else
                {
                    if (isUpdateSupport) {
                        update(data,lambdaQueryWrapper);
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、订单号： " + data.getOrderNumber() + " 更新成功");
                    } else {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、订单号： " + data.getOrderNumber() + " 已存在且不允许更新");
                    }
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、订单号： " + data.getOrderNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 查询已领用工单物料
     * 
     * @param id 已领用工单物料主键
     * @return 已领用工单物料
     */
    @Override
    public AutoAccReceivedMaterials selectAutoAccReceivedMaterialsById(Long id)
    {
        return autoAccReceivedMaterialsMapper.selectAutoAccReceivedMaterialsById(id);
    }

    /**
     * 查询已领用工单物料列表
     * 
     * @param autoAccReceivedMaterials 已领用工单物料
     * @return 已领用工单物料
     */
    @Override
    public List<AutoAccReceivedMaterials> selectAutoAccReceivedMaterialsList(AutoAccReceivedMaterials autoAccReceivedMaterials)
    {
        return autoAccReceivedMaterialsMapper.selectAutoAccReceivedMaterialsList(autoAccReceivedMaterials);
    }

    /**
     * 新增已领用工单物料
     * 
     * @param autoAccReceivedMaterials 已领用工单物料
     * @return 结果
     */
    @Override
    public int insertAutoAccReceivedMaterials(AutoAccReceivedMaterials autoAccReceivedMaterials)
    {
        return autoAccReceivedMaterialsMapper.insertAutoAccReceivedMaterials(autoAccReceivedMaterials);
    }

    /**
     * 修改已领用工单物料
     * 
     * @param autoAccReceivedMaterials 已领用工单物料
     * @return 结果
     */
    @Override
    public int updateAutoAccReceivedMaterials(AutoAccReceivedMaterials autoAccReceivedMaterials)
    {
        return autoAccReceivedMaterialsMapper.updateAutoAccReceivedMaterials(autoAccReceivedMaterials);
    }

    /**
     * 批量删除已领用工单物料
     * 
     * @param ids 需要删除的已领用工单物料主键
     * @return 结果
     */
    @Override
    public int deleteAutoAccReceivedMaterialsByIds(Long[] ids)
    {
        return autoAccReceivedMaterialsMapper.deleteAutoAccReceivedMaterialsByIds(ids);
    }

    /**
     * 删除已领用工单物料信息
     * 
     * @param id 已领用工单物料主键
     * @return 结果
     */
    @Override
    public int deleteAutoAccReceivedMaterialsById(Long id)
    {
        return autoAccReceivedMaterialsMapper.deleteAutoAccReceivedMaterialsById(id);
    }
}
