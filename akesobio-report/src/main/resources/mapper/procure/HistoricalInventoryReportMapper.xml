<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.procure.mapper.HistoricalInventoryReportMapper">
    
    <resultMap type="HistoricalInventoryReport" id="HistoricalInventoryReportResult">
        <result property="sid"    column="sid"    />
        <result property="matnr"    column="matnr"    />
        <result property="zzcms"    column="zzcms"    />
        <result property="meins"    column="meins"    />
        <result property="menge1000"    column="menge_1000"    />
        <result property="menge1020"    column="menge_1020"    />
        <result property="menge1030"    column="menge_1030"    />
        <result property="menge1050"    column="menge_1050"    />
        <result property="menge1060"    column="menge_1060"    />
        <result property="menge1070"    column="menge_1070"    />
        <result property="mengeSum"    column="menge_sum"    />
        <result property="date"    column="date"    />
    </resultMap>

    <sql id="selectHistoricalInventoryReportVo">
        select sid, matnr, zzcms, meins, menge_1000, menge_1020, menge_1030, menge_1050, menge_1060, menge_1070, menge_sum, date from HistoricalInventoryReport
    </sql>

    <select id="selectHistoricalInventoryReportList" parameterType="HistoricalInventoryReport" resultMap="HistoricalInventoryReportResult">
        <include refid="selectHistoricalInventoryReportVo"/>
        <where>  
            <if test="matnr != null  and matnr != ''"> and matnr LIKE CONCAT('%', #{matnr}, '%')</if>
            <if test="zzcms != null  and zzcms != ''"> and zzcms = #{zzcms}</if>
            <if test="meins != null  and meins != ''"> and meins = #{meins}</if>
            <if test="menge1000 != null  and menge1000 != ''"> and menge_1000 = #{menge1000}</if>
            <if test="menge1020 != null  and menge1020 != ''"> and menge_1020 = #{menge1020}</if>
            <if test="menge1030 != null  and menge1030 != ''"> and menge_1030 = #{menge1030}</if>
            <if test="menge1050 != null  and menge1050 != ''"> and menge_1050 = #{menge1050}</if>
            <if test="menge1060 != null  and menge1060 != ''"> and menge_1060 = #{menge1060}</if>
            <if test="menge1070 != null  and menge1070 != ''"> and menge_1070 = #{menge1070}</if>
            <if test="mengeSum != null  and mengeSum != ''"> and menge_sum = #{mengeSum}</if>
            <if test="date != null  and date != ''"> and date = #{date}</if>
        </where>
    </select>
    
    <select id="selectHistoricalInventoryReportBySid" parameterType="Long" resultMap="HistoricalInventoryReportResult">
        <include refid="selectHistoricalInventoryReportVo"/>
        where sid = #{sid}
    </select>
        
    <insert id="insertHistoricalInventoryReport" parameterType="HistoricalInventoryReport">
        insert into HistoricalInventoryReport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="matnr != null">matnr,</if>
            <if test="zzcms != null">zzcms,</if>
            <if test="meins != null">meins,</if>
            <if test="menge1000 != null">menge_1000,</if>
            <if test="menge1020 != null">menge_1020,</if>
            <if test="menge1030 != null">menge_1030,</if>
            <if test="menge1050 != null">menge_1050,</if>
            <if test="menge1060 != null">menge_1060,</if>
            <if test="menge1070 != null">menge_1070,</if>
            <if test="mengeSum != null">menge_sum,</if>
            <if test="date != null">date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="matnr != null">#{matnr},</if>
            <if test="zzcms != null">#{zzcms},</if>
            <if test="meins != null">#{meins},</if>
            <if test="menge1000 != null">#{menge1000},</if>
            <if test="menge1020 != null">#{menge1020},</if>
            <if test="menge1030 != null">#{menge1030},</if>
            <if test="menge1050 != null">#{menge1050},</if>
            <if test="menge1060 != null">#{menge1060},</if>
            <if test="menge1070 != null">#{menge1070},</if>
            <if test="mengeSum != null">#{mengeSum},</if>
            <if test="date != null">#{date},</if>
         </trim>
    </insert>

    <update id="updateHistoricalInventoryReport" parameterType="HistoricalInventoryReport">
        update HistoricalInventoryReport
        <trim prefix="SET" suffixOverrides=",">
            <if test="matnr != null">matnr = #{matnr},</if>
            <if test="zzcms != null">zzcms = #{zzcms},</if>
            <if test="meins != null">meins = #{meins},</if>
            <if test="menge1000 != null">menge_1000 = #{menge1000},</if>
            <if test="menge1020 != null">menge_1020 = #{menge1020},</if>
            <if test="menge1030 != null">menge_1030 = #{menge1030},</if>
            <if test="menge1050 != null">menge_1050 = #{menge1050},</if>
            <if test="menge1060 != null">menge_1060 = #{menge1060},</if>
            <if test="menge1070 != null">menge_1070 = #{menge1070},</if>
            <if test="mengeSum != null">menge_sum = #{mengeSum},</if>
            <if test="date != null">date = #{date},</if>
        </trim>
        where sid = #{sid}
    </update>

    <delete id="deleteHistoricalInventoryReportBySid" parameterType="Long">
        delete from HistoricalInventoryReport where sid = #{sid}
    </delete>

    <delete id="deleteHistoricalInventoryReportBySids" parameterType="String">
        delete from HistoricalInventoryReport where sid in 
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>