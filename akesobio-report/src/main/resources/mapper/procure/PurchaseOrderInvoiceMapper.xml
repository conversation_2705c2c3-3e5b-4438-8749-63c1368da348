<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.procure.mapper.PurchaseOrderInvoiceMapper">
    <!-- 实体类  -->
    <resultMap type="PurchaseOrderInvoice" id="PurchaseOrderInvoiceResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectPurchaseOrderInvoiceList">
        SELECT a.serialno            AS jobNum,
               a.application_date    AS applicationDate,
               b.material_code       AS materialCode,
               b.material_name       AS materialName,
               b.specification_model AS specificationModel,
               a.factory,
               a.pur_order_num       AS purOrderNum,
               b.supplier_name       AS supplierName,
               a.purchase_order_date AS purchaseOrderDate,
               b.quantity,
               (CASE
                    WHEN b.unit IS NULL THEN b.basic_measurement
                    ELSE b.unit
                   END)              AS units,
               b.unit,
               b.basic_measurement   AS basicMeasurement,
               a.pur_app_num         AS purAppNum
        FROM ekp_CG04 a
                 LEFT JOIN ekp_ekp_CG04_add b
                           ON a.fd_id = b.fd_parent_id
    </sql>

    <!-- 查询订单信息 -->
    <select id="queryPurchaseOrderInvoiceList" parameterType="PurchaseOrderInvoice"
            resultMap="PurchaseOrderInvoiceResult">
        <include refid="selectPurchaseOrderInvoiceList"/>
        <where>
            <if test="jobNum != null and jobNum != ''">and a.serialno like concat('%', #{jobNum}, '%')</if>
            <if test="materialCode != null and materialCode != ''">and b.material_code like concat('%', #{materialCode},
                '%')
            </if>
            <if test="materialName != null and materialName != ''">and b.material_name like concat('%', #{materialName},
                '%')
            </if>
            <if test="specificationModel != null and specificationModel != ''">and b.specification_model like
                concat('%', #{specificationModel}, '%')
            </if>
            <if test="factory != null and factory != ''">and a.factory like concat('%', #{factory}, '%')</if>
            <if test="purOrderNum != null and purOrderNum != ''">and a.pur_order_num like concat('%', #{purOrderNum},
                '%')
            </if>
            <if test="supplierName != null and supplierName != ''">and b.supplier_name like concat('%', #{supplierName},
                '%')
            </if>
            <if test="purAppNum != null and purAppNum != ''">and a.pur_app_num like concat('%', #{purAppNum}, '%')</if>
            <if test="units != null and units != ''">and (b.unit like concat('%', #{units}, '%') OR b.basic_measurement
                like concat('%', #{units}, '%'))
            </if>

            <if test="startDate != null and startDate != ''">and a.application_date &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and a.application_date &lt;= #{endDate}</if>
        </where>
        ORDER BY a.application_date DESC
    </select>
</mapper>