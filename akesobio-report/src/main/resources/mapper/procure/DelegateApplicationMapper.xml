<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.procure.mapper.DelegateApplicationMapper">
    <resultMap type="DelegateApplication" id="DelegateApplicationrResultMap">
        <result property="fdId" column="fd_id"/>
        <result property="fdName" column="fd_name"/>
        <result property="specificationModel" column="specification_model"/>
        <result property="serialno" column="serialno"/>
        <result property="department" column="department"/>
        <result property="quantity" column="quantity"/>
        <result property="unit" column="unit"/>
        <result property="applicationDate" column="application_date"/>
        <result property="applicationSubject" column="application_subject"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="basicMeasurement" column="basic_measurement"/>
        <result property="vendorCode" column="vendor_code"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="assetCode" column="asset_code"/>
        <result property="productionBatches" column="fd_3c132763c7073e"/>
        <result property="deliveryDate" column="delivery_date"/>
        <result property="urgent" column="fd_3b6aeac5ec1652"/>
        <result property="remark" column="remark"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentHandler" column="currentHandler"/>
        <result property="currentLink1" column="currentLink1"/>
        <result property="fdStartDate" column="fd_start_date"/>
    </resultMap>

    <sql id="DelegateApplicationrResult">
        SELECT DISTINCT fd_id,
               serialno,
               fd_name,
               department,
               application_date,
               application_subject,
               material_code,
               material_name,
               quantity,
               unit,
               basic_measurement,
               vendor_code,
               supplier_name,
               delivery_date,
               brand,
               item_no,
               specification_model,
               remark,
               documentStatus,

               currentLink,
               fd_start_date
    </sql>


    <select id="GetEkpCGSALl" resultMap="DelegateApplicationrResultMap">
        <include refid="DelegateApplicationrResult"/>
        FROM (
        <if test="param.includeCG04 == true">

            <include refid="DelegateApplicationrResult"/>
            FROM ekp_CG04_delegate_application
            <where>
                <if test="dep != null">
                    and
                    (department like concat('%', #{dep.quality},'%')
                    or
                    department like concat('%',#{dep.synthesis1},'%')
                    or
                    department like concat('%',#{dep.synthesis2},'%')
                    or
                    department like concat('%',#{dep.synthesis3},'%')
                    or
                    department like concat('%',#{dep.synthesis4},'%')
                    or
                    department like concat('%',#{dep.synthesis5},'%')
                    or
                    department like concat('%',#{dep.synthesis6},'%')
                    )
                </if>
                <if test="fdName != null and fdName != '' ">and fd_name like concat('%',#{fdName},'%')</if>
                <if test="params.beginMealDate != null and params.beginMealDate != '' and params.endMealDate != null and params.endMealDate != ''">
                    and application_date between #{params.beginMealDate} and #{params.endMealDate}
                </if>
                <if test="serialno != null and serialno != ''">and serialno like concat('%',#{serialno},'%')</if>
                <if test="factory != null and  factory.size() >0">
                    and factory in
                    <foreach item="num" collection="factory" open="(" separator="," close=")">
                        #{num}
                    </foreach>
                </if>
                <if test="status != null and  status.size() >0">
                    and doc_status in
                    <foreach item="stu" collection="status" open="(" separator="," close=")">
                        #{stu}
                    </foreach>
                </if>
                <if test="materialName != null and materialName != '' ">
                    and material_name like concat('%',#{materialName},'%')
                </if>
                <if test="materialCode != null and materialCode != '' ">
                    and material_code like concat('%',#{materialCode},'%')
                </if>
            </where>

        </if>
        <if test="param.includeCG05 == true">
            <if test="param.includeCG04 == true">
                UNION
            </if>
            <include refid="DelegateApplicationrResult"/>
            FROM
            ekp_CG05_delegate_application
            <where>
                <if test="dep != null">
                    and
                    (department like concat('%', #{dep.quality},'%')
                    or
                    department like concat('%',#{dep.synthesis1},'%')
                    or
                    department like concat('%',#{dep.synthesis2},'%')
                    or
                    department like concat('%',#{dep.synthesis3},'%')
                    or
                    department like concat('%',#{dep.synthesis4},'%')
                    or
                    department like concat('%',#{dep.synthesis5},'%')
                    )
                </if>
                <if test="fdName != null and fdName != '' ">and fd_name like concat('%',#{fdName},'%')</if>
                <if test="params.beginMealDate != null and params.beginMealDate != '' and params.endMealDate != null and params.endMealDate != ''">
                    and
                    application_date between #{params.beginMealDate} and #{
            params.endMealDate}
                </if>
                <if test="serialno != null and serialno != ''">and serialno like concat('%',#{serialno},'%')</if>
                <if test="factory != null and  factory.size() >0">
                    and factory in
                    <foreach item="num" collection="factory" open="(" separator="," close=")">
                        #{num}
                    </foreach>
                </if>
                <if test="status != null and  status.size() >0">
                    and doc_status in
                    <foreach item="stu" collection="status" open="(" separator="," close=")">
                        #{stu}
                    </foreach>
                </if>
                <if test="materialName != null and materialName != '' ">
                    and material_name like concat('%',#{materialName},'%')
                </if>
                <if test="materialCode != null and materialCode != '' ">
                    and material_code like concat('%',#{materialCode},'%')
                </if>
            </where>

        </if>
        <if test="param.includeCG06 == true">
            <if test="param.includeCG05 == true">
                UNION
            </if>
            <include refid="DelegateApplicationrResult"/>
            FROM ekp_CG06_delegate_application
            <where>
                <if test="dep != null">
                    and
                    (department like concat('%', #{dep.quality},'%')
                    or
                    department like concat('%',#{dep.synthesis1},'%')
                    or
                    department like concat('%',#{dep.synthesis2},'%')
                    or
                    department like concat('%',#{dep.synthesis3},'%')
                    or
                    department like concat('%',#{dep.synthesis4},'%')
                    or
                    department like concat('%',#{dep.synthesis5},'%')
                    )
                </if>
                <if test="fdName != null and fdName != '' ">and fd_name like concat('%',#{fdName},'%')</if>
                <if test="params.beginMealDate != null and params.beginMealDate != '' and params.endMealDate != null and params.endMealDate != ''">
                    and application_date between #{params.beginMealDate} and #{params.endMealDate}
                </if>
                <if test="serialno != null and serialno != ''">and serialno like concat('%',#{serialno},'%')</if>
                <if test="factory != null and  factory.size() >0">
                    and factory in
                    <foreach item="num" collection="factory" open="(" separator="," close=")">
                        #{num}
                    </foreach>
                </if>
                <if test="status != null and  status.size() >0">
                    and doc_status in
                    <foreach item="stu" collection="status" open="(" separator="," close=")">
                        #{stu}
                    </foreach>
                </if>
                <if test="materialName != null and materialName != '' ">
                    and material_name like concat('%',#{materialName},'%')
                </if>
                <if test="materialCode != null and materialCode != '' ">
                    and material_code like concat('%',#{materialCode},'%')
                </if>
            </where>

        </if>
        )AS combined
        ORDER BY application_date DESC
    </select>

    <select id="GetEkpCGALl" resultMap="DelegateApplicationrResultMap">
        <include refid="DelegateApplicationrResult"/>
        FROM (
        <include refid="DelegateApplicationrResult"/>
        FROM ekp_CG04_delegate_application
        <where>
            <if test="dep != null">
                and
                (department like concat('%', #{dep.quality},'%')
                or
                department like concat('%',#{dep.synthesis1},'%')
                or
                department like concat('%',#{dep.synthesis2},'%')
                or
                department like concat('%',#{dep.synthesis3},'%')
                or
                department like concat('%',#{dep.synthesis4},'%')
                or
                department like concat('%',#{dep.synthesis5},'%')
                or
                department like concat('%',#{dep.synthesis6},'%')
                )
            </if>
            <if test="fdName != null and fdName != '' ">and fd_name like concat('%',#{fdName},'%')</if>
            <if test="params.beginMealDate != null and params.beginMealDate != '' and params.endMealDate != null and params.endMealDate != ''">
                and application_date between #{params.beginMealDate} and #{params.endMealDate}
            </if>
            <if test="serialno != null and serialno != ''">and serialno like concat('%',#{serialno},'%')</if>
            <if test="factory != null and  factory.size() >0">
                and factory in
                <foreach item="num" collection="factory" open="(" separator="," close=")">
                    #{num}
                </foreach>
            </if>
            <if test="status != null and  status.size() >0">
                and doc_status in
                <foreach item="stu" collection="status" open="(" separator="," close=")">
                    #{stu}
                </foreach>
            </if>
            <if test="materialName != null and materialName != '' ">
                and material_name like concat('%',#{materialName},'%')
            </if>
            <if test="materialCode != null and materialCode != '' ">
                and material_code like concat('%',#{materialCode},'%')
            </if>
        </where>
        UNION
        <include refid="DelegateApplicationrResult"/>
        FROM
        ekp_CG05_delegate_application
        <where>
            <if test="dep != null">
                and
                (department like concat('%', #{dep.quality},'%')
                or
                department like concat('%',#{dep.synthesis1},'%')
                or
                department like concat('%',#{dep.synthesis2},'%')
                or
                department like concat('%',#{dep.synthesis3},'%')
                or
                department like concat('%',#{dep.synthesis4},'%')
                or
                department like concat('%',#{dep.synthesis5},'%')
                )
            </if>
            <if test="fdName != null and fdName != '' ">and fd_name like concat('%',#{fdName},'%')</if>
            <if test="params.beginMealDate != null and params.beginMealDate != '' and params.endMealDate != null and params.endMealDate != ''">
                and
                application_date between #{params.beginMealDate} and #{
            params.endMealDate}
            </if>
            <if test="serialno != null and serialno != ''">and serialno like concat('%',#{serialno},'%')</if>
            <if test="factory != null and  factory.size() >0">
                and factory in
                <foreach item="num" collection="factory" open="(" separator="," close=")">
                    #{num}
                </foreach>
            </if>
            <if test="status != null and  status.size() >0">
                and doc_status in
                <foreach item="stu" collection="status" open="(" separator="," close=")">
                    #{stu}
                </foreach>
            </if>
            <if test="materialName != null and materialName != '' ">
                and material_name like concat('%',#{materialName},'%')
            </if>
            <if test="materialCode != null and materialCode != '' ">
                and material_code like concat('%',#{materialCode},'%')
            </if>
        </where>
        UNION
        <include refid="DelegateApplicationrResult"/>
        FROM ekp_CG06_delegate_application
        <where>
            <if test="dep != null">
                and
                (department like concat('%', #{dep.quality},'%')
                or
                department like concat('%',#{dep.synthesis1},'%')
                or
                department like concat('%',#{dep.synthesis2},'%')
                or
                department like concat('%',#{dep.synthesis3},'%')
                or
                department like concat('%',#{dep.synthesis4},'%')
                or
                department like concat('%',#{dep.synthesis5},'%')
                )
            </if>
            <if test="fdName != null and fdName != '' ">and fd_name like concat('%',#{fdName},'%')</if>
            <if test="params.beginMealDate != null and params.beginMealDate != '' and params.endMealDate != null and params.endMealDate != ''">
                and application_date between #{params.beginMealDate} and #{params.endMealDate}
            </if>
            <if test="serialno != null and serialno != ''">and serialno like concat('%',#{serialno},'%')</if>
            <if test="factory != null and  factory.size() >0">
                and factory in
                <foreach item="num" collection="factory" open="(" separator="," close=")">
                    #{num}
                </foreach>
            </if>
            <if test="status != null and  status.size() >0">
                and doc_status in
                <foreach item="stu" collection="status" open="(" separator="," close=")">
                    #{stu}
                </foreach>
            </if>
            <if test="materialName != null and materialName != '' ">
                and material_name like concat('%',#{materialName},'%')
            </if>
            <if test="materialCode != null and materialCode != '' ">
                and material_code like concat('%',#{materialCode},'%')
            </if>
        </where>
        )AS combined
        ORDER BY application_date DESC
    </select>


    <select id="GetEkpCGALlTow" resultMap="DelegateApplicationrResultMap">

        select * from ekp_CG09_delegate_application
        <where>
            <if test="supplierName != null and supplierName != '' ">and supplier_name like
                concat('%',#{supplierName},'%')
            </if>
            <if test="materialName != null and materialName != ''">and material_name like
                concat('%',#{materialName},'%')
            </if>
            <if test="department != null and department != '' ">and department like concat('%',#{department},'%')</if>
            <if test="fdName != null and fdName != '' ">and fd_name like concat('%',#{fdName},'%')</if>
            <if test="serialno != null and serialno != ''">and serialno =#{serialno}</if>
            <if test="params.beginMealDate != null and params.beginMealDate != '' and params.endMealDate != null and params.endMealDate != ''">
                and application_date between #{params.beginMealDate} and #{params.endMealDate}
            </if>
            <if test="dep != null">
                and
                (department like concat('%', #{dep.quality},'%')
                or
                department like concat('%',#{dep.synthesis},'%'))
            </if>
            <if test="factory != null and  factory.size() >0">
                and factory in
                <foreach item="num" collection="factory" open="(" separator="," close=")">
                    #{num}
                </foreach>
            </if>

            <if test="status != null and  status.size() >0">
                and doc_status in
                <foreach item="stu" collection="status" open="(" separator="," close=")">
                    #{stu}
                </foreach>
            </if>
            <if test="applicationSubject != null and applicationSubject != ''">
                 and application_subject like concat('%',#{applicationSubject},'%')
            </if>
        </where>
        order by application_date desc
    </select>

    <select id="getFdName" resultType="java.lang.String">
        select top 1 soe.fd_name
        from lbpm_process lp
                 LEFT JOIN lbpm_node ln ON lp.fd_id = ln.fd_process_id
                 LEFT JOIN lbpm_workitem lw ON ln.fd_id = lw.fd_node_id
                 LEFT JOIN sys_org_element soe ON lw.fd_expected_id = soe.fd_id
        WHERE lp.fd_id = #{fdId}
    </select>

</mapper>