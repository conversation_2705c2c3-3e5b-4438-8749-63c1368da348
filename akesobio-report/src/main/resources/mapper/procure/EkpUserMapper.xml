<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.procure.mapper.EkpUserMapper">

    <resultMap id="EkpDeptUsersDeptResult" type="com.akesobio.report.procure.domain.EkpUserDept">
        <result property="fdLoginName" column="fd_login_name"/>
        <result property="fdName" column="fd_name"/>
        <result property="fdDeptAll" column="fd_dept_all"/>
        <result property="fdOrgParentOrg" column="fd_org_parent_org"/>
    </resultMap>
    <select id="selectUsersDeptByUserName" resultMap="EkpDeptUsersDeptResult">

        SELECT fd_login_name, fd_name, fd_dept_all, fd_org_parent_org
        FROM hr_staff_person_info_v  WHERE  fd_name = #{userName}

    </select>
</mapper>