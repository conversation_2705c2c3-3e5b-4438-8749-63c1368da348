<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.procure.mapper.InvoiceProcurementMapper">
    
    <resultMap type="InvoiceProcurement" id="InvoiceProcurementResult">
        <result property="invoiceTypeDes"    column="invoiceTypeDes"    />
        <result property="invoiceCode"    column="invoiceCode"    />
        <result property="invoiceNo"    column="invoiceNo"    />
        <result property="invoiceDate"    column="invoiceDate"    />
        <result property="totalAmount"    column="totalAmount"    />
        <result property="totalTax"    column="totalTax"    />
        <result property="amountTax"    column="amountTax"    />
        <result property="saleName"    column="saleName"    />
        <result property="purchaserName"    column="purchaserName"    />
        <result property="invoiceContent"    column="invoiceContent"    />
        <result property="quantity"    column="quantity"    />
        <result property="amount"    column="amount"    />
        <result property="taxRate"    column="taxRate"    />
        <result property="tax"    column="tax"    />
        <result property="remark"    column="remark"    />
        <result property="specificationModel"    column="specificationModel"    />
        <result property="unit"    column="unit"    />
        <result property="unitPrice"    column="unitPrice"    />
    </resultMap>

    <sql id="selectInvoiceProcurementVo">
        select invoiceTypeDes, invoiceCode, invoiceNo, invoiceDate, totalAmount, totalTax, amountTax, saleName, purchaserName, invoiceContent, quantity, amount, taxRate, tax, remark, specificationModel, unit, unitPrice from invoiceProcurement
    </sql>

    <select id="selectInvoiceProcurementList" parameterType="InvoiceProcurement" resultMap="InvoiceProcurementResult">
        <include refid="selectInvoiceProcurementVo"/>
        <where>  
            <if test="invoiceTypeDes != null  and invoiceTypeDes != ''"> and invoiceTypeDes like concat('%', #{invoiceTypeDes}, '%')</if>
            <if test="invoiceCode != null  and invoiceCode != ''"> and invoiceCode like concat('%', #{invoiceCode}, '%')</if>
            <if test="invoiceNo != null  and invoiceNo != ''"> and invoiceNo like concat('%', #{invoiceNo}, '%')</if>
            <if test="params.beginInvoiceDate != null and params.beginInvoiceDate != '' and params.endInvoiceDate != null and params.endInvoiceDate != ''"> and invoiceDate between #{params.beginInvoiceDate} and #{params.endInvoiceDate}</if>
            <if test="totalAmount != null  and totalAmount != ''"> and totalAmount = #{totalAmount}</if>
            <if test="totalTax != null  and totalTax != ''"> and totalTax = #{totalTax}</if>
            <if test="amountTax != null  and amountTax != ''"> and amountTax = #{amountTax}</if>
            <if test="saleName != null  and saleName != ''"> and saleName like concat('%', #{saleName}, '%')</if>
            <if test="purchaserName != null  and purchaserName != ''"> and purchaserName like concat('%', #{purchaserName}, '%')</if>
            <if test="invoiceContent != null  and invoiceContent != ''"> and invoiceContent like concat('%', #{invoiceContent}, '%')</if>
            <if test="quantity != null  and quantity != ''"> and quantity = #{quantity}</if>
            <if test="amount != null  and amount != ''"> and amount = #{amount}</if>
            <if test="taxRate != null  and taxRate != ''"> and taxRate = #{taxRate}</if>
            <if test="tax != null  and tax != ''"> and tax = #{tax}</if>
            <if test="specificationModel != null  and specificationModel != ''"> and specificationModel like concat('%', #{specificationModel}, '%')</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="unitPrice != null  and unitPrice != ''"> and unitPrice = #{unitPrice}</if>
        </where>
        order by invoiceDate desc
    </select>
</mapper>