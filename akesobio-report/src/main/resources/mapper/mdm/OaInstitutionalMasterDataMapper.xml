<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.mdm.mapper.OaInstitutionalMasterDataMapper">

    <resultMap type="OaInstitutionalMasterData" id="OaInstitutionalMasterDataResult">
        <result property="id" column="id"/>
        <result property="oaTrackingNumber" column="oaTrackingNumber"/>
        <result property="state" column="state"/>
        <result property="institutionCode" column="institutionCode"/>
        <result property="institutionName" column="institutionName"/>
        <result property="institutionAlias" column="institutionAlias"/>
        <result property="address" column="address"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="businessStatus" column="businessStatus"/>
        <result property="creditCode" column="creditCode"/>
        <result property="hospitalLevel" column="hospitalLevel"/>
        <result property="grade" column="grade"/>
        <result property="medicalInstitutionRelations" column="medicalInstitutionRelations"/>
        <result property="primaryAttribute" column="primaryAttribute"/>
        <result property="secondaryAttribute" column="secondaryAttribute"/>
        <result property="thirdLevelAttribute" column="thirdLevelAttribute"/>
        <result property="creationTime" column="creationTime"/>
    </resultMap>

    <sql id="selectOaInstitutionalMasterDataVo">
        select id,
               oaTrackingNumber,
               state,
               institutionCode,
               institutionName,
               institutionAlias,
               address,
               province,
               city,
               district,
               businessStatus,
               creditCode,
               hospitalLevel,
               grade,
               medicalInstitutionRelations,
               primaryAttribute,
               secondaryAttribute,
               thirdLevelAttribute,
               creationTime
        from oa_institutional_master_data
    </sql>

    <select id="selectOaInstitutionalMasterDataList" parameterType="OaInstitutionalMasterData"
            resultMap="OaInstitutionalMasterDataResult">
        <include refid="selectOaInstitutionalMasterDataVo"/>
        <where>
            <if test="oaTrackingNumber != null  and oaTrackingNumber != ''">and oaTrackingNumber like concat('%',
                #{oaTrackingNumber}, '%')
            </if>
            <if test="state != null  and state != ''">and state like concat('%', #{state}, '%')</if>
            <if test="institutionCode != null  and institutionCode != ''">and institutionCode like concat('%',
                #{institutionCode}, '%')
            </if>
            <if test="institutionName != null  and institutionName != ''">and institutionName like concat('%',
                #{institutionName}, '%')
            </if>
            <if test="institutionAlias != null  and institutionAlias != ''">and institutionAlias like concat('%',
                #{institutionAlias}, '%')
            </if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>
            <if test="province != null  and province != ''">and province like concat('%', #{province}, '%')</if>
            <if test="city != null  and city != ''">and city like concat('%', #{city}, '%')</if>
            <if test="district != null  and district != ''">and district like concat('%', #{district}, '%')</if>
            <if test="businessStatus != null  and businessStatus != ''">and businessStatus = #{businessStatus}</if>
            <if test="creditCode != null  and creditCode != ''">and creditCode = #{creditCode}</if>
            <if test="hospitalLevel != null  and hospitalLevel != ''">and hospitalLevel = #{hospitalLevel}</if>
            <if test="grade != null  and grade != ''">and grade = #{grade}</if>
            <if test="medicalInstitutionRelations != null  and medicalInstitutionRelations != ''">and
                medicalInstitutionRelations = #{medicalInstitutionRelations}
            </if>
            <if test="primaryAttribute != null  and primaryAttribute != ''">and primaryAttribute = #{primaryAttribute}
            </if>
            <if test="secondaryAttribute != null  and secondaryAttribute != ''">and secondaryAttribute =
                #{secondaryAttribute}
            </if>
            <if test="thirdLevelAttribute != null  and thirdLevelAttribute != ''">and thirdLevelAttribute =
                #{thirdLevelAttribute}
            </if>
            <if test="params.beginCreationTime != null and params.beginCreationTime != '' and params.endCreationTime != null and params.endCreationTime != ''">
                and creationTime between #{params.beginCreationTime} and #{params.endCreationTime}
            </if>
        </where>
    </select>

    <select id="selectOaInstitutionalMasterDataById" parameterType="Integer"
            resultMap="OaInstitutionalMasterDataResult">
        <include refid="selectOaInstitutionalMasterDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertOaInstitutionalMasterData" parameterType="OaInstitutionalMasterData">
        insert into oa_institutional_master_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="oaTrackingNumber != null">oaTrackingNumber,</if>
            <if test="state != null">state,</if>
            <if test="institutionCode != null">institutionCode,</if>
            <if test="institutionName != null">institutionName,</if>
            <if test="institutionAlias != null">institutionAlias,</if>
            <if test="address != null">address,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="businessStatus != null">businessStatus,</if>
            <if test="creditCode != null">creditCode,</if>
            <if test="hospitalLevel != null">hospitalLevel,</if>
            <if test="grade != null">grade,</if>
            <if test="medicalInstitutionRelations != null">medicalInstitutionRelations,</if>
            <if test="primaryAttribute != null">primaryAttribute,</if>
            <if test="secondaryAttribute != null">secondaryAttribute,</if>
            <if test="thirdLevelAttribute != null">thirdLevelAttribute,</if>
            <if test="creationTime != null">creationTime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="oaTrackingNumber != null">#{oaTrackingNumber},</if>
            <if test="state != null">#{state},</if>
            <if test="institutionCode != null">#{institutionCode},</if>
            <if test="institutionName != null">#{institutionName},</if>
            <if test="institutionAlias != null">#{institutionAlias},</if>
            <if test="address != null">#{address},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="businessStatus != null">#{businessStatus},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="hospitalLevel != null">#{hospitalLevel},</if>
            <if test="grade != null">#{grade},</if>
            <if test="medicalInstitutionRelations != null">#{medicalInstitutionRelations},</if>
            <if test="primaryAttribute != null">#{primaryAttribute},</if>
            <if test="secondaryAttribute != null">#{secondaryAttribute},</if>
            <if test="thirdLevelAttribute != null">#{thirdLevelAttribute},</if>
            <if test="creationTime != null">#{creationTime},</if>
        </trim>
    </insert>

    <update id="updateOaInstitutionalMasterData" parameterType="OaInstitutionalMasterData">
        update oa_institutional_master_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="oaTrackingNumber != null">oaTrackingNumber = #{oaTrackingNumber},</if>
            <if test="state != null">state = #{state},</if>
            <if test="institutionCode != null">institutionCode = #{institutionCode},</if>
            <if test="institutionName != null">institutionName = #{institutionName},</if>
            <if test="institutionAlias != null">institutionAlias = #{institutionAlias},</if>
            <if test="address != null">address = #{address},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="businessStatus != null">businessStatus = #{businessStatus},</if>
            <if test="creditCode != null">creditCode = #{creditCode},</if>
            <if test="hospitalLevel != null">hospitalLevel = #{hospitalLevel},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="medicalInstitutionRelations != null">medicalInstitutionRelations =
                #{medicalInstitutionRelations},
            </if>
            <if test="primaryAttribute != null">primaryAttribute = #{primaryAttribute},</if>
            <if test="secondaryAttribute != null">secondaryAttribute = #{secondaryAttribute},</if>
            <if test="thirdLevelAttribute != null">thirdLevelAttribute = #{thirdLevelAttribute},</if>
            <if test="creationTime != null">creationTime = #{creationTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOaInstitutionalMasterDataById" parameterType="Integer">
        delete
        from oa_institutional_master_data
        where id = #{id}
    </delete>

    <delete id="deleteOaInstitutionalMasterDataByIds" parameterType="String">
        delete from oa_institutional_master_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertOaInstitutionalMasterDataBatch" parameterType="java.util.List">
        insert into oa_institutional_master_data
        (
        <trim suffixOverrides=",">
            <if test="list[0].id != null">id,</if>
            <if test="list[0].oaTrackingNumber != null">oaTrackingNumber,</if>
            <if test="list[0].state != null">state,</if>
            <if test="list[0].institutionCode != null">institutionCode,</if>
            <if test="list[0].institutionName != null">institutionName,</if>
            <if test="list[0].institutionAlias != null">institutionAlias,</if>
            <if test="list[0].address != null">address,</if>
            <if test="list[0].province != null">province,</if>
            <if test="list[0].city != null">city,</if>
            <if test="list[0].district != null">district,</if>
            <if test="list[0].businessStatus != null">businessStatus,</if>
            <if test="list[0].creditCode != null">creditCode,</if>
            <if test="list[0].hospitalLevel != null">hospitalLevel,</if>
            <if test="list[0].grade != null">grade,</if>
            <if test="list[0].medicalInstitutionRelations != null">medicalInstitutionRelations,</if>
            <if test="list[0].primaryAttribute != null">primaryAttribute,</if>
            <if test="list[0].secondaryAttribute != null">secondaryAttribute,</if>
            <if test="list[0].thirdLevelAttribute != null">thirdLevelAttribute,</if>
            <if test="list[0].creationTime != null">creationTime,</if>
        </trim>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            <trim suffixOverrides=",">
                <if test="item.id != null">#{item.id},</if>
                <if test="item.oaTrackingNumber != null">#{item.oaTrackingNumber},</if>
                <if test="item.state != null">#{item.state},</if>
                <if test="item.institutionCode != null">#{item.institutionCode},</if>
                <if test="item.institutionName != null">#{item.institutionName},</if>
                <if test="item.institutionAlias != null">#{item.institutionAlias},</if>
                <if test="item.address != null">#{item.address},</if>
                <if test="item.province != null">#{item.province},</if>
                <if test="item.city != null">#{item.city},</if>
                <if test="item.district != null">#{item.district},</if>
                <if test="item.businessStatus != null">#{item.businessStatus},</if>
                <if test="item.creditCode != null">#{item.creditCode},</if>
                <if test="item.hospitalLevel != null">#{item.hospitalLevel},</if>
                <if test="item.grade != null">#{item.grade},</if>
                <if test="item.medicalInstitutionRelations != null">#{item.medicalInstitutionRelations},</if>
                <if test="item.primaryAttribute != null">#{item.primaryAttribute},</if>
                <if test="item.secondaryAttribute != null">#{item.secondaryAttribute},</if>
                <if test="item.thirdLevelAttribute != null">#{item.thirdLevelAttribute},</if>
                <if test="item.creationTime != null">#{item.creationTime},</if>
            </trim>
            )
        </foreach>
    </insert>
</mapper>