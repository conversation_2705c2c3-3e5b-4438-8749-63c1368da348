<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.mdm.mapper.InstitutionalMasterDataMapper">

    <resultMap type="InstitutionalMasterData" id="InstitutionalMasterDataResult">
        <result property="id" column="id"/>
        <result property="institutionName" column="institutionName"/>
        <result property="institutionCode" column="institutionCode"/>
        <result property="customerType" column="customerType"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="address" column="address"/>
        <result property="iongitude" column="iongitude"/>
        <result property="state" column="state"/>
        <result property="institutionAlias" column="institutionAlias"/>
        <result property="primaryAttribute" column="primaryAttribute"/>
        <result property="secondaryAttribute" column="secondaryAttribute"/>
        <result property="thirdLevelAttribute" column="thirdLevelAttribute"/>
        <result property="creditCode" column="creditCode"/>
        <result property="hospitalLevel" column="hospitalLevel"/>
        <result property="grade" column="grade"/>
        <result property="medicalInstitutionRelations" column="medicalInstitutionRelations"/>
    </resultMap>

    <sql id="selectInstitutionalMasterDataVo">
        select id,
               institutionName,
               institutionCode,
               customerType,
               province,
               city,
               district,
               address,
               iongitude,
               state,
               institutionAlias,
               primaryAttribute,
               secondaryAttribute,
               thirdLevelAttribute,
               creditCode,
               hospitalLevel,
               grade,
               medicalInstitutionRelations
        from institutional_master_data
    </sql>


    <!-- 根据机构编码查询机构主数据 -->
    <select id="selectInstitutionalMasterDataByCode" parameterType="String" resultMap="InstitutionalMasterDataResult">
        <include refid="selectInstitutionalMasterDataVo"/>
        where institutionCode = #{institutionCode}
    </select>

    <select id="selectInstitutionalMasterDataList" parameterType="InstitutionalMasterData"
            resultMap="InstitutionalMasterDataResult">
        <include refid="selectInstitutionalMasterDataVo"/>
        <where>
            <if test="institutionName != null  and institutionName != ''">and institutionName like concat('%',
                #{institutionName}, '%')
            </if>
            <if test="institutionCode != null  and institutionCode != ''">and institutionCode like concat('%',
                #{institutionCode}, '%')
            </if>
            <if test="customerType != null  and customerType != ''">and customerType like concat('%', #{customerType},
                '%')
            </if>
            <if test="province != null  and province != ''">and province like concat('%', #{province}, '%')</if>
            <if test="city != null  and city != ''">and city like concat('%', #{city}, '%')</if>
            <if test="district != null  and district != ''">and district like concat('%', #{district}, '%')</if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>
            <if test="iongitude != null  and iongitude != ''">and iongitude = #{iongitude}</if>
            <if test="state != null  and state != ''">and state like concat('%', #{state}, '%')</if>
            <if test="institutionAlias != null  and institutionAlias != ''">and institutionAlias like concat('%',
                #{institutionAlias}, '%')
            </if>
            <if test="primaryAttribute != null  and primaryAttribute != ''">and primaryAttribute like concat('%',
                #{primaryAttribute}, '%')
            </if>
            <if test="secondaryAttribute != null  and secondaryAttribute != ''">and secondaryAttribute like concat('%',
                #{secondaryAttribute}, '%')
            </if>
            <if test="thirdLevelAttribute != null  and thirdLevelAttribute != ''">and thirdLevelAttribute like
                concat('%', #{thirdLevelAttribute}, '%')
            </if>
            <if test="creditCode != null  and creditCode != ''">and creditCode like concat('%', #{creditCode}, '%')</if>
            <if test="hospitalLevel != null  and hospitalLevel != ''">and hospitalLevel like concat('%',
                #{hospitalLevel}, '%')
            </if>
            <if test="grade != null  and grade != ''">and grade like concat('%', #{grade}, '%')</if>
            <if test="medicalInstitutionRelations != null  and medicalInstitutionRelations != ''">and
                medicalInstitutionRelations like concat('%', #{medicalInstitutionRelations}, '%')
            </if>
        </where>
    </select>

    <select id="selectInstitutionalMasterDataById" parameterType="Integer" resultMap="InstitutionalMasterDataResult">
        <include refid="selectInstitutionalMasterDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertInstitutionalMasterData" parameterType="InstitutionalMasterData">
        insert into institutional_master_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="institutionName != null">institutionName,</if>
            <if test="institutionCode != null">institutionCode,</if>
            <if test="customerType != null">customerType,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="address != null">address,</if>
            <if test="iongitude != null">iongitude,</if>
            <if test="state != null">state,</if>
            <if test="institutionAlias != null">institutionAlias,</if>
            <if test="primaryAttribute != null">primaryAttribute,</if>
            <if test="secondaryAttribute != null">secondaryAttribute,</if>
            <if test="thirdLevelAttribute != null">thirdLevelAttribute,</if>
            <if test="creditCode != null">creditCode,</if>
            <if test="hospitalLevel != null">hospitalLevel,</if>
            <if test="grade != null">grade,</if>
            <if test="medicalInstitutionRelations != null">medicalInstitutionRelations,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="institutionName != null">#{institutionName},</if>
            <if test="institutionCode != null">#{institutionCode},</if>
            <if test="customerType != null">#{customerType},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="address != null">#{address},</if>
            <if test="iongitude != null">#{iongitude},</if>
            <if test="state != null">#{state},</if>
            <if test="institutionAlias != null">#{institutionAlias},</if>
            <if test="primaryAttribute != null">#{primaryAttribute},</if>
            <if test="secondaryAttribute != null">#{secondaryAttribute},</if>
            <if test="thirdLevelAttribute != null">#{thirdLevelAttribute},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="hospitalLevel != null">#{hospitalLevel},</if>
            <if test="grade != null">#{grade},</if>
            <if test="medicalInstitutionRelations != null">#{medicalInstitutionRelations},</if>
        </trim>
    </insert>

    <update id="updateInstitutionalMasterData" parameterType="InstitutionalMasterData">
        update institutional_master_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="institutionName != null">institutionName = #{institutionName},</if>
            <if test="institutionCode != null">institutionCode = #{institutionCode},</if>
            <if test="customerType != null">customerType = #{customerType},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="address != null">address = #{address},</if>
            <if test="iongitude != null">iongitude = #{iongitude},</if>
            <if test="state != null">state = #{state},</if>
            <if test="institutionAlias != null">institutionAlias = #{institutionAlias},</if>
            <if test="primaryAttribute != null">primaryAttribute = #{primaryAttribute},</if>
            <if test="secondaryAttribute != null">secondaryAttribute = #{secondaryAttribute},</if>
            <if test="thirdLevelAttribute != null">thirdLevelAttribute = #{thirdLevelAttribute},</if>
            <if test="creditCode != null">creditCode = #{creditCode},</if>
            <if test="hospitalLevel != null">hospitalLevel = #{hospitalLevel},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="medicalInstitutionRelations != null">medicalInstitutionRelations =
                #{medicalInstitutionRelations},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInstitutionalMasterDataById" parameterType="Integer">
        delete
        from institutional_master_data
        where id = #{id}
    </delete>

    <delete id="deleteInstitutionalMasterDataByIds" parameterType="String">
        delete from institutional_master_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>