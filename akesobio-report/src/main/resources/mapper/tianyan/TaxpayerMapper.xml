<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.tianyan.mapper.TaxpayerMapper">
    
    <resultMap type="com.akesobio.report.tianyan.domain.Taxpayer" id="TaxpayerResult">
        <result property="id"    column="id"    />
        <result property="gid"    column="gid"    />
        <result property="taxpayerQualificationType"    column="taxpayer_qualification_type"    />
        <result property="endDate"    column="end_date"    />
        <result property="name"    column="name"    />
        <result property="logo"    column="logo"    />
        <result property="alias"    column="alias"    />
        <result property="taxpayerIdentificationNumber"    column="taxpayer_Identification_number"    />
        <result property="startDate"    column="start_date"    />
    </resultMap>

    <sql id="selectTaxpayerVo">
        select id, gid, taxpayer_qualification_type, end_date, name, logo, alias, taxpayer_Identification_number, start_date from taxpayer
    </sql>

    <select id="selectTaxpayerList" parameterType="com.akesobio.report.tianyan.domain.Taxpayer" resultMap="TaxpayerResult">
        <include refid="selectTaxpayerVo"/>
        <where>  
            <if test="taxpayerQualificationType != null  and taxpayerQualificationType != ''"> and taxpayer_qualification_type = #{taxpayerQualificationType}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="alias != null  and alias != ''"> and alias like concat('%', #{alias}, '%')</if>
            <if test="taxpayerIdentificationNumber != null  and taxpayerIdentificationNumber != ''"> and taxpayer_Identification_number = #{taxpayerIdentificationNumber}</if>
            <if test="startDate != null  and startDate != ''"> and start_date = #{startDate}</if>
        </where>
    </select>
    
    <select id="selectTaxpayerById" parameterType="String" resultMap="TaxpayerResult">
        <include refid="selectTaxpayerVo"/>
        where id = #{id}
    </select>
    <select id="selTaxpayer" resultMap="TaxpayerResult">
        select * from taxpayer where name  = #{name}
    </select>

    <insert id="insertTaxpayer" parameterType="com.akesobio.report.tianyan.domain.Taxpayer">
        insert into taxpayer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="gid != null">gid,</if>
            <if test="taxpayerQualificationType != null">taxpayer_qualification_type,</if>
            <if test="endDate != null">end_date,</if>
            <if test="name != null">name,</if>
            <if test="logo != null">logo,</if>
            <if test="alias != null">alias,</if>
            <if test="taxpayerIdentificationNumber != null">taxpayer_Identification_number,</if>
            <if test="startDate != null">start_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="gid != null">#{gid},</if>
            <if test="taxpayerQualificationType != null">#{taxpayerQualificationType},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="name != null">#{name},</if>
            <if test="logo != null">#{logo},</if>
            <if test="alias != null">#{alias},</if>
            <if test="taxpayerIdentificationNumber != null">#{taxpayerIdentificationNumber},</if>
            <if test="startDate != null">#{startDate},</if>
         </trim>
    </insert>

    <update id="updateTaxpayer" parameterType="com.akesobio.report.tianyan.domain.Taxpayer">
        update taxpayer
        <trim prefix="SET" suffixOverrides=",">
            <if test="gid != null">gid = #{gid},</if>
            <if test="taxpayerQualificationType != null">taxpayer_qualification_type = #{taxpayerQualificationType},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="name != null">name = #{name},</if>
            <if test="logo != null">logo = #{logo},</if>
            <if test="alias != null">alias = #{alias},</if>
            <if test="taxpayerIdentificationNumber != null">taxpayer_Identification_number = #{taxpayerIdentificationNumber},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTaxpayerById" parameterType="String">
        delete from taxpayer where id = #{id}
    </delete>

    <delete id="deleteTaxpayerByIds" parameterType="String">
        delete from taxpayer where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>