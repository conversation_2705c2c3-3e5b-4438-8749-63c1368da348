<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.tianyan.mapper.CompanyInformationMapper">
    
    <resultMap type="com.akesobio.report.tianyan.domain.CompanyInformation" id="CompanyInformationResult">
        <result property="id"    column="id"    />
        <result property="staffNumRange"    column="staff_num_range"    />
        <result property="fromTime"    column="from_time"    />
        <result property="type"    column="type"    />
        <result property="bondName"    column="bond_name"    />
        <result property="isMicroEnt"    column="is_micro_ent"    />
        <result property="usedBondName"    column="used_bond_name"    />
        <result property="regNumber"    column="reg_number"    />
        <result property="percentileScore"    column="percentile_score"    />
        <result property="regCapital"    column="reg_capital"    />
        <result property="name"    column="name"    />
        <result property="regInstitute"    column="regInstitute"    />
        <result property="regLocation"    column="reg_location"    />
        <result property="industry"    column="industry"    />
        <result property="approvedTime"    column="approved_time"    />
        <result property="socialStaffNum"    column="social_staff_num"    />
        <result property="tags"    column="tags"    />
        <result property="taxNumber"    column="tax_number"    />
        <result property="businessScope"    column="business_scope"    />
        <result property="property3"    column="property3"    />
        <result property="alias"    column="alias"    />
        <result property="orgNumber"    column="org_number"    />
        <result property="regStatus"    column="reg_status"    />
        <result property="estiblishTime"    column="estiblish_time"    />
        <result property="updateTimes"    column="update_times"    />
        <result property="bondType"    column="bond_type"    />
        <result property="legalPersonName"    column="legal_person_name"    />
        <result property="toTime"    column="to_time"    />
        <result property="actualCapital"    column="actual_capital"    />
        <result property="companyOrgType"    column="company_org_type"    />
        <result property="compForm"    column="comp_form"    />
        <result property="base"    column="base"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="historyNames"    column="history_names"    />
<!--        <result property="historyNameList"    column="history_name_list"    />-->
        <result property="bondNum"    column="bond_num"    />
        <result property="regCapitalCurrency"    column="reg_capital_currency"    />
        <result property="actualCapitalCurrency"    column="actual_capital_currency"    />
        <result property="revokeDate"    column="revoke_date"    />
        <result property="revokeReason"    column="revoke_reason"    />
        <result property="cancelDate"    column="cancelDate"    />
        <result property="cancelReason"    column="cancelReason"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
<!--        <result property="industryAll"    column="industryAll"    />-->
    </resultMap>

    <sql id="selectCompanyInformationVo">
        select id, staff_num_range, from_time, type, bond_name, is_micro_ent, used_bond_name, reg_number, percentile_score, reg_capital, name, regInstitute, reg_location, industry, approved_time, social_staff_num, tags, tax_number, business_scope, property3, alias, org_number, reg_status, estiblish_time, update_times, bond_type, legal_person_name, to_time, actual_capital, company_org_type, comp_form, base, credit_code, history_names, history_name_list, bond_num, reg_capital_currency, actual_capital_currency, revoke_date, revoke_reason, cancelDate, cancelReason, city, district, industryAll from company_information
    </sql>

    <select id="selectCompanyInformationList" parameterType="com.akesobio.report.tianyan.domain.CompanyInformation" resultMap="CompanyInformationResult">
        <include refid="selectCompanyInformationVo"/>
        <where>  
            <if test="bondName != null  and bondName != ''"> and bond_name like concat('%', #{bondName}, '%')</if>
            <if test="regNumber != null "> and reg_number = #{regNumber}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="industry != null  and industry != ''"> and industry like concat('%', #{industry}, '%')</if>
            <if test="alias != null  and alias != ''"> and alias = #{alias}</if>
            <if test="orgNumber != null  and orgNumber != ''"> and org_number = #{orgNumber}</if>
            <if test="legalPersonName != null  and legalPersonName != ''"> and legal_person_name like concat('%', #{legalPersonName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
        </where>
    </select>
    
    <select id="selectCompanyInformationById" parameterType="Long" resultMap="CompanyInformationResult">
        <include refid="selectCompanyInformationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCompanyInformation" parameterType="com.akesobio.report.tianyan.domain.CompanyInformation">
        insert into company_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="staffNumRange != null">staff_num_range,</if>
            <if test="fromTime != null">from_time,</if>
            <if test="type != null">type,</if>
            <if test="bondName != null">bond_name,</if>
            <if test="isMicroEnt != null">is_micro_ent,</if>
            <if test="usedBondName != null">used_bond_name,</if>
            <if test="regNumber != null">reg_number,</if>
            <if test="percentileScore != null">percentile_score,</if>
            <if test="regCapital != null">reg_capital,</if>
            <if test="name != null">name,</if>
            <if test="regInstitute != null">regInstitute,</if>
            <if test="regLocation != null">reg_location,</if>
            <if test="industry != null">industry,</if>
            <if test="approvedTime != null">approved_time,</if>
            <if test="socialStaffNum != null">social_staff_num,</if>
            <if test="tags != null">tags,</if>
            <if test="taxNumber != null">tax_number,</if>
            <if test="businessScope != null">business_scope,</if>
            <if test="property3 != null">property3,</if>
            <if test="alias != null">alias,</if>
            <if test="orgNumber != null">org_number,</if>
            <if test="regStatus != null">reg_status,</if>
            <if test="estiblishTime != null">estiblish_time,</if>
            <if test="updateTimes != null">update_times,</if>
            <if test="bondType != null">bond_type,</if>
            <if test="legalPersonName != null">legal_person_name,</if>
            <if test="toTime != null">to_time,</if>
            <if test="actualCapital != null">actual_capital,</if>
            <if test="companyOrgType != null">company_org_type,</if>
            <if test="compForm != null">comp_form,</if>
            <if test="base != null">base,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="historyNames != null">history_names,</if>

            <if test="bondNum != null">bond_num,</if>
            <if test="regCapitalCurrency != null">reg_capital_currency,</if>
            <if test="actualCapitalCurrency != null">actual_capital_currency,</if>
            <if test="revokeDate != null">revoke_date,</if>
            <if test="revokeReason != null">revoke_reason,</if>
            <if test="cancelDate != null">cancelDate,</if>
            <if test="cancelReason != null">cancelReason,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>

         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="staffNumRange != null">#{staffNumRange},</if>
            <if test="fromTime != null">#{fromTime},</if>
            <if test="type != null">#{type},</if>
            <if test="bondName != null">#{bondName},</if>
            <if test="isMicroEnt != null">#{isMicroEnt},</if>
            <if test="usedBondName != null">#{usedBondName},</if>
            <if test="regNumber != null">#{regNumber},</if>
            <if test="percentileScore != null">#{percentileScore},</if>
            <if test="regCapital != null">#{regCapital},</if>
            <if test="name != null">#{name},</if>
            <if test="regInstitute != null">#{regInstitute},</if>
            <if test="regLocation != null">#{regLocation},</if>
            <if test="industry != null">#{industry},</if>
            <if test="approvedTime != null">#{approvedTime},</if>
            <if test="socialStaffNum != null">#{socialStaffNum},</if>
            <if test="tags != null">#{tags},</if>
            <if test="taxNumber != null">#{taxNumber},</if>
            <if test="businessScope != null">#{businessScope},</if>
            <if test="property3 != null">#{property3},</if>
            <if test="alias != null">#{alias},</if>
            <if test="orgNumber != null">#{orgNumber},</if>
            <if test="regStatus != null">#{regStatus},</if>
            <if test="estiblishTime != null">#{estiblishTime},</if>
            <if test="updateTimes != null">#{updateTimes},</if>
            <if test="bondType != null">#{bondType},</if>
            <if test="legalPersonName != null">#{legalPersonName},</if>
            <if test="toTime != null">#{toTime},</if>
            <if test="actualCapital != null">#{actualCapital},</if>
            <if test="companyOrgType != null">#{companyOrgType},</if>
            <if test="compForm != null">#{compForm},</if>
            <if test="base != null">#{base},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="historyNames != null">#{historyNames},</if>

            <if test="bondNum != null">#{bondNum},</if>
            <if test="regCapitalCurrency != null">#{regCapitalCurrency},</if>
            <if test="actualCapitalCurrency != null">#{actualCapitalCurrency},</if>
            <if test="revokeDate != null">#{revokeDate},</if>
            <if test="revokeReason != null">#{revokeReason},</if>
            <if test="cancelDate != null">#{cancelDate},</if>
            <if test="cancelReason != null">#{cancelReason},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>

         </trim>
    </insert>

    <update id="updateCompanyInformation" parameterType="com.akesobio.report.tianyan.domain.CompanyInformation">
        update company_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="staffNumRange != null">staff_num_range = #{staffNumRange},</if>
            <if test="fromTime != null">from_time = #{fromTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="bondName != null">bond_name = #{bondName},</if>
            <if test="isMicroEnt != null">is_micro_ent = #{isMicroEnt},</if>
            <if test="usedBondName != null">used_bond_name = #{usedBondName},</if>
            <if test="regNumber != null">reg_number = #{regNumber},</if>
            <if test="percentileScore != null">percentile_score = #{percentileScore},</if>
            <if test="regCapital != null">reg_capital = #{regCapital},</if>
            <if test="name != null">name = #{name},</if>
            <if test="regInstitute != null">regInstitute = #{regInstitute},</if>
            <if test="regLocation != null">reg_location = #{regLocation},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="approvedTime != null">approved_time = #{approvedTime},</if>
            <if test="socialStaffNum != null">social_staff_num = #{socialStaffNum},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="taxNumber != null">tax_number = #{taxNumber},</if>
            <if test="businessScope != null">business_scope = #{businessScope},</if>
            <if test="property3 != null">property3 = #{property3},</if>
            <if test="alias != null">alias = #{alias},</if>
            <if test="orgNumber != null">org_number = #{orgNumber},</if>
            <if test="regStatus != null">reg_status = #{regStatus},</if>
            <if test="estiblishTime != null">estiblish_time = #{estiblishTime},</if>
            <if test="updateTimes != null">update_times = #{updateTimes},</if>
            <if test="bondType != null">bond_type = #{bondType},</if>
            <if test="legalPersonName != null">legal_person_name = #{legalPersonName},</if>
            <if test="toTime != null">to_time = #{toTime},</if>
            <if test="actualCapital != null">actual_capital = #{actualCapital},</if>
            <if test="companyOrgType != null">company_org_type = #{companyOrgType},</if>
            <if test="compForm != null">comp_form = #{compForm},</if>
            <if test="base != null">base = #{base},</if>
            <if test="creditCode != null">credit_code = #{creditCode},</if>
            <if test="historyNames != null">history_names = #{historyNames},</if>
            <if test="historyNameList != null">history_name_list = #{historyNameList},</if>
            <if test="bondNum != null">bond_num = #{bondNum},</if>
            <if test="regCapitalCurrency != null">reg_capital_currency = #{regCapitalCurrency},</if>
            <if test="actualCapitalCurrency != null">actual_capital_currency = #{actualCapitalCurrency},</if>
            <if test="revokeDate != null">revoke_date = #{revokeDate},</if>
            <if test="revokeReason != null">revoke_reason = #{revokeReason},</if>
            <if test="cancelDate != null">cancelDate = #{cancelDate},</if>
            <if test="cancelReason != null">cancelReason = #{cancelReason},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="industryAll != null">industryAll = #{industryAll},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCompanyInformationById" parameterType="Long">
        delete from company_information where id = #{id}
    </delete>

    <delete id="deleteCompanyInformationByIds" parameterType="String">
        delete from company_information where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>