<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.FndLovMapper">

    <select id="costType" resultType="com.akesobio.report.costControl.domain.FndLovDTO">
        SELECT
        flv.value_code AS code,
        flvt.value_meaning As meaning
        FROM fnd_lov fl
        LEFT JOIN fnd_lov_value flv ON fl.lov_id = flv.lov_id
        LEFT JOIN fnd_lov_value_tl flvt ON flv.value_id = flvt.value_id and flvt.language = 'zh_CN'
        where fl.lov_name in('YW01', 'yw00')
    </select>
</mapper>

