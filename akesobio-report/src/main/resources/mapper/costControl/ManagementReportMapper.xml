<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.ManagementReportMapper">

    <select id="old" parameterType="com.akesobio.report.costControl.mapper.query.ManagementReportQuery"
            resultType="com.akesobio.report.costControl.domain.ManagementReportDTO">
        -- 26、管报费用报表 ✔

        SELECT
        hea.header_id AS header_id,
        hea.submit_user AS user_id,
        hea.submit_department AS submit_department_id,
        lin.cost_center_id AS charge_department_id,
        hea.link_header_id AS link_header_id,

        lin.line_id AS line_id,
        lin.budget_id AS budget_id,
        lin.link_line_id AS link_line_id,

        -- 单据类型
        typ.type AS type_name,
        -- OA单号/云简单号
        hea.document_num,
        -- 申请日期（OA)/提交日期（云简）->localDate
        hea.submit_date,
        -- 会议类别/费用类型/报销类型（取头上费用类型）
        hea.column17 AS cost_type,
        -- 预算科目（行上，budget_id）
        CONCAT(budg.budget_code, '-', budgl.budget_name) AS fbudget_name,

        hea.column_json AS header_column_json,
        lin.column_json AS line_column_json,
        -- 费用说明
        hea.description,
        -- 项目号
        hea.column46 AS project_number,
        -- 产品管线
        hea.column41 AS product_pipeline,
        # -- 费用承担部门分摊金额
        hea.total_receipt_amount AS amount,
        # -- 职级(交际费)
        # lev.description AS level,
        -- 提交部门
        -- 预算期间
        per.period_name AS period_time,
        -- 业务审批时间（OA和云简分别取数）
        hea.end_datetime AS end_datetime,
        -- 关闭月份
        hea.closed_date AS closed_date,
        -- 差额(关闭才算) exp_claim_line_pay.pay_currency_amount
        # CASE
        # WHEN hea.status = 'closed' THEN
        # hea.total_receipt_amount - IFNULL(SUM(lin.receipt_amount), 0)
        # END AS balance,
        # hea.total_receipt_amount,
        # depu.department_name = depc.department_name AS same_department,
        # -- 核报单预算部门
        # deps.department_name AS report_department,
        -- 核报单预算科目
        CONCAT(budg.budget_code, '-', budgl.budget_name) AS report_budgetName,
        # -- 核报单预算金额
        lin.receipt_amount AS receipt_amount,

        hea.status
        FROM exp_claim_header hea
        LEFT JOIN exp_claim_line lin ON lin.header_id = hea.header_id
        LEFT JOIN exp_header_type_tl typ ON hea.header_type_id = typ.type_id and typ.language = 'zh_CN'

        LEFT JOIN gl_budget_account budg ON budg.budget_id = lin.budget_id
        LEFT JOIN gl_budget_account_tl budgl ON budgl.budget_id = lin.budget_id and budgl.language = 'zh_CN'

        LEFT JOIN gl_period per ON per.period_id = hea.gl_period
        # LEFT JOIN exp_claim_line_pay pay ON hea.header_id = pay.header_id

        <where>
            <if test="query.headerTypeIds!=null and query.headerTypeIds.size>0">
                hea.header_type_id IN
                <foreach collection="query.headerTypeIds" item="typeId" open="(" separator="," close=")">
                    #{typeId}
                </foreach>
            </if>
            <if test="query.headerIds!=null and query.headerIds.size>0">
                AND hea.header_id IN
                <foreach collection="query.headerIds" item="headerId" open="(" separator="," close=")">
                    #{headerId}
                </foreach>
            </if>
            <if test="query.typeName != null and query.typeName != ''">
                AND typ.type LIKE CONCAT('%', #{query.typeName}, '%')
            </if>
            <if test="query.documentNum != null and query.documentNum != ''">
                AND hea.document_num LIKE CONCAT('%', #{query.documentNum}, '%')
            </if>
            <if test="query.periodId != null ">
                AND hea.gl_period = #{query.periodId}
                # OR hea.submit_date BETWEEN per.end_date AND per.start_date
            </if>
            AND hea.status != 'deleted'
        </where>
        group by lin.line_id
        LIMIT #{query.pageSize} OFFSET #{query.pageNum}
    </select>

    <select id="linkLines" parameterType="Integer" resultType="ManagementReportDTO">
        SELECT
        lin.line_id,
        lin.link_line_id,
        deps.department_name AS report_department,
        IF(lin.receipt_amount IS NULL, 0, lin.receipt_amount) AS report_amount,
        # hea.closed_date,
        # hea.status,
        lin.budget_id,
        CONCAT(budg.budget_code, '-', budgl.budget_name) AS report_budgetName
        FROM exp_claim_line lin
        LEFT JOIN exp_claim_header hea ON lin.header_id = hea.header_id
        LEFT JOIN fnd_department_tl deps ON hea.submit_department = deps.department_id and deps.language = 'zh_CN'
        LEFT JOIN gl_budget_account budg ON budg.budget_id = lin.budget_id
        LEFT JOIN gl_budget_account_tl budgl ON budgl.budget_id = lin.budget_id and budgl.language = 'zh_CN'

        <where>
            <if test="list!=null and list.size>0">
                lin.link_line_id IN
                <foreach collection="list" item="lineId" open="(" separator="," close=")">
                    #{lineId}
                </foreach>
            </if>
            # and hea.status != 'deleted'
        </where>
    </select>

    <select id="linkHeaders" parameterType="Integer" resultType="ManagementReportDTO">
#         WITH
#         DepartmentForHeader AS (
#         SELECT
#         fd.department_id AS departmentId,
#         fdt.department_name AS departmentName,
#         fdt2.department_name AS departmentName2,
#         fdt3.department_name AS departmentName3
#         FROM fnd_department fd
#         LEFT JOIN fnd_department_tl fdt ON fd.department_id = fdt.department_id AND fdt.language = 'zh_CN'
#         LEFT JOIN fnd_department fd2 ON fd.supervisor_id = fd2.department_id
#         LEFT JOIN fnd_department_tl fdt2 ON fd2.department_id = fdt2.department_id AND fdt2.language = 'zh_CN'
#         LEFT JOIN fnd_department fd3 ON fd2.supervisor_id = fd3.department_id
#         LEFT JOIN fnd_department_tl fdt3 ON fd3.department_id = fdt3.department_id AND fdt3.language = 'zh_CN'
#         )
        SELECT
        hea.header_id,
        hea.link_header_id,
        hea.link_budget_id,
        hea.document_num as link_document_num,
        lin.cost_center_id as chargeDepartmentId,
#         hea.charge_department as chargeDepartmentId,
        hea.submit_department as submitDepartmentId,
        -- 核报单预算部门
#         depc.departmentName AS chargeDepartmentName,
#         depc.departmentName2 AS chargeDepartmentName2,
#         depc.departmentName3 AS chargeDepartmentName3,
#         deps.departmentName AS submitDepartmentName,
#         deps.departmentName2 AS submitDepartmentName2,
#         deps.departmentName3 AS submitDepartmentName3,
        -- 核报单预算科目
        hea.closed_date,
        IF(lin.receipt_amount IS NULL, 0, -lin.receipt_amount) AS receipt_amount,

        lin.budget_id,

        CONCAT(budg.budget_code, '-', budgl.budget_name) AS report_budgetName,
        typ.type AS type_name,

        hea.status
        FROM exp_claim_header hea
        LEFT JOIN exp_claim_line lin ON lin.header_id = hea.header_id
#         LEFT JOIN DepartmentForHeader depc ON hea.charge_department = depc.departmentId
#         LEFT JOIN DepartmentForHeader deps ON hea.submit_department = deps.departmentId
        LEFT JOIN gl_budget_account budg ON budg.budget_id = lin.budget_id
        LEFT JOIN gl_budget_account_tl budgl ON budgl.budget_id = lin.budget_id and budgl.language = 'zh_CN'
        LEFT JOIN exp_header_type_tl typ ON hea.header_type_id = typ.type_id AND typ.language = 'zh_CN'

        <where>
            <if test="query.linkHeaderIdList!=null and query.linkHeaderIdList.size>0">
                hea.link_header_id IN
                <foreach collection="query.linkHeaderIdList" item="headerId" open="(" separator="," close=")">
                    #{headerId}
                </foreach>
            </if>
            <if test="query.headerTypeIdList!=null and query.headerTypeIdList.size>0">
                AND hea.header_type_id IN
                <foreach collection="query.headerTypeIdList" item="typeId" open="(" separator="," close=")">
                    #{typeId}
                </foreach>
            </if>
            and hea.status not in ('deleted', 'cancelled', 'withdrawed', 'rejected')
        </where>
    </select>


    <select id="list" parameterType="com.akesobio.report.costControl.mapper.query.ManagementReportQuery"  resultType="com.akesobio.report.costControl.domain.ManagementReportDTO">
        WITH
#         DepartmentForHeader AS (
#         SELECT
#         fd.department_id AS departmentId,
#         fdt.department_name AS departmentName,
#         fdt2.department_name AS departmentName2,
#         fdt3.department_name AS departmentName3
#         FROM fnd_department fd
#         LEFT JOIN fnd_department_tl fdt ON fd.department_id = fdt.department_id AND fdt.language = 'zh_CN'
#         LEFT JOIN fnd_department fd2 ON fd.supervisor_id = fd2.department_id
#         LEFT JOIN fnd_department_tl fdt2 ON fd2.department_id = fdt2.department_id AND fdt2.language = 'zh_CN'
#         LEFT JOIN fnd_department fd3 ON fd2.supervisor_id = fd3.department_id
#         LEFT JOIN fnd_department_tl fdt3 ON fd3.department_id = fdt3.department_id AND fdt3.language = 'zh_CN'
#         ),
        LOVForHeader AS (
        SELECT
        flv.value_code AS cost_type,
        flvt.value_meaning As cost_real
        FROM fnd_lov fl
        LEFT JOIN fnd_lov_value flv ON fl.lov_id = flv.lov_id
        LEFT JOIN fnd_lov_value_tl flvt ON flv.value_id = flvt.value_id
        and flvt.language = 'zh_CN'
        where fl.lov_name in ('YW01', 'yw00')
        ),
        UserForHeader AS (
        SELECT us.user_id,
        -- 申请人
        us.full_name,
        -- 申请人工号
        us.employee_number,
        -- 职级(交际费)
        lev.level AS level,
        lev.level_id
        -- 提交部门
        FROM fnd_user us
        JOIN fnd_level lev ON lev.level_id = us.level_id
        ),
        CalimMain AS (
        SELECT
        hea.header_id,
        hea.submit_user,
        hea.header_type_id,
        hea.link_header_id,
        hea.document_num,
        hea.submit_date,
        hea.column17 AS cost_type,
        hea.submit_department as submitDepartmentId,
#         hea.charge_department as chargeDepartmentId,
        lin.cost_center_id as chargeDepartmentId,
        lin.line_id,
        lin.budget_id,
        lin.link_line_id,
        typ.type AS type_name,
        CONCAT(budg.budget_code, '-', budgl.budget_name) AS fbudget_name,
        hea.column_json AS header_column_json,
        lin.column_json AS line_column_json,
        hea.description,
        hea.column46 AS project_number,
        hea.column41 AS product_pipeline,
        hea.total_receipt_amount AS amount,
        depc.department_name AS chargeDepartmentName,
        IF(
        LOCATE('年', per.period_name) > 0 AND LOCATE('月', per.period_name) > 0,
        CONCAT(
        SUBSTRING_INDEX(per.period_name, '年', 1),
        LPAD(SUBSTRING_INDEX(SUBSTRING_INDEX(per.period_name, '月', 1), '年', -1), 2, '0')
        ),
        per.period_name
        ) AS period_time,
        hea.end_datetime,
        if(hea.status='closed', hea.closed_date, null) as closed_date,
        CONCAT(budg.budget_code, '-', budgl.budget_name) AS report_budgetName,
        IF(lin.receipt_amount IS NULL, 0, lin.receipt_amount) AS receipt_amount,
        hea.gl_period,
        hea.status
        FROM exp_claim_header hea
        LEFT JOIN exp_claim_line lin ON lin.header_id = hea.header_id
        LEFT JOIN fnd_department_tl depc ON lin.cost_center_id = depc.department_id and depc.language = 'zh_CN'
        LEFT JOIN exp_header_type_tl typ ON hea.header_type_id = typ.type_id AND typ.language = 'zh_CN'
        LEFT JOIN gl_budget_account budg ON budg.budget_id = lin.budget_id
        LEFT JOIN gl_budget_account_tl budgl ON budgl.budget_id = lin.budget_id AND budgl.language = 'zh_CN'
        LEFT JOIN gl_period per ON per.period_id = hea.gl_period
        WHERE hea.status not in ('deleted', 'cancelled', 'withdrawed', 'rejected')
        )
        SELECT
        cm.*,
        us.*,
        lo.cost_real AS costType
#         deps.departmentName AS submitDepartmentName,
#         deps.departmentName2 AS submitDepartmentName2,
#         deps.departmentName3 AS submitDepartmentName3,
#         depc.departmentName AS chargeDepartmentName,
#         depc.departmentName2 AS chargeDepartmentName2,
#         depc.departmentName3 AS chargeDepartmentName3
        FROM CalimMain cm
        JOIN LOVForHeader lo ON cm.cost_type = lo.cost_type
        JOIN UserForHeader us ON cm.submit_user = us.user_id
#         JOIN DepartmentForHeader deps ON cm.submit_department = deps.departmentId
#         JOIN DepartmentForHeader depc ON cm.cost_center_id = depc.departmentId
        <where>
            <if test="query.headerTypeIds!=null and query.headerTypeIds.size>0">
                AND cm.header_type_id IN
                <foreach collection="query.headerTypeIds" item="typeId" open="(" separator="," close=")">
                    #{typeId}
                </foreach>
            </if>
            <if test="query.chargeDepartmentName != null and query.chargeDepartmentName != ''">
                AND cm.chargeDepartmentName LIKE CONCAT('%', #{query.chargeDepartmentName}, '%')
            </if>
            <if test="query.fullName != null and query.fullName != ''">
                AND us.full_name = #{query.fullName}
            </if>
        <if test="query.typeName!= null and query.typeName!= ''">
            AND cm.type_name LIKE CONCAT('%', #{query.typeName}, '%')
        </if>
        <if test="query.documentNum!= null and query.documentNum!= ''">
            AND cm.document_num LIKE CONCAT('%', #{query.documentNum}, '%')
        </if>
            <if test="query.periodId!= null">
                AND EXISTS(
                SELECT 1
                FROM gl_period AS parent_period
                JOIN gl_period AS child_period
                WHERE parent_period.period_id = #{query.periodId}
                AND child_period.start_date BETWEEN parent_period.start_date AND parent_period.end_date
                AND cm.gl_period = child_period.period_id
                )
            </if>
        </where>
        ORDER BY cm.submit_date DESC
    </select>
</mapper>