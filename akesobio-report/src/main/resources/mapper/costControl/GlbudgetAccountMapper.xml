<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.GlBudgetAccountMapper">
    <select id="list" resultType="GlBudgetAccountDTO">
        SELECT
        gba.budget_id,
        gba.budget_code,
        gbat.budget_name
        from gl_budget_account gba
        JOIN gl_budget_account_tl gbat ON gba.budget_id = gbat.budget_id AND gbat.language = 'zh_CN'
        WHERE gba.enabled_flag = 'Y'
    </select>
</mapper>

