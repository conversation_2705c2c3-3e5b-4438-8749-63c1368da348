<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.FndDepartmentMapper">
<!--    <select id="all" parameterType="com.akesobio.report.costControl.mapper.query.FndDepartmentQuery"-->
<!--            resultType="com.akesobio.report.costControl.domain.HeaderDepartmentDTO">-->
<!--        SELECT-->
<!--            fd.department_id AS departmentId,-->
<!--            fdt.department_name AS departmentName,-->
<!--            fdt2.department_name AS departmentName2,-->
<!--            fdt3.department_name AS departmentName3-->
<!--        FROM fnd_department fd-->
<!--                 LEFT JOIN fnd_department_tl fdt ON fd.department_id = fdt.department_id AND fdt.language = 'zh_CN'-->
<!--                 LEFT JOIN fnd_department fd2 ON fd.supervisor_id = fd2.department_id-->
<!--                 LEFT JOIN fnd_department_tl fdt2 ON fd2.department_id = fdt2.department_id AND fdt2.language = 'zh_CN'-->
<!--                 LEFT JOIN fnd_department fd3 ON fd2.supervisor_id = fd3.department_id-->
<!--                 LEFT JOIN fnd_department_tl fdt3 ON fd3.department_id = fdt3.department_id AND fdt3.language = 'zh_CN'-->
<!--    </select>-->

<!--    <select id="claimIdFilter" parameterType="com.akesobio.report.costControl.mapper.query.FndDepartmentQuery" resultType="Integer">-->
<!--        SELECT  hea.header_id-->
<!--        FROM exp_claim_header hea-->
<!--        JOIN fnd_user us ON us.department_id = hea.submit_user-->
<!--        JOIN fnd_department_tl dt ON dt.department_id = us.department_id AND dt.language = 'zh_CN'-->
<!--        <where>-->
<!--            1=1-->
<!--            <if test="query.departmentName!=null and query.departmentName!=''">-->
<!--                AND dt.department_name LIKE CONCAT('%', #{query.departmentName}, '%')-->
<!--            </if>-->
<!--            <if test="query.headerTypeIds != null and query.headerTypeIds.size>0">-->
<!--                AND hea.header_type_id IN-->
<!--                <foreach item="id" index="index" collection="query.headerTypeIds" open="(" separator="," close=")">-->
<!--                    #{id}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->

<!--    <select id="claimChargeIdFilter" parameterType="com.akesobio.report.costControl.mapper.query.FndDepartmentQuery" resultType="Integer">-->
<!--        SELECT-->
<!--        lin.header_id-->
<!--        &#45;&#45; dt.department_id,-->
<!--        from fnd_department_tl dt-->
<!--        JOIN exp_claim_line lin ON lin.cost_center_id = dt.department_id-->
<!--        JOIN exp_claim_header hea ON lin.header_id = hea.header_id-->
<!--        JOIN exp_header_type_tl typ ON hea.header_type_id = typ.type_id AND typ.language = 'zh_CN'-->
<!--        <where>-->
<!--            1=1-->
<!--            <if test="query.departmentName!= null and query.departmentName != ''">-->
<!--                AND dt.department_name LIKE CONCAT('%', #{query.chargeDepartmentName}, '%')-->
<!--            </if>-->
<!--            <if test="query.headerTypeName!= null and query.headerTypeName != ''">-->
<!--                AND typ.type_name LIKE CONCAT('%', #{query.headerTypeName}, '%')-->
<!--            </if>-->
<!--            <if test="query.headerTypeIds != null and query.headerTypeIds.size>0">-->
<!--                AND hea.header_type_id IN-->
<!--                <foreach item="id" index="index" collection="query.headerTypeIds" open="(" separator="," close=")">-->
<!--                    #{id}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->

    <select id="list" resultType="FndDepartmentDTO">
        SELECT
        fd.department_id,
        fd.supervisor_id,
        fdt.department_name,
        fdt.department_name AS departmentName,
        fdt2.department_name AS departmentName2,
        fdt3.department_name AS departmentName3
        -- dt.department_id,
        from fnd_department fd
        LEFT JOIN fnd_department_tl fdt ON fdt.department_id = fd.department_id AND fdt.language = 'zh_CN'
        LEFT JOIN fnd_department fd2 ON fd.supervisor_id = fd2.department_id
        LEFT JOIN fnd_department_tl fdt2 ON fd2.department_id = fdt2.department_id AND fdt2.language = 'zh_CN'
        LEFT JOIN fnd_department fd3 ON fd2.supervisor_id = fd3.department_id
        LEFT JOIN fnd_department_tl fdt3 ON fd3.department_id = fdt3.department_id AND fdt3.language = 'zh_CN'
    </select>
</mapper>

