<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.MeetingRecordReportMapper">

    <select id="list" parameterType="com.akesobio.report.costControl.mapper.query.MeetingRecordReportQuery"
            resultType="com.akesobio.report.costControl.domain.MeetingRecordReportDTO">
        -- 9、会议台账报表

        SELECT
        -- 会议申请部分
        h_req.document_num AS "meetingApplicationNumber", -- 会议申请号（云简）
        h_req.external_id AS "meetingApplicationNumberCrm", -- 会议申请号（CRM）
        u_req_submit.full_name AS "meetingApplicationSubmitter", -- 会议申请提交人
        u_req_submit.employee_number AS "employeeNumber", -- 会议申请提交人-员工号
        d_req_submit.department_name AS "department", -- 会议申请-提交部门
        c_req_tl.department_name AS "company", -- 会议申请-所属公司
        lov_tl_cpgx_req.value_meaning AS "productPipeline", -- 会议申请-产品管线
        lov_tl_proj_req.value_meaning AS "projectNumber", -- 会议申请-项目号
        COALESCE(h_req.column39, l_req.column39) AS "seriesProjectNumber", -- 会议申请-系列会项目号
        COALESCE(h_req.column_json ->> '$.column120', h_req.description) AS "meetingName", -- 会议名称
        lov_tl_meet_req.value_meaning AS "meetingForm", -- 会议形式
        lov_tl_biz_req.value_meaning AS businessType, -- 业务类型
        et_req.type AS "costType", -- 费用类型
        h_req.start_datetime AS "meetingStartTime", -- 会议开始时间
        h_req.end_datetime AS "meetingEndTime", -- 会议结束时间
        lov_tl_zd01_req.value_meaning AS "targetTerminal", -- 目标终端
        # h_claim.column_json ->> '$.column42' AS "targetTerminal", -- 目标终端
        lov_tl_target_req.value_meaning AS "targetDepartment", -- 目标科室
        h_req.total_amount AS "totalAmount", -- 会议申请-总金额

        -- 后续单据部分（核报/预付）
        COALESCE(h_claim.document_num, h_prepay.document_num) AS "documentNumber", -- 单据号
        eht_main_tl.type AS "documentType", -- 单据类型
        COALESCE(u_claim_submit.full_name, u_prepay_submit.full_name) AS "submitter", -- 提交人
        COALESCE(u_claim_submit.employee_number,
        u_prepay_submit.employee_number) AS "employeeNumberCrm", -- 员工号
        COALESCE(d_claim_submit.department_name,
        d_prepay_submit.department_name) AS "departmentCrm", -- 提交部门
        COALESCE(c_claim_tl.department_name, c_prepay_tl.department_name) AS "companyCrm", -- 所属公司
        COALESCE(lov_tl_cpgx_main.value_meaning,
        lov_tl_cpgx_req.value_meaning) AS "productPipelineCrm", -- 产品管线
        COALESCE(lov_tl_proj_main.value_meaning,
        lov_tl_proj_req.value_meaning) AS "projectNumberCrm", -- 项目号
        COALESCE(l_main.column_json ->> '$.column123', l_req.column_json ->>
        '$.column123') AS "internalOrderNumber", -- 内部订单号
        COALESCE(l_main.column45, l_req.column45) AS "seriesNumber", -- 系列会编号
        COALESCE(lov_tl_biz_main.value_meaning,
        lov_tl_biz_req.value_meaning) AS businessTypeCrm, -- 业务类型
        et_main.type AS "costTypeCrm", -- 费用类型
        COALESCE(city_main_tl.city_name, city_req_tl.city_name) AS "meetingLocation", -- 会议举办地点
        COALESCE(h_claim.start_datetime, h_prepay.start_datetime,
        h_req.start_datetime) AS "actualMeetingStartTime", -- 实际会议开始时间
        COALESCE(h_claim.end_datetime, h_prepay.end_datetime,
        h_req.end_datetime) AS "actualMeetingEndTime", -- 实际会议结束时间
        COALESCE(
        h_claim.column_json ->> '$.column120', h_claim.description, h_prepay.column_json ->> '$.column120',
        h_prepay.description, h_req.column_json ->> '$.column120',
        h_req.description) AS "meetingNameCrm", -- 会议名称
        COALESCE(lov_tl_meet_main.value_meaning,
        lov_tl_meet_req.value_meaning) AS "meetingFormCrm", -- 会议形式
        COALESCE(h_claim.column_json ->> '$.column186', h_prepay.column_json ->> '$.column186') AS
        "actualAttendeesNumber", -- 实际参会人数
        COALESCE(h_claim.column4, h_prepay.column4) AS "actualExpertNumber", -- 实际参会专家数
        CASE COALESCE(h_claim.pay_object, h_prepay.pay_object)
        WHEN 'S' THEN '供应商'
        WHEN 'U' THEN '员工'
        WHEN 'E' THEN '外部人员'
        ELSE NULL
        END AS "paymentObject", -- 支付对象
        CASE COALESCE(h_claim.pay_object, h_prepay.pay_object)
        WHEN 'S' THEN sup_main.supplier_name
        WHEN 'U' THEN u_payee.full_name
        ELSE NULL
        END AS "supplierOrEmployee", -- 供应商 / 员工
        COALESCE(h_claim.total_pay_amount, h_prepay.total_pay_amount) AS "paymentTotalAmount", -- 支付总金额

        -- 凭证信息 (来源于支付 单)
        glb.journal_num AS "voucherNumber", -- 凭证号
        YEAR(glb.gl_date) AS "voucherYear", -- 凭证年度
        MONTH(glb.gl_date) AS "voucherMonth", -- 凭证月份
        glb.detail AS "voucherAmount", -- 凭证金额
        comp_gl.company_name AS "voucherCompany" -- 凭证归属公司

        FROM exp_claim_header h_req
        LEFT JOIN exp_claim_header h_claim
        ON h_req.header_id = h_claim.link_header_id AND h_claim.header_type_id =
        (SELECT type_id FROM exp_header_type WHERE type_code = 'YXHY02') AND
        h_claim.status != 'deleted'
        LEFT JOIN exp_claim_header h_prepay
        ON h_req.header_id = h_prepay.link_header_id AND h_prepay.header_type_id IN
        (SELECT type_id
        FROM exp_header_type
        WHERE type_code IN ('YXDG01', 'YXDG02')) AND
        h_prepay.status != 'deleted'

        -- **新增：关联支付单**
        LEFT JOIN exp_claim_header h_pay
        ON COALESCE(h_claim.header_id, h_prepay.header_id) = h_pay.link_header_id AND
        h_pay.status != 'deleted'

        LEFT JOIN (SELECT header_id, MIN(line_id) AS line_id
        FROM exp_claim_line
        GROUP BY header_id) l_req_first ON
        h_req.header_id = l_req_first.header_id
        LEFT JOIN exp_claim_line l_req ON l_req_first.line_id = l_req.line_id
        LEFT JOIN (SELECT header_id, MIN(line_id) AS line_id
        FROM exp_claim_line
        GROUP BY header_id) l_main_first ON
        COALESCE(h_claim.header_id, h_prepay.header_id) = l_main_first.header_id
        LEFT JOIN exp_claim_line l_main ON l_main_first.line_id = l_main.line_id
        LEFT JOIN fnd_user u_req_submit ON h_req.submit_user = u_req_submit.user_id
        LEFT JOIN fnd_department_tl d_req_submit ON u_req_submit.department_id = d_req_submit.department_id AND
        d_req_submit.language = 'zh_CN'
        LEFT JOIN fnd_department c_req ON h_req.branch_id = c_req.department_id
        LEFT JOIN fnd_department_tl c_req_tl
        ON c_req.department_id = c_req_tl.department_id AND c_req_tl.language =
        'zh_CN'
        LEFT JOIN fnd_user u_claim_submit ON h_claim.submit_user = u_claim_submit.user_id
        LEFT JOIN fnd_department_tl d_claim_submit
        ON u_claim_submit.department_id = d_claim_submit.department_id AND
        d_claim_submit.language = 'zh_CN'
        LEFT JOIN fnd_department c_claim ON h_claim.branch_id = c_claim.department_id
        LEFT JOIN fnd_department_tl c_claim_tl ON c_claim.department_id = c_claim_tl.department_id AND
        c_claim_tl.language = 'zh_CN'
        LEFT JOIN fnd_user u_prepay_submit ON h_prepay.submit_user = u_prepay_submit.user_id
        LEFT JOIN fnd_department_tl d_prepay_submit
        ON u_prepay_submit.department_id = d_prepay_submit.department_id AND
        d_prepay_submit.language = 'zh_CN'
        LEFT JOIN fnd_department c_prepay ON h_prepay.branch_id = c_prepay.department_id
        LEFT JOIN fnd_department_tl c_prepay_tl ON c_prepay.department_id = c_prepay_tl.department_id AND
        c_prepay_tl.language = 'zh_CN'
        LEFT JOIN exp_header_type_tl eht_main_tl ON COALESCE(h_claim.header_type_id, h_prepay.header_type_id) =
        eht_main_tl.type_id AND eht_main_tl.language = 'zh_CN'
        LEFT JOIN exp_supplier_tl sup_main
        ON COALESCE(h_claim.supplier_id, h_prepay.supplier_id) = sup_main.supplier_id
        AND sup_main.language = 'zh_CN'
        LEFT JOIN fnd_user u_payee ON COALESCE(h_claim.pay_user, h_prepay.pay_user) = u_payee.user_id
        LEFT JOIN exp_type_tl et_main ON l_main.type_id = et_main.type_id AND et_main.language = 'zh_CN'
        LEFT JOIN exp_type_tl et_req ON l_req.type_id = et_req.type_id AND et_req.language = 'zh_CN'

        -- **修正：关联支付单的凭证批次**
        LEFT JOIN gl_je_batch glb ON h_pay.gl_batch_id = glb.batch_id

        LEFT JOIN fnd_company comp_gl ON glb.company_id = comp_gl.company_id
        LEFT JOIN fnd_city city_req ON l_req.destination_city = city_req.city_id
        LEFT JOIN fnd_city_tl city_req_tl
        ON city_req.city_id = city_req_tl.city_id AND city_req_tl.language = 'zh_CN'
        LEFT JOIN fnd_city city_main ON l_main.destination_city = city_main.city_id
        LEFT JOIN fnd_city_tl city_main_tl
        ON city_main.city_id = city_main_tl.city_id AND city_main_tl.language =
        'zh_CN'
        LEFT JOIN fnd_lov lov_cpgx_req
        ON lov_cpgx_req.lov_name = 'CPGX' AND lov_cpgx_req.company_id = h_req.company_id
        LEFT JOIN fnd_lov_value lov_val_cpgx_req ON lov_cpgx_req.lov_id = lov_val_cpgx_req.lov_id AND
        COALESCE(h_req.column41, l_req.column41) =
        lov_val_cpgx_req.value_code
        LEFT JOIN fnd_lov_value_tl lov_tl_cpgx_req ON lov_val_cpgx_req.value_id = lov_tl_cpgx_req.value_id AND
        lov_tl_cpgx_req.language = 'zh_CN'
        LEFT JOIN fnd_lov lov_proj_req
        ON lov_proj_req.lov_name = 'Project_number' AND lov_proj_req.company_id =
        h_req.company_id
        LEFT JOIN fnd_lov_value lov_val_proj_req ON lov_proj_req.lov_id = lov_val_proj_req.lov_id AND
        COALESCE(h_req.column46, l_req.column46) =
        lov_val_proj_req.value_code
        LEFT JOIN fnd_lov_value_tl lov_tl_proj_req ON lov_val_proj_req.value_id = lov_tl_proj_req.value_id AND
        lov_tl_proj_req.language = 'zh_CN'
        LEFT JOIN fnd_lov lov_meet_req ON lov_meet_req.lov_name = 'meeting_type' AND lov_meet_req.company_id =
        h_req.company_id
        LEFT JOIN fnd_lov_value lov_val_meet_req ON lov_meet_req.lov_id = lov_val_meet_req.lov_id AND
        COALESCE(h_req.column_json ->> '$.column113',
        l_req.column_json ->> '$.column113') =
        lov_val_meet_req.value_code
        LEFT JOIN fnd_lov_value_tl lov_tl_meet_req ON lov_val_meet_req.value_id = lov_tl_meet_req.value_id AND
        lov_tl_meet_req.language = 'zh_CN'
        LEFT JOIN fnd_lov lov_biz_req
        ON lov_biz_req.lov_name = 'yw00' AND lov_biz_req.company_id = h_req.company_id
        LEFT JOIN fnd_lov_value lov_val_biz_req ON lov_biz_req.lov_id = lov_val_biz_req.lov_id AND
        COALESCE(h_req.column38, l_req.column38) =
        lov_val_biz_req.value_code
        LEFT JOIN fnd_lov_value_tl lov_tl_biz_req ON lov_val_biz_req.value_id = lov_tl_biz_req.value_id AND
        lov_tl_biz_req.language = 'zh_CN'
        LEFT JOIN fnd_lov lov_zd01_req
        ON lov_zd01_req.lov_name = 'ZD01' AND lov_zd01_req.company_id = h_req.company_id
        LEFT JOIN fnd_lov_value lov_val_zd01_req ON lov_zd01_req.lov_id = lov_val_zd01_req.lov_id AND
        COALESCE(h_req.column26, l_req.column26) =
        lov_val_zd01_req.value_code
        LEFT JOIN fnd_lov_value_tl lov_tl_zd01_req ON lov_val_zd01_req.value_id = lov_tl_zd01_req.value_id AND
        lov_tl_zd01_req.language = 'zh_CN'
        LEFT JOIN fnd_lov lov_target_req
        ON lov_target_req.lov_name = 'Target_Departments' AND lov_target_req.company_id
        = h_req.company_id
        LEFT JOIN fnd_lov_value lov_val_target_req ON lov_target_req.lov_id = lov_val_target_req.lov_id AND
        COALESCE(h_req.column_json ->> '$.column109',
        l_req.column_json ->> '$.column109') =
        lov_val_target_req.value_code
        LEFT JOIN fnd_lov_value_tl lov_tl_target_req
        ON lov_val_target_req.value_id = lov_tl_target_req.value_id AND
        lov_tl_target_req.language = 'zh_CN'
        LEFT JOIN fnd_lov lov_cpgx_main ON lov_cpgx_main.lov_name = 'CPGX' AND lov_cpgx_main.company_id =
        COALESCE(h_claim.company_id, h_prepay.company_id)
        LEFT JOIN fnd_lov_value lov_val_cpgx_main ON lov_cpgx_main.lov_id = lov_val_cpgx_main.lov_id AND
        COALESCE(h_claim.column41, l_main.column41) =
        lov_val_cpgx_main.value_code
        LEFT JOIN fnd_lov_value_tl lov_tl_cpgx_main
        ON lov_val_cpgx_main.value_id = lov_tl_cpgx_main.value_id AND
        lov_tl_cpgx_main.language = 'zh_CN'
        LEFT JOIN fnd_lov lov_proj_main
        ON lov_proj_main.lov_name = 'Project_number' AND lov_proj_main.company_id =
        COALESCE(h_claim.company_id, h_prepay.company_id)
        LEFT JOIN fnd_lov_value lov_val_proj_main ON lov_proj_main.lov_id = lov_val_proj_main.lov_id AND
        COALESCE(h_claim.column46, l_main.column46) =
        lov_val_proj_main.value_code
        LEFT JOIN fnd_lov_value_tl lov_tl_proj_main
        ON lov_val_proj_main.value_id = lov_tl_proj_main.value_id AND
        lov_tl_proj_main.language = 'zh_CN'
        LEFT JOIN fnd_lov lov_meet_main
        ON lov_meet_main.lov_name = 'meeting_type' AND lov_meet_main.company_id =
        COALESCE(h_claim.company_id, h_prepay.company_id)
        LEFT JOIN fnd_lov_value lov_val_meet_main ON lov_meet_main.lov_id = lov_val_meet_main.lov_id AND
        COALESCE(h_claim.column_json ->> '$.column113',
        l_main.column_json ->> '$.column113') =
        lov_val_meet_main.value_code
        LEFT JOIN fnd_lov_value_tl lov_tl_meet_main
        ON lov_val_meet_main.value_id = lov_tl_meet_main.value_id AND
        lov_tl_meet_main.language = 'zh_CN'
        LEFT JOIN fnd_lov lov_biz_main ON lov_biz_main.lov_name = 'yw00' AND lov_biz_main.company_id =
        COALESCE(h_claim.company_id, h_prepay.company_id)
        LEFT JOIN fnd_lov_value lov_val_biz_main ON lov_biz_main.lov_id = lov_val_biz_main.lov_id AND
        COALESCE(h_claim.column38, l_main.column38) =
        lov_val_biz_main.value_code
        LEFT JOIN fnd_lov_value_tl lov_tl_biz_main ON lov_val_biz_main.value_id = lov_tl_biz_main.value_id AND
        lov_tl_biz_main.language = 'zh_CN'
        <where>
            h_req.header_type_id = (SELECT type_id FROM exp_header_type WHERE type_code = 'YXHYCRM')
            <if test="query.meetingApplyNumber != null and query.meetingApplyNumber != ''">
                AND h_req.document_num = #{query.meetingApplyNumber}
            </if>
            <if test="query.meetingApplyNumberCrm != null and query.meetingApplyNumberCrm != ''">
                AND h_req.external_id = #{query.meetingApplyNumberCrm}
            </if>
            <if test="query.submitDepartment!= null and query.submitDepartment!= ''">
                AND COALESCE(d_claim_submit.department_name, d_prepay_submit.department_name) =
                #{query.submitDepartment}
                OR d_req_submit.department_name = #{query.submitDepartment}
            </if>
            <if test="query.targetTerminal!= null and query.targetTerminal!= ''">
                AND targetTerminal = #{query.targetTerminal}
            </if>
            <if test="query.businessType!= null and query.businessType!= ''">
                AND COALESCE(lov_tl_biz_main.value_meaning, lov_tl_biz_req.value_meaning) = #{query.businessType}
                OR lov_tl_biz_req.value_meaning = #{query.businessType}
            </if>
            <if test="query.costType!= null and query.costType!= ''">
                AND et_req.type = #{query.costType}
            </if>
            <if test="query.applicant!= null and query.applicant!= ''">
                AND u_req_submit.full_name = #{query.applicant}
                OR COALESCE(u_claim_submit.full_name, u_prepay_submit.full_name) = #{query.applicant}
            </if>
            AND h_req.status != 'deleted'
        </where>
    </select>
</mapper>

