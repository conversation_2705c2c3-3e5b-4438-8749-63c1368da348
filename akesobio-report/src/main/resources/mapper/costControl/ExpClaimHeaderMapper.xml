<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.ExpClaimHeaderMapper">
    
    <!-- 查询PAY开头的支付单据 -->
    <select id="selectPaymentDocuments" resultType="com.akesobio.report.costControl.domain.ExpClaimHeaderDTO">
        SELECT 
            header_id,
            document_num,
            journal_num,
            link_header_id,
            submit_date
        FROM exp_claim_header
        WHERE document_num LIKE 'PAY%'
        AND status != 'deleted'
        LIMIT #{limit}
    </select>
    
    <!-- 根据link_header_id查询相同前序单据的支付单据 -->
    <select id="selectByLinkHeaderId" resultType="com.akesobio.report.costControl.domain.ExpClaimHeaderDTO">
        SELECT 
            header_id,
            document_num,
            journal_num,
            link_header_id,
            submit_date
        FROM exp_claim_header
        WHERE link_header_id = #{linkHeaderId}
        AND status != 'deleted'
    </select>
    
    <!-- 根据单号列表查询支付单据 -->
    <select id="selectPaymentDocumentsByNumbers" resultType="com.akesobio.report.costControl.domain.ExpClaimHeaderDTO">
        SELECT 
            header_id,
            document_num,
            journal_num,
            link_header_id,
            submit_date
        FROM exp_claim_header
        WHERE document_num IN
        <foreach collection="documentNums" item="docNum" separator="," open="(" close=")">
            #{docNum}
        </foreach>
        AND status != 'deleted'
    </select>
    
</mapper>