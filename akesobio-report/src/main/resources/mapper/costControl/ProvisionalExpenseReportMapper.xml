<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.ProvisionalExpenseReportMapper">

    <select id="list" parameterType="com.akesobio.report.costControl.mapper.query.ProvisionalExpenseReportQuery"
            resultType="com.akesobio.report.costControl.domain.ProvisionalExpenseReportDTO">

        -- 21、预提费用清单报表 ✔

        -- 商业运营部与准入策略部 - 全业务单据详情总报表（最终版）
        -- 该版本根据最终的字段梳理需求，整合了单据所有维度的详细信息。
        -- 使用CTE（公用表表达式）预处理当前审批人信息
        WITH
        cte_first_line AS (SELECT *,
        ROW_NUMBER() OVER (PARTITION BY header_id ORDER BY line_id) as rn
        FROM exp_claim_line)

        SELECT
        ROW_NUMBER() OVER (ORDER BY h.submit_date DESC) AS '序号',
        h.header_type_id AS 'headerTypeId', -- 单据类型ID
        ht_tl.type AS 'receiptsType', -- 单据类型
        h.journal_num AS 'voucherNum', -- 凭证号
#         CASE
#         WHEN ht.internal_type IN ('REQUEST', 'PAYMENT_REQUISITION')
#         THEN '申请类'
#         WHEN ht.internal_type IN ('EXPENSE', 'PAYMENT')
#         THEN '报销类'
#         ELSE '其他'
#         END AS 'receiptsClass', -- 单据大类
        h.document_num AS 'receiptsNum', -- 单据号
        h.submit_date AS 'submitTime', -- 提单时间
        u_submit.full_name AS 'submitter', -- 提交人
        u_submit.department_id AS'submitDepartmentId', -- 提交人所属部门ID
        # d_submit_tl.department_name AS 'submitterDepartment', -- 提交人所属部门
        # d_submit_parent_tl.department_name AS 'submitterArea', -- 提交人所属大区
        # lov_area_tl.value_meaning AS 'submitterRegion', -- 提交人所属片区


        CASE h.status
        WHEN 'incomplete' THEN '保存未提交'
        WHEN 'submitted' THEN '审批中'
        WHEN 'deleted' THEN '已删除'
        WHEN 'cancelled' THEN '已取消'
        WHEN 'withdrawed' THEN '已撤回'
        WHEN 'preapproved' THEN '业务审批通过'
        WHEN 'approved' THEN '审批通过'
        WHEN 'closed' THEN '已关闭/已支付'
        WHEN 'rejected' THEN '已拒绝'
        ELSE h.status
        END AS 'status', -- 单据状态
        IFNULL(vt_step.value_meaning, '审批完成或已关闭') AS 'currentSession', -- 当前审批节点
        IFNULL(u_approver.full_name, '无') AS 'currentApprover', -- 当前审批人
        d_branch_tl.department_name AS 'companyName', -- 所属公司
        b.department_code AS 'companyCode', -- 公司代码
        p.period_name AS 'periodName', -- 预算期间
        h.total_pay_amount AS 'totalPayAmount', -- 支付总金额
        h.column3 AS 'offsetAmount', -- 冲抵金额
        h.total_claim_amount AS 'adjustmentReportAmount', -- 调整核报金额
        h.column_json ->> '$.column102' AS 'internalOrderNum', -- 内部订单号

        h.start_datetime AS 'startTime', -- '开始时间',
        h.end_datetime AS 'endTime', -- '结束时间',
        fl.receipt_date AS 'invoiceDate', -- '发票日期',
        fl.invoice_num AS 'invoiceNum', -- '发票号码',
        line_type_tl.type AS 'invoiceType', -- '发票类型',
        ga.account_code AS 'accountCode', -- 借会计科目代码
        ga_tl.account_name AS 'accountName', -- 借会计科目名称
        bga_tl.budget_name AS 'budgetName', -- 预算科目
        d_charge.column1 AS 'costCenterCode', -- SAP记账成本中心代码
        lov_sap_cc_tl.value_meaning AS 'costCenterName', -- SAP记账成本中心名称
        d_charge.department_code AS 'chargeDeptCode', -- 费用承担部门代码
        d_charge_tl.department_name AS 'chargeDeptName', -- 费用承担部门名称
        link_h.document_num AS 'preDocumentNumber', -- 前序单据号
        # lov_biz_type_tl.value_meaning AS 'businessType', -- 业务类型
        h.column_json ->> '$.column106' AS 'businessType', -- 业务类型
        lov_exp_type_tl.value_meaning AS 'costType' -- 费用类型

        FROM exp_claim_header h
        -- 所属公司信息关联
        LEFT JOIN fnd_department b
        ON h.branch_id = b.department_id AND b.type = 'B'
        LEFT JOIN fnd_department_tl d_branch_tl
        ON b.department_id = d_branch_tl.department_id AND d_branch_tl.language = 'zh_CN'
        -- 基础信息关联
        LEFT JOIN exp_header_type ht
        ON h.header_type_id = ht.type_id
        LEFT JOIN exp_header_type_tl ht_tl
        ON ht.type_id = ht_tl.type_id AND ht_tl.language = 'zh_CN'
        LEFT JOIN fnd_user u_submit
        ON h.submit_user = u_submit.user_id
        LEFT JOIN gl_period p
        ON h.gl_period = p.period_id
        -- 行信息关联
        LEFT JOIN cte_first_line fl
        ON h.header_id = fl.header_id AND fl.rn = 1
        LEFT JOIN exp_type_tl line_type_tl
        ON fl.type_id = line_type_tl.type_id AND line_type_tl.language = 'zh_CN'
        -- 前序单据关联
        LEFT JOIN exp_claim_header link_h
        ON h.link_header_id = link_h.header_id
        -- 部门层级和属性关联
        LEFT JOIN fnd_department d_submit ON h.submit_department = d_submit.department_id
        LEFT JOIN fnd_department_tl d_submit_tl ON d_submit.department_id = d_submit_tl.department_id AND
        d_submit_tl.language = 'zh_CN'
        LEFT JOIN fnd_department d_submit_parent ON d_submit.supervisor_id = d_submit_parent.department_id
        # LEFT JOIN fnd_department_tl d_submit_parent_tl
        # ON d_submit_parent.department_id = d_submit_parent_tl.department_id AND
        # d_submit_parent_tl.language = 'zh_CN'
        -- 所属片区关联 (值列表)
        LEFT JOIN fnd_lov lov_area_def ON lov_area_def.lov_name = 'CBZXDX'
        # LEFT JOIN fnd_lov_value lov_area
        # ON d_submit.column2 = lov_area.value_code AND lov_area.lov_id = lov_area_def.lov_id
        # LEFT JOIN fnd_lov_value_tl lov_area_tl
        # ON lov_area.value_id = lov_area_tl.value_id AND lov_area_tl.language = 'zh_CN'
        -- 费用承担部门关联
        LEFT JOIN fnd_department d_charge ON h.charge_department = d_charge.department_id
        LEFT JOIN fnd_department_tl d_charge_tl ON d_charge.department_id = d_charge_tl.department_id AND
        d_charge_tl.language = 'zh_CN'
        -- =================================================================
        -- 【核心】当前审批节点与审批人关联逻辑
        -- =================================================================
        -- 1. 关联审批流程表，找到当前正在审批的节点
        LEFT JOIN fnd_workflow_path p_approving ON h.header_id = p_approving.source_id AND p_approving.workflow_type =
        'EXP' -- 根据文档，单据审批流类型为'EXP'
        AND p_approving.status = 'approving' -- 'approving'状态表示“审批中”，是定位当前节点的关键
        -- 2. 关联用户表，获取审批人姓名
        LEFT JOIN
        fnd_user u_approver
        ON p_approving.user_id = u_approver.user_id
        -- 3. 关联值列表系列表，将节点代码转换为中文名称
        LEFT JOIN
        fnd_lov lov_step
        ON lov_step.lov_name = 'Workflow Step' -- 根据知识图谱，审批节点的值列表类型代码为'Workflow Step'
        LEFT JOIN
        fnd_lov_value v_step
        ON lov_step.lov_id = v_step.lov_id AND p_approving.workflow_step = v_step.value_code
        LEFT JOIN
        fnd_lov_value_tl vt_step
        ON v_step.value_id = vt_step.value_id AND vt_step.language = 'zh_CN'
        -- 财务及业务类型关联
        LEFT JOIN gl_account ga ON fl.dr_account_id = ga.account_id
        LEFT JOIN gl_account_tl ga_tl ON ga.account_id = ga_tl.account_id AND ga_tl.language = 'zh_CN'
        LEFT JOIN gl_budget_account bga ON fl.budget_id = bga.budget_id
        LEFT JOIN gl_budget_account_tl bga_tl  ON bga.budget_id = bga_tl.budget_id AND bga_tl.language = 'zh_CN'
        -- SAP记账成本中心关联 (值列表)
        LEFT JOIN fnd_lov lov_sap_cc_def
        ON lov_sap_cc_def.lov_name = 'SAP_Cost_Center'
        LEFT JOIN fnd_lov_value lov_sap_cc
        ON d_charge.column1 = lov_sap_cc.value_code AND lov_sap_cc.lov_id = lov_sap_cc_def.lov_id
        LEFT JOIN fnd_lov_value_tl lov_sap_cc_tl
        ON lov_sap_cc.value_id = lov_sap_cc_tl.value_id AND lov_sap_cc_tl.language = 'zh_CN'
        -- 业务类型关联 (值列表)
        LEFT JOIN fnd_lov lov_biz_type_def
        ON lov_biz_type_def.lov_name = 'WY00'
        LEFT JOIN fnd_lov_value lov_biz_type
        ON h.column38 = lov_biz_type.value_code AND lov_biz_type.lov_id = lov_biz_type_def.lov_id
        LEFT JOIN fnd_lov_value_tl lov_biz_type_tl
        ON lov_biz_type.value_id = lov_biz_type_tl.value_id AND lov_biz_type_tl.language = 'zh_CN'
        -- 费用类型关联 (值列表)
        LEFT JOIN fnd_lov lov_exp_type_def
        ON lov_exp_type_def.lov_name = 'YW01'
        LEFT JOIN fnd_lov_value lov_exp_type
        ON h.column17 = lov_exp_type.value_code AND lov_exp_type.lov_id = lov_exp_type_def.lov_id
        LEFT JOIN fnd_lov_value_tl lov_exp_type_tl
        ON lov_exp_type.value_id = lov_exp_type_tl.value_id AND lov_exp_type_tl.language = 'zh_CN'
        WHERE h.status != 'deleted'
        AND ht.type_code IN (
        'FSAA-差旅申请', 'YXCL01', 'FMAA-差旅申请-准入', 'ZRCL01', 'FSAB-差旅报销', 'YXCL02',
        'FMAB-差旅报销-准入', 'ZRCL02',
        'FSBA-费用申请', 'YXFY01', 'FMBA-费用申请-准入', 'ZRFY01', 'FSGA-京东采购申请', 'JDHC01',
        'FSBB-费用报销', 'YXFY02',
        'FMBB-费用报销-准入', 'ZRFY02', 'FSCB-会议申请（CRM发起）', 'YXHYCRM',
        'FMCA-会议申请-准入（CRM发起）', 'ZRHYCRM',
        'FSCD-会议核报（含对公）', 'YXHY02', 'FMCC-会议核报-准入（含对公）', 'ZRHY02', 'FSFA-终端准入申请',
        'YXZR01',
        'FSFB-终端准入核报', 'YXZR02', 'FSHB-对公预付（含会议）', 'YXDG01', 'FSHC-对公应付（除会议外）',
        'YXDG02'
        )


        <if test="query.hasInvoice != null and query.hasInvoice == 1">
            AND h.journal_num IS NOT NULL
        </if>
        <if test="query.hasInvoice != null and query.hasInvoice == 2">
            AND h.journal_num IS NULL
        </if>

        ORDER BY `序号` ASC
    </select>
</mapper>

