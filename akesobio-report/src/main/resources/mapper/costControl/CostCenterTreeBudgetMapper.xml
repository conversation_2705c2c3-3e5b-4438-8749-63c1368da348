<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.CostCenterTreeBudgetMapper">

    <select id="list" resultType="com.akesobio.report.costControl.domain.CostCenterTreeBudgetDTO">
        WITH RECURSIVE department_tree AS (
            -- ==================== 递归查询起始部分 ====================
            SELECT 
                d.department_id,
                d.company_id,
                d.supervisor_id,
                p.department_code as parent_department_code, -- 父级部门编码
                d.department_code,
                d.type,
                d.natural_level,
                d.column2 as region_code,
                dt.department_name,
                
                -- 层级和路径信息
                1 as tree_level,
                CAST(d.department_code AS CHAR(1000)) as department_path,
                CAST(dt.department_name AS CHAR(1000)) as department_name_path,
                CAST(d.department_id AS CHAR(2000)) as id_path
                
            FROM fnd_department d
            LEFT JOIN fnd_department_tl dt ON d.department_id = dt.department_id 
                AND dt.language = 'zh_CN'
            LEFT JOIN fnd_department p ON d.supervisor_id = p.department_id -- 关联父级部门
            WHERE d.enabled_flag = 'Y'
                AND d.department_code = '202' -- 从商业运营部开始
            
            UNION ALL
            
            -- ==================== 递归查询递归部分 ====================
            SELECT 
                d.department_id,
                d.company_id,
                d.supervisor_id,
                p.department_code as parent_department_code, -- 父级部门编码
                d.department_code,
                d.type,
                d.natural_level,
                d.column2 as region_code,
                dt.department_name,
                
                -- 层级和路径信息（递增）
                dt_parent.tree_level + 1 as tree_level,
                CONCAT(dt_parent.department_path, '/', d.department_code) as department_path,
                CONCAT(dt_parent.department_name_path, ' / ', dt.department_name) as department_name_path,
                CONCAT(dt_parent.id_path, '/', d.department_id) as id_path
                
            FROM fnd_department d
            INNER JOIN department_tree dt_parent ON d.supervisor_id = dt_parent.department_id
            LEFT JOIN fnd_department_tl dt ON d.department_id = dt.department_id 
                AND dt.language = 'zh_CN'
            LEFT JOIN fnd_department p ON d.supervisor_id = p.department_id -- 关联父级部门
            WHERE d.enabled_flag = 'Y'
        ),

        -- ==================== 预算数据汇总（无条件限制）====================
        budget_data AS (
            SELECT 
                dt.department_id,
                dt.company_id,
                dt.department_code,
                dt.parent_department_code,
                dt.department_name,
                dt.tree_level,
                dt.department_path,
                dt.department_name_path,
                dt.region_code,
                dt.id_path,
                
                -- 汇总所有期间的预算数据
                COALESCE(SUM(gba.budget_amount), 0) as direct_budget_amount,
                
                -- 判断是否为叶子节点
                CASE 
                    WHEN EXISTS (
                        SELECT 1 FROM department_tree dt_child 
                        WHERE dt_child.supervisor_id = dt.department_id
                    ) THEN 'N'
                    ELSE 'Y'
                END as is_leaf_node
                
            FROM department_tree dt
            LEFT JOIN gl_budget_combination gbc ON dt.department_id = gbc.department_id
            LEFT JOIN gl_budget_amount gba ON gbc.cid = gba.cid
            <where>
                <if test="query.companyId != null">
                    AND dt.company_id = #{query.companyId}
                </if>
                <if test="query.departmentCode != null and query.departmentCode != ''">
                    AND dt.department_code LIKE CONCAT('%', #{query.departmentCode}, '%')
                </if>
                <if test="query.departmentName != null and query.departmentName != ''">
                    AND dt.department_name LIKE CONCAT('%', #{query.departmentName}, '%')
                </if>
                <if test="query.regionCode != null and query.regionCode != ''">
                    AND dt.region_code = #{query.regionCode}
                </if>
                <if test="query.periodId != null and query.periodId != ''">
                    AND gba.period_id = #{query.periodId}
                </if>
            </where>
            GROUP BY dt.department_id, dt.company_id, dt.department_code, dt.parent_department_code, dt.department_name,
                     dt.tree_level, dt.department_path, dt.department_name_path, 
                     dt.region_code, dt.id_path
        ),

        -- ==================== 计算子部门预算汇总 ====================
        budget_summary AS (
            SELECT 
                bd.*,
                -- 计算子部门预算汇总
                COALESCE(
                    (SELECT SUM(bd2.direct_budget_amount)
                     FROM budget_data bd2 
                     WHERE bd2.id_path LIKE CONCAT(bd.id_path, '/%')
                    ), 0
                ) as child_budget_amount
            FROM budget_data bd
        )

        -- ==================== 最终查询结果 ====================
        SELECT 
            bs.department_code AS departmentCode,
            bs.parent_department_code AS parentDepartmentCode,
            bs.department_name AS departmentName,
            CONCAT(REPEAT('    ', bs.tree_level - 1), bs.department_name) as departmentLevelName,
            bs.tree_level AS treeLevel,
            bs.department_path AS departmentPath,
            bs.department_name_path AS departmentNamePath,
            bs.is_leaf_node AS isLeafNode,
            COALESCE(region_tl.value_meaning, bs.region_code) as regionName,
            bs.direct_budget_amount AS directBudgetAmount,
            bs.child_budget_amount AS childBudgetAmount

        FROM budget_summary bs
        -- 关联部门所属片区信息
        LEFT JOIN fnd_lov lov ON lov.lov_name = 'CBZXDX'
        LEFT JOIN fnd_lov_value lv ON lv.lov_id = lov.lov_id AND lv.value_code = bs.region_code
        LEFT JOIN fnd_lov_value_tl region_tl ON region_tl.value_id = lv.value_id 
            AND region_tl.language = 'zh_CN'
        
        <where>
            <if test="query.leafNodeOnly != null and query.leafNodeOnly == true">
                AND bs.is_leaf_node = 'Y'
            </if>
        </where>

        ORDER BY 
            bs.department_path
    </select>

    <select id="listCount" resultType="java.lang.Integer">
        WITH RECURSIVE department_tree AS (
            -- ==================== 递归查询起始部分 ====================
            SELECT 
                d.department_id,
                d.company_id,
                d.supervisor_id,
                p.department_code as parent_department_code,
                d.department_code,
                d.type,
                d.natural_level,
                d.column2 as region_code,
                dt.department_name,
                
                1 as tree_level,
                CAST(d.department_code AS CHAR(1000)) as department_path,
                CAST(dt.department_name AS CHAR(1000)) as department_name_path,
                CAST(d.department_id AS CHAR(2000)) as id_path
                
            FROM fnd_department d
            LEFT JOIN fnd_department_tl dt ON d.department_id = dt.department_id 
                AND dt.language = 'zh_CN'
            LEFT JOIN fnd_department p ON d.supervisor_id = p.department_id
            WHERE d.enabled_flag = 'Y'
                AND d.department_code = '202'
            
            UNION ALL
            
            -- ==================== 递归查询递归部分 ====================
            SELECT 
                d.department_id,
                d.company_id,
                d.supervisor_id,
                p.department_code as parent_department_code,
                d.department_code,
                d.type,
                d.natural_level,
                d.column2 as region_code,
                dt.department_name,
                
                dt_parent.tree_level + 1 as tree_level,
                CONCAT(dt_parent.department_path, '/', d.department_code) as department_path,
                CONCAT(dt_parent.department_name_path, ' / ', dt.department_name) as department_name_path,
                CONCAT(dt_parent.id_path, '/', d.department_id) as id_path
                
            FROM fnd_department d
            INNER JOIN department_tree dt_parent ON d.supervisor_id = dt_parent.department_id
            LEFT JOIN fnd_department_tl dt ON d.department_id = dt.department_id 
                AND dt.language = 'zh_CN'
            LEFT JOIN fnd_department p ON d.supervisor_id = p.department_id
            WHERE d.enabled_flag = 'Y'
        ),

        budget_data AS (
            SELECT 
                dt.department_id,
                dt.company_id,
                dt.department_code,
                dt.parent_department_code,
                dt.department_name,
                dt.tree_level,
                dt.department_path,
                dt.department_name_path,
                dt.region_code,
                dt.id_path,
                
                COALESCE(SUM(gba.budget_amount), 0) as direct_budget_amount,
                
                CASE 
                    WHEN EXISTS (
                        SELECT 1 FROM department_tree dt_child 
                        WHERE dt_child.supervisor_id = dt.department_id
                    ) THEN 'N'
                    ELSE 'Y'
                END as is_leaf_node
                
            FROM department_tree dt
            LEFT JOIN gl_budget_combination gbc ON dt.department_id = gbc.department_id
            LEFT JOIN gl_budget_amount gba ON gbc.cid = gba.cid
            <where>
                <if test="query.companyId != null">
                    AND dt.company_id = #{query.companyId}
                </if>
                <if test="query.departmentCode != null and query.departmentCode != ''">
                    AND dt.department_code LIKE CONCAT('%', #{query.departmentCode}, '%')
                </if>
                <if test="query.departmentName != null and query.departmentName != ''">
                    AND dt.department_name LIKE CONCAT('%', #{query.departmentName}, '%')
                </if>
                <if test="query.regionCode != null and query.regionCode != ''">
                    AND dt.region_code = #{query.regionCode}
                </if>
                <if test="query.periodId != null and query.periodId != ''">
                    AND gba.period_id = #{query.periodId}
                </if>
            </where>
            GROUP BY dt.department_id, dt.company_id, dt.department_code, dt.parent_department_code, dt.department_name,
                     dt.tree_level, dt.department_path, dt.department_name_path, 
                     dt.region_code, dt.id_path
        )

        SELECT COUNT(1)
        FROM budget_data bs
        <where>
            <if test="query.leafNodeOnly != null and query.leafNodeOnly == true">
                AND bs.is_leaf_node = 'Y'
            </if>
        </where>
    </select>

</mapper>