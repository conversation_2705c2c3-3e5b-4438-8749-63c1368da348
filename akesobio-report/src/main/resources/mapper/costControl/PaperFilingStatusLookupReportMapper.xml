<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.PaperFilingStatusLookupReportMapper">

    <select id="list" parameterType="com.akesobio.report.costControl.mapper.query.PaperFilingStatusLookupReportQuery"
            resultType="com.akesobio.report.costControl.domain.PaperFilingStatusLookupReportDTO">
        -- 4、纸质归档情况查询报表
        -- 报表名称：纸质归档情况查询报表
        -- 核心调整逻辑：
        -- 1. SAP凭证号：从单据头的 column39 调整为更稳定、标准的 journal_num 字段。
        -- 2. 收单相关信息：通过关联纸质单投递表 (dlv_delivery) 获取更准确的收单人和收单状态。
        -- 3. 大区/地区/片区：严格按照知识库中描述的部门层级关系及自定义字段进行关联和值列表转换，不再直接取单据头的自定义字段。
        -- 4. 票据类型：通过关联到更底层的消费记录表 (exp_expense) 来获取，确保数据的原始性和准确性。
        -- 5. 连接方式：关键业务表（如纸质单、消费记录）均采用 LEFT JOIN，确保主表（单据头）数据的完整性，避免因部分信息缺失导致整行数据丢失。
        SELECT ehttl.type                                                    AS "documentType",-- 单据类型
               ech.document_num                                              AS "documentNumber",-- 单号
               u.full_name                                                   AS "applicant",-- 申请人
               DATE_FORMAT(ech.submit_date, '%Y-%m-%d')                      AS "applicationDate",-- 申请日期
               u.employee_number                                             AS "applicantEmployeeNumber",-- 申请人工号
               ech.submit_department                                         AS "submitDepartmentId",-- 申请人部门
#                dept1_tl.department_name                                      AS "department",-- 申请人部门
#                -- 调整后：通过部门层级关联获取大区--
#                dept3_tl.department_name                                      AS "region",-- 大区
#                -- 调整后：通过部门层级关联获取地区--
#                dept2_tl.department_name                                      AS "area",-- 地区
#                -- 调整后：根据部门自定义字段column2，通过值列表(CBZXDX)转换获得片区--
#                lov_pq_tl.value_meaning                                       AS "district",-- 片区
               ech.total_claim_amount                                        AS "reimbursementAmount",-- 报销金额
               -- 调整后：直接使用更准确的 ech.journal_num--
               ech.journal_num                                               AS "sapVoucherNumber",-- SAP凭证号
               -- 调整后：收单人信息从纸质单投递表获取，更准确--
               collector.full_name                                           AS "financialReceiptPerson",-- 财务收单人
               -- 调整后：收单状态从纸质单投递表获取，并通过值列表转换--
               lov_ds_tl.value_meaning                                       AS "receiptStatus",-- 收单情况
               -- 调整后：通过关联到消费记录表获取票据类型，并通过值列表转换--
               GROUP_CONCAT(DISTINCT lov_it_tl.value_meaning SEPARATOR ', ') AS "billType",-- 票据类型（若有）
               CASE ech.delivery_status
                   WHEN 'received' AND ech.column_json ->> '$.column175'='Y'  THEN  '已收单'
                   WHEN 'pending' THEN '未收单'
                   ELSE '未收单'

                END AS "billed"-- 纸质单投递状态

#                CASE ech.column_json ->> '$.column100'
#                    WHEN dd.delivery_status = 'received' THEN '是'
#                    ELSE '否'
#                    END                                                       AS "billed"-- 已收单
#                CASE
#                    WHEN dd.delivery_status != 'received'
#                        OR dd.delivery_status IS NULL THEN '是'
#                    ELSE '否'
#                    END                                                       AS "unbilled" -- 未收单
        FROM exp_claim_header ech
                 -- 申请人信息
                 LEFT JOIN fnd_user u ON ech.submit_user = u.user_id
            -- 部门层级结构 (用于获取地区、大区、片区)
                 LEFT JOIN fnd_department dept1 ON ech.submit_department = dept1.department_id
                 LEFT JOIN fnd_department_tl dept1_tl
                           ON dept1.department_id = dept1_tl.department_id AND dept1_tl.language = 'zh_CN'
                 LEFT JOIN fnd_department dept2 ON dept1.supervisor_id = dept2.department_id
                 LEFT JOIN fnd_department_tl dept2_tl
                           ON dept2.department_id = dept2_tl.department_id AND dept2_tl.language = 'zh_CN'
                 LEFT JOIN fnd_department dept3 ON dept2.supervisor_id = dept3.department_id
                 LEFT JOIN fnd_department_tl dept3_tl
                           ON dept3.department_id = dept3_tl.department_id AND dept3_tl.language = 'zh_CN'
            -- 单据类型
                 LEFT JOIN exp_header_type eht ON ech.header_type_id = eht.type_id
                 LEFT JOIN exp_header_type_tl ehttl ON eht.type_id = ehttl.type_id AND ehttl.language = 'zh_CN'
            -- 纸质单及收单情况 (通过delivery_num关联)
                 LEFT JOIN dlv_delivery dd ON ech.delivery_num = dd.delivery_num
            -- 财务收单人
                 LEFT JOIN fnd_user collector ON dd.reviewed_by = collector.user_id
            -- 票据类型 (通过费用行和消费记录关联)
                 LEFT JOIN exp_claim_line ecl ON ech.header_id = ecl.header_id
                 LEFT JOIN exp_expense ee ON ecl.expense_id = ee.expense_id
            -- 值列表关联: 片区 (LOV: CBZXDX)
                 LEFT JOIN fnd_lov lov_pq ON lov_pq.lov_name = 'CBZXDX'
                 LEFT JOIN fnd_lov_value lov_pq_val
                           ON lov_pq.lov_id = lov_pq_val.lov_id AND dept1.column2 = lov_pq_val.value_code
                 LEFT JOIN fnd_lov_value_tl lov_pq_tl
                           ON lov_pq_val.value_id = lov_pq_tl.value_id AND lov_pq_tl.language = 'zh_CN'
            -- 值列表关联: 收单情况 (LOV: Delivery Status)
                 LEFT JOIN fnd_lov lov_ds ON lov_ds.lov_name = 'Delivery Status'
                 LEFT JOIN fnd_lov_value lov_ds_val
                           ON dd.delivery_status = lov_ds_val.value_code AND lov_ds.lov_id = lov_ds_val.lov_id
                 LEFT JOIN fnd_lov_value_tl lov_ds_tl
                           ON lov_ds_val.value_id = lov_ds_tl.value_id AND lov_ds_tl.language = 'zh_CN'
            -- 值列表关联: 票据类型 (LOV: Invoice Type)
                 LEFT JOIN fnd_lov lov_it ON lov_it.lov_name = 'Invoice Type'
                 LEFT JOIN fnd_lov_value lov_it_val
                           ON ee.invoice_type = lov_it_val.value_code AND lov_it.lov_id = lov_it_val.lov_id
                 LEFT JOIN fnd_lov_value_tl lov_it_tl
                           ON lov_it_val.value_id = lov_it_tl.value_id AND lov_it_tl.language = 'zh_CN'
        WHERE eht.type_code IN (
                                'YXCL02', -- FSAB-差旅报销
                                'YXFY02', -- FSBB-费用报销
                                'YXZR02', -- FSFB-终端准入核报
                                'YXHY02', -- FSCD-会议核报（含对公）
                                'ZRCL02', -- FMAB-差旅报销-准入
                                'ZRFY02', -- FMBB-费用报销-准入
                                'ZRHY02' -- FMCC-会议核报-准入（含对公）
            )
        and ech.status != 'deleted'
#         where 所属部门、申请人、单号、SAP凭证号、财务收单人、收单情况
        GROUP BY ech.header_id,
                 ehttl.type,
                 ech.document_num,
                 u.full_name,
                 ech.submit_date,
                 u.employee_number,
                 dept1_tl.department_name,
                 dept2_tl.department_name,
                 dept3_tl.department_name,
                 lov_pq_tl.value_meaning,
                 ech.total_claim_amount,
                 ech.journal_num,
                 collector.full_name,
                 lov_ds_tl.value_meaning,
                 ech.delivery_status,
                 dd.delivery_status
        ORDER BY collector.full_name desc
    </select>
</mapper>

