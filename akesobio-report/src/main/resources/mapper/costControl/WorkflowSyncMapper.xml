<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.WorkflowSyncMapper">
    
    <resultMap type="WorkflowSyncRecord" id="WorkflowSyncRecordResult">
        <result property="formId"        column="form_id"        />
        <result property="syncStatus"    column="sync_status"    />
        <result property="syncTime"      column="sync_time"      />
        <result property="syncResult"    column="sync_result"    />
        <result property="errorMessage"  column="error_message"  />
        <result property="retryCount"    column="retry_count"    />
        <result property="formType"      column="form_type"      />
        <result property="businessData"  column="business_data"  />
        <result property="createBy"      column="create_by"      />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"      column="update_by"      />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"        column="remark"         />
    </resultMap>
    
    <sql id="selectWorkflowSyncRecordVo">
        select form_id, sync_status, sync_time, sync_result, error_message, retry_count, 
               form_type, business_data, create_by, create_time, update_by, update_time, remark
        from workflow_sync_record
    </sql>
    
    <select id="selectWorkflowSyncRecord" parameterType="String" resultMap="WorkflowSyncRecordResult">
        <include refid="selectWorkflowSyncRecordVo"/>
        where form_id = #{formId}
    </select>
    
    <select id="selectWorkflowSyncRecordList" parameterType="WorkflowSyncRecord" resultMap="WorkflowSyncRecordResult">
        <include refid="selectWorkflowSyncRecordVo"/>
        <where>
            <if test="formId != null and formId != ''">
                AND form_id = #{formId}
            </if>
            <if test="syncStatus != null and syncStatus != ''">
                AND sync_status = #{syncStatus}
            </if>
            <if test="formType != null and formType != ''">
                AND form_type = #{formType}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND sync_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND sync_time &lt;= #{params.endTime}
            </if>
        </where>
        ORDER BY sync_time DESC
    </select>
    
    <insert id="insertWorkflowSyncRecord" parameterType="WorkflowSyncRecord">
        insert into workflow_sync_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formId != null and formId != ''">form_id,</if>
            <if test="syncStatus != null">sync_status,</if>
            <if test="syncTime != null">sync_time,</if>
            <if test="syncResult != null">sync_result,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="formType != null">form_type,</if>
            <if test="businessData != null">business_data,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formId != null and formId != ''">#{formId},</if>
            <if test="syncStatus != null">#{syncStatus},</if>
            <if test="syncTime != null">#{syncTime},</if>
            <if test="syncResult != null">#{syncResult},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="formType != null">#{formType},</if>
            <if test="businessData != null">#{businessData},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
    
    <update id="updateWorkflowSyncRecord" parameterType="WorkflowSyncRecord">
        update workflow_sync_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="syncStatus != null">sync_status = #{syncStatus},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
            <if test="syncResult != null">sync_result = #{syncResult},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="formType != null">form_type = #{formType},</if>
            <if test="businessData != null">business_data = #{businessData},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where form_id = #{formId}
    </update>
    
    <delete id="deleteWorkflowSyncRecord" parameterType="String">
        delete from workflow_sync_record where form_id = #{formId}
    </delete>
    
    <delete id="deleteWorkflowSyncRecordByIds" parameterType="String">
        delete from workflow_sync_record where form_id in
        <foreach item="formId" collection="array" open="(" separator="," close=")">
            #{formId}
        </foreach>
    </delete>
    
    <select id="selectTerminalNodeInfo" parameterType="String" resultType="String">
        select node_code from terminal_node_info where node_code = #{nodeCode}
    </select>
    
</mapper>