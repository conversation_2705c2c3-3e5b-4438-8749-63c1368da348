<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.IITProjectApprovalReportMapper">

    <select id="list" parameterType="com.akesobio.report.costControl.mapper.query.IITProjectApprovalReportQuery"
            resultType="com.akesobio.report.costControl.domain.IITProjectApprovalReportDTO">
        -- 24、IIT立项报表
        SELECT ech.document_num                         AS "documentNumber",                -- 单号（OA/云简）
               DATE_FORMAT(ech.submit_date, '%Y-%m-%d') AS "applicationDate",               -- 申请日期
               fu.full_name                             AS "applicant",                     -- 申请人
               ech.column30                             AS "position",                      -- 职级
               fd3_tl.department_name                   AS "department3",                   -- 所属部门（末三级）
               fd2_tl.department_name                   AS "department2",                   -- 所属部门（末二级）
               fd1_tl.department_name                   AS "department",                    -- 所属部门（末一级）
               CASE ech.status
                   WHEN 'incomplete' THEN '保存未提交'
                   WHEN 'submitted' THEN '已提交'
                   WHEN 'deleted' THEN '删除'
                   WHEN 'cancelled' THEN '取消'
                   WHEN 'withdrawed' THEN '撤回'
                   WHEN 'preapproved' THEN '业务审批通过'
                   WHEN 'approved' THEN '审批通过'
                   WHEN 'closed' THEN '已关闭/已支付'
                   WHEN 'rejected' THEN '已拒绝'
                   WHEN 'checked' THEN '已审批'
                   ELSE ech.status
                   END                                  AS "approvalStatus",                -- 审批状态
               iit_class_tl.value_meaning               AS "iitClassification",             -- IIT分类
               ech.column46                             AS "projectNumber",                 -- 项目号
               ech.column_json ->> '$.column158'        AS "projectName",                   -- 项目名称
               ech.column31                             AS "researchCenter",                -- 研究中心及其地址（OA）/申办方 （云简）
               cost_center_dept.department_code         AS "department4",                   -- 成本中心代码todo
               ech.column_json ->> '$.column186'        AS "estimatedNumberOfCases",        -- 预估入组病例数
               ech.column_json ->> '$.column171'        AS "mainResearcher",                -- 主要研究者
               ech.column35                             AS "fundingScheme",                 -- 资助方案
               ech.column36                             AS "budgetForExpectedExpenses",     -- 预计费用预算
               ech.column37                             AS "budgetForExpectedDrugQuantity", -- 预计赠药数量
               et_tl.type                               AS "costType",                      -- 费用类型
               ecl.claim_amount                         AS "amount",                        -- 金额
               -- 【已更新】根据您的指示， 从JSON字段中获取药品规格
               ecl.column_json ->> '$.column100'        AS "drugSpecification",             -- 药品规格
               ecl.quantity                             AS "drugQuantity"                   -- 药品数量
        FROM exp_claim_header AS ech
                 LEFT JOIN exp_claim_line AS ecl ON ech.header_id = ecl.header_id
                 LEFT JOIN fnd_user AS fu ON ech.submit_user = fu.user_id
                 LEFT JOIN fnd_department AS fd1 ON ech.submit_department = fd1.department_id
                 LEFT JOIN fnd_department_tl AS fd1_tl
                           ON fd1.department_id = fd1_tl.department_id AND fd1_tl.language = 'zh_CN'
                 LEFT JOIN fnd_department AS fd2 ON fd1.supervisor_id = fd2.department_id
                 LEFT JOIN fnd_department_tl AS fd2_tl
                           ON fd2.department_id = fd2_tl.department_id AND fd2_tl.language = 'zh_CN'
                 LEFT JOIN fnd_department AS fd3 ON fd2.supervisor_id = fd3.department_id
                 LEFT JOIN fnd_department_tl AS fd3_tl
                           ON fd3.department_id = fd3_tl.department_id AND fd3_tl.language = 'zh_CN'
                 LEFT JOIN fnd_department AS cost_center_dept ON ecl.cost_center_id = cost_center_dept.department_id
                 LEFT JOIN fnd_lov AS iit_class_lov ON iit_class_lov.lov_name = 'categories'
                 LEFT JOIN fnd_lov_value AS iit_class_v
                           ON iit_class_lov.lov_id = iit_class_v.lov_id AND ech.column15 = iit_class_v.value_code
                 LEFT JOIN fnd_lov_value_tl AS iit_class_tl
                           ON iit_class_v.value_id = iit_class_tl.value_id AND iit_class_tl.language = 'zh_CN'
                 LEFT JOIN exp_type AS et ON ecl.type_id = et.type_id
                 LEFT JOIN exp_type_tl AS et_tl ON et.type_id = et_tl.type_id AND et_tl.language = 'zh_CN'
        WHERE ech.header_type_id =
              (SELECT type_id FROM exp_header_type WHERE type_code = 'YXIIT01' AND company_id = ech.company_id)
          AND ech.status != 'deleted'
        AND ech.column46 like '%IIT%'
        ORDER BY ech.submit_date DESC
    </select>
</mapper>

