<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.EkpAttFileMapper">
    <select id="selectEkpAttFile" parameterType="com.akesobio.report.costControl.domain.EkpAttFile" resultType="com.akesobio.report.costControl.domain.EkpAttFile">
        SELECT sys_att_file.fd_id           AS fd_file_id,
               sys_att_main.fd_id           AS fd_id,
               sys_att_file.fd_md5          AS fd_md5,
               sys_att_file.fd_file_size    AS fd_file_size,
               sys_att_file.fd_status       AS fd_status,
               sys_att_file.doc_create_time AS doc_create_time,
               sys_att_main.fd_creator_id   AS fd_creator_id,
               sys_org_person.fd_login_name AS fd_jobnum,
               sys_att_main.fd_uploader_id  AS fd_uploader_id,
               sys_att_main.fd_order        AS fd_order,
               sys_att_file.fd_file_path    AS fd_file_path,
               sys_att_main.fd_file_name    AS fd_file_name
        FROM sys_att_file
             LEFT JOIN sys_att_main ON sys_att_file.fd_id = sys_att_main.fd_file_id
             LEFT JOIN sys_org_person ON sys_org_person.fd_id = sys_att_main.fd_creator_id
        <where>
            sys_att_main.fd_model_name = 'com.landray.kmss.sys.modeling.main.model.ModelingAppModelMain'
            <if test="fdKey!= null and fdKey!= null">
                AND sys_att_main.fd_key = #{fdKey}
            </if>
            <if test="fdModelId!= null and fdModelId!= null">
                AND sys_att_main.fd_model_id = #{fdModelId}
            </if>
        </where>

    </select>
</mapper>