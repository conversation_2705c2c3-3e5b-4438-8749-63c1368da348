<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.ValidNodeMapper">

    <select id="list" resultType="com.akesobio.report.costControl.domain.ValidNodeDTO">
        -- 场景一：查询分配到岗位但岗位下无有效审批人的节点
        SELECT
        h.document_num AS "documentNum",
        p.path_id AS "approvalId",
        step_lov.value_meaning AS "approvalStage",
        NULL AS "approval",
        '岗位下无有效审批人' AS "description"
        FROM
        fnd_workflow_path p
        JOIN
        exp_claim_header h ON p.source_id = h.header_id AND p.workflow_type = 'EXP'
        LEFT JOIN
        fnd_lov lov ON lov.lov_name = 'Workflow Step'
        LEFT JOIN
        fnd_lov_value lov_val ON lov.lov_id = lov_val.lov_id AND lov_val.value_code = p.workflow_step
        LEFT JOIN
        fnd_lov_value_tl step_lov ON lov_val.value_id = step_lov.value_id AND step_lov.language = 'zh_CN'
        WHERE
        p.status = 'approving'  -- 筛选正在审批中的节点
        AND p.position_id IS NOT NULL
        AND p.user_id IS NULL
        AND NOT EXISTS (
        SELECT 1
        FROM fnd_user_position up
        JOIN fnd_user u ON up.user_id = u.user_id
        WHERE up.position_id = p.position_id
        AND (u.inactive_date IS NULL OR u.inactive_date > CURDATE()) -- 确保岗位下的员工是有效的
        )

        UNION ALL

        -- 场景二：查询分配到人但该人员已失效的节点
        SELECT
        h.document_num AS "documentNum",
        p.path_id AS "approvalId",
        step_lov.value_meaning AS "approvalStage",
        u.full_name AS "approval",
        '指定审批人已离职或失效' AS "description"
        FROM
        fnd_workflow_path p
        JOIN
        exp_claim_header h ON p.source_id = h.header_id AND p.workflow_type = 'EXP'
        JOIN
        fnd_user u ON p.user_id = u.user_id
        LEFT JOIN
        fnd_lov lov ON lov.lov_name = 'Workflow Step'
        LEFT JOIN
        fnd_lov_value lov_val ON lov.lov_id = lov_val.lov_id AND lov_val.value_code = p.workflow_step
        LEFT JOIN
        fnd_lov_value_tl step_lov ON lov_val.value_id = step_lov.value_id AND step_lov.language = 'zh_CN'
        WHERE
        p.status = 'approving' -- 筛选正在审批中的节点
        AND p.user_id IS NOT NULL
        AND u.inactive_date IS NOT NULL
        AND u.inactive_date &lt; CURDATE(); -- 判断员工是否已失效
    </select>
</mapper>

