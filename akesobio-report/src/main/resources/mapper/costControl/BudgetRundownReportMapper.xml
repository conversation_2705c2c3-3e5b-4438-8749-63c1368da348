<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.BudgetRundownReportMapper">

    <select id="list" parameterType="com.akesobio.report.costControl.mapper.query.BudgetRundownReportQuery"
            resultType="com.akesobio.report.costControl.domain.BudgetRundownReportDTO">
        -- 25、预算整体执行情况报表
        -- 该查询根据最终需求进行重构，旨在生成一个全局的、包含所有公司和期间的预算变动流水报表。
        -- 报表模型为“历史流水 + 实时快照”。
        --
        -- 步骤1: 使用CTE定义影响预算基数的历史事件流水
        WITH BudgetTimeline AS (
        -- 初始预算
        SELECT gba.company_id,
        gba.cid,
        gba.gl_period,
        '期初' AS document_num,
        p.start_date AS transaction_date,
        '初始预算' AS transaction_type,
        gba.budget_amount AS adjusted_change
        FROM gl_budget_amount AS gba
        JOIN gl_period AS p ON gba.gl_period = p.period_id AND gba.company_id = p.company_id
        <where>
            AND gba.gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>

        UNION ALL

        -- 预算调整(已生效)
        SELECT h.company_id,
        lcba.cid,
        lcba.gl_period,
        h.document_num,
        h.creation_date,
        '预算调整',
        lcba.adjustment_amount
        FROM exp_claim_line_budget_adjustment AS lcba
        JOIN exp_claim_header AS h ON lcba.header_id = h.header_id AND lcba.company_id = h.company_id
        <where>
            h.status = 'APPROVED'
            AND lcba.adjustment_status = 2
            AND lcba.gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        # WHERE h.status = 'APPROVED'
        # AND lcba.adjustment_status = 2
        UNION ALL

        -- 预算调拨(已生效)
        SELECT company_id, cid, gl_period, document_num, creation_date, transaction_type, adjustment_amount
        FROM (SELECT h.company_id,
        cid_in AS cid,
        gl_period_in AS gl_period,
        h.document_num,
        h.creation_date,
        '预算调拨（调入）' AS transaction_type,
        adjustment_amount
        FROM exp_claim_line_budget_transfer t
        JOIN exp_claim_header h ON t.header_id = h.header_id AND t.company_id = h.company_id
        <where>
            h.status = 'APPROVED'
            AND t.adjustment_status = 2
            AND gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        UNION ALL
        SELECT h.company_id,
        cid_out AS cid,
        gl_period_out AS gl_period,
        h.document_num,
        h.creation_date,
        '预算调拨（调出）' AS transaction_type,
        -adjustment_amount
        FROM exp_claim_line_budget_transfer t
        JOIN exp_claim_header h ON t.header_id = h.header_id AND t.company_id = h.company_id
        <where>
            h.status = 'APPROVED'
            AND t.adjustment_status = 2
            AND gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        ) AS transfers),

        -- 步骤2: 使用CTE计算每个预算维度当前的占用、消耗、冻结总额（实时快照）
        CurrentStatusSnapshot AS (
        -- 计算占用和消耗
        SELECT company_id,
        cid,
        gl_period,
        SUM(IF(budget_status = 'request', budget_amount, 0)) AS total_occupied,
        SUM(IF(budget_status = 'expense', budget_amount, 0)) AS total_consumed
        FROM gl_budget_transaction
        <where>
            active = 1
            AND gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY company_id, cid, gl_period),
        CurrentFrozenSnapshot AS (
        -- 计算冻结
        SELECT t.company_id,
        t.cid_out AS cid,
        t.gl_period_out AS gl_period,
        SUM(t.adjustment_amount) AS total_frozen
        FROM exp_claim_line_budget_transfer t
        JOIN exp_claim_header h ON t.header_id = h.header_id AND t.company_id = h.company_id
        WHERE h.status NOT IN ('APPROVED', 'REJECTED', 'WITHDRAWN', 'CLOSED') -- 审批未结束
        GROUP BY t.company_id, t.cid_out, t.gl_period_out)

        -- 步骤3: 最终查询，将历史流水与实时快照结合
        SELECT c.company_name AS '公司名称',
        p.period_name AS 'periodName', -- 预算期间
        fdt.department_name AS 'budgetDepartment', -- 预算部门（末一级）末级部门
        fd.department_code AS 'budgetDepartmentCode', -- 预算部门编码
        bat.budget_name AS 'budgetName', -- 预算科目名称
        ba.budget_code AS 'budgetCode', -- 预算科目编码
        gbc.column1 AS 'productPipeline', -- 产品管线
        bt.transaction_date AS 'transactionDate', -- 交易日期
        bt.document_num AS 'documentNumber', -- 单据号
        bt.transaction_type AS 'transactionType', -- 交易类型

        -- 流水发生额
        bt.adjusted_change AS 'adjustmentAmount', -- 预算调整/调拨金额

        -- 滚动计算的预算总额
        SUM(bt.adjusted_change) OVER w AS 'sumAdjustmentAmount', -- 调整后预算总额

        -- 当前的快照数据
        IFNULL(cs.total_occupied, 0) AS 'occupied',-- '当前占用总额 (Request)'
        IFNULL(cs.total_consumed, 0) AS 'consumed',-- '当前消耗总额 (Expense)'
        IFNULL(cf.total_frozen, 0) AS 'frozen',-- 当前冻结总额 (调拨中)',
        --
        -- 基于滚动预算和实时快照计算的可用余额
        (SUM(bt.adjusted_change) OVER w -- 截至本行的预算总额
        - IFNULL(cs.total_occupied, 0) -- 减去当前总占用
        - IFNULL(cs.total_consumed, 0) -- 减去当前总消耗
        - IFNULL(cf.total_frozen, 0)) -- 减去当前总冻结
        AS 'budgetBalance' -- 当前可用余额
        FROM BudgetTimeline AS bt
        -- 关联维度表以获取编码和名称
        JOIN gl_budget_combination AS gbc ON bt.cid = gbc.cid AND bt.company_id = gbc.company_id
        LEFT JOIN CurrentStatusSnapshot AS cs ON bt.cid = cs.cid AND bt.gl_period = cs.gl_period
        LEFT JOIN CurrentFrozenSnapshot AS cf ON bt.cid = cf.cid AND bt.gl_period = cf.gl_period
        JOIN fnd_company AS c ON bt.company_id = c.company_id
        JOIN fnd_department AS fd ON gbc.department_id = fd.department_id
        JOIN fnd_department_tl AS fdt ON gbc.department_id = fdt.department_id AND fdt.language = 'zh_CN'
        JOIN gl_budget_account_tl AS bat ON gbc.budget_id = bat.budget_id AND bat.language = 'zh_CN' AND bat.budget_id IN
        <foreach item="item" collection="query.budgetIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        JOIN gl_budget_account AS ba ON gbc.budget_id = ba.budget_id AND ba.company_id = gbc.company_id
        JOIN gl_period AS p ON bt.gl_period = p.period_id AND gbc.company_id = p.company_id
        <where>
            <if test="query.department != null and query.department != ''">
                AND fdt.department_name LIKE CONCAT('%', #{query.department}, '%')
            </if>
            <if test="query.budgetId != null">
                AND bat.budget_id = #{query.budgetId}
            </if>
            <if test="query.productPipeline != null and query.productPipeline != ''">
                AND gbc.column1 LIKE CONCAT('%', #{query.productPipeline}, '%')
            </if>
            <if test="query.periodId!= null">
                AND EXISTS(
                SELECT 1
                FROM gl_period AS parent_period
                JOIN gl_period AS child_period ON bt.gl_period = child_period.period_id
                AND bt.company_id = child_period.company_id
                WHERE parent_period.period_id = #{query.periodId}
                AND parent_period.company_id = bt.company_id
                AND child_period.start_date BETWEEN parent_period.start_date AND parent_period.end_date
                )
            </if>
            AND bt.gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND gbc.column1 in
            <foreach item="item" collection="query.productPipelines" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        WINDOW w AS ( PARTITION BY bt.company_id, bt.cid, bt.gl_period ORDER BY bt.transaction_date, bt.document_num )

        ORDER BY bt.company_id,
        gbc.cid,
        bt.transaction_date,
        bt.document_num
        <if test="query.pageSize!= null and query.pageSize!= ''">
            LIMIT #{query.pageSize} OFFSET #{query.pageNum}
        </if>
    </select>

    <select id="list_COUNT" resultType="java.lang.Integer">
        WITH BudgetTimeline AS (
        -- 初始预算
        SELECT gba.company_id,
        gba.cid,
        gba.gl_period,
        '期初' AS document_num,
        p.start_date AS transaction_date,
        '初始预算' AS transaction_type,
        gba.budget_amount AS adjusted_change
        FROM gl_budget_amount AS gba
        JOIN gl_period AS p ON gba.gl_period = p.period_id AND gba.company_id = p.company_id
        <where>
            AND gba.gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>

        UNION ALL

        -- 预算调整(已生效)
        SELECT h.company_id,
        lcba.cid,
        lcba.gl_period,
        h.document_num,
        h.creation_date,
        '预算调整',
        lcba.adjustment_amount
        FROM exp_claim_line_budget_adjustment AS lcba
        JOIN exp_claim_header AS h ON lcba.header_id = h.header_id AND lcba.company_id = h.company_id
        <where>
            h.status = 'APPROVED'
            AND lcba.adjustment_status = 2
            AND lcba.gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        # WHERE h.status = 'APPROVED'
        # AND lcba.adjustment_status = 2
        UNION ALL

        -- 预算调拨(已生效)
        SELECT company_id, cid, gl_period, document_num, creation_date, transaction_type, adjustment_amount
        FROM (SELECT h.company_id,
        cid_in AS cid,
        gl_period_in AS gl_period,
        h.document_num,
        h.creation_date,
        '预算调拨（调入）' AS transaction_type,
        adjustment_amount
        FROM exp_claim_line_budget_transfer t
        JOIN exp_claim_header h ON t.header_id = h.header_id AND t.company_id = h.company_id
        <where>
            h.status = 'APPROVED'
            AND t.adjustment_status = 2
            AND gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        UNION ALL
        SELECT h.company_id,
        cid_out AS cid,
        gl_period_out AS gl_period,
        h.document_num,
        h.creation_date,
        '预算调拨（调出）' AS transaction_type,
        -adjustment_amount
        FROM exp_claim_line_budget_transfer t
        JOIN exp_claim_header h ON t.header_id = h.header_id AND t.company_id = h.company_id
        <where>
            h.status = 'APPROVED'
            AND t.adjustment_status = 2
            AND gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        ) AS transfers),

        -- 步骤2: 使用CTE计算每个预算维度当前的占用、消耗、冻结总额（实时快照）
        CurrentStatusSnapshot AS (
        -- 计算占用和消耗
        SELECT company_id,
        cid,
        gl_period,
        SUM(IF(budget_status = 'request', budget_amount, 0)) AS total_occupied,
        SUM(IF(budget_status = 'expense', budget_amount, 0)) AS total_consumed
        FROM gl_budget_transaction
        <where>
            active = 1
            AND gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY company_id, cid, gl_period),
        CurrentFrozenSnapshot AS (
        -- 计算冻结
        SELECT t.company_id,
        t.cid_out AS cid,
        t.gl_period_out AS gl_period,
        SUM(t.adjustment_amount) AS total_frozen
        FROM exp_claim_line_budget_transfer t
        JOIN exp_claim_header h ON t.header_id = h.header_id AND t.company_id = h.company_id
        WHERE h.status NOT IN ('APPROVED', 'REJECTED', 'WITHDRAWN', 'CLOSED') -- 审批未结束
        GROUP BY t.company_id, t.cid_out, t.gl_period_out)

        -- 步骤3: 最终查询，将历史流水与实时快照结合
        SELECT  COUNT(1)
        FROM BudgetTimeline AS bt
        JOIN gl_budget_combination AS gbc ON bt.cid = gbc.cid AND bt.company_id = gbc.company_id
        LEFT JOIN CurrentStatusSnapshot AS cs ON bt.cid = cs.cid AND bt.gl_period = cs.gl_period
        LEFT JOIN CurrentFrozenSnapshot AS cf ON bt.cid = cf.cid AND bt.gl_period = cf.gl_period
        JOIN fnd_company AS c ON bt.company_id = c.company_id
        JOIN fnd_department AS fd ON gbc.department_id = fd.department_id
        JOIN fnd_department_tl AS fdt ON gbc.department_id = fdt.department_id AND fdt.language = 'zh_CN'
        JOIN gl_budget_account_tl AS bat ON gbc.budget_id = bat.budget_id AND bat.language = 'zh_CN' AND bat.budget_id IN
        <foreach item="item" collection="query.budgetIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        JOIN gl_budget_account AS ba ON gbc.budget_id = ba.budget_id AND ba.company_id = gbc.company_id
        JOIN gl_period AS p ON bt.gl_period = p.period_id AND gbc.company_id = p.company_id
        <where>
            <if test="query.department != null and query.department != ''">
                AND fdt.department_name LIKE CONCAT('%', #{query.department}, '%')
            </if>
            <if test="query.budgetId != null">
                AND bat.budget_id = #{query.budgetId}
            </if>
            <if test="query.productPipeline != null and query.productPipeline != ''">
                AND gbc.column1 LIKE CONCAT('%', #{query.productPipeline}, '%')
            </if>
            <if test="query.periodId!= null">
                AND EXISTS(
                SELECT 1
                FROM gl_period AS parent_period
                JOIN gl_period AS child_period ON bt.gl_period = child_period.period_id
                AND bt.company_id = child_period.company_id
                WHERE parent_period.period_id = #{query.periodId}
                AND parent_period.company_id = bt.company_id
                AND child_period.start_date BETWEEN parent_period.start_date AND parent_period.end_date
                )
            </if>
            AND bt.gl_period in
            <foreach item="item" collection="query.glPeriods" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND gbc.column1 in
            <foreach item="item" collection="query.productPipelines" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        WINDOW w AS ( PARTITION BY bt.company_id, bt.cid, bt.gl_period ORDER BY bt.transaction_date, bt.document_num )

        ORDER BY bt.company_id,
        gbc.cid,
        bt.transaction_date,
        bt.document_num
    </select>
</mapper>

