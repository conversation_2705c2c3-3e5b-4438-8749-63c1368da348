<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.FndUserMapper">
    <select id="listForClaim" parameterType="com.akesobio.report.costControl.mapper.query.FndUserQuery"
            resultType="com.akesobio.report.costControl.domain.FndUserDTO">
        SELECT
        us.user_id,
        -- 申请人
        us.full_name,
        -- 申请人工号
        us.employee_number,
        -- 职级(交际费)
        lev.level AS level,

        lev.level_id
        -- 提交部门
        FROM fnd_user us
        JOIN fnd_level lev ON lev.level_id = us.level_id

        <where>
            <if test="query.userIds!=null and query.userIds.size>0">
                us.user_id IN
                <foreach collection="query.userIds" item="userId" separator="," open="(" close=")">
                    #{userId}
                </foreach>
            </if>

            <if test="query.fullName!=null and query.fullName!=''">
                AND us.full_name = #{query.fullName}
            </if>
        </where>
    </select>


    <select id="claimIdFilter" parameterType="com.akesobio.report.costControl.mapper.query.FndUserQuery"
            resultType="Integer">
        SELECT hea.header_id
        FROM fnd_user us
        LEFT JOIN exp_claim_header hea ON us.user_id = hea.submit_user
        <where>
            <if test="query.fullName!=null and query.fullName!=''">
                us.full_name LIKE CONCAT('%', #{query.fullName}, '%')
            </if>
            <if test="query.headerTypeIds!=null and query.headerTypeIds.size>0">
                AND hea.header_type_id IN
                <foreach collection="query.headerTypeIds" item="typeId" separator="," open="(" close=")">
                    #{typeId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>

