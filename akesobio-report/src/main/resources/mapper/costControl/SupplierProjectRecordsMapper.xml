<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.SupplierProjectRecordsMapper">

    <select id="list" parameterType="com.akesobio.report.costControl.mapper.query.SupplierProjectRecordsQuery"
            resultType="com.akesobio.report.costControl.domain.SupplierProjectRecordsDTO">
        -- 28、供应商项目台账
        -- 报表：从费用申请到合同及付款的全链路追踪报表 (V10 - 正确JSON字段版)
        -- 功能：使用正确的JSON字段名 `column_json` 来提取高编号的列值。

        SELECT
        -- 费用申请单信息
        req.document_num AS "applicationNo", -- 申请单号
        COALESCE(expense_type_lov.value_meaning, req.column17) AS "costType", -- 费用类型
        req.total_amount AS "applicationAmount", -- 申请金额
        u_req.full_name AS "applicant", -- 申请人
        dept1_tl.department_name AS "department", -- 申请部门

        -- 关联合同信息 --
        con.document_num AS "contractNo", -- 合同单号
        con.total_amount AS "contractAmount", -- 合同金额
        u_con.full_name AS "contractApplicant", -- 合同申请人
        sup_tl.supplier_name AS "supplier", -- 供应商

        -- 付款单详情 --
        pmt_milestone_lov.value_meaning AS "paymentType", -- 付款类型/里程碑
        pmt.submit_date AS "paymentTime", -- 付款时间
        pmt_user.full_name AS "paymentApplicant", -- 付款申请人
        pmt.document_num AS "paymentNo", -- 付款单号
        pmt.total_amount AS "paymentAmount" -- 付款金额
        FROM
        -- 1. 起点：费用申请单
        exp_claim_header req
        JOIN exp_header_type req_eht ON req.header_type_id = req_eht.type_id
        LEFT JOIN fnd_user u_req ON req.submit_user = u_req.user_id
        LEFT JOIN fnd_department dept1 ON req.submit_department = dept1.department_id
        LEFT JOIN fnd_department_tl dept1_tl ON dept1.department_id = dept1_tl.department_id AND dept1_tl.language =
        'zh_CN'
        LEFT JOIN (
        SELECT lov_val.value_code, lov_val_tl.value_meaning, lov.company_id
        FROM fnd_lov lov
        JOIN fnd_lov_value lov_val ON lov.lov_id = lov_val.lov_id
        JOIN fnd_lov_value_tl lov_val_tl ON lov_val.value_id = lov_val_tl.value_id AND lov_val_tl.language = 'zh_CN'
        WHERE lov.lov_name = 'YW01'
        ) AS expense_type_lov ON expense_type_lov.value_code = req.column17 AND (expense_type_lov.company_id =
        req.company_id OR expense_type_lov.company_id IS NULL OR expense_type_lov.company_id = 0)

        -- 2. 关联：申请单 -> 合同
        INNER JOIN exp_claim_header con ON req.header_id = con.link_header_id
        INNER JOIN exp_header_type con_eht ON con.header_type_id = con_eht.type_id
        LEFT JOIN fnd_user u_con ON con.submit_user = u_con.user_id
        LEFT JOIN exp_supplier sup ON con.supplier_id = sup.supplier_id
        LEFT JOIN exp_supplier_tl sup_tl ON sup.supplier_id = sup_tl.supplier_id AND sup_tl.language = 'zh_CN'

        -- 3. 关联：合同 -> 付款单
        LEFT JOIN exp_claim_header_link pmt_link ON con.header_id = pmt_link.link_header_id
        LEFT JOIN exp_claim_header pmt ON pmt_link.header_id = pmt.header_id
        LEFT JOIN exp_header_type pmt_eht ON pmt.header_type_id = pmt_eht.type_id
        AND pmt_eht.type_code IN ('YXDG01', 'YXDG02')
        AND pmt.status != 'deleted'
        LEFT JOIN fnd_user pmt_user ON pmt.submit_user = pmt_user.user_id

        -- 关联付款类型值列表
        LEFT JOIN (
        SELECT lov_val.value_code, lov_val_tl.value_meaning, lov.company_id
        FROM fnd_lov lov
        JOIN fnd_lov_value lov_val ON lov.lov_id = lov_val.lov_id
        JOIN fnd_lov_value_tl lov_val_tl ON lov_val.value_id = lov_val_tl.value_id AND lov_val_tl.language = 'zh_CN'
        WHERE lov.lov_name = 'PAY LCB'
        ) AS pmt_milestone_lov
        ON pmt_milestone_lov.value_code = JSON_UNQUOTE(JSON_EXTRACT(pmt.column_json, '$.column117'))
        AND (pmt_milestone_lov.company_id = pmt.company_id OR pmt_milestone_lov.company_id IS NULL OR
        pmt_milestone_lov.company_id = 0)

        <where>
            -- 主筛选条件
            req_eht.type_code = 'YXFY01'
            AND req.status != 'deleted'
            AND con_eht.type_code = 'YXHT'
            AND con.status != 'deleted'
            <if test="query.departmentName != null and query.departmentName != ''">
                AND dept1_tl.department_name = #{query.departmentName}
            </if>
            <if test="query.supplierName != null and query.supplierName != ''">
                AND sup_tl.supplier_name = #{query.supplierName}
            </if>
            <if test="query.applicant != null and query.applicant!= ''">
                AND u_req.full_name = #{query.applicant}
            </if>
            <if test="query.applicationNo!= null and query.applicationNo!= ''">
                AND req.document_num = #{query.applicationNo}
            </if>
            <if test="query.contractNo!= null and query.contractNo!= ''">
                AND con.document_num = #{query.contractNo}
            </if>
            <if test="query.actualExecutionSide!= null and query.actualExecutionSide!= ''">
                AND pmt_milestone_lov.value_meaning = #{query.actualExecutionSide}
            </if>
            <if test="query.paymentNo!= null and query.paymentNo!= ''">
                AND pmt.document_num = #{query.paymentNo}
            </if>
            <if test="query.paymentApplicant!= null and query.paymentApplicant!= ''">
                AND pmt_user.full_name = #{query.paymentApplicant}
            </if>
        </where>


    </select>
</mapper>

