<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.TerminalAndExpenseReportMapper">

    <select id="list" parameterType="com.akesobio.report.costControl.mapper.query.TerminalAndExpenseReportQuery"
            resultType="com.akesobio.report.costControl.domain.TerminalAndExpenseReportDTO">

        -- 23、终端准入申请及费用使用进度报表


        --
        -- 商业运营部与准入策略部 - 全业务单据详情总报表（最终版）
        -- 该版本根据最终的字段梳理需求，整合了单据所有维度的详细信息。
        WITH cte_first_line AS (SELECT *,
                                       ROW_NUMBER() OVER (PARTITION BY header_id ORDER BY line_id) AS rn
                                FROM exp_claim_line)
        SELECT ROW_NUMBER() OVER (ORDER BY h.submit_date DESC, h.document_num) AS 'serialNumber',        -- 序号
               h.document_num                                                  AS 'documentNumber',      -- 单据号
               u_submit.full_name                                              AS 'fullName',            -- 单据所有人姓名
               submit_user_dept_l1_tl.department_name                          AS 'department',          -- 申请人部门
               u_submit.department_id                                          AS 'departmentId',        -- 申请人部门ID
#                submit_user_dept_l2_tl.department_name                          AS 'area',                -- 所属大区
#                lov_tl_area.value_meaning                                       AS 'region',              -- 所属片区
               h.submit_date                                                   AS 'submitTime',          -- 单据提交时间
               CASE h.status
                   WHEN 'incomplete' THEN '保存未提交'
                   WHEN 'submitted' THEN '已提交'
                   WHEN 'deleted' THEN '删除'
                   WHEN 'cancelled' THEN '取消'
                   WHEN 'withdrawed' THEN '撤回'
                   WHEN 'preapproved' THEN '业务审批通过'
                   WHEN 'approved' THEN '审批通过'
                   WHEN 'closed' THEN '已关闭/已支付'
                   WHEN 'rejected' THEN '审批拒绝'
                   WHEN 'checked' THEN '已审批'
                   ELSE h.status
                   END                                                         AS 'reportDescription',   -- 单据状态（描述）

               -- 【修正】使用 ht_tl.type 获取单据类型名称
               ht_tl.type                                                      AS 'reportType',          -- 单据类型
               CASE
                   WHEN ht_tl.type LIKE '%申请%' THEN '申请'
                   ELSE '使用'
                   -- 【修正】当不匹配时，也从 ht_tl 获取默认类型名称
                   END                                                         AS 'reportClass',         -- 单据分类

               (SELECT document_num
                FROM exp_claim_header
                WHERE header_id = h.link_header_id)                            AS 'preDocumentNumber',   -- 预算冲抵前序单据

               CASE ht.type_code
                   WHEN 'YXZR01' THEN '占用'
                   WHEN 'YXZR02' THEN '消耗'
                   ELSE NULL
                   END                                                         AS 'budgetStatus',        -- 预算状态

               CASE h.status
                   WHEN 'incomplete' THEN '保存未提交'
                   WHEN 'submitted' THEN '已提交'
                   WHEN 'deleted' THEN '删除'
                   WHEN 'cancelled' THEN '取消'
                   WHEN 'withdrawed' THEN '撤回'
                   WHEN 'preapproved' THEN '业务审批通过'
                   WHEN 'approved' THEN '审批通过'
                   WHEN 'closed' THEN '已关闭/已支付'
                   WHEN 'rejected' THEN '审批拒绝'
                   WHEN 'checked' THEN '已审批'
                   ELSE h.status
                   END                                                         AS 'reportStatus',        -- 单据状态
               lov_tl_cpgx.value_meaning                                       AS 'productPipeline',     --  产品管线
               lov_tl_proj.value_meaning                                       AS 'projectName',         --  项目号
               charge_dept_tl.department_name                                  AS 'departmentName',      -- 部门/成本中心名称
               CASE h.total_claim_amount
                   WHEN NULL THEN 0
                   ELSE h.total_claim_amount
                   END                                                         AS 'budgetAmount',        -- 预算值
               lov_tl_zd01.value_meaning                                       AS 'targetTerminal',      -- 目标终端
               h.business_purpose                                              AS 'documentDescription', -- 单据头描述
               h.description                                                   AS 'description',         -- 备注
               lov_proj.lov_id
        FROM exp_claim_header h
                 JOIN exp_header_type ht ON h.header_type_id = ht.type_id
                 JOIN fnd_user u_submit ON h.submit_user = u_submit.user_id

                 LEFT JOIN exp_header_type_tl ht_tl ON ht.type_id = ht_tl.type_id AND ht_tl.language = 'zh_CN'

                 LEFT JOIN fnd_department submit_user_dept_l1
                           ON u_submit.department_id = submit_user_dept_l1.department_id
                 LEFT JOIN fnd_department_tl submit_user_dept_l1_tl
                           ON submit_user_dept_l1.department_id = submit_user_dept_l1_tl.department_id AND
                              submit_user_dept_l1_tl.language = 'zh_CN'
                 LEFT JOIN fnd_department submit_user_dept_l2
                           ON submit_user_dept_l1.supervisor_id = submit_user_dept_l2.department_id
                 LEFT JOIN fnd_department_tl submit_user_dept_l2_tl
                           ON submit_user_dept_l2.department_id = submit_user_dept_l2_tl.department_id AND
                              submit_user_dept_l2_tl.language = 'zh_CN'

                 LEFT JOIN fnd_lov_value lov_val_area ON submit_user_dept_l1.column2 = lov_val_area.value_code
                 LEFT JOIN fnd_lov lov_area ON lov_val_area.lov_id = lov_area.lov_id AND lov_area.lov_name = 'CBZXDX'
                 LEFT JOIN fnd_lov_value_tl lov_tl_area
                           ON lov_val_area.value_id = lov_tl_area.value_id AND lov_tl_area.language = 'zh_CN'

                 LEFT JOIN fnd_department_tl charge_dept_tl
                           ON h.charge_department = charge_dept_tl.department_id AND charge_dept_tl.language = 'zh_CN'
                 JOIN (SELECT *, ROW_NUMBER() OVER (PARTITION BY value_code ORDER BY value_id DESC) AS rn
                       FROM fnd_lov_value) lov_val_cpgx ON h.column41 = lov_val_cpgx.value_code -- 可能会重名
                 JOIN (SELECT *, ROW_NUMBER() OVER (PARTITION BY lov_name ORDER BY lov_id DESC) AS rn
                       FROM fnd_lov) lov_cpgx ON lov_val_cpgx.lov_id = lov_cpgx.lov_id AND lov_cpgx.lov_name = 'CPGX'
                 JOIN fnd_lov_value_tl lov_tl_cpgx
                      ON lov_val_cpgx.value_id = lov_tl_cpgx.value_id AND lov_tl_cpgx.language = 'zh_CN'
#
                 JOIN (SELECT *, ROW_NUMBER() OVER (PARTITION BY value_id ORDER BY value_code DESC) AS rn
                       FROM fnd_lov_value) lov_val_proj ON h.column46 = lov_val_proj.value_code
                 LEFT JOIN fnd_lov lov_proj
                           ON lov_val_proj.lov_id = lov_proj.lov_id AND lov_proj.lov_name = 'Project_number'
                 LEFT JOIN fnd_lov_value_tl lov_tl_proj
                           ON lov_val_proj.value_id = lov_tl_proj.value_id AND lov_tl_proj.language = 'zh_CN'

                 LEFT JOIN (SELECT *, ROW_NUMBER() OVER (PARTITION BY header_id ORDER BY line_id) AS rn
                            FROM exp_claim_line) l ON h.header_id = l.header_id AND l.rn = 1
                 LEFT JOIN fnd_lov_value lov_val_zd01 ON l.column42 = lov_val_zd01.value_code
                 LEFT JOIN fnd_lov lov_zd01 ON lov_val_zd01.lov_id = lov_zd01.lov_id AND lov_zd01.lov_name = 'ZD01'
                 LEFT JOIN fnd_lov_value_tl lov_tl_zd01
                           ON lov_val_zd01.value_id = lov_tl_zd01.value_id AND lov_tl_zd01.language = 'zh_CN'
                 LEFT JOIN cte_first_line fl ON h.header_id = fl.header_id AND fl.rn = 1
                 LEFT JOIN gl_account ga ON fl.dr_account_id = ga.account_id
                 LEFT JOIN gl_account_tl ga_tl ON ga.account_id = ga_tl.account_id AND ga_tl.language = 'zh_CN'
                 LEFT JOIN gl_budget_account bga ON fl.budget_id = bga.budget_id
        WHERE ht.type_code IN ('YXZR01', 'YXZR02')
          AND fl.budget_id IN (3030, 3029, 3007, 3025, 3026, 3027, 3028)
          AND h.status != 'deleted'
          AND lov_proj.lov_id IS NOT NULL

        ORDER BY h.submit_date
            DESC,
                 h.document_num
            DESC
    </select>
</mapper>

