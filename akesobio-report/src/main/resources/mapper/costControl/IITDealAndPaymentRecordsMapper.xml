<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.IITDealAndPaymentRecordsMapper">

    <select id="list" parameterType="com.akesobio.report.costControl.mapper.query.IITDealAndPaymentRecordsQuery"
            resultType="com.akesobio.report.costControl.domain.IITDealAndPaymentRecordsDTO">
        -- 27、IIT合同以及支付台账  IITDealAndPaymentRecords

-- 报表：合同付款全景追踪报表 (V14 - 修正支付进度)
-- 功能：将支付进度的最大值限制为100%，避免出现超过100%的情况。

-- 步骤1: 预处理，聚合计算每个合同的“已支付总额”和“FY2025支付额”
        WITH AggregatedPayments AS (
            SELECT
                link_tbl.link_header_id AS contract_header_id,
                SUM(h.total_amount) AS total_paid_amount,
                SUM(CASE WHEN YEAR(h.submit_date) = 2025 THEN h.total_amount ELSE 0 END) AS fy2025_paid_amount
            FROM
                exp_claim_header h
                    JOIN
                exp_claim_header_link link_tbl ON h.header_id = link_tbl.header_id
                    JOIN
                exp_header_type h_eht ON h.header_type_id = h_eht.type_id
            WHERE
                h_eht.type_code IN ('YXDG01', 'YXDG02')
              AND h.status != 'deleted'
            GROUP BY
                link_tbl.link_header_id
        )
-- 步骤2: 主查询，以合同为起点
        SELECT
            -- 合同要抓取的数据
            dept3_tl.department_name AS "departmentName3", -- 末三级部门
            dept2_tl.department_name AS "departmentName2", -- 末二级部门
            dept1_tl.department_name AS "departmentName", -- 末一级部门
            JSON_UNQUOTE(JSON_EXTRACT(con.column_json, '$.project_type')) AS projectType, -- 项目类型
            COALESCE(project_num_lov.value_meaning, con.column46) AS "projectNo", -- 项目号
            sup_tl.supplier_name AS "cooperationUnit", -- 合作单位
            con.document_num AS "contractApplicationNo", -- 合同申请单号
            con.external_id,--OA合同单号
            con.total_amount AS "contractAmount", -- 合同金额（人民币元）

            -- 已支付模块
            agg_pmt.total_paid_amount AS "paidAmount", -- 已支付金额
            agg_pmt.fy2025_paid_amount AS "totalAmount", -- FY2025 总金额
            -- 【核心修改】增加判断逻辑，当已支付金额超过合同金额时，进度最高显示为100%
            CASE
                WHEN con.total_amount IS NOT NULL AND con.total_amount > 0 THEN
                    CASE
                        WHEN agg_pmt.total_paid_amount >= con.total_amount THEN '100.00%'
                        ELSE CONCAT(FORMAT((agg_pmt.total_paid_amount / con.total_amount) * 100, 2), '%')
                        END
                ELSE '0.00%'
                END AS "支付进度",

            -- 应付、预付的数据抓取
            pmt_milestone_lov.value_meaning AS "paymentType", -- 付款类型/里程碑
            pmt.submit_date AS "paymentTime", -- 付款时间
            pmt_user.full_name AS "paymentApplicant", -- 付款申请人
            pmt.document_num AS "paymentNo", -- 付款单号
            pmt.total_amount AS "paymentAmount" -- 付款金额
        FROM
            exp_claim_header con
                JOIN exp_header_type con_eht ON con.header_type_id = con_eht.type_id
                LEFT JOIN fnd_department dept1 ON con.submit_department = dept1.department_id
                LEFT JOIN fnd_department_tl dept1_tl ON dept1.department_id = dept1_tl.department_id AND dept1_tl.language = 'zh_CN'
                LEFT JOIN fnd_department dept2 ON dept1.supervisor_id = dept2.department_id
                LEFT JOIN fnd_department_tl dept2_tl ON dept2.department_id = dept2_tl.department_id AND dept2_tl.language = 'zh_CN'
                LEFT JOIN fnd_department dept3 ON dept2.supervisor_id = dept3.department_id
                LEFT JOIN fnd_department_tl dept3_tl ON dept3.department_id = dept3_tl.department_id AND dept3_tl.language = 'zh_CN'
                LEFT JOIN fnd_user u_con ON con.submit_user = u_con.user_id
                LEFT JOIN exp_supplier sup ON con.supplier_id = sup.supplier_id
                LEFT JOIN exp_supplier_tl sup_tl ON sup.supplier_id = sup_tl.supplier_id AND sup_tl.language = 'zh_CN'
                LEFT JOIN (
                SELECT lov_val.value_code, lov_val_tl.value_meaning, lov.company_id
                FROM fnd_lov lov
                         JOIN fnd_lov_value lov_val ON lov.lov_id = lov_val.lov_id
                         JOIN fnd_lov_value_tl lov_val_tl ON lov_val.value_id = lov_val_tl.value_id AND lov_val_tl.language = 'zh_CN'
                WHERE lov.lov_name = 'Project_numbe'
            ) AS project_num_lov ON project_num_lov.value_code = con.column46 AND (project_num_lov.company_id = con.company_id OR project_num_lov.company_id IS NULL OR project_num_lov.company_id = 0)
                LEFT JOIN AggregatedPayments agg_pmt ON con.header_id = agg_pmt.contract_header_id
                LEFT JOIN exp_claim_header_link pmt_link ON con.header_id = pmt_link.link_header_id
                LEFT JOIN exp_claim_header pmt ON pmt_link.header_id = pmt.header_id
                LEFT JOIN exp_header_type pmt_eht ON pmt.header_type_id = pmt_eht.type_id
                AND pmt_eht.type_code IN ('YXDG01', 'YXDG02')
                AND pmt.status != 'deleted'
                LEFT JOIN fnd_user pmt_user ON pmt.submit_user = pmt_user.user_id
                LEFT JOIN (
                SELECT lov_val.value_code, lov_val_tl.value_meaning, lov.company_id
                FROM fnd_lov lov
                         JOIN fnd_lov_value lov_val ON lov.lov_id = lov_val.lov_id
                         JOIN fnd_lov_value_tl lov_val_tl ON lov_val.value_id = lov_val_tl.value_id AND lov_val_tl.language = 'zh_CN'
                WHERE lov.lov_name = 'PAY LCB'
            ) AS pmt_milestone_lov ON pmt_milestone_lov.value_code = JSON_UNQUOTE(JSON_EXTRACT(pmt.column_json, '$.column117'))
                AND (pmt_milestone_lov.company_id = pmt.company_id OR pmt_milestone_lov.company_id IS NULL OR pmt_milestone_lov.company_id = 0)
        <where>
            con_eht.type_code = 'YXHT'
            AND con.status != 'deleted'
        <if test="query.departmentName != null and query.departmentName!=''">
            AND (dept3_tl.department_name LIKE CONCAT('%', #{query.departmentName}, '%')
            OR dept2_tl.department_name LIKE CONCAT('%', #{query.departmentName}, '%')
            OR dept1_tl.department_name LIKE CONCAT('%', #{query.departmentName}, '%'))
        </if>
        <if test="query.projectNo!= null and query.projectNo!=''">
            AND COALESCE(project_num_lov.value_meaning, con.column46) LIKE CONCAT('%', #{query.projectNo}, '%')
        </if>
        <if test="query.cooperationUnit!= null and query.cooperationUnit!=''">
            AND sup_tl.supplier_name LIKE CONCAT('%', #{query.cooperationUnit}, '%')
        </if>
        <if test="query.projectType!= null and query.projectType!=''">
            AND projectType LIKE CONCAT('%', #{query.contractApplicationNo}, '%')
        </if>
        </where>


    </select>
</mapper>

