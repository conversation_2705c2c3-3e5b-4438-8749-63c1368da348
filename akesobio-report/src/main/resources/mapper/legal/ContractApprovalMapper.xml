<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.legal.mapper.ContractApprovalMapper">

    <resultMap type="ContractApproval" id="contractApprovalResult">
        <result property="dateCreated" column="dateCreated"/>
        <result property="businessType" column="businessType"/>
        <result property="contractType" column="contractType"/>
        <result property="costBearer" column="costBearer"/>
        <result property="contractNo" column="contractNo"/>
        <result property="projectNo" column="projectNo"/>
        <result property="applicant" column="applicant"/>
        <result property="processNo" column="processNo"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentHandler" column="currentHandler"/>
        <result property="currentLink" column="currentLink"/>
        <result property="contractAmount" column="contractAmount"/>
        <result property="cooperativeUnits" column="cooperativeUnits"/>
        <result property="department" column="department"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="link" column="applicationSubject"/>
    </resultMap>

    <sql id="selectContractApprovalVo">
        select fd_id,
               dateCreated,
               businessType,
               contractType,
               costBearer,
               applicationSubject,
               department,
               cooperativeUnits,
               contractNo,
               projectNo,
               applicant,
               processNo,
               documentStatus,
               currentHandler,
               currentLink,
               contractAmount
        from contract_approval
    </sql>

    <select id="selectContractApprovalList" parameterType="ContractApproval" resultMap="contractApprovalResult">
        <include refid="selectContractApprovalVo"/>
        <where>
            <if test="fdId != null  and fdId != ''">and fd_id like concat('%', #{fdId}, '%')</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and dateCreated between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
            <if test="businessType != null  and businessType != ''">and businessType like concat('%', #{businessType},
                '%')
            </if>
            <if test="contractType != null  and contractType != ''">and contractType like concat('%', #{contractType},
                '%')
            </if>
            <if test="costBearer != null  and costBearer != ''">and costBearer like concat('%', #{costBearer}, '%')</if>
            <if test="contractNo != null  and contractNo != ''">and contractNo like concat('%', #{contractNo}, '%')</if>
            <if test="projectNo != null  and projectNo != ''">and projectNo like concat('%', #{projectNo}, '%')</if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="processNo != null  and processNo != ''">and processNo like concat('%', #{processNo}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentHandler != null  and currentHandler != ''">and currentHandler like concat('%',
                #{currentHandler}, '%')
            </if>
            <if test="currentLink != null  and currentLink != ''">and currentLink like concat('%', #{currentLink},
                '%')
            </if>
            <if test="contractAmount != null  and contractAmount != ''">and contractAmount = #{contractAmount}</if>
            <if test="cooperativeUnits != null  and cooperativeUnits != ''">and cooperativeUnits like concat('%',
                #{cooperativeUnits}, '%')
            </if>
            <if test="applicationSubject != null  and applicationSubject != ''">and applicationSubject like concat('%',
                #{applicationSubject}, '%')
            </if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
        </where>
        order by dateCreated desc
    </select>


    <select id="selectContractApprovalList1" parameterType="ContractApproval" resultMap="contractApprovalResult">
        select * from
        (select fd_id,
        dateCreated,
        businessType,
        contractType,
        costBearer,
        applicationSubject,
        department,
        cooperativeUnits,
        contractNo,
        projectNo,
        applicant,
        processNo,
        documentStatus,
        currentHandler,
        currentLink,
        contractAmount
        from contract_approval where businessType='临床业务' or contractType='临床药品' or department like '%临床运行部门%' ) a
        <where>
            <if test="fdId != null  and fdId != ''">and fd_id like concat('%', #{fdId}, '%')</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and dateCreated between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
            <if test="businessType != null  and businessType != ''">and businessType like concat('%', #{businessType},
                '%')
            </if>
            <if test="contractType != null  and contractType != ''">and contractType like concat('%', #{contractType},
                '%')
            </if>
            <if test="costBearer != null  and costBearer != ''">and costBearer like concat('%', #{costBearer}, '%')</if>
            <if test="contractNo != null  and contractNo != ''">and contractNo like concat('%', #{contractNo}, '%')</if>
            <if test="projectNo != null  and projectNo != ''">and projectNo like concat('%', #{projectNo}, '%')</if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="processNo != null  and processNo != ''">and processNo like concat('%', #{processNo}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentHandler != null  and currentHandler != ''">and currentHandler like concat('%',
                #{currentHandler}, '%')
            </if>
            <if test="currentLink != null  and currentLink != ''">and currentLink like concat('%', #{currentLink},
                '%')
            </if>
            <if test="contractAmount != null  and contractAmount != ''">and contractAmount = #{contractAmount}</if>
            <if test="cooperativeUnits != null  and cooperativeUnits != ''">and cooperativeUnits like concat('%',
                #{cooperativeUnits}, '%')
            </if>
            <if test="applicationSubject != null  and applicationSubject != ''">and applicationSubject like concat('%',
                #{applicationSubject}, '%')
            </if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
        </where>
        order by dateCreated desc
    </select>

    <select id="selectContractApprovalByDateCreated" parameterType="Date" resultMap="contractApprovalResult">
        <include refid="selectContractApprovalVo"/>
        where dateCreated = #{dateCreated}
    </select>

    <select id="selectContractApprovalByBusinessType" parameterType="string" resultType="int">
        select COUNT(*)
        FROM contract_approval
        where businessType = #{businessType}
          AND documentStatus NOT IN ('草稿', '废弃')
    </select>

    <select id="selectBusinessType" resultType="string">
        select DISTINCT businessType
        FROM contract_approval
    </select>

    <select id="selectContractTotalByBusinessType" parameterType="string" resultType="int">
        SELECT COUNT(*) AS result_count
        FROM contract_approval
        WHERE businessType = #{businessType}
          AND documentStatus NOT IN ('草稿', '废弃')
        UNION ALL
        SELECT COUNT(*) AS result_count
        FROM dealer_executes_contract
        WHERE documentStatus NOT IN ('草稿', '废弃')
        UNION ALL
        SELECT COUNT(*) AS result_count
        FROM labor_service_contract
        WHERE documentStatus NOT IN ('草稿', '废弃')
        UNION ALL
        SELECT COUNT(*) AS result_count
        FROM research_contract_approval
        WHERE documentStatus NOT IN ('草稿', '废弃')
        UNION ALL
        SELECT COUNT(*) AS result_count
        FROM sale_contract_approval
        WHERE documentStatus NOT IN ('草稿', '废弃')
        UNION ALL
        SELECT COUNT(*) AS result_count
        FROM sales_compensation_record
        WHERE documentStatus NOT IN ('草稿', '废弃')
        UNION ALL
        SELECT COUNT(*) AS result_count
        FROM yx_contract_approval
        WHERE documentStatus NOT IN ('草稿', '废弃')
    </select>


</mapper>