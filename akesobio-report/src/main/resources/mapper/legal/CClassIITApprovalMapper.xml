<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.legal.mapper.CClassIITApprovalMapper">

    <resultMap type="CClassIITApproval" id="CClassIITApprovalResult">
        <result property="dateCreated"    column="date_created"    />
        <result property="businessType"    column="business_type"    />
        <result property="contractType"    column="contract_type"    />
        <result property="formNum"    column="form_num"    />
        <result property="applicant"    column="applicant"    />
        <result property="department"    column="department"    />
        <result property="costAttribution"    column="cost_attribution"    />
        <result property="contractNum"    column="contract_num"    />
        <result property="projectNum"    column="project_num"    />
        <result property="cooperativeUnits"    column="cooperative_units"    />
        <result property="centerName"    column="center_Name"    />
        <result property="contractAmount"    column="contract_amount"    />
        <result property="documentStatus"    column="document_status"    />
        <result property="currentHandler" column="current_handler"/>
        <result property="currentLink" column="current_link"/>
        <result property="fdId" column="fd_id"/>
    </resultMap>

    <sql id="selectCClassIITApprovalVo">
        SELECT
            fd_id,
            form_num,
            date_created,
            applicant,
            department,
            business_type,
            contract_type,
            cost_attribution,
            contract_num,
            project_num,
            cooperative_units,
            center_name,
            contract_amount,
            document_status,
            current_handler,
            current_link
        FROM
            cClassIITApprovalVo
    </sql>

    <select id="selectCClassIITApprovalList" parameterType="CClassIITApproval" resultMap="CClassIITApprovalResult">
        <include refid="selectCClassIITApprovalVo"/>
        <where>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and date_created between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="contractType != null  and contractType != ''"> and contract_type like concat('%', #{contractType}, '%')</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="contractNum != null  and contractNum != ''"> and contract_num like concat('%', #{contractNum}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''"> and document_status = #{documentStatus}</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="cooperativeUnits != null  and cooperativeUnits != ''">and cooperative_units like concat('%', #{cooperativeUnits}, '%')</if>
            <if test="currentHandler != null  and currentHandler != ''"> and current_handler like concat('%', #{currentHandler}, '%')</if>
            <if test="currentLink != null  and currentLink != ''"> and current_link like concat('%', #{currentLink}, '%')</if>
            <if test="contractAmount != null  and contractAmount != ''"> and contract_amount = #{contractAmount}</if>
            <if test="formNum != null  and formNum != ''"> and form_num like concat('%', #{formNum}, '%')</if>
            <if test="fdId != null  and fdId != ''"> and fd_id = #{fdId}</if>
            and document_status NOT IN ('草稿', '废弃')
        </where>
        order by date_created desc
    </select>

    <select id="selectCClassIITApprovalByDateCreated" parameterType="Date" resultMap="CClassIITApprovalResult">
        <include refid="selectCClassIITApprovalVo"/>
        where date_created = #{dateCreated}
    </select>

</mapper>