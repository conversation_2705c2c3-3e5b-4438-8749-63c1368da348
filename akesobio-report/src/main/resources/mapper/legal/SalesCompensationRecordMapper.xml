<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.legal.mapper.SalesCompensationRecordMapper">
    
    <resultMap type="SalesCompensationRecord" id="SalesCompensationRecordResult">
        <result property="dateCreated"    column="dateCreated"    />
        <result property="compensationType"    column="compensationType"    />
        <result property="contractNo"    column="contractNo"    />
        <result property="applicant"    column="applicant"    />
        <result property="processNo"    column="processNo"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentHandler"    column="currentHandler"    />
        <result property="currentLink"    column="currentLink"    />
        <result property="department" column="department"/>
        <result property="subject" column="subject"/>
        <result property="fdId" column="fd_id"/>
    </resultMap>

    <sql id="selectSalesCompensationRecordVo">
        select department,
               subject,
               dateCreated,
               compensationType,
               contractNo,
               applicant,
               processNo,
               documentStatus,
               currentHandler,
               currentLink,
               fd_id
        from sales_compensation_record
    </sql>

    <select id="selectSalesCompensationRecordList" parameterType="SalesCompensationRecord" resultMap="SalesCompensationRecordResult">
        <include refid="selectSalesCompensationRecordVo"/>
        <where>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and dateCreated between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="compensationType != null  and compensationType != ''"> and compensationType = #{compensationType}</if>
            <if test="contractNo != null  and contractNo != ''"> and contractNo = #{contractNo}</if>
            <if test="applicant != null  and applicant != ''"> and applicant = #{applicant}</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="subject != null  and subject != ''">and subject like concat('%', #{subject}, '%')</if>
            <if test="processNo != null  and processNo != ''"> and processNo = #{processNo}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus = #{documentStatus}</if>
            <if test="currentHandler != null  and currentHandler != ''"> and currentHandler = #{currentHandler}</if>
            <if test="currentLink != null  and currentLink != ''"> and currentLink = #{currentLink}</if>
            <if test="fdId != null  and fdId != ''"> and fd_id = #{fdId}</if>
        </where>
        order by dateCreated desc
    </select>
    
    <select id="selectSalesCompensationRecordByDateCreated" parameterType="Date" resultMap="SalesCompensationRecordResult">
        <include refid="selectSalesCompensationRecordVo"/>
        where dateCreated = #{dateCreated}
    </select>
</mapper>