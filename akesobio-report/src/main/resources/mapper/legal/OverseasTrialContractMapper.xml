<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.legal.mapper.OverseasTrialContractMapper">
    
    <resultMap type="OverseasTrialContract" id="OverseasTrialContractResult">
        <result property="dateCreated"    column="dateCreated"    />
        <result property="serviceType"    column="serviceType"    />
        <result property="vendorType"    column="vendorType"    />
        <result property="expenseAt"    column="expenseAt"    />
        <result property="contractNo"    column="contractNo"    />
        <result property="studyNo"    column="studyNo"    />
        <result property="applicant"    column="applicant"    />
        <result property="processNo"    column="processNo"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentHandler"    column="currentHandler"    />
        <result property="currentLink"    column="currentLink"    />
        <result property="contractAmount"    column="contractAmount"    />
        <result property="currency"    column="currency"    />
        <result property="department" column="department"/>
        <result property="subject" column="subject"/>
        <result property="fdId" column="fd_id"/>
    </resultMap>

    <sql id="selectOverseasTrialContractVo">
        select department,
               subject,
               dateCreated,
               serviceType,
               vendorType,
               expenseAt,
               contractNo,
               studyNo,
               applicant,
               processNo,
               documentStatus,
               currentHandler,
               currentLink,
               contractAmount,
               currency,
               fd_id
        from overseas_trial_contract
    </sql>

    <select id="selectOverseasTrialContractList" parameterType="OverseasTrialContract" resultMap="OverseasTrialContractResult">
        <include refid="selectOverseasTrialContractVo"/>
        <where>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and dateCreated between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="serviceType != null  and serviceType != ''"> and serviceType = #{serviceType}</if>
            <if test="vendorType != null  and vendorType != ''"> and vendorType = #{vendorType}</if>
            <if test="expenseAt != null  and expenseAt != ''"> and expenseAt = #{expenseAt}</if>
            <if test="contractNo != null  and contractNo != ''"> and contractNo = #{contractNo}</if>
            <if test="studyNo != null  and studyNo != ''"> and studyNo = #{studyNo}</if>
            <if test="applicant != null  and applicant != ''"> and applicant = #{applicant}</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="subject != null  and subject != ''">and subject like concat('%', #{subject}, '%')</if>
            <if test="processNo != null  and processNo != ''"> and processNo = #{processNo}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus = #{documentStatus}</if>
            <if test="currentHandler != null  and currentHandler != ''"> and currentHandler = #{currentHandler}</if>
            <if test="currentLink != null  and currentLink != ''"> and currentLink = #{currentLink}</if>
            <if test="contractAmount != null  and contractAmount != ''"> and contractAmount = #{contractAmount}</if>
            <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
            <if test="fdId != null  and fdId != ''"> and fd_id = #{fdId}</if>
        </where>
        order by dateCreated desc
    </select>
    
    <select id="selectOverseasTrialContractByDateCreated" parameterType="Date" resultMap="OverseasTrialContractResult">
        <include refid="selectOverseasTrialContractVo"/>
        where dateCreated = #{dateCreated}
    </select>

    <select id="selectOverseasTrialContractByBusinessType" parameterType="string" resultType="int">
        select COUNT(*) FROM overseas_trial_contract
        where serviceType = #{serviceType} AND documentStatus NOT IN ('草稿','废弃')
    </select>

    <select id="selectOverseasTrialContract" resultType="string">
        select DISTINCT serviceType FROM overseas_trial_contract
    </select>
</mapper>