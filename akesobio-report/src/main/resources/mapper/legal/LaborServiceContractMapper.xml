<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.legal.mapper.LaborServiceContractMapper">
    
    <resultMap type="LaborServiceContract" id="LaborServiceContractResult">
        <result property="dateCreated"    column="dateCreated"    />
        <result property="processNo"    column="processNo"    />
        <result property="costBearer"    column="costBearer"    />
        <result property="projectNo"    column="projectNo"    />
        <result property="applicant"    column="applicant"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentHandler"    column="currentHandler"    />
        <result property="currentLink"    column="currentLink"    />
        <result property="contractAmount"    column="contractAmount"    />
        <result property="department" column="department"/>
        <result property="subject" column="subject"/>
        <result property="fdId" column="fd_id"/>
    </resultMap>

    <sql id="selectLaborServiceContractVo">
        select department,
               subject,
               dateCreated,
               processNo,
               costBearer,
               projectNo,
               applicant,
               documentStatus,
               currentHandler,
               currentLink,
               contractAmount,
               fd_id
        from labor_service_contract
    </sql>

    <select id="selectLaborServiceContractList" parameterType="LaborServiceContract" resultMap="LaborServiceContractResult">
        <include refid="selectLaborServiceContractVo"/>
        <where>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and dateCreated between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="processNo != null  and processNo != ''"> and processNo = #{processNo}</if>
            <if test="costBearer != null  and costBearer != ''"> and costBearer = #{costBearer}</if>
            <if test="projectNo != null  and projectNo != ''"> and projectNo = #{projectNo}</if>
            <if test="applicant != null  and applicant != ''"> and applicant = #{applicant}</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="subject != null  and subject != ''">and subject like concat('%', #{subject}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus = #{documentStatus}</if>
            <if test="currentHandler != null  and currentHandler != ''"> and currentHandler = #{currentHandler}</if>
            <if test="currentLink != null  and currentLink != ''"> and currentLink = #{currentLink}</if>
            <if test="contractAmount != null  and contractAmount != ''"> and contractAmount = #{contractAmount}</if>
            <if test="fdId != null  and fdId != ''"> and fd_id = #{fdId}</if>
        </where>
        order by dateCreated desc
    </select>
    
    <select id="selectLaborServiceContractByDateCreated" parameterType="Date" resultMap="LaborServiceContractResult">
        <include refid="selectLaborServiceContractVo"/>
        where dateCreated = #{dateCreated}
    </select>
</mapper>