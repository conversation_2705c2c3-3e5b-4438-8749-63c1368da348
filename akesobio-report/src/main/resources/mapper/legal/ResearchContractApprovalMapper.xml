<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.legal.mapper.ResearchContractApprovalMapper">

    <resultMap type="ResearchContractApproval" id="ResearchContractApprovalResult">
    </resultMap>

    <sql id="selectResearchContractApprovalVo">
        select dateCreated,
               businessType,
               contractType,
               costBearer,
               contractNo,
               projectNo,
               applicant,
               department,
               processNo,
               subject,
               documentStatus,
               currentHandler,
               currentLink,
               contractAmount,
               cooperativeUnit,
               whetherOrNot,
               projectType,
               fd_id,
               centerName,
               regionalManager,
               salesAreaDirector
        from research_contract_approval
    </sql>

    <select id="selectResearchContractApprovalList" parameterType="ResearchContractApproval"
            resultMap="ResearchContractApprovalResult">
        <include refid="selectResearchContractApprovalVo"/>
        <where>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and dateCreated between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
            <if test="businessType != null  and businessType != ''">and businessType like concat('%', #{businessType},
                '%')
            </if>
            <if test="contractType != null  and contractType != ''">and contractType like concat('%', #{contractType},
                '%')
            </if>
            <if test="costBearer != null  and costBearer != ''">and costBearer like concat('%', #{costBearer}, '%')</if>
            <if test="contractNo != null  and contractNo != ''">and contractNo like concat('%', #{contractNo}, '%')</if>
            <if test="projectNo != null  and projectNo != ''">and projectNo like concat('%', #{projectNo}, '%')</if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="subject != null  and subject != ''">and subject like concat('%', #{subject}, '%')</if>
            <if test="processNo != null  and processNo != ''">and processNo like concat('%', #{processNo}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentHandler != null  and currentHandler != ''">and currentHandler like concat('%',
                #{currentHandler}, '%')
            </if>
            <if test="currentLink != null  and currentLink != ''">and currentLink like concat('%', #{currentLink},
                '%')
            </if>
            <if test="contractAmount != null  and contractAmount != ''">and contractAmount = #{contractAmount}</if>
            <if test="cooperativeUnit != null  and cooperativeUnit != ''">and cooperativeUnit like concat('%',
                #{cooperativeUnit}, '%')
            </if>
            <if test="fdId != null  and fdId != ''">and fd_id like concat('%', #{fdId}, '%')</if>
            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')
            </if>
            <if test="regionalManager != null  and regionalManager != ''">and regionalManager like concat('%',
                #{regionalManager}, '%')
            </if>
            <if test="salesAreaDirector != null  and salesAreaDirector != ''">and salesAreaDirector like concat('%',
                #{salesAreaDirector}, '%')
            </if>
        </where>
        order by dateCreated desc
    </select>

    <select id="selectResearchContractApprovalByDateCreated" parameterType="Date"
            resultMap="ResearchContractApprovalResult">
        <include refid="selectResearchContractApprovalVo"/>
        where dateCreated = #{dateCreated}
    </select>
</mapper>