<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.legal.mapper.YxContractApprovalMapper">
    
    <resultMap type="YxContractApproval" id="YxContractApprovalResult">
        <result property="dateCreated"    column="dateCreated"    />
        <result property="expenseType" column="expenseType"/>
        <result property="businessCatalogType" column="businessCatalogType"/>
        <result property="businessType"    column="businessType"    />
        <result property="meetingType"    column="meetingType"    />
        <result property="costBearer"    column="costBearer"    />
        <result property="contractNo"    column="contractNo"    />
        <result property="projectNo"    column="projectNo"    />
        <result property="applicant"    column="applicant"    />
        <result property="processNo"    column="processNo"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentHandler"    column="currentHandler"    />
        <result property="currentLink"    column="currentLink"    />
        <result property="contractAmount"    column="contractAmount"    />
        <result property="department" column="department"/>
        <result property="subject" column="subject"/>
        <result property="fdId" column="fd_id"/>
    </resultMap>

    <sql id="selectYxContractApprovalVo">
        select department,
               subject,
               dateCreated,
               expenseType,
               businessCatalogType,
               businessType,
               meetingType,
               costBearer,
               contractNo,
               projectNo,
               applicant,
               processNo,
               documentStatus,
               currentHandler,
               currentLink,
               contractAmount,
               fd_id
        from yx_contract_approval
    </sql>

    <select id="selectYxContractApprovalList" parameterType="YxContractApproval" resultMap="YxContractApprovalResult">
        <include refid="selectYxContractApprovalVo"/>
        <where>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and dateCreated between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="expenseType != null  and expenseType != ''">and expenseType = #{expenseType}</if>
            <if test="businessCatalogType != null  and businessCatalogType != ''">and businessCatalogType =
                #{businessCatalogType}
            </if>
            <if test="businessType != null  and businessType != ''"> and businessType = #{businessType}</if>
            <if test="meetingType != null  and meetingType != ''"> and meetingType = #{meetingType}</if>
            <if test="costBearer != null  and costBearer != ''"> and costBearer = #{costBearer}</if>
            <if test="contractNo != null  and contractNo != ''"> and contractNo = #{contractNo}</if>
            <if test="projectNo != null  and projectNo != ''"> and projectNo = #{projectNo}</if>
            <if test="applicant != null  and applicant != ''"> and applicant = #{applicant}</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="subject != null  and subject != ''">and subject like concat('%', #{subject}, '%')</if>
            <if test="processNo != null  and processNo != ''"> and processNo = #{processNo}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus = #{documentStatus}</if>
            <if test="currentHandler != null  and currentHandler != ''"> and currentHandler = #{currentHandler}</if>
            <if test="currentLink != null  and currentLink != ''"> and currentLink = #{currentLink}</if>
            <if test="contractAmount != null  and contractAmount != ''"> and contractAmount = #{contractAmount}</if>
            <if test="fdId != null  and fdId != ''"> and fd_id = #{fdId}</if>
        </where>
        order by dateCreated desc
    </select>
    
    <select id="selectYxContractApprovalByDateCreated" parameterType="Date" resultMap="YxContractApprovalResult">
        <include refid="selectYxContractApprovalVo"/>
        where dateCreated = #{dateCreated}
    </select>
</mapper>