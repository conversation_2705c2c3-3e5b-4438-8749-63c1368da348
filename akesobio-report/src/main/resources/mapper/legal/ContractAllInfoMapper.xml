<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.legal.mapper.ContractAllInfoMapper">

    <resultMap type="ContractAllInfo" id="ContractAllInfoResult">
        <result property="contractNumber" column="contract_number"/>
        <result property="businessType" column="business_type"/>
        <result property="contractType" column="contract_type"/>
        <result property="subdivision" column="subdivision"/>
        <result property="itemNumber" column="item_number"/>
        <result property="ourContractingEntity" column="our_contracting_entity"/>
        <result property="otherContractingEntity" column="other_contracting_entity"/>
        <result property="contractAmount" column="contract_amount"/>
        <result property="currency" column="currency"/>
        <result property="contractStart" column="contract_start"/>
        <result property="contractEnd" column="contract_end"/>
        <result property="applicant" column="applicant"/>
        <result property="creationTime" column="creation_time"/>
        <result property="processNumber" column="process_number"/>
        <result property="documentStatus" column="document_status"/>
        <result property="currentProcessor" column="current_processor"/>
        <result property="currentLink" column="current_link"/>
        <result property="akesoContractTemplate" column="akeso_contract_template"/>
        <result property="department" column="department"/>
        <result property="subject" column="subject"/>
        <result property="fdId" column="fd_id"/>
        <result property="businessTypeNew" column="business_type_new"/>
        <result property="contractTypeNew" column="contract_type_new"/>
    </resultMap>

    <sql id="selectContractAllInfoVo">
        select contract_number,
               business_type,
               contract_type,
               subdivision,
               item_number,
               our_contracting_entity,
               other_contracting_entity,
               contract_amount,
               currency,
               contract_start,
               contract_end,
               applicant,
               department,
               creation_time,
               process_number,
               subject,
               document_status,
               current_processor,
               current_link,
               akeso_contract_template,
               fd_id,
               business_type_new,
               contract_type_new
        from contract_all_info
    </sql>

    <select id="selectContractAllInfoList" parameterType="ContractAllInfo" resultMap="ContractAllInfoResult">
        <include refid="selectContractAllInfoVo"/>
        <where>
            <if test="contractNumber != null  and contractNumber != ''">and contract_number like concat('%',
                #{contractNumber}, '%')
            </if>
            <if test="businessType != null  and businessType != ''">and business_type like concat('%', #{businessType},
                '%')
            </if>
            <if test="contractType != null  and contractType != ''">and contract_type like concat('%', #{contractType},
                '%')
            </if>
            <if test="subdivision != null  and subdivision != ''">and subdivision like concat('%', #{subdivision},
                '%')
            </if>
            <if test="itemNumber != null  and itemNumber != ''">and item_number like concat('%', #{itemNumber}, '%')
            </if>
            <if test="ourContractingEntity != null  and ourContractingEntity != ''">and our_contracting_entity like
                concat('%', #{ourContractingEntity}, '%')
            </if>
            <if test="otherContractingEntity != null  and otherContractingEntity != ''">and other_contracting_entity
                like concat('%', #{otherContractingEntity}, '%')
            </if>
            <if test="contractAmount != null  and contractAmount != ''">and contract_amount = #{contractAmount}</if>
            <if test="currency != null  and currency != ''">and currency = #{currency}</if>
            <if test="contractStart != null ">and contract_start = #{contractStart}</if>
            <if test="contractEnd != null ">and contract_end = #{contractEnd}</if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="subject != null  and subject != ''">and subject like concat('%', #{subject}, '%')</if>
            <if test="params.beginCreationTime != null and params.beginCreationTime != '' and params.endCreationTime != null and params.endCreationTime != ''">
                and CONVERT(VARCHAR(100),creation_time,23) between #{params.beginCreationTime} and
                #{params.endCreationTime}
            </if>
            <if test="processNumber != null  and processNumber != ''">and process_number like concat('%',
                #{processNumber}, '%')
            </if>
            <if test="documentStatus != null  and documentStatus != ''">and document_status like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentProcessor != null  and currentProcessor != ''">and current_processor like concat('%',
                #{currentProcessor}, '%')
            </if>
            <if test="currentLink != null  and currentLink != ''">and current_link = #{currentLink}</if>
            <if test="akesoContractTemplate != null  and akesoContractTemplate != ''">and akeso_contract_template =
                #{akesoContractTemplate}
            </if>
            <if test="fdId != null  and fdId != ''">and fd_id =#{fdId}
            </if>
            <if test="businessTypeNew != null  and businessTypeNew != ''">and business_type_new like concat('%',
                #{businessTypeNew}, '%')
            </if>
            <if test="contractTypeNew  != null  and contractTypeNew != ''">and contract_type_new like concat('%',
                #{contractTypeNew }, '%')
            </if>
        </where>
    </select>
</mapper>