<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.mainData.mapper.SupplierMDMapper">
    
    <resultMap type="SupplierMD" id="SupplierMDResult">
        <result property="sid"    column="sid"    />
        <result property="partner"    column="partner"    />
        <result property="bkvid"    column="bkvid"    />
        <result property="nameOrg1"    column="name_org1"    />
        <result property="buSort1"    column="bu_sort1"    />
        <result property="buGroup"    column="bu_group"    />
        <result property="idnumber"    column="idnumber"    />
        <result property="country"    column="country"    />
        <result property="langu"    column="langu"    />
        <result property="timeZone"    column="time_zone"    />
        <result property="steet"    column="steet"    />
        <result property="telNumber"    column="tel_number"    />
        <result property="mobNumber"    column="mob_number"    />
        <result property="smtpAddr"    column="smtp_addr"    />
        <result property="akont"    column="akont"    />
        <result property="loevm"    column="loevm"    />
        <result property="banks"    column="banks"    />
        <result property="bankl"    column="bankl"    />
        <result property="koinh"    column="koinh"    />
        <result property="bankn"    column="bankn"    />
        <result property="bkont"    column="bkont"    />
        <result property="xezer"    column="xezer"    />
        <result property="accname"    column="accname"    />
        <result property="efficient"    column="efficient"    />
<!--        <result property="createTime"    column="create_time"    />-->
<!--        <result property="updatetime"    column="updatetime"    />-->
        <result property="banka"    column="banka"    />
        <result property="zsfyxgys"    column="zsfyxgys"    />
        <result property="zyxlb"    column="zyxlb"    />
    </resultMap>

    <sql id="selectSupplierMDVo">
        select sid, partner, bkvid, name_org1, bu_sort1, bu_group, idnumber, country, langu, time_zone, steet, tel_number, mob_number, smtp_addr, akont, loevm, banks, bankl, koinh, bankn, bkont, xezer, accname, efficient,  banka, zsfyxgys, zyxlb from SupplierMD
    </sql>

    <select id="selectSupplierMDList" parameterType="SupplierMD" resultMap="SupplierMDResult">
        <include refid="selectSupplierMDVo"/>
        <where>  
            <if test="partner != null  and partner != ''"> and partner = #{partner}</if>
            <if test="bkvid != null  and bkvid != ''"> and bkvid = #{bkvid}</if>
            <if test="nameOrg1 != null  and nameOrg1 != ''"> and name_org1 = #{nameOrg1}</if>
            <if test="buSort1 != null  and buSort1 != ''"> and bu_sort1 = #{buSort1}</if>
            <if test="buGroup != null  and buGroup != ''"> and bu_group = #{buGroup}</if>
            <if test="idnumber != null  and idnumber != ''"> and idnumber = #{idnumber}</if>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="langu != null  and langu != ''"> and langu = #{langu}</if>
            <if test="timeZone != null  and timeZone != ''"> and time_zone = #{timeZone}</if>
            <if test="steet != null  and steet != ''"> and steet = #{steet}</if>
            <if test="telNumber != null  and telNumber != ''"> and tel_number = #{telNumber}</if>
            <if test="mobNumber != null  and mobNumber != ''"> and mob_number = #{mobNumber}</if>
            <if test="smtpAddr != null  and smtpAddr != ''"> and smtp_addr = #{smtpAddr}</if>
            <if test="akont != null  and akont != ''"> and akont = #{akont}</if>
            <if test="loevm != null  and loevm != ''"> and loevm = #{loevm}</if>
            <if test="banks != null  and banks != ''"> and banks = #{banks}</if>
            <if test="bankl != null  and bankl != ''"> and bankl = #{bankl}</if>
            <if test="koinh != null  and koinh != ''"> and koinh = #{koinh}</if>
            <if test="bankn != null  and bankn != ''"> and bankn = #{bankn}</if>
            <if test="bkont != null  and bkont != ''"> and bkont = #{bkont}</if>
            <if test="xezer != null  and xezer != ''"> and xezer = #{xezer}</if>
            <if test="accname != null  and accname != ''"> and accname like concat('%', #{accname}, '%')</if>
            <if test="efficient != null "> and efficient = #{efficient}</if>
<!--            <if test="updatetime != null "> and updatetime = #{updatetime}</if>-->
            <if test="banka != null  and banka != ''"> and banka = #{banka}</if>
            <if test="zsfyxgys != null  and zsfyxgys != ''"> and zsfyxgys = #{zsfyxgys}</if>
            <if test="zyxlb != null  and zyxlb != ''"> and zyxlb = #{zyxlb}</if>
        </where>
    </select>
    
    <select id="selectSupplierMDBySid" parameterType="Long" resultMap="SupplierMDResult">
        <include refid="selectSupplierMDVo"/>
        where sid = #{sid}
    </select>
        
    <insert id="insertSupplierMD" parameterType="SupplierMD">
        insert into SupplierMD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="partner != null">partner,</if>
            <if test="bkvid != null">bkvid,</if>
            <if test="nameOrg1 != null">name_org1,</if>
            <if test="buSort1 != null">bu_sort1,</if>
            <if test="buGroup != null">bu_group,</if>
            <if test="idnumber != null">idnumber,</if>
            <if test="country != null">country,</if>
            <if test="langu != null">langu,</if>
            <if test="timeZone != null">time_zone,</if>
            <if test="steet != null">steet,</if>
            <if test="telNumber != null">tel_number,</if>
            <if test="mobNumber != null">mob_number,</if>
            <if test="smtpAddr != null">smtp_addr,</if>
            <if test="akont != null">akont,</if>
            <if test="loevm != null">loevm,</if>
            <if test="banks != null">banks,</if>
            <if test="bankl != null">bankl,</if>
            <if test="koinh != null">koinh,</if>
            <if test="bankn != null">bankn,</if>
            <if test="bkont != null">bkont,</if>
            <if test="xezer != null">xezer,</if>
            <if test="accname != null">accname,</if>
            <if test="efficient != null">efficient,</if>
<!--            <if test="createTime != null">create_time,</if>-->
<!--            <if test="updatetime != null">updatetime,</if>-->
            <if test="banka != null">banka,</if>
            <if test="zsfyxgys != null">zsfyxgys,</if>
            <if test="zyxlb != null">zyxlb,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="partner != null">#{partner},</if>
            <if test="bkvid != null">#{bkvid},</if>
            <if test="nameOrg1 != null">#{nameOrg1},</if>
            <if test="buSort1 != null">#{buSort1},</if>
            <if test="buGroup != null">#{buGroup},</if>
            <if test="idnumber != null">#{idnumber},</if>
            <if test="country != null">#{country},</if>
            <if test="langu != null">#{langu},</if>
            <if test="timeZone != null">#{timeZone},</if>
            <if test="steet != null">#{steet},</if>
            <if test="telNumber != null">#{telNumber},</if>
            <if test="mobNumber != null">#{mobNumber},</if>
            <if test="smtpAddr != null">#{smtpAddr},</if>
            <if test="akont != null">#{akont},</if>
            <if test="loevm != null">#{loevm},</if>
            <if test="banks != null">#{banks},</if>
            <if test="bankl != null">#{bankl},</if>
            <if test="koinh != null">#{koinh},</if>
            <if test="bankn != null">#{bankn},</if>
            <if test="bkont != null">#{bkont},</if>
            <if test="xezer != null">#{xezer},</if>
            <if test="accname != null">#{accname},</if>
            <if test="efficient != null">#{efficient},</if>
<!--            <if test="createTime != null">#{createTime},</if>-->
<!--            <if test="updatetime != null">#{updatetime},</if>-->
            <if test="banka != null">#{banka},</if>
            <if test="zsfyxgys != null">#{zsfyxgys},</if>
            <if test="zyxlb != null">#{zyxlb},</if>
         </trim>
    </insert>

    <update id="updateSupplierMD" parameterType="SupplierMD">
        update SupplierMD
        <trim prefix="SET" suffixOverrides=",">
            <if test="partner != null">partner = #{partner},</if>
            <if test="bkvid != null">bkvid = #{bkvid},</if>
            <if test="nameOrg1 != null">name_org1 = #{nameOrg1},</if>
            <if test="buSort1 != null">bu_sort1 = #{buSort1},</if>
            <if test="buGroup != null">bu_group = #{buGroup},</if>
            <if test="idnumber != null">idnumber = #{idnumber},</if>
            <if test="country != null">country = #{country},</if>
            <if test="langu != null">langu = #{langu},</if>
            <if test="timeZone != null">time_zone = #{timeZone},</if>
            <if test="steet != null">steet = #{steet},</if>
            <if test="telNumber != null">tel_number = #{telNumber},</if>
            <if test="mobNumber != null">mob_number = #{mobNumber},</if>
            <if test="smtpAddr != null">smtp_addr = #{smtpAddr},</if>
            <if test="akont != null">akont = #{akont},</if>
            <if test="loevm != null">loevm = #{loevm},</if>
            <if test="banks != null">banks = #{banks},</if>
            <if test="bankl != null">bankl = #{bankl},</if>
            <if test="koinh != null">koinh = #{koinh},</if>
            <if test="bankn != null">bankn = #{bankn},</if>
            <if test="bkont != null">bkont = #{bkont},</if>
            <if test="xezer != null">xezer = #{xezer},</if>
            <if test="accname != null">accname = #{accname},</if>
            <if test="efficient != null">efficient = #{efficient},</if>
<!--            <if test="createTime != null">create_time = #{createTime},</if>-->
<!--            <if test="updatetime != null">updatetime = #{updatetime},</if>-->
            <if test="banka != null">banka = #{banka},</if>
            <if test="zsfyxgys != null">zsfyxgys = #{zsfyxgys},</if>
            <if test="zyxlb != null">zyxlb = #{zyxlb},</if>
        </trim>
        where sid = #{sid}
    </update>

    <delete id="deleteSupplierMDBySid" parameterType="Long">
        delete from SupplierMD where sid = #{sid}
    </delete>

    <delete id="deleteSupplierMDBySids" parameterType="String">
        delete from SupplierMD where sid in 
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>