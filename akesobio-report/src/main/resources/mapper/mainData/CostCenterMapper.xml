<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.mainData.mapper.CostCenterMapper">
    
    <resultMap type="CostCenter" id="CostCenterResult">
        <result property="parentId"    column="parent_id"    />
        <result property="costCenterName"    column="cost_center_name"    />
        <result property="id"    column="id"    />
        <result property="area"    column="area"    />
        <result property="sapSuffixCode"    column="sap_suffix_code"    />
        <result property="supervisor"    column="supervisor"    />
        <result property="businessTerminalNode"    column="business_terminal_node"    />
        <result property="enabledFlag"    column="enabled_flag"    />
        <result property="fullName"    column="full_name"    />
        <result property="code"    column="code"    />
        <result property="ekpIds"    column="ekp_ids"    />
        <result property="businessTerminalNodeFlag"    column="business_terminal_node_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCostCenterVo">
        select parent_id, cost_center_name, id, area, sap_suffix_code, supervisor, business_terminal_node, enabled_flag, full_name, code, ekp_ids, business_terminal_node_flag, create_time, update_time from cost_center
    </sql>

    <select id="selectCostCenterList" parameterType="CostCenter" resultMap="CostCenterResult">
        <include refid="selectCostCenterVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="costCenterName != null  and costCenterName != ''"> and cost_center_name like concat('%', #{costCenterName}, '%')</if>
        </where>
    </select>
    
    <select id="selectCostCenterById" parameterType="Long" resultMap="CostCenterResult">
        <include refid="selectCostCenterVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCostCenter" parameterType="CostCenter">
        insert into cost_center
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="costCenterName != null">cost_center_name,</if>
            <if test="id != null">id,</if>
            <if test="area != null">area,</if>
            <if test="sapSuffixCode != null">sap_suffix_code,</if>
            <if test="supervisor != null">supervisor,</if>
            <if test="businessTerminalNode != null">business_terminal_node,</if>
            <if test="enabledFlag != null">enabled_flag,</if>
            <if test="fullName != null">full_name,</if>
            <if test="code != null">code,</if>
            <if test="ekpIds != null">ekp_ids,</if>
            <if test="businessTerminalNodeFlag != null">business_terminal_node_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="costCenterName != null">#{costCenterName},</if>
            <if test="id != null">#{id},</if>
            <if test="area != null">#{area},</if>
            <if test="sapSuffixCode != null">#{sapSuffixCode},</if>
            <if test="supervisor != null">#{supervisor},</if>
            <if test="businessTerminalNode != null">#{businessTerminalNode},</if>
            <if test="enabledFlag != null">#{enabledFlag},</if>
            <if test="fullName != null">#{fullName},</if>
            <if test="code != null">#{code},</if>
            <if test="ekpIds != null">#{ekpIds},</if>
            <if test="businessTerminalNodeFlag != null">#{businessTerminalNodeFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCostCenter" parameterType="CostCenter">
        update cost_center
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="costCenterName != null">cost_center_name = #{costCenterName},</if>
            <if test="area != null">area = #{area},</if>
            <if test="sapSuffixCode != null">sap_suffix_code = #{sapSuffixCode},</if>
            <if test="supervisor != null">supervisor = #{supervisor},</if>
            <if test="businessTerminalNode != null">business_terminal_node = #{businessTerminalNode},</if>
            <if test="enabledFlag != null">enabled_flag = #{enabledFlag},</if>
            <if test="fullName != null">full_name = #{fullName},</if>
            <if test="code != null">code = #{code},</if>
            <if test="ekpIds != null">ekp_ids = #{ekpIds},</if>
            <if test="businessTerminalNodeFlag != null">business_terminal_node_flag = #{businessTerminalNodeFlag},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCostCenterById" parameterType="Long">
        delete from cost_center where id = #{id}
    </delete>

    <delete id="deleteCostCenterByIds" parameterType="String">
        delete from cost_center where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>