<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ekp.mapper.SysCategoryMainMapper">
    
    <resultMap type="SysCategoryMain" id="EkpSysCategoryMainResult">
        <result property="id"    column="fd_id"    />
        <result property="name"    column="fd_name"    />
        <result property="desc"    column="fd_desc"    />
        <result property="hierarchyId"    column="fd_hierarchy_id"    />
        <result property="order"    column="fd_order"    />
        <result property="modelName"    column="fd_model_name"    />
        <result property="docCreateTime"    column="doc_create_time"    />
        <result property="docAlterTime"    column="doc_alter_time"    />
        <result property="isInheritMaintainer"    column="fd_isinherit_maintainer"    />
        <result property="isInheritUser"    column="fd_isinherit_user"    />
        <result property="authReaderFlag"    column="auth_reader_flag"    />
        <result property="docCreatorId"    column="doc_creator_id"    />
        <result property="docAlterorId"    column="doc_alteror_id"    />
        <result property="parentId"    column="fd_parent_id"    />
        <result property="authAreaId"    column="auth_area_id"    />
        <result property="nameCn"    column="fd_name_CN"    />
        <result property="nameUs"    column="fd_name_US"    />
    </resultMap>

    <sql id="selectEkpSysCategoryMainVo">
        select fd_id, fd_name, fd_desc, fd_hierarchy_id, fd_order, fd_model_name, doc_create_time, doc_alter_time, fd_isinherit_maintainer, fd_isinherit_user, auth_reader_flag, doc_creator_id, doc_alteror_id, fd_parent_id, auth_area_id, fd_name_CN, fd_name_US from sys_category_main
    </sql>
    <select id="selectCategoryList" resultMap="EkpSysCategoryMainResult">
        <include refid="selectEkpSysCategoryMainVo"/>
    </select>
</mapper>