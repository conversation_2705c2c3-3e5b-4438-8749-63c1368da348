<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ekp.mapper.KmReviewTemplateMapper">

    <resultMap type="KmReviewTemplate" id="EkpKmReviewTemplateResult">
        <result property="id" column="fd_id"/>
        <result property="syncDataToCalendarTime" column="sync_data_to_calendar_time"/>
        <result property="name" column="fd_name"/>
        <result property="desc" column="fd_desc"/>
        <result property="isAvailable" column="fd_is_available"/>
        <result property="canCircularize" column="fd_can_circularize"/>
        <result property="lableVisiable" column="fd_lable_visiable"/>
        <result property="feedbackModify" column="fd_feedback_modify"/>
        <result property="order" column="fd_order"/>
        <result property="numberPrefix" column="fd_number_prefix"/>
        <result property="docContent" column="doc_content"/>
        <result property="useForm" column="fd_use_form"/>
        <result property="useWord" column="fd_use_word"/>
        <result property="disableMobileForm" column="fd_disable_mobile_form"/>
        <result property="docCreatorId" column="doc_creator_id"/>
        <result property="categoryId" column="fd_category_id"/>
        <result property="docCreateTime" column="doc_create_time"/>
        <result property="authReaderFlag" column="auth_reader_flag"/>
        <result property="authTmpAttNodownload" column="auth_tmp_att_nodownload"/>
        <result property="authTmpAttNocopy" column="auth_tmp_att_nocopy"/>
        <result property="authTmpAttNoprint" column="auth_tmp_att_noprint"/>
        <result property="isExternal" column="fd_is_external"/>
        <result property="externalUrl" column="fd_external_url"/>
        <result property="isImport" column="fd_is_import"/>
        <result property="unImportFieldIds" column="fd_un_import_field_ids"/>
        <result property="unImportFieldNames" column="fd_un_import_field_names"/>
        <result property="signEnable" column="fd_sign_enable"/>
        <result property="docAlterorId" column="doc_alteror_id"/>
        <result property="docAlterTime" column="doc_alter_time"/>
        <result property="changeReaderFlag" column="fd_change_reader_flag"/>
        <result property="rbpFlag" column="fd_rbp_flag"/>
        <result property="changeAtt" column="fd_change_att"/>
        <result property="authAreaId" column="auth_area_id"/>
        <result property="titleRegulation" column="fd_title_regulation"/>
        <result property="titleRegulationName" column="fd_title_regulation_name"/>
        <result property="isMobileCreate" column="fd_is_mobile_create"/>
        <result property="isMobileApprove" column="fd_is_mobile_approve"/>
        <result property="isMobileView" column="fd_is_mobile_view"/>
        <result property="isCopyDoc" column="fd_is_copy_doc"/>
        <result property="nameCn" column="fd_name_CN"/>
        <result property="nameUs" column="fd_name_US"/>
        <result property="code" column="fd_code"/>
    </resultMap>

    <sql id="selectEkpKmReviewTemplateVo">
        select fd_id,
               sync_data_to_calendar_time,
               fd_name,
               fd_desc,
               fd_is_available,
               fd_can_circularize,
               fd_lable_visiable,
               fd_feedback_modify,
               fd_order,
               fd_number_prefix,
               doc_content,
               fd_use_form,
               fd_use_word,
               fd_disable_mobile_form,
               doc_creator_id,
               fd_category_id,
               doc_create_time,
               auth_reader_flag,
               auth_tmp_att_nodownload,
               auth_tmp_att_nocopy,
               auth_tmp_att_noprint,
               fd_is_external,
               fd_external_url,
               fd_is_import,
               fd_un_import_field_ids,
               fd_un_import_field_names,
               fd_sign_enable,
               doc_alteror_id,
               doc_alter_time,
               fd_change_reader_flag,
               fd_rbp_flag,
               fd_change_att,
               auth_area_id,
               fd_title_regulation,
               fd_title_regulation_name,
               fd_is_mobile_create,
               fd_is_mobile_approve,
               fd_is_mobile_view,
               fd_is_copy_doc,
               fd_name_CN,
               fd_name_US,
               fd_code
        from km_review_template
    </sql>
    <select id="selectTemplateByIds" resultMap="EkpKmReviewTemplateResult">
        <include refid="selectEkpKmReviewTemplateVo"/>
        <where>
            <if test="list != null and list.size > 0">
                <foreach collection="list" item="id" open="fd_category_id IN (" close=")" separator=",">
                    <!-- 每次遍历要拼接的串 -->
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectAllTemplate" resultMap="EkpKmReviewTemplateResult">
        <include refid="selectEkpKmReviewTemplateVo"/>
        where fd_is_available = 1
    </select>


</mapper>