<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ekp.mapper.SysNotifyMapper">

    <resultMap type="SysNotify" id="EkpSysNotifyResult">
        <result property="id" column="fd_id"/>
        <result property="appName" column="fd_app_name"/>
        <result property="modelName" column="fd_model_name"/>
        <result property="modelId" column="fd_model_id"/>
        <result property="key" column="fd_key"/>
        <result property="parameter1" column="fd_parameter1"/>
        <result property="parameter2" column="fd_parameter2"/>
        <result property="createTime" column="fd_create_time"/>
        <result property="subject" column="fd_subject"/>
        <result property="type" column="fd_type"/>
        <result property="link" column="fd_link"/>
        <result property="mobileLink" column="fd_mobile_link"/>
        <result property="padLink" column="fd_pad_link"/>
        <result property="bundle" column="fd_bundle"/>
        <result property="replaceText" column="fd_replace_text"/>
        <result property="md5" column="fd_md5"/>
        <result property="delFlag" column="fd_del_flag"/>
        <result property="level" column="fd_level"/>
        <result property="creatorId" column="doc_creator_id"/>
        <result property="extendContent" column="fd_extend_content"/>
        <result property="lang" column="fd_lang"/>
        <result property="cateName" column="fd_cate_name"/>
        <result property="cateId" column="fd_cate_id"/>
        <result property="templateName" column="fd_template_name"/>
        <result property="templateId" column="fd_template_id"/>
        <result property="hierarchyId" column="fd_hierarchy_id"/>
        <result property="subjectCn" column="fd_subject_CN"/>
        <result property="extendContentCn" column="fd_extend_content_CN"/>
        <result property="subjectUs" column="fd_subject_US"/>
        <result property="extendContentUs" column="fd_extend_content_US"/>
        <result property="creatorName" column="creator_name"/>
        <result property="docRead" column="doc_read"/>
    </resultMap>

    <sql id="selectEkpSysNotifyTodoVo">
        select fd_id,
               fd_app_name,
               fd_model_name,
               fd_model_id,
               fd_key,
               fd_parameter1,
               fd_parameter2,
               fd_create_time,
               fd_subject,
               fd_type,
               fd_link,
               fd_mobile_link,
               fd_pad_link,
               fd_bundle,
               fd_replace_text,
               fd_md5,
               fd_del_flag,
               fd_level,
               doc_creator_id,
               fd_extend_content,
               fd_lang,
               fd_cate_name,
               fd_cate_id,
               fd_template_name,
               fd_template_id,
               fd_hierarchy_id,
               fd_subject_CN,
               fd_extend_content_CN,
               fd_subject_US,
               fd_extend_content_US
        from sys_notify_todo
    </sql>

    <sql id="selectEkpSysNotifySystemTodoVo">
        select fd_id,
               fd_app_name,
               fd_model_name,
               fd_model_id,
               fd_key,
               fd_parameter1,
               fd_parameter2,
               fd_create_time,
               fd_subject,
               fd_type,
               fd_link,
               fd_mobile_link,
               fd_pad_link,
               fd_bundle,
               fd_replace_text,
               fd_md5,
               fd_del_flag,
               fd_level,
               doc_creator_id,
               fd_extend_content,
               fd_lang,
               fd_subject_CN,
               fd_extend_content_CN,
               fd_subject_US,
               fd_extend_content_US
        from sys_notify_system_todo
    </sql>

    <select id="selectTodoByHandlerId" parameterType="String" resultMap="EkpSysNotifyResult">
        SELECT a.fd_id,
               a.fd_model_name,
               a.fd_model_id,
               a.fd_create_time,
               a.fd_subject,
               a.fd_type,
               a.fd_level,
               a.fd_link,
               a.doc_creator_id,
               a.fd_extend_content,
               c.fd_name as creator_name,
               ISNULL(d.count_num, 0) AS doc_read
        FROM sys_notify_todotarget b
                 right JOIN sys_notify_todo a on a.fd_id = b.fd_todoid
                 LEFT JOIN sys_org_element c on a.doc_creator_id = c.fd_id
                 LEFT JOIN (SELECT fd_todoid, COUNT(*) AS count_num
                            FROM sys_notify_read_todotarget
                            WHERE fd_orgid =  #{handlerId}
                            GROUP BY fd_todoid) d ON d.fd_todoid = a.fd_id
        WHERE b.fd_orgid = #{handlerId}
        ORDER BY a.fd_create_time DESC
    </select>
    <select id="selectDoneByHandlerId" parameterType="String" resultMap="EkpSysNotifyResult">
        SELECT a.fd_id,
               a.fd_model_name,
               a.fd_model_id,
               a.fd_create_time,
               a.fd_subject,
               a.fd_type,
               a.fd_level,
               a.fd_link,
               a.doc_creator_id,
               a.fd_extend_content,
               c.fd_name as creator_name
        FROM sys_notify_todo_done_info b
                 right JOIN sys_notify_todo a on a.fd_id = b.fd_todoid
                 LEFT JOIN sys_org_element c on a.doc_creator_id = c.fd_id
        WHERE b.fd_elementid = #{handlerId}
        ORDER BY b.fd_finish_time DESC
    </select>
    <select id="selectNoticeByTargetId" parameterType="String" resultMap="EkpSysNotifyResult">
        SELECT a.fd_id,
               a.fd_model_name,
               a.fd_model_id,
               a.fd_create_time,
               a.fd_subject,
               a.fd_type,
               a.fd_level,
               a.fd_link,
               a.doc_creator_id,
               a.fd_extend_content,
               c.fd_name as creator_name
        FROM sys_notify_systodotarget b
                 right JOIN sys_notify_system_todo a on a.fd_id = b.fd_todoid
                 LEFT JOIN sys_org_element c on a.doc_creator_id = c.fd_id
        WHERE b.fd_orgid = #{targetId}
        ORDER BY a.fd_create_time DESC
    </select>


    <select id="selectNotifyById" resultMap="EkpSysNotifyResult">
        <include refid="selectEkpSysNotifyTodoVo"/>
        where fd_id = #{id}
    </select>

    <select id="selectSystemNotifyById" resultMap="EkpSysNotifyResult">
        <include refid="selectEkpSysNotifySystemTodoVo"/>
        where fd_id = #{id}
    </select>



    <select id="selectTargetTodo" resultType="java.lang.Integer">
        select count(0)
        from sys_notify_todotarget
        where fd_orgid = #{targetId}
          and fd_todoid = #{todoId}
    </select>

    <select id="selectSystemTargetTodo" resultType="java.lang.Integer">
        select count(0)
        from sys_notify_systodotarget
        where fd_orgid = #{targetId}
          and fd_todoid = #{todoId}
    </select>

    <select id="selectReadInfo" resultType="java.lang.Integer">
        select count(0)
        from sys_notify_read_todotarget
        where fd_orgid = #{targetId}
          and fd_todoid = #{todoId}
    </select>


</mapper>