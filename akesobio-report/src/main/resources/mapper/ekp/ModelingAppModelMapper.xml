<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ekp.mapper.ModelingAppModelMapper">
    
    <resultMap type="ModelingAppModel" id="ModelingAppModelResult">
        <result property="id"    column="fd_id"    />
        <result property="name"    column="fd_name"    />
        <result property="baseModelId"    column="fd_base_model_id"    />
        <result property="icon"    column="fd_icon"    />
        <result property="subjectRegulationValue"    column="fd_subject_regulation_value"    />
        <result property="subjectRegulationText"    column="fd_subject_regulation_text"    />
        <result property="mechanismConfig"    column="fd_mechanism_config"    />
        <result property="enableFlow"    column="fd_enable_flow"    />
        <result property="valid"    column="fd_valid"    />
        <result property="docCreateTime"    column="doc_create_time"    />
        <result property="applicationId"    column="fd_application_id"    />
        <result property="docCreatorId"    column="doc_creator_id"    />
        <result property="authReaderFlag"    column="auth_reader_flag"    />
        <result property="authTmpAttNodownload"    column="auth_tmp_att_nodownload"    />
        <result property="authTmpAttNocopy"    column="auth_tmp_att_nocopy"    />
        <result property="authTmpAttNoprint"    column="auth_tmp_att_noprint"    />
        <result property="changeReaderFlag"    column="fd_change_reader_flag"    />
        <result property="changeEditorFlag"    column="fd_change_editor_flag"    />
        <result property="rbpFlag"    column="fd_rbp_flag"    />
        <result property="changeAtt"    column="fd_change_att"    />
        <result property="tree"    column="fd_tree"    />
        <result property="tableName"    column="fd_table_name"    />
        <result property="authNotReaderFlag"    column="auth_not_reader_flag"    />
        <result property="docAlterorId"    column="doc_alteror_id"    />
        <result property="docAlterTime"    column="doc_alter_time"    />
    </resultMap>

    <sql id="selectModelingAppModelVo">
        select fd_id, fd_name, fd_base_model_id, fd_icon, fd_subject_regulation_value, fd_subject_regulation_text, fd_mechanism_config, fd_enable_flow, fd_valid, doc_create_time, fd_application_id, doc_creator_id, auth_reader_flag, auth_tmp_att_nodownload, auth_tmp_att_nocopy, auth_tmp_att_noprint, fd_change_reader_flag, fd_change_editor_flag, fd_rbp_flag, fd_change_att, fd_tree, fd_table_name, auth_not_reader_flag, doc_alteror_id, doc_alter_time from modeling_app_model
    </sql>


    <select id="selectAll" parameterType="String" resultMap="ModelingAppModelResult">
        <include refid="selectModelingAppModelVo"/>
    </select>

    <select id="ekpFlwhtsp" resultType="java.util.Map">
        SELECT * FROM ekp_flwhtsp
    </select>

    <!-- 查询父表ekp_flwhtsp -->
    <select id="selectParentTable" resultType="java.util.Map">
        SELECT * FROM ekp_flwhtsp WHERE
        fd_3996ac8c37d5a0 = 'YXHT-20250409001'
    </select>
    
    <!-- 查询子表ekp_kp_flwhtsp_77be2bc29a -->
    <select id="selectChildTable1" resultType="java.util.Map">
        SELECT * FROM ekp_kp_flwhtsp_77be2bc29a
    </select>
    
    <!-- 查询子表ekp_kp_flwhtsp_a4e5a433e2 -->
    <select id="selectChildTable2" resultType="java.util.Map">
        SELECT * FROM ekp_kp_flwhtsp_a4e5a433e2
    </select>

    <!-- 动态查询主表数据 -->
    <select id="selectDynamicParentTable" parameterType="map" resultType="java.util.Map">
        SELECT * FROM ${mainTableName}
        <where>
            <if test="documentNumber != null and documentNumber != '' and documentNumberField != null and documentNumberField != ''">
                AND ${documentNumberField} = #{documentNumber}
            </if>
        </where>
    </select>

    <!-- 动态查询子表数据 -->
    <select id="selectDynamicChildTable" parameterType="map" resultType="java.util.Map">
        SELECT * FROM ${childTableName}
        <where>
            <if test="parentIds != null and parentIds.size() > 0">
                AND fd_parent_id IN
                <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
                    #{parentId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>