<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ekp.mapper.IITProjectMapper">

    <resultMap id="ProjectAmountResultMap" type="com.akesobio.report.ekp.domain.entity.IITProjectAmount">
        <result column="project_code" property="projectCode"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="valid_amount" property="validAmount"/>
        <result column="valid_amount_limit" property="validAmountLimit"/>
    </resultMap>

    <select id="getProjectAmountByCode" resultMap="ProjectAmountResultMap">
        WITH ValidLineItems AS (
            SELECT
                m.fd_id,
                m.fd_3d8f959baf9d86 AS project_code,
                m.fd_3d8f95a9e7c1dc AS total_amount,
                CAST(d.fd_3d8f95c07bd298 AS DECIMAL(18, 2)) AS line_amount,
                d.fd_3d8f9756ff262e AS line_type
            FROM
                ekp_9b39c5f35bc423d80a47 m
                    JOIN
                ekp_c423d80a47_93f1033ffe d ON m.fd_id = d.fd_parent_id
            WHERE
                m.fd_3d8f959baf9d86 = #{projectCode}
              AND d.fd_3d8f9756ff262e NOT IN ('I010', 'I011', 'I000')
        )
        SELECT
            project_code,
            MAX(total_amount) AS total_amount,
            SUM(line_amount) AS valid_amount,
            SUM(line_amount) * 1.025 AS valid_amount_limit
        FROM
            ValidLineItems
        GROUP BY
            project_code
    </select>

    <resultMap id="TotalContractAmount" type="com.akesobio.report.ekp.domain.entity.TotalContractAmount">
        <result column="project_code" property="projectCode"/>
        <result column="transaction_amount" property="transactionAmount"/>
    </resultMap>

    <select id="getTotalContractAmountByProjectCode" resultMap="TotalContractAmount">
--         SELECT
--             fd_39fa49cd09fe7c AS project_code,
--             ISNULL(fd_3997760e7cd64e, 0) AS transaction_amount
--         FROM
--             contract_table
--         WHERE
--             fd_39fa49cd09fe7c = #{projectCode}
        SELECT
            c.fd_39fa49cd09fe7c AS project_code,
            SUM(ISNULL(c.fd_3997760e7cd64e, 0)) AS total_transaction_amount
        FROM
            ekp_af144534fd14f5ea8dc0 c
                JOIN
            modeling_model_main mm ON mm.fd_id = c.fd_id
        WHERE
            c.fd_39fa49cd09fe7c = #{projectCode}
          AND mm.doc_status IN ('20', '30')
        GROUP BY
            c.fd_39fa49cd09fe7c
    </select>

</mapper>