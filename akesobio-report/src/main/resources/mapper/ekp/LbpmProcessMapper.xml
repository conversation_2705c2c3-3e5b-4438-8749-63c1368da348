<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ekp.mapper.LbpmProcessMapper">

    <resultMap type="LbpmProcess" id="EkpLbpmProcessResult">
        <result property="fdId" column="fd_id"/>
        <result property="fdName" column="fd_name"/>
        <result property="docSubject" column="doc_subject"/>
        <result property="fdTemplateId" column="fd_template_id"/>
        <result property="fdStatus" column="fd_status"/>
        <result property="fdModelName" column="fd_model_name"/>
        <result property="fdModelId" column="fd_model_id"/>
        <result property="fdTemplateModelId" column="fd_template_model_id"/>
        <result property="fdKey" column="fd_key"/>
        <result property="fdDetail" column="fd_detail"/>
        <result property="fdCreateTime" column="fd_create_time"/>
        <result property="fdCreateYear" column="fd_create_year"/>
        <result property="fdCreateMonth" column="fd_create_month"/>
        <result property="fdLastHandleTime" column="fd_last_handle_time"/>
        <result property="fdEndedTime" column="fd_ended_time"/>
        <result property="fdCreatorId" column="fd_creator_id"/>
        <result property="fdLoadType" column="fd_load_type"/>
        <result property="fdCostTime" column="fd_cost_time"/>
        <result property="fdWorkTime" column="fd_work_time"/>
        <result property="fdEfficiency" column="fd_efficiency"/>
        <result property="fdIdentityId" column="fd_identity_id"/>
        <result property="fdHierarchyId" column="fd_hierarchy_id"/>
        <result property="fdSourceId" column="fd_source_id"/>
        <result property="fdParentNodeFdid" column="fd_parent_node_fdid"/>
        <result property="fdParentNodeid" column="fd_parent_nodeid"/>
        <result property="fdSubStatus" column="fd_sub_status"/>
        <result property="docDeleteFlag" column="doc_delete_flag"/>
        <result property="fdParentid" column="fd_parentid"/>
        <result property="docStatus" column="doc_status"/>
        <result property="currentProcessor" column="current_processor"/>
    </resultMap>

    <resultMap id="HrProcessResult" type="com.akesobio.report.ekp.domain.dto.ProcessHrDTO">
        <result property="id" column="id"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="docSubject" column="doc_subject"/>
        <result property="currentProcessor" column="current_processor"/>
        <result property="modelClass" column="model_class"/>
        <result property="kmTemplateName" column="km_template_name"/>
        <result property="modelingTemplateName" column="modeling_template_name"/>
        <result property="hrTemplateName" column="hr_ratify_name"/>
        <result property="kmNumber" column="km_number"/>
        <result property="modelingNumber" column="modeling_number"/>
        <result property="hrRatifyNumber" column="hr_ratify_number"/>
    </resultMap>
    <sql id="selectEkpLbpmProcessVo">
        select fd_id,
               fd_name,
               doc_subject,
               fd_template_id,
               fd_status,
               fd_model_name,
               fd_model_id,
               fd_template_model_id,
               fd_key,
               fd_detail,
               fd_create_time,
               fd_create_year,
               fd_create_month,
               fd_last_handle_time,
               fd_ended_time,
               fd_creator_id,
               fd_load_type,
               fd_cost_time,
               fd_work_time,
               fd_efficiency,
               fd_identity_id,
               fd_hierarchy_id,
               fd_source_id,
               fd_parent_node_fdid,
               fd_parent_nodeid,
               fd_sub_status,
               doc_delete_flag,
               fd_parentid,
               doc_status
        from lbpm_process
    </sql>

    <select id="selectProcessByFdCreatorId" parameterType="String" resultMap="EkpLbpmProcessResult">
        select tab1.fd_id,
               tab1.doc_subject,
               tab1.fd_status,
               tab1.fd_model_name,
               tab1.doc_status,
               tab1.fd_create_time,
               tab1.fd_last_handle_time,
               STUFF((SELECT DISTINCT ',' + fd_name
                      FROM sys_org_element
                      WHERE fd_id IN
                            (SELECT fd_expected_id FROM lbpm_workitem WHERE fd_node_id = tab2.fd_id)
                      FOR XML PATH ( '' )), 1, 1, '') as current_processor
        from lbpm_process tab1
                 LEFT JOIN lbpm_node tab2 ON tab1.fd_id = tab2.fd_process_id
        where tab1.fd_creator_id = #{creatorId}
          and tab1.doc_delete_flag = 0
          and tab1.fd_last_handle_time is not NULL
        ORDER BY tab1.fd_create_time DESC
    </select>

    <select id="selectRunningProcessByTemplateIds" resultMap="EkpLbpmProcessResult">
        <include refid="selectEkpLbpmProcessVo"/>
        WHERE ((fd_status = 20 OR doc_status = 20) AND doc_delete_flag = 0)
        <choose>
            <when test="kmReviews != null and kmReviews.size > 0 and modelings != null and modelings.size > 0">
                AND (fd_template_model_id IN
                <foreach collection="kmReviews" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
                OR fd_id IN (
                SELECT process.fd_id
                FROM lbpm_process process
                LEFT JOIN modeling_model_main main ON process.fd_id = main.fd_id
                WHERE main.fd_model_id IN
                <foreach collection="modelings" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
                )
                OR fd_parentid IN (
                select fd_id from lbpm_process WHERE doc_delete_flag = 0
                AND (fd_template_model_id IN
                <foreach collection="kmReviews" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
                OR fd_id IN (
                SELECT process.fd_id
                FROM lbpm_process process
                LEFT JOIN modeling_model_main main ON process.fd_id = main.fd_id
                WHERE main.fd_model_id IN
                <foreach collection="modelings" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
                ))))
            </when>
            <otherwise>
                <if test="kmReviews != null and kmReviews.size > 0">
                    AND fd_template_model_id IN
                    <foreach collection="kmReviews" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                    OR fd_parentid IN (
                    select fd_id from lbpm_process WHERE fd_template_model_id IN
                    <foreach collection="kmReviews" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                    )
                </if>
                <if test="modelings != null and modelings.size > 0">
                    AND fd_id IN (
                    SELECT process.fd_id
                    FROM [dbo].[lbpm_process] process
                    LEFT JOIN modeling_model_main main ON process.fd_id = main.fd_id
                    WHERE main.fd_model_id IN
                    <foreach collection="modelings" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                    )
                    OR fd_parentid IN (
                    select fd_id from lbpm_process
                    WHERE fd_id IN (
                    SELECT process.fd_id
                    FROM [dbo].[lbpm_process] process
                    LEFT JOIN modeling_model_main main ON process.fd_id = main.fd_id
                    WHERE main.fd_model_id IN
                    <foreach collection="modelings" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                    ))
                </if>
            </otherwise>
        </choose>

        order by fd_create_time desc
    </select>

    <select id="selectHrRunningProcess" resultMap="HrProcessResult">
        select t1.fd_id as id,
        t6.fd_name as creator,
        t1.fd_create_time as create_time,
        t1.doc_subject as doc_subject,
        t11.current_processor,
        t1.fd_model_name as model_class,
        t3.fd_name as km_template_name,
        t5.fd_name as modeling_template_name,
        t8.fd_name as hr_ratify_name,
        t2.fd_number as km_number,
        t4.fd_number as modeling_number,
        t7.doc_number as hr_ratify_number
        from lbpm_process t1
        left join km_review_main t2 on t2.fd_id = t1.fd_id
        left join km_review_template t3 on t3.fd_id = t2.fd_template_id
        left join modeling_model_main t4 on t4.fd_id = t1.fd_id
        left join modeling_app_model t5 on t5.fd_id = t4.fd_model_id
        left join sys_org_element t6 on t6.fd_id = t1.fd_creator_id
        left join hr_ratify_main t7 on t7.fd_id = t1.fd_id
        left join hr_ratify_template t8 on t8.fd_id = t7.doc_template_id
        left join (SELECT a.fd_process_id,  STRING_AGG(a.fd_fact_node_name, ';')  as current_node
                   FROM lbpm_node a GROUP BY a.fd_process_id) t9 on t9.fd_process_id = t1.fd_id
        left join (SELECT w.fd_process_id, STRING_AGG(c.fd_name, ';') AS current_processor
        FROM lbpm_workitem w
        LEFT JOIN sys_org_element c ON c.fd_id = w.fd_expected_id
        GROUP BY w.fd_process_id) t11 on t11.fd_process_id = t1.fd_id
        WHERE ((t1.fd_status = 20 OR t1.doc_status = 20) AND t1.doc_delete_flag = 0 AND t9.current_node not like '%起草节点%')
        AND (
        <trim suffixOverrides="OR">
            <if test="template.kmTemplateIds != null and template.kmTemplateIds.size > 0">
                t3.fd_id in
                <foreach collection="template.kmTemplateIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
                OR
            </if>
            <if test="template.modelingTemplateIds != null and template.modelingTemplateIds.size > 0">
                t5.fd_id in
                <foreach collection="template.modelingTemplateIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
                OR
            </if>
            <if test="template.hrRatifyModelIds != null and template.hrRatifyModelIds.size > 0">
                t8.fd_id in
                <foreach collection="template.hrRatifyModelIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </trim>
        )
        ORDER BY t1.fd_create_time desc
    </select>
</mapper>