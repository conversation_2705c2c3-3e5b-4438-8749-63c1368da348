<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ekp.mapper.EkpCommonMapper">

    <select id="ekpBmcbzx" resultType="map">
        SELECT fd_3d6cf9489aeb7c 'dept_name',fd_3d6cf941698e32 'sap_suffix_name',fd_3d6cf947a76d3a 'sap_suffix_code',fd_3d75996eb4915c 'area_code', fd_3d6cf949ec0306 'enabled_flag'
        FROM ekp_bmcbzx
    </select>

    <select id="ekpUser" resultType="map">
        SELECT a.*,b.fd_name_CN dept_name
        FROM sys_org_element a
                 LEFT JOIN sys_org_element b ON b.fd_id = a.fd_parentid
                 LEFT JOIN sys_org_element c ON c.fd_id = a.fd_parentorgid
                 LEFT JOIN sys_org_element d ON d.fd_id = a.fd_this_leaderid
        WHERE a.fd_org_type = '8'
          AND a.fd_is_available = '1'
    </select>

    <select id="ekpDept" resultType="map">
        SELECT * FROM sys_org_element
                 WHERE fd_org_type = '2' AND fd_is_available = '1'
    </select>

    <select id="sysNotifyTodo" resultType="map">
        SELECT * FROM sys_notify_todo WHERE fd_app_name = '云简业财'
    </select>

    <select id="selectAllHrStaffPersonInfo" resultType="map">
        SELECT * FROM [dbo].[hr_staff_person_info]
    </select>
</mapper>