<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ekp.mapper.ApprovalRecordMapper">

    <resultMap id="approvalRecordMap" type="com.akesobio.report.ekp.domain.dto.ApprovalRecord">
        <result column="fd_fact_node_name" property="nodeName"/>
        <result column="approve_time" property="approveTime"/>
        <result column="fd_create_time" property="createTime"/>
        <result column="fd_staff_no" property="staffNo"/>
    </resultMap>


    <select id="findFdIdByDocumentNumber" resultType="String">
        SELECT fd_id from ${dynamicTableName} where ${filedName} = #{documentNumber}
    </select>

    <select id="findApprovalRecordsByProcessId" resultMap="approvalRecordMap">
        SELECT
            lan.fd_fact_node_name,
            DATEADD(SECOND, lan.fd_cost_time/1000, lan.fd_create_time) AS approve_time,
            lan.fd_create_time,
            hspi.fd_staff_no
        FROM
            ${dynamicTableName} edc LEFT JOIN
            lbpm_audit_note lan ON edc.fd_id = lan.fd_process_id
                                    LEFT JOIN
            hr_staff_person_info hspi ON lan.fd_handler_id = hspi.fd_id
        WHERE
            edc.fd_id = #{processId}
        ORDER BY
            lan.fd_create_time ASC
    </select>

<!--    <select id="findApprovalRecordsByProcessId" resultMap="approvalRecordMap">-->
<!--        SELECT-->
<!--            lan.fd_fact_node_name,-->
<!--            DATEADD(SECOND, lan.fd_cost_time/1000, lan.fd_create_time) AS approve_time,-->
<!--            lan.fd_create_time,-->
<!--            hspi.fd_staff_no-->
<!--        FROM-->
<!--            ekp_flwhtsp edc-->
<!--                LEFT JOIN-->
<!--            lbpm_audit_note lan ON edc.fd_id = lan.fd_process_id-->
<!--                LEFT JOIN-->
<!--            hr_staff_person_info hspi ON lan.fd_handler_id = hspi.fd_id-->
<!--        WHERE-->
<!--            edc.fd_3996ac8c37d5a0 = #{processId}-->
<!--        ORDER BY-->
<!--            lan.fd_create_time ASC-->
<!--    </select>-->
    

</mapper>