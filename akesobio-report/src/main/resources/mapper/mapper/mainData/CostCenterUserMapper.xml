<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.mainData.mapper.CostCenterUserMapper">
    
    <resultMap type="CostCenterUser" id="CostCenterUserResult">
        <result property="costCenterId"    column="cost_center_id"    />
        <result property="user"    column="user"    />
        <result property="id"    column="id"    />
    </resultMap>

    <sql id="selectCostCenterUserVo">
        select cost_center_id, user, id from cost_center_user
    </sql>

    <select id="selectCostCenterUserList" parameterType="CostCenterUser" resultMap="CostCenterUserResult">
        <include refid="selectCostCenterUserVo"/>
        <where>  
            <if test="costCenterId != null "> and cost_center_id = #{costCenterId}</if>
            <if test="user != null  and user != ''"> and user = #{user}</if>
        </where>
    </select>
    
    <select id="selectCostCenterUserById" parameterType="Long" resultMap="CostCenterUserResult">
        <include refid="selectCostCenterUserVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCostCenterUser" parameterType="CostCenterUser">
        insert into cost_center_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="costCenterId != null">cost_center_id,</if>
            <if test="user != null">user,</if>
            <if test="id != null">id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="costCenterId != null">#{costCenterId},</if>
            <if test="user != null">#{user},</if>
            <if test="id != null">#{id},</if>
         </trim>
    </insert>

    <update id="updateCostCenterUser" parameterType="CostCenterUser">
        update cost_center_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="costCenterId != null">cost_center_id = #{costCenterId},</if>
            <if test="user != null">user = #{user},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCostCenterUserById" parameterType="Long">
        delete from cost_center_user where id = #{id}
    </delete>

    <delete id="deleteCostCenterUserByIds" parameterType="String">
        delete from cost_center_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>