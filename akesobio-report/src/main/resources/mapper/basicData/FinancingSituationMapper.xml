<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.basicData.mapper.FinancingSituationMapper">

    <resultMap type="FinancingSituation" id="FinancingSituationResult">
        <result property="id" column="id"/>
        <result property="companyName" column="companyName"/>
        <result property="companyAbbreviation" column="companyAbbreviation"/>
        <result property="companyCode" column="companyCode"/>
        <result property="yearTime" column="yearTime"/>
        <result property="businessType" column="businessType"/>
        <result property="creditExposure" column="creditExposure"/>
        <result property="contractSigningAmount" column="contractSigningAmount"/>
        <result property="withdrawalSituation" column="withdrawalSituation"/>
        <result property="repaymentSituation" column="repaymentSituation"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="loanBalance" column="loanBalance"/>
    </resultMap>

    <sql id="selectFinancingSituationVo">
        select id,
               companyName,
               companyAbbreviation,
               companyCode,
               yearTime,
               businessType,
               creditExposure,
               contractSigningAmount,
               withdrawalSituation,
               repaymentSituation,
               deleteStatus,
               loanBalance
        from financing_situation
    </sql>

    <select id="selectFinancingSituationList" parameterType="FinancingSituation" resultMap="FinancingSituationResult">
        <include refid="selectFinancingSituationVo"/>
        <where>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="companyAbbreviation != null  and companyAbbreviation != ''">and companyAbbreviation like
                concat('%', #{companyAbbreviation}, '%')
            </if>
            <if test="companyCode != null  and companyCode != ''">and companyCode like concat('%', #{companyCode},
                '%')
            </if>
            <if test="yearTime != null ">and yearTime = #{yearTime}</if>
            <if test="businessType != null  and businessType != ''">and businessType like concat('%', #{businessType},
                '%')
            </if>
            <if test="creditExposure != null ">and creditExposure = #{creditExposure}</if>
            <if test="contractSigningAmount != null ">and contractSigningAmount = #{contractSigningAmount}</if>
            <if test="withdrawalSituation != null ">and withdrawalSituation = #{withdrawalSituation}</if>
            <if test="repaymentSituation != null ">and repaymentSituation = #{repaymentSituation}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
        </where>
    </select>

    <select id="selectFinancingSituationById" parameterType="Integer" resultMap="FinancingSituationResult">
        <include refid="selectFinancingSituationVo"/>
        where id = #{id}
    </select>

    <insert id="insertFinancingSituation" parameterType="FinancingSituation">
        insert into financing_situation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyAbbreviation != null">companyAbbreviation,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="yearTime != null">yearTime,</if>
            <if test="businessType != null">businessType,</if>
            <if test="creditExposure != null">creditExposure,</if>
            <if test="contractSigningAmount != null">contractSigningAmount,</if>
            <if test="withdrawalSituation != null">withdrawalSituation,</if>
            <if test="repaymentSituation != null">repaymentSituation,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="loanBalance != null">loanBalance,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyAbbreviation != null">#{companyAbbreviation},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="yearTime != null">#{yearTime},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="creditExposure != null">#{creditExposure},</if>
            <if test="contractSigningAmount != null">#{contractSigningAmount},</if>
            <if test="withdrawalSituation != null">#{withdrawalSituation},</if>
            <if test="repaymentSituation != null">#{repaymentSituation},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="loanBalance != null">#{loanBalance},</if>
        </trim>
    </insert>

    <update id="updateFinancingSituation" parameterType="FinancingSituation">
        update financing_situation
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyAbbreviation != null">companyAbbreviation = #{companyAbbreviation},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="yearTime != null">yearTime = #{yearTime},</if>
            <if test="businessType != null">businessType = #{businessType},</if>
            <if test="creditExposure != null">creditExposure = #{creditExposure},</if>
            <if test="contractSigningAmount != null">contractSigningAmount = #{contractSigningAmount},</if>
            <if test="withdrawalSituation != null">withdrawalSituation = #{withdrawalSituation},</if>
            <if test="repaymentSituation != null">repaymentSituation = #{repaymentSituation},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
            <if test="loanBalance != null">loanBalance = #{loanBalance},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinancingSituationById" parameterType="Integer">
        delete
        from financing_situation
        where id = #{id}
    </delete>

    <delete id="deleteFinancingSituationByIds" parameterType="String">
        delete from financing_situation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>