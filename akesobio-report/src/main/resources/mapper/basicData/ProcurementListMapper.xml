<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.basicData.mapper.ProcurementListMapper">
    
    <resultMap type="ProcurementList" id="ProcurementListResult">
        <result property="id"    column="id"    />
        <result property="remarks"    column="remarks"    />
        <result property="companyName"    column="companyName"    />
        <result property="feeAttributableYear"    column="feeAttributableYear"    />
        <result property="voucherNo"    column="voucherNo"    />
        <result property="accountDate"    column="accountDate"    />
        <result property="paymentDate"    column="paymentDate"    />
        <result property="oaProcessNumber"    column="oaProcessNumber"    />
        <result property="abstractTo"    column="abstractTo"    />
        <result property="paymentAmount"    column="paymentAmount"    />
        <result property="invoiceType"    column="invoiceType"    />
        <result property="taxRate"    column="taxRate"    />
        <result property="excludingTaxAmount"    column="excludingTaxAmount"    />
        <result property="supplierName"    column="supplierName"    />
        <result property="projectNo"    column="projectNo"    />
        <result property="paymentMilestones"    column="paymentMilestones"    />
        <result property="contractCode"    column="contractCode"    />
        <result property="contractType"    column="contractType"    />
        <result property="totalContractAmount"    column="totalContractAmount"    />
    </resultMap>

    <sql id="selectProcurementListVo">
        select id, remarks, companyName, feeAttributableYear, voucherNo, accountDate, paymentDate, oaProcessNumber, abstractTo, paymentAmount, invoiceType, taxRate, excludingTaxAmount, supplierName, projectNo, paymentMilestones, contractCode, contractType, totalContractAmount from procurement_list
    </sql>

    <select id="selectProcurementListList" parameterType="ProcurementList" resultMap="ProcurementListResult">
        <include refid="selectProcurementListVo"/>
        <where>  
            <if test="remarks != null  and remarks != ''"> and remarks like concat('%', #{remarks}, '%')</if>
            <if test="companyName != null  and companyName != ''"> and companyName like concat('%', #{companyName}, '%')</if>
            <if test="feeAttributableYear != null  and feeAttributableYear != ''"> and feeAttributableYear like concat('%', #{feeAttributableYear}, '%')</if>
            <if test="voucherNo != null  and voucherNo != ''"> and voucherNo like concat('%', #{voucherNo}, '%')</if>
            <if test="params.beginAccountDate != null and params.beginAccountDate != '' and params.endAccountDate != null and params.endAccountDate != ''"> and accountDate between #{params.beginAccountDate} and #{params.endAccountDate}</if>
            <if test="params.beginPaymentDate != null and params.beginPaymentDate != '' and params.endPaymentDate != null and params.endPaymentDate != ''"> and paymentDate between #{params.beginPaymentDate} and #{params.endPaymentDate}</if>
            <if test="oaProcessNumber != null  and oaProcessNumber != ''"> and oaProcessNumber like concat('%', #{oaProcessNumber}, '%')</if>
            <if test="abstractTo != null  and abstractTo != ''"> and abstractTo = #{abstractTo}</if>
            <if test="paymentAmount != null "> and paymentAmount = #{paymentAmount}</if>
            <if test="invoiceType != null  and invoiceType != ''"> and invoiceType like concat('%', #{invoiceType}, '%')</if>
            <if test="taxRate != null  and taxRate != ''"> and taxRate = #{taxRate}</if>
            <if test="excludingTaxAmount != null "> and excludingTaxAmount = #{excludingTaxAmount}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplierName like concat('%', #{supplierName}, '%')</if>
            <if test="projectNo != null  and projectNo != ''"> and projectNo like concat('%', #{projectNo}, '%')</if>
            <if test="paymentMilestones != null  and paymentMilestones != ''"> and paymentMilestones = #{paymentMilestones}</if>
            <if test="contractCode != null  and contractCode != ''"> and contractCode like concat('%', #{contractCode}, '%')</if>
            <if test="contractType != null  and contractType != ''"> and contractType like concat('%', #{contractType}, '%')</if>
            <if test="totalContractAmount != null "> and totalContractAmount = #{totalContractAmount}</if>
        </where>
    </select>
    
    <select id="selectProcurementListById" parameterType="Long" resultMap="ProcurementListResult">
        <include refid="selectProcurementListVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertProcurementList" parameterType="ProcurementList">
        insert into procurement_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="remarks != null">remarks,</if>
            <if test="companyName != null">companyName,</if>
            <if test="feeAttributableYear != null">feeAttributableYear,</if>
            <if test="voucherNo != null">voucherNo,</if>
            <if test="accountDate != null">accountDate,</if>
            <if test="paymentDate != null">paymentDate,</if>
            <if test="oaProcessNumber != null">oaProcessNumber,</if>
            <if test="abstractTo != null">abstractTo,</if>
            <if test="paymentAmount != null">paymentAmount,</if>
            <if test="invoiceType != null">invoiceType,</if>
            <if test="taxRate != null">taxRate,</if>
            <if test="excludingTaxAmount != null">excludingTaxAmount,</if>
            <if test="supplierName != null">supplierName,</if>
            <if test="projectNo != null">projectNo,</if>
            <if test="paymentMilestones != null">paymentMilestones,</if>
            <if test="contractCode != null">contractCode,</if>
            <if test="contractType != null">contractType,</if>
            <if test="totalContractAmount != null">totalContractAmount,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="feeAttributableYear != null">#{feeAttributableYear},</if>
            <if test="voucherNo != null">#{voucherNo},</if>
            <if test="accountDate != null">#{accountDate},</if>
            <if test="paymentDate != null">#{paymentDate},</if>
            <if test="oaProcessNumber != null">#{oaProcessNumber},</if>
            <if test="abstractTo != null">#{abstractTo},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="taxRate != null">#{taxRate},</if>
            <if test="excludingTaxAmount != null">#{excludingTaxAmount},</if>
            <if test="supplierName != null">#{supplierName},</if>
            <if test="projectNo != null">#{projectNo},</if>
            <if test="paymentMilestones != null">#{paymentMilestones},</if>
            <if test="contractCode != null">#{contractCode},</if>
            <if test="contractType != null">#{contractType},</if>
            <if test="totalContractAmount != null">#{totalContractAmount},</if>
         </trim>
    </insert>

    <update id="updateProcurementList" parameterType="ProcurementList">
        update procurement_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="feeAttributableYear != null">feeAttributableYear = #{feeAttributableYear},</if>
            <if test="voucherNo != null">voucherNo = #{voucherNo},</if>
            <if test="accountDate != null">accountDate = #{accountDate},</if>
            <if test="paymentDate != null">paymentDate = #{paymentDate},</if>
            <if test="oaProcessNumber != null">oaProcessNumber = #{oaProcessNumber},</if>
            <if test="abstractTo != null">abstractTo = #{abstractTo},</if>
            <if test="paymentAmount != null">paymentAmount = #{paymentAmount},</if>
            <if test="invoiceType != null">invoiceType = #{invoiceType},</if>
            <if test="taxRate != null">taxRate = #{taxRate},</if>
            <if test="excludingTaxAmount != null">excludingTaxAmount = #{excludingTaxAmount},</if>
            <if test="supplierName != null">supplierName = #{supplierName},</if>
            <if test="projectNo != null">projectNo = #{projectNo},</if>
            <if test="paymentMilestones != null">paymentMilestones = #{paymentMilestones},</if>
            <if test="contractCode != null">contractCode = #{contractCode},</if>
            <if test="contractType != null">contractType = #{contractType},</if>
            <if test="totalContractAmount != null">totalContractAmount = #{totalContractAmount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProcurementListById" parameterType="Long">
        delete from procurement_list where id = #{id}
    </delete>

    <delete id="deleteProcurementListByIds" parameterType="String">
        delete from procurement_list where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>