<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.basicData.mapper.WithdrawalDetailsMapper">

    <resultMap type="WithdrawalDetails" id="WithdrawalDetailsResult">
        <result property="id" column="id"/>
        <result property="companyName" column="companyName"/>
        <result property="companyAbbreviation" column="companyAbbreviation"/>
        <result property="companyCode" column="companyCode"/>
        <result property="businessType" column="businessType"/>
        <result property="yearTime" column="yearTime"/>
        <result property="lendingBank" column="lendingBank"/>
        <result property="withdrawalAmount" column="withdrawalAmount"/>
        <result property="interestRate" column="interestRate"/>
        <result property="drawMoneyDate" column="drawMoneyDate"/>
        <result property="loanMaturityDate" column="loanMaturityDate"/>
        <result property="notes" column="notes"/>
    </resultMap>

    <sql id="selectWithdrawalDetailsVo">
        select id,
               companyName,
               companyAbbreviation,
               companyCode,
               businessType,
               yearTime,
               lendingBank,
               withdrawalAmount,
               interestRate,
               drawMoneyDate,
               loanMaturityDate,
               notes
        from withdrawal_details
    </sql>

    <select id="selectWithdrawalDetailsList" parameterType="WithdrawalDetails" resultMap="WithdrawalDetailsResult">
        <include refid="selectWithdrawalDetailsVo"/>
        <where>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="companyAbbreviation != null  and companyAbbreviation != ''">and companyAbbreviation like
                concat('%', #{companyAbbreviation}, '%')
            </if>
            <if test="companyCode != null  and companyCode != ''">and companyCode like concat('%', #{companyCode},
                '%')
            </if>
            <if test="businessType != null  and businessType != ''">and businessType like concat('%', #{businessType},
                '%')
            </if>
            <if test="yearTime != null  and yearTime != ''">and yearTime like concat('%', #{yearTime}, '%')</if>
            <if test="lendingBank != null  and lendingBank != ''">and lendingBank = #{lendingBank} </if>
            <if test="withdrawalAmount != null ">and withdrawalAmount = #{withdrawalAmount}</if>
            <if test="interestRate != null  and interestRate != ''">and interestRate = #{interestRate}</if>
            <if test="params.beginDrawMoneyDate != null and params.beginDrawMoneyDate != '' and params.endDrawMoneyDate != null and params.endDrawMoneyDate != ''">
                and drawMoneyDate between #{params.beginDrawMoneyDate} and #{params.endDrawMoneyDate}
            </if>
            <if test="params.beginLoanMaturityDate != null and params.beginLoanMaturityDate != '' and params.endLoanMaturityDate != null and params.endLoanMaturityDate != ''">
                and loanMaturityDate between #{params.beginLoanMaturityDate} and #{params.endLoanMaturityDate}
            </if>
            <if test="notes != null  and notes != ''">and notes like concat('%', #{notes}, '%')</if>
        </where>
    </select>

    <select id="selectWithdrawalDetailsById" parameterType="Integer" resultMap="WithdrawalDetailsResult">
        <include refid="selectWithdrawalDetailsVo"/>
        where id = #{id}
    </select>

    <insert id="insertWithdrawalDetails" parameterType="WithdrawalDetails">
        insert into withdrawal_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyAbbreviation != null">companyAbbreviation,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="businessType != null">businessType,</if>
            <if test="yearTime != null">yearTime,</if>
            <if test="lendingBank != null">lendingBank,</if>
            <if test="withdrawalAmount != null">withdrawalAmount,</if>
            <if test="interestRate != null">interestRate,</if>
            <if test="drawMoneyDate != null">drawMoneyDate,</if>
            <if test="loanMaturityDate != null">loanMaturityDate,</if>
            <if test="notes != null">notes,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyAbbreviation != null">#{companyAbbreviation},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="yearTime != null">#{yearTime},</if>
            <if test="lendingBank != null">#{lendingBank},</if>
            <if test="withdrawalAmount != null">#{withdrawalAmount},</if>
            <if test="interestRate != null">#{interestRate},</if>
            <if test="drawMoneyDate != null">#{drawMoneyDate},</if>
            <if test="loanMaturityDate != null">#{loanMaturityDate},</if>
            <if test="notes != null">#{notes},</if>
        </trim>
    </insert>

    <update id="updateWithdrawalDetails" parameterType="WithdrawalDetails">
        update withdrawal_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyAbbreviation != null">companyAbbreviation = #{companyAbbreviation},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="businessType != null">businessType = #{businessType},</if>
            <if test="yearTime != null">yearTime = #{yearTime},</if>
            <if test="lendingBank != null">lendingBank = #{lendingBank},</if>
            <if test="withdrawalAmount != null">withdrawalAmount = #{withdrawalAmount},</if>
            <if test="interestRate != null">interestRate = #{interestRate},</if>
            <if test="drawMoneyDate != null">drawMoneyDate = #{drawMoneyDate},</if>
            <if test="loanMaturityDate != null">loanMaturityDate = #{loanMaturityDate},</if>
            <if test="notes != null">notes = #{notes},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWithdrawalDetailsById" parameterType="Integer">
        delete
        from withdrawal_details
        where id = #{id}
    </delete>

    <delete id="deleteWithdrawalDetailsByIds" parameterType="String">
        delete from withdrawal_details where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>