<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.basicData.mapper.RfExpensesMapper">

    <resultMap type="RfExpenses" id="RfExpensesResult">
        <result property="id" column="id"/>
        <result property="indicationProjectNo" column="indicationProjectNo"/>
        <result property="indication" column="indication"/>
        <result property="indicationName" column="indicationName"/>
        <result property="isProject" column="isProject"/>
        <result property="casesNumber" column="casesNumber"/>
        <result property="amount" column="amount"/>
        <result property="road" column="road"/>
        <result property="paymentYear" column="paymentYear"/>
        <result property="paymentPeriod" column="paymentPeriod"/>
    </resultMap>

    <sql id="selectRfExpensesVo">
        select id,
               indicationProjectNo,
               indication,
               indicationName,
               isProject,
               casesNumber,
               amount,
               road,
               paymentYear,
               paymentPeriod
        from rf_expenses
    </sql>

    <select id="selectRfExpensesList" parameterType="RfExpenses" resultMap="RfExpensesResult">
        <include refid="selectRfExpensesVo"/>
        <where>
            <if test="indicationProjectNo != null  and indicationProjectNo != ''">and indicationProjectNo like
                concat('%', #{indicationProjectNo}, '%')
            </if>
            <if test="indication != null  and indication != ''">and indication like concat('%', #{indication}, '%')</if>
            <if test="indicationName != null  and indicationName != ''">and indicationName like concat('%',
                #{indicationName}, '%')
            </if>
            <if test="isProject != null  and isProject != ''">and isProject like concat('%', #{isProject}, '%')</if>
            <if test="casesNumber != null ">and casesNumber = #{casesNumber}</if>
            <if test="amount  != null ">and amount = #{amount }</if>
            <if test="road != null  and road != ''">and road like concat('%', #{road}, '%')</if>
            <if test="paymentYear != null  and paymentYear != ''">and paymentYear like concat('%', #{paymentYear},
                '%')
            </if>
            <if test="paymentPeriod != null  and paymentPeriod != ''">and paymentPeriod like concat('%',
                #{paymentPeriod}, '%')
            </if>
            <if test="isProjectList !=null and isProjectList.size>0">
                and isProject in
                <foreach collection="isProjectList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectRfExpensesById" parameterType="Long" resultMap="RfExpensesResult">
        <include refid="selectRfExpensesVo"/>
        where id = #{id}
    </select>

    <insert id="insertRfExpenses" parameterType="RfExpenses">
        insert into rf_expenses
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="indicationProjectNo != null">indicationProjectNo,</if>
            <if test="indication != null">indication,</if>
            <if test="indicationName != null">indicationName,</if>
            <if test="isProject != null">isProject,</if>
            <if test="casesNumber != null">casesNumber,</if>
            <if test="amount  != null">amount ,</if>
            <if test="road != null">road,</if>
            <if test="paymentYear != null">paymentYear,</if>
            <if test="paymentPeriod != null">paymentPeriod,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="indicationProjectNo != null">#{indicationProjectNo},</if>
            <if test="indication != null">#{indication},</if>
            <if test="indicationName != null">#{indicationName},</if>
            <if test="isProject != null">#{isProject},</if>
            <if test="casesNumber != null">#{casesNumber},</if>
            <if test="amount  != null">#{amount },</if>
            <if test="road != null">#{road},</if>
            <if test="paymentYear != null">#{paymentYear},</if>
            <if test="paymentPeriod != null">#{paymentPeriod},</if>
        </trim>
    </insert>

    <update id="updateRfExpenses" parameterType="RfExpenses">
        update rf_expenses
        <trim prefix="SET" suffixOverrides=",">
            <if test="indicationProjectNo != null">indicationProjectNo = #{indicationProjectNo},</if>
            <if test="indication != null">indication = #{indication},</if>
            <if test="indicationName != null">indicationName = #{indicationName},</if>
            <if test="isProject != null">isProject = #{isProject},</if>
            <if test="casesNumber != null">casesNumber = #{casesNumber},</if>
            <if test="amount  != null">amount = #{amount },</if>
            <if test="road != null">road = #{road},</if>
            <if test="paymentYear != null">paymentYear = #{paymentYear},</if>
            <if test="paymentPeriod != null">paymentPeriod = #{paymentPeriod},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRfExpensesById" parameterType="Long">
        delete
        from rf_expenses
        where id = #{id}
    </delete>

    <delete id="deleteRfExpensesByIds" parameterType="String">
        delete from rf_expenses where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>