<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.basicData.mapper.LoanUsageMapper">

    <resultMap type="LoanUsage" id="LoanUsageResult">
        <result property="id" column="id"/>
        <result property="companyName" column="companyName"/>
        <result property="companyAbbreviation" column="companyAbbreviation"/>
        <result property="companyCode" column="companyCode"/>
        <result property="yearTime" column="yearTime"/>
        <result property="businessType" column="businessType"/>
        <result property="loanTerm" column="loanTerm"/>
        <result property="lendingBank" column="lendingBank"/>
        <result property="loanLimit" column="loanLimit"/>
        <result property="loanBalance" column="loanBalance"/>
        <result property="enabledAmount" column="enabledAmount"/>
        <result property="dueLoanBalance" column="dueLoanBalance"/>
        <result property="amountRepaid" column="amountRepaid"/>
        <result property="notes" column="notes"/>
    </resultMap>

    <sql id="selectLoanUsageVo">
        select id,
               companyName,
               companyAbbreviation,
               companyCode,
               yearTime,
               businessType,
               loanTerm,
               lendingBank,
               loanLimit,
               loanBalance,
               enabledAmount,
               dueLoanBalance,
               amountRepaid,
               notes
        from loan_usage
    </sql>

    <select id="selectLoanUsageList" parameterType="LoanUsage" resultMap="LoanUsageResult">
        <include refid="selectLoanUsageVo"/>
        <where>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="companyAbbreviation != null  and companyAbbreviation != ''">and companyAbbreviation like
                concat('%', #{companyAbbreviation}, '%')
            </if>
            <if test="companyCode != null  and companyCode != ''">and companyCode like concat('%', #{companyCode},
                '%')
            </if>
            <if test="yearTime != null  and yearTime != ''">and yearTime like concat('%', #{yearTime}, '%')</if>
            <if test="businessType != null  and businessType != ''">and businessType like concat('%', #{businessType},
                '%')
            </if>
            <if test="loanTerm != null  and loanTerm != ''">and loanTerm like concat('%', #{loanTerm}, '%')</if>
            <if test="lendingBank != null  and lendingBank != ''">and lendingBank like concat('%', #{lendingBank},
                '%')
            </if>
            <if test="loanLimit != null ">and loanLimit = #{loanLimit}</if>
            <if test="loanBalance != null ">and loanBalance = #{loanBalance}</if>
            <if test="enabledAmount  != null ">and enabledAmount = #{enabledAmount}</if>
            <if test="dueLoanBalance != null ">and dueLoanBalance = #{dueLoanBalance}</if>
            <if test="amountRepaid != null ">and amountRepaid = #{amountRepaid}</if>
            <if test="notes != null  and notes != ''">and notes = #{notes}</if>
        </where>
    </select>

    <select id="selectLoanUsageById" parameterType="Integer" resultMap="LoanUsageResult">
        <include refid="selectLoanUsageVo"/>
        where id = #{id}
    </select>

    <insert id="insertLoanUsage" parameterType="LoanUsage">
        insert into loan_usage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyAbbreviation != null">companyAbbreviation,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="yearTime != null">yearTime,</if>
            <if test="businessType != null">businessType,</if>
            <if test="loanTerm != null">loanTerm,</if>
            <if test="lendingBank != null">lendingBank,</if>
            <if test="loanLimit != null">loanLimit,</if>
            <if test="loanBalance != null">loanBalance,</if>
            <if test="enabledAmount  != null">enabledAmount,</if>
            <if test="dueLoanBalance != null">dueLoanBalance,</if>
            <if test="amountRepaid != null">amountRepaid,</if>
            <if test="notes != null">notes,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyAbbreviation != null">#{companyAbbreviation},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="yearTime != null">#{yearTime},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="loanTerm != null">#{loanTerm},</if>
            <if test="lendingBank != null">#{lendingBank},</if>
            <if test="loanLimit != null">#{loanLimit},</if>
            <if test="loanBalance != null">#{loanBalance},</if>
            <if test="enabledAmount  != null">#{enabledAmount},</if>
            <if test="dueLoanBalance != null">#{dueLoanBalance},</if>
            <if test="amountRepaid != null">#{amountRepaid},</if>
            <if test="notes != null">#{notes},</if>
        </trim>
    </insert>

    <update id="updateLoanUsage" parameterType="LoanUsage">
        update loan_usage
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyAbbreviation != null">companyAbbreviation = #{companyAbbreviation},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="yearTime != null">yearTime = #{yearTime},</if>
            <if test="businessType != null">businessType = #{businessType},</if>
            <if test="loanTerm != null">loanTerm = #{loanTerm},</if>
            <if test="lendingBank != null">lendingBank = #{lendingBank},</if>
            <if test="loanLimit != null">loanLimit = #{loanLimit},</if>
            <if test="loanBalance != null">loanBalance = #{loanBalance},</if>
            <if test="enabledAmount  != null">enabledAmount = #{enabledAmount},</if>
            <if test="dueLoanBalance != null">dueLoanBalance = #{dueLoanBalance},</if>
            <if test="amountRepaid != null">amountRepaid = #{amountRepaid},</if>
            <if test="notes != null">notes = #{notes},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLoanUsageById" parameterType="Integer">
        delete
        from loan_usage
        where id = #{id}
    </delete>

    <delete id="deleteLoanUsageByIds" parameterType="String">
        delete from loan_usage where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>