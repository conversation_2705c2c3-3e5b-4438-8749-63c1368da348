<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.basicData.mapper.RepaymentDetailsMapper">

    <resultMap type="RepaymentDetails" id="RepaymentDetailsResult">
        <result property="id" column="id"/>
        <result property="companyName" column="companyName"/>
        <result property="companyAbbreviation" column="companyAbbreviation"/>
        <result property="companyCode" column="companyCode"/>
        <result property="businessType" column="businessType"/>
        <result property="yearTime" column="yearTime"/>
        <result property="lendingBank" column="lendingBank"/>
        <result property="repaymentDate" column="repaymentDate"/>
        <result property="repaymentAmount" column="repaymentAmount"/>
        <result property="notes" column="notes"/>
    </resultMap>

    <sql id="selectRepaymentDetailsVo">
        select id,
               companyName,
               companyAbbreviation,
               companyCode,
               businessType,
               yearTime,
               lendingBank,
               repaymentDate,
               repaymentAmount,
               notes
        from repayment_details
    </sql>

    <select id="selectRepaymentDetailsList" parameterType="RepaymentDetails" resultMap="RepaymentDetailsResult">
        <include refid="selectRepaymentDetailsVo"/>
        <where>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="companyAbbreviation != null  and companyAbbreviation != ''">and companyAbbreviation like
                concat('%', #{companyAbbreviation}, '%')
            </if>
            <if test="companyCode != null  and companyCode != ''">and companyCode like concat('%', #{companyCode},
                '%')
            </if>
            <if test="businessType != null  and businessType != ''">and businessType like concat('%', #{businessType},
                '%')
            </if>
            <if test="yearTime != null  and yearTime != ''">and yearTime like concat('%', #{yearTime}, '%')</if>
            <if test="lendingBank != null  and lendingBank != ''">and lendingBank like concat('%', #{lendingBank},
                '%')
            </if>
            <if test="params.beginRepaymentDate != null and params.beginRepaymentDate != '' and params.endRepaymentDate != null and params.endRepaymentDate != ''">
                and repaymentDate between #{params.beginRepaymentDate} and #{params.endRepaymentDate}
            </if>
            <if test="repaymentAmount != null ">and repaymentAmount = #{repaymentAmount}</if>
            <if test="notes != null  and notes != ''">and notes like concat('%', #{notes}, '%')</if>
        </where>
    </select>

    <select id="selectRepaymentDetailsById" parameterType="Integer" resultMap="RepaymentDetailsResult">
        <include refid="selectRepaymentDetailsVo"/>
        where id = #{id}
    </select>

    <insert id="insertRepaymentDetails" parameterType="RepaymentDetails">
        insert into repayment_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyAbbreviation != null">companyAbbreviation,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="businessType != null">businessType,</if>
            <if test="yearTime != null">yearTime,</if>
            <if test="lendingBank != null">lendingBank,</if>
            <if test="repaymentDate != null">repaymentDate,</if>
            <if test="repaymentAmount != null">repaymentAmount,</if>
            <if test="notes != null">notes,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyAbbreviation != null">#{companyAbbreviation},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="yearTime != null">#{yearTime},</if>
            <if test="lendingBank != null">#{lendingBank},</if>
            <if test="repaymentDate != null">#{repaymentDate},</if>
            <if test="repaymentAmount != null">#{repaymentAmount},</if>
            <if test="notes != null">#{notes},</if>
        </trim>
    </insert>

    <update id="updateRepaymentDetails" parameterType="RepaymentDetails">
        update repayment_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyAbbreviation != null">companyAbbreviation = #{companyAbbreviation},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="businessType != null">businessType = #{businessType},</if>
            <if test="yearTime != null">yearTime = #{yearTime},</if>
            <if test="lendingBank != null">lendingBank = #{lendingBank},</if>
            <if test="repaymentDate != null">repaymentDate = #{repaymentDate},</if>
            <if test="repaymentAmount != null">repaymentAmount = #{repaymentAmount},</if>
            <if test="notes != null">notes = #{notes},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRepaymentDetailsById" parameterType="Integer">
        delete
        from repayment_details
        where id = #{id}
    </delete>

    <delete id="deleteRepaymentDetailsByIds" parameterType="String">
        delete from repayment_details where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>