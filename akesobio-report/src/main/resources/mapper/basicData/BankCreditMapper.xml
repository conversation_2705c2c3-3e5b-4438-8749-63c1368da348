<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.basicData.mapper.BankCreditMapper">
    
    <resultMap type="BankCredit" id="BankCreditResult">
        <result property="id"    column="id"    />
        <result property="companyName"    column="companyName"    />
        <result property="companyAbbreviation"    column="companyAbbreviation"    />
        <result property="companyCode"    column="companyCode"    />
        <result property="yearTime"    column="yearTime"    />
        <result property="businessType"    column="businessType"    />
        <result property="creditGrantingBank"    column="creditGrantingBank"    />
        <result property="creditLimit"    column="creditLimit"    />
        <result property="creditExposure"    column="creditExposure"    />
        <result property="loanPricing"    column="loanPricing"    />
        <result property="capitalRequirements"    column="capitalRequirements"    />
        <result property="loanTerm"    column="loanTerm"    />
        <result property="repaymentGracePeriod"    column="repaymentGracePeriod"    />
        <result property="availablePeriod"    column="availablePeriod"    />
        <result property="guaranteeMethod"    column="guaranteeMethod"    />
        <result property="fundingWithdrawalRestrictions"    column="fundingWithdrawalRestrictions"    />
        <result property="notes"    column="notes"    />
    </resultMap>

    <sql id="selectBankCreditVo">
        select id, companyName, companyAbbreviation, companyCode, yearTime, businessType, creditGrantingBank, creditLimit, creditExposure, loanPricing, capitalRequirements, loanTerm, repaymentGracePeriod, availablePeriod, guaranteeMethod, fundingWithdrawalRestrictions, notes from bank_credit
    </sql>

    <select id="selectBankCreditList" parameterType="BankCredit" resultMap="BankCreditResult">
        <include refid="selectBankCreditVo"/>
        <where>  
            <if test="companyName != null  and companyName != ''"> and companyName like concat('%', #{companyName}, '%')</if>
            <if test="companyAbbreviation != null  and companyAbbreviation != ''"> and companyAbbreviation like concat('%', #{companyAbbreviation}, '%')</if>
            <if test="companyCode != null  and companyCode != ''"> and companyCode like concat('%', #{companyCode}, '%')</if>
            <if test="yearTime != null  and yearTime != ''"> and yearTime like concat('%', #{yearTime}, '%')</if>
            <if test="businessType != null  and businessType != ''"> and businessType like concat('%', #{businessType}, '%')</if>
            <if test="creditGrantingBank != null  and creditGrantingBank != ''"> and creditGrantingBank like concat('%', #{creditGrantingBank}, '%')</if>
            <if test="creditLimit != null "> and creditLimit = #{creditLimit}</if>
            <if test="creditExposure != null "> and creditExposure = #{creditExposure}</if>
            <if test="loanPricing != null  and loanPricing != ''"> and loanPricing = #{loanPricing}</if>
            <if test="capitalRequirements != null  and capitalRequirements != ''"> and capitalRequirements = #{capitalRequirements}</if>
            <if test="loanTerm != null  and loanTerm != ''"> and loanTerm like concat('%', #{loanTerm}, '%')</if>
            <if test="repaymentGracePeriod != null  and repaymentGracePeriod != ''"> and repaymentGracePeriod like concat('%', #{repaymentGracePeriod}, '%')</if>
            <if test="availablePeriod != null  and availablePeriod != ''"> and availablePeriod = #{availablePeriod}</if>
            <if test="guaranteeMethod != null  and guaranteeMethod != ''"> and guaranteeMethod = #{guaranteeMethod}</if>
            <if test="fundingWithdrawalRestrictions != null  and fundingWithdrawalRestrictions != ''"> and fundingWithdrawalRestrictions = #{fundingWithdrawalRestrictions}</if>
            <if test="notes != null  and notes != ''"> and notes = #{notes}</if>
        </where>
    </select>
    
    <select id="selectBankCreditById" parameterType="Integer" resultMap="BankCreditResult">
        <include refid="selectBankCreditVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBankCredit" parameterType="BankCredit">
        insert into bank_credit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyAbbreviation != null">companyAbbreviation,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="yearTime != null">yearTime,</if>
            <if test="businessType != null">businessType,</if>
            <if test="creditGrantingBank != null">creditGrantingBank,</if>
            <if test="creditLimit != null">creditLimit,</if>
            <if test="creditExposure != null">creditExposure,</if>
            <if test="loanPricing != null">loanPricing,</if>
            <if test="capitalRequirements != null">capitalRequirements,</if>
            <if test="loanTerm != null">loanTerm,</if>
            <if test="repaymentGracePeriod != null">repaymentGracePeriod,</if>
            <if test="availablePeriod != null">availablePeriod,</if>
            <if test="guaranteeMethod != null">guaranteeMethod,</if>
            <if test="fundingWithdrawalRestrictions != null">fundingWithdrawalRestrictions,</if>
            <if test="notes != null">notes,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyAbbreviation != null">#{companyAbbreviation},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="yearTime != null">#{yearTime},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="creditGrantingBank != null">#{creditGrantingBank},</if>
            <if test="creditLimit != null">#{creditLimit},</if>
            <if test="creditExposure != null">#{creditExposure},</if>
            <if test="loanPricing != null">#{loanPricing},</if>
            <if test="capitalRequirements != null">#{capitalRequirements},</if>
            <if test="loanTerm != null">#{loanTerm},</if>
            <if test="repaymentGracePeriod != null">#{repaymentGracePeriod},</if>
            <if test="availablePeriod != null">#{availablePeriod},</if>
            <if test="guaranteeMethod != null">#{guaranteeMethod},</if>
            <if test="fundingWithdrawalRestrictions != null">#{fundingWithdrawalRestrictions},</if>
            <if test="notes != null">#{notes},</if>
         </trim>
    </insert>

    <update id="updateBankCredit" parameterType="BankCredit">
        update bank_credit
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyAbbreviation != null">companyAbbreviation = #{companyAbbreviation},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="yearTime != null">yearTime = #{yearTime},</if>
            <if test="businessType != null">businessType = #{businessType},</if>
            <if test="creditGrantingBank != null">creditGrantingBank = #{creditGrantingBank},</if>
            <if test="creditLimit != null">creditLimit = #{creditLimit},</if>
            <if test="creditExposure != null">creditExposure = #{creditExposure},</if>
            <if test="loanPricing != null">loanPricing = #{loanPricing},</if>
            <if test="capitalRequirements != null">capitalRequirements = #{capitalRequirements},</if>
            <if test="loanTerm != null">loanTerm = #{loanTerm},</if>
            <if test="repaymentGracePeriod != null">repaymentGracePeriod = #{repaymentGracePeriod},</if>
            <if test="availablePeriod != null">availablePeriod = #{availablePeriod},</if>
            <if test="guaranteeMethod != null">guaranteeMethod = #{guaranteeMethod},</if>
            <if test="fundingWithdrawalRestrictions != null">fundingWithdrawalRestrictions = #{fundingWithdrawalRestrictions},</if>
            <if test="notes != null">notes = #{notes},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBankCreditById" parameterType="Integer">
        delete from bank_credit where id = #{id}
    </delete>

    <delete id="deleteBankCreditByIds" parameterType="String">
        delete from bank_credit where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>