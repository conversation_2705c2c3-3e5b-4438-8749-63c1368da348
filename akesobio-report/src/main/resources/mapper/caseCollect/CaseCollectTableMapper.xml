<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.caseCollect.mapper.CaseCollectTableMapper">

    <resultMap type="CaseCollectTable" id="CaseCollectTableResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="sex"    column="sex"    />
        <result property="age"    column="age"    />
        <result property="chiefComplaint"    column="chief_complaint"    />
        <result property="familyMedicalHistory"    column="family_medical_history"    />
        <result property="presentIllnessHistory"    column="present_illness_history"    />
        <result property="physicalExamination"    column="physical_examination"    />
        <result property="bloodRoutineExamination"    column="blood_routine_examination"    />
        <result property="biochemicalExamination"    column="biochemical_examination"    />
        <result property="tumorMarker"    column="tumor_marker"    />
        <result property="laboratoryExaminationElse"    column="laboratory_examination_else"    />
        <result property="petctImage"    column="PETCT_image"    />
        <result property="petctReport"    column="PETCT_report"    />
        <result property="ctImage"    column="CT_image"    />
        <result property="ctReport"    column="CT_report"    />
        <result property="mriImage"    column="MRI_image"    />
        <result property="mriReport"    column="MRI_report"    />
        <result property="elseImage"    column="else_image"    />
        <result property="elseReport"    column="else_report"    />
        <result property="pd1"    column="PD_1"    />
        <result property="geneticTest"    column="genetic_test"    />
        <result property="immunohistochemical"    column="immunohistochemical"    />
        <result property="elseExamination"    column="else_examination"    />
        <result property="pathologicDiagnosis"    column="pathologic_diagnosis"    />
        <result property="neoadjuvantTreatmentOptions"    column="neoadjuvant_treatment_options"    />
        <result property="neoadjuvantTreatmentCycle"    column="neoadjuvant_treatment_cycle"    />
        <result property="ntEvaluation"    column="nt_evaluation"    />
        <result property="surgicalTreatmentTime"    column="surgical_treatment_time"    />
        <result property="surgicalTreatmentResult"    column="surgical_treatment_result"    />
        <result property="patPlan"    column="pat_plan"    />
        <result property="patCycle"    column="pat_cycle"    />
        <result property="patEvaluation"    column="pat_evaluation"    />
        <result property="topicalTreatmentPlan"    column="topical_treatment_plan"    />
        <result property="topicalTreatmentCycle"    column="topical_treatment_cycle"    />
        <result property="topicalTreatmentImage"    column="topical_treatment_image"    />
        <result property="topicalTreatmentImagReport"    column="topical_treatment_imag_report"    />
        <result property="ttTumorMarkerExamination"    column="tt_tumor_marker_examination"    />
        <result property="ttEfficacyAssessment"    column="tt_efficacy_assessment"    />
        <result property="topicalTreatmentElse"    column="topical_treatment_else"    />
        <result property="firstLineTreatmentPlan"    column="first_line_treatment_plan"    />
        <result property="firstLineTreatmentCycle"    column="first_line_treatment_cycle"    />
        <result property="firstLineTreatmentImage"    column="first_line_treatment_image"    />
        <result property="fltImageReport"    column="flt_image_report"    />
        <result property="fltTumorMarkerExamination"    column="flt_tumor_marker_examination"    />
        <result property="fltEfficacyAssessment"    column="flt_efficacy_assessment"    />
        <result property="firstLineTreatmentElse"    column="first_line_treatment_else"    />
        <result property="secondLineTreatmentPlan"    column="second_line_treatment_plan"    />
        <result property="secondLineTreatmentCycle"    column="second_line_treatment_cycle"    />
        <result property="secondLineTreatmentImage"    column="second_line_treatment_image"    />
        <result property="sltImageReport"    column="slt_image_report"    />
        <result property="sltTumorMarkerExamination"    column="slt_tumor_marker_examination"    />
        <result property="sltEfficacyAssessment"    column="slt_efficacy_assessment"    />
        <result property="secondLineTreatmentElse"    column="second_line_treatment_else"    />
        <result property="thirdLineTreatmentPlan"    column="third_line_treatment_plan"    />
        <result property="thirdLineTreatmentCycle"    column="third_line_treatment_cycle"    />
        <result property="thirdLineTreatmentImage"    column="third_line_treatment_image"    />
        <result property="tltImageReport"    column="tlt_image_report"    />
        <result property="tltTumorMarkerExamination"    column="tlt_tumor_marker_examination"    />
        <result property="tltEfficacyAssessment"    column="tlt_efficacy_assessment"    />
        <result property="thirdLineTreatmentElse"    column="third_line_treatment_else"    />
        <result property="adverseReactionHandle"    column="adverse_reaction_handle"    />
        <result property="caseSummaryAndReflection"    column="case_summary_and_reflection"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCaseCollectTableVo">
        select id, name, sex, age, chief_complaint, family_medical_history, present_illness_history, physical_examination, blood_routine_examination, biochemical_examination, tumor_marker, laboratory_examination_else, PETCT_image, PETCT_report, CT_image, CT_report, MRI_image, MRI_report, else_image, else_report, PD_1, genetic_test, immunohistochemical, else_examination, pathologic_diagnosis, neoadjuvant_treatment_options, neoadjuvant_treatment_cycle, nt_evaluation, surgical_treatment_time, surgical_treatment_result, pat_plan, pat_cycle, pat_evaluation, topical_treatment_plan, topical_treatment_cycle, topical_treatment_image, topical_treatment_imag_report, tt_tumor_marker_examination, tt_efficacy_assessment, topical_treatment_else, first_line_treatment_plan, first_line_treatment_cycle, first_line_treatment_image, flt_image_report, flt_tumor_marker_examination, flt_efficacy_assessment, first_line_treatment_else, second_line_treatment_plan, second_line_treatment_cycle, second_line_treatment_image, slt_image_report, slt_tumor_marker_examination, slt_efficacy_assessment, second_line_treatment_else, third_line_treatment_plan, third_line_treatment_cycle, third_line_treatment_image, tlt_image_report, tlt_tumor_marker_examination, tlt_efficacy_assessment, third_line_treatment_else, adverse_reaction_handle, case_summary_and_reflection, create_time, update_time, create_by, update_by, remark from case_collect_table
    </sql>

    <select id="selectCaseCollectTableList" parameterType="CaseCollectTable" resultMap="CaseCollectTableResult">
        <include refid="selectCaseCollectTableVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="age != null  and age != ''"> and age = #{age}</if>
            <if test="chiefComplaint != null  and chiefComplaint != ''"> and chief_complaint = #{chiefComplaint}</if>
            <if test="familyMedicalHistory != null  and familyMedicalHistory != ''"> and family_medical_history = #{familyMedicalHistory}</if>
            <if test="presentIllnessHistory != null  and presentIllnessHistory != ''"> and present_illness_history = #{presentIllnessHistory}</if>
            <if test="physicalExamination != null  and physicalExamination != ''"> and physical_examination = #{physicalExamination}</if>
            <if test="bloodRoutineExamination != null  and bloodRoutineExamination != ''"> and blood_routine_examination = #{bloodRoutineExamination}</if>
            <if test="biochemicalExamination != null  and biochemicalExamination != ''"> and biochemical_examination = #{biochemicalExamination}</if>
            <if test="tumorMarker != null  and tumorMarker != ''"> and tumor_marker = #{tumorMarker}</if>
            <if test="laboratoryExaminationElse != null  and laboratoryExaminationElse != ''"> and laboratory_examination_else = #{laboratoryExaminationElse}</if>
            <if test="petctImage != null  and petctImage != ''"> and PETCT_image = #{petctImage}</if>
            <if test="petctReport != null  and petctReport != ''"> and PETCT_report = #{petctReport}</if>
            <if test="ctImage != null  and ctImage != ''"> and CT_image = #{ctImage}</if>
            <if test="ctReport != null  and ctReport != ''"> and CT_report = #{ctReport}</if>
            <if test="mriImage != null  and mriImage != ''"> and MRI_image = #{mriImage}</if>
            <if test="mriReport != null  and mriReport != ''"> and MRI_report = #{mriReport}</if>
            <if test="elseImage != null  and elseImage != ''"> and else_image = #{elseImage}</if>
            <if test="elseReport != null  and elseReport != ''"> and else_report = #{elseReport}</if>
            <if test="pd1 != null  and pd1 != ''"> and PD_1 = #{pd1}</if>
            <if test="geneticTest != null  and geneticTest != ''"> and genetic_test = #{geneticTest}</if>
            <if test="immunohistochemical != null  and immunohistochemical != ''"> and immunohistochemical = #{immunohistochemical}</if>
            <if test="elseExamination != null  and elseExamination != ''"> and else_examination = #{elseExamination}</if>
            <if test="pathologicDiagnosis != null  and pathologicDiagnosis != ''"> and pathologic_diagnosis = #{pathologicDiagnosis}</if>
            <if test="neoadjuvantTreatmentOptions != null  and neoadjuvantTreatmentOptions != ''"> and neoadjuvant_treatment_options = #{neoadjuvantTreatmentOptions}</if>
            <if test="neoadjuvantTreatmentCycle != null  and neoadjuvantTreatmentCycle != ''"> and neoadjuvant_treatment_cycle = #{neoadjuvantTreatmentCycle}</if>
            <if test="ntEvaluation != null  and ntEvaluation != ''"> and nt_evaluation = #{ntEvaluation}</if>
            <if test="surgicalTreatmentTime != null "> and surgical_treatment_time = #{surgicalTreatmentTime}</if>
            <if test="surgicalTreatmentResult != null  and surgicalTreatmentResult != ''"> and surgical_treatment_result = #{surgicalTreatmentResult}</if>
            <if test="patPlan != null  and patPlan != ''"> and pat_plan = #{patPlan}</if>
            <if test="patCycle != null  and patCycle != ''"> and pat_cycle = #{patCycle}</if>
            <if test="patEvaluation != null  and patEvaluation != ''"> and pat_evaluation = #{patEvaluation}</if>
            <if test="topicalTreatmentPlan != null  and topicalTreatmentPlan != ''"> and topical_treatment_plan = #{topicalTreatmentPlan}</if>
            <if test="topicalTreatmentCycle != null  and topicalTreatmentCycle != ''"> and topical_treatment_cycle = #{topicalTreatmentCycle}</if>
            <if test="topicalTreatmentImage != null  and topicalTreatmentImage != ''"> and topical_treatment_image = #{topicalTreatmentImage}</if>
            <if test="topicalTreatmentImagReport != null  and topicalTreatmentImagReport != ''"> and topical_treatment_imag_report = #{topicalTreatmentImagReport}</if>
            <if test="ttTumorMarkerExamination != null  and ttTumorMarkerExamination != ''"> and tt_tumor_marker_examination = #{ttTumorMarkerExamination}</if>
            <if test="ttEfficacyAssessment != null  and ttEfficacyAssessment != ''"> and tt_efficacy_assessment = #{ttEfficacyAssessment}</if>
            <if test="topicalTreatmentElse != null  and topicalTreatmentElse != ''"> and topical_treatment_else = #{topicalTreatmentElse}</if>
            <if test="firstLineTreatmentPlan != null  and firstLineTreatmentPlan != ''"> and first_line_treatment_plan = #{firstLineTreatmentPlan}</if>
            <if test="firstLineTreatmentCycle != null  and firstLineTreatmentCycle != ''"> and first_line_treatment_cycle = #{firstLineTreatmentCycle}</if>
            <if test="firstLineTreatmentImage != null  and firstLineTreatmentImage != ''"> and first_line_treatment_image = #{firstLineTreatmentImage}</if>
            <if test="fltImageReport != null  and fltImageReport != ''"> and flt_image_report = #{fltImageReport}</if>
            <if test="fltTumorMarkerExamination != null  and fltTumorMarkerExamination != ''"> and flt_tumor_marker_examination = #{fltTumorMarkerExamination}</if>
            <if test="fltEfficacyAssessment != null  and fltEfficacyAssessment != ''"> and flt_efficacy_assessment = #{fltEfficacyAssessment}</if>
            <if test="firstLineTreatmentElse != null  and firstLineTreatmentElse != ''"> and first_line_treatment_else = #{firstLineTreatmentElse}</if>
            <if test="secondLineTreatmentPlan != null  and secondLineTreatmentPlan != ''"> and second_line_treatment_plan = #{secondLineTreatmentPlan}</if>
            <if test="secondLineTreatmentCycle != null  and secondLineTreatmentCycle != ''"> and second_line_treatment_cycle = #{secondLineTreatmentCycle}</if>
            <if test="secondLineTreatmentImage != null  and secondLineTreatmentImage != ''"> and second_line_treatment_image = #{secondLineTreatmentImage}</if>
            <if test="sltImageReport != null  and sltImageReport != ''"> and slt_image_report = #{sltImageReport}</if>
            <if test="sltTumorMarkerExamination != null  and sltTumorMarkerExamination != ''"> and slt_tumor_marker_examination = #{sltTumorMarkerExamination}</if>
            <if test="sltEfficacyAssessment != null  and sltEfficacyAssessment != ''"> and slt_efficacy_assessment = #{sltEfficacyAssessment}</if>
            <if test="secondLineTreatmentElse != null  and secondLineTreatmentElse != ''"> and second_line_treatment_else = #{secondLineTreatmentElse}</if>
            <if test="thirdLineTreatmentPlan != null  and thirdLineTreatmentPlan != ''"> and third_line_treatment_plan = #{thirdLineTreatmentPlan}</if>
            <if test="thirdLineTreatmentCycle != null  and thirdLineTreatmentCycle != ''"> and third_line_treatment_cycle = #{thirdLineTreatmentCycle}</if>
            <if test="thirdLineTreatmentImage != null  and thirdLineTreatmentImage != ''"> and third_line_treatment_image = #{thirdLineTreatmentImage}</if>
            <if test="tltImageReport != null  and tltImageReport != ''"> and tlt_image_report = #{tltImageReport}</if>
            <if test="tltTumorMarkerExamination != null  and tltTumorMarkerExamination != ''"> and tlt_tumor_marker_examination = #{tltTumorMarkerExamination}</if>
            <if test="tltEfficacyAssessment != null  and tltEfficacyAssessment != ''"> and tlt_efficacy_assessment = #{tltEfficacyAssessment}</if>
            <if test="thirdLineTreatmentElse != null  and thirdLineTreatmentElse != ''"> and third_line_treatment_else = #{thirdLineTreatmentElse}</if>
            <if test="adverseReactionHandle != null  and adverseReactionHandle != ''"> and adverse_reaction_handle = #{adverseReactionHandle}</if>
            <if test="caseSummaryAndReflection != null  and caseSummaryAndReflection != ''"> and case_summary_and_reflection = #{caseSummaryAndReflection}</if>
        </where>
    </select>

    <select id="selectCaseCollectTableById" parameterType="String" resultMap="CaseCollectTableResult">
        <include refid="selectCaseCollectTableVo"/>
        where id = #{id}
    </select>

    <insert id="insertCaseCollectTable" parameterType="CaseCollectTable">
        insert into case_collect_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="sex != null">sex,</if>
            <if test="age != null">age,</if>
            <if test="chiefComplaint != null">chief_complaint,</if>
            <if test="familyMedicalHistory != null">family_medical_history,</if>
            <if test="presentIllnessHistory != null">present_illness_history,</if>
            <if test="physicalExamination != null">physical_examination,</if>
            <if test="bloodRoutineExamination != null">blood_routine_examination,</if>
            <if test="biochemicalExamination != null">biochemical_examination,</if>
            <if test="tumorMarker != null">tumor_marker,</if>
            <if test="laboratoryExaminationElse != null">laboratory_examination_else,</if>
            <if test="petctImage != null">PETCT_image,</if>
            <if test="petctReport != null">PETCT_report,</if>
            <if test="ctImage != null">CT_image,</if>
            <if test="ctReport != null">CT_report,</if>
            <if test="mriImage != null">MRI_image,</if>
            <if test="mriReport != null">MRI_report,</if>
            <if test="elseImage != null">else_image,</if>
            <if test="elseReport != null">else_report,</if>
            <if test="pd1 != null">PD_1,</if>
            <if test="geneticTest != null">genetic_test,</if>
            <if test="immunohistochemical != null">immunohistochemical,</if>
            <if test="elseExamination != null">else_examination,</if>
            <if test="pathologicDiagnosis != null">pathologic_diagnosis,</if>
            <if test="neoadjuvantTreatmentOptions != null">neoadjuvant_treatment_options,</if>
            <if test="neoadjuvantTreatmentCycle != null">neoadjuvant_treatment_cycle,</if>
            <if test="ntEvaluation != null">nt_evaluation,</if>
            <if test="surgicalTreatmentTime != null">surgical_treatment_time,</if>
            <if test="surgicalTreatmentResult != null">surgical_treatment_result,</if>
            <if test="patPlan != null">pat_plan,</if>
            <if test="patCycle != null">pat_cycle,</if>
            <if test="patEvaluation != null">pat_evaluation,</if>
            <if test="topicalTreatmentPlan != null">topical_treatment_plan,</if>
            <if test="topicalTreatmentCycle != null">topical_treatment_cycle,</if>
            <if test="topicalTreatmentImage != null">topical_treatment_image,</if>
            <if test="topicalTreatmentImagReport != null">topical_treatment_imag_report,</if>
            <if test="ttTumorMarkerExamination != null">tt_tumor_marker_examination,</if>
            <if test="ttEfficacyAssessment != null">tt_efficacy_assessment,</if>
            <if test="topicalTreatmentElse != null">topical_treatment_else,</if>
            <if test="firstLineTreatmentPlan != null">first_line_treatment_plan,</if>
            <if test="firstLineTreatmentCycle != null">first_line_treatment_cycle,</if>
            <if test="firstLineTreatmentImage != null">first_line_treatment_image,</if>
            <if test="fltImageReport != null">flt_image_report,</if>
            <if test="fltTumorMarkerExamination != null">flt_tumor_marker_examination,</if>
            <if test="fltEfficacyAssessment != null">flt_efficacy_assessment,</if>
            <if test="firstLineTreatmentElse != null">first_line_treatment_else,</if>
            <if test="secondLineTreatmentPlan != null">second_line_treatment_plan,</if>
            <if test="secondLineTreatmentCycle != null">second_line_treatment_cycle,</if>
            <if test="secondLineTreatmentImage != null">second_line_treatment_image,</if>
            <if test="sltImageReport != null">slt_image_report,</if>
            <if test="sltTumorMarkerExamination != null">slt_tumor_marker_examination,</if>
            <if test="sltEfficacyAssessment != null">slt_efficacy_assessment,</if>
            <if test="secondLineTreatmentElse != null">second_line_treatment_else,</if>
            <if test="thirdLineTreatmentPlan != null">third_line_treatment_plan,</if>
            <if test="thirdLineTreatmentCycle != null">third_line_treatment_cycle,</if>
            <if test="thirdLineTreatmentImage != null">third_line_treatment_image,</if>
            <if test="tltImageReport != null">tlt_image_report,</if>
            <if test="tltTumorMarkerExamination != null">tlt_tumor_marker_examination,</if>
            <if test="tltEfficacyAssessment != null">tlt_efficacy_assessment,</if>
            <if test="thirdLineTreatmentElse != null">third_line_treatment_else,</if>
            <if test="adverseReactionHandle != null">adverse_reaction_handle,</if>
            <if test="caseSummaryAndReflection != null">case_summary_and_reflection,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="sex != null">#{sex},</if>
            <if test="age != null">#{age},</if>
            <if test="chiefComplaint != null">#{chiefComplaint},</if>
            <if test="familyMedicalHistory != null">#{familyMedicalHistory},</if>
            <if test="presentIllnessHistory != null">#{presentIllnessHistory},</if>
            <if test="physicalExamination != null">#{physicalExamination},</if>
            <if test="bloodRoutineExamination != null">#{bloodRoutineExamination},</if>
            <if test="biochemicalExamination != null">#{biochemicalExamination},</if>
            <if test="tumorMarker != null">#{tumorMarker},</if>
            <if test="laboratoryExaminationElse != null">#{laboratoryExaminationElse},</if>
            <if test="petctImage != null">#{petctImage},</if>
            <if test="petctReport != null">#{petctReport},</if>
            <if test="ctImage != null">#{ctImage},</if>
            <if test="ctReport != null">#{ctReport},</if>
            <if test="mriImage != null">#{mriImage},</if>
            <if test="mriReport != null">#{mriReport},</if>
            <if test="elseImage != null">#{elseImage},</if>
            <if test="elseReport != null">#{elseReport},</if>
            <if test="pd1 != null">#{pd1},</if>
            <if test="geneticTest != null">#{geneticTest},</if>
            <if test="immunohistochemical != null">#{immunohistochemical},</if>
            <if test="elseExamination != null">#{elseExamination},</if>
            <if test="pathologicDiagnosis != null">#{pathologicDiagnosis},</if>
            <if test="neoadjuvantTreatmentOptions != null">#{neoadjuvantTreatmentOptions},</if>
            <if test="neoadjuvantTreatmentCycle != null">#{neoadjuvantTreatmentCycle},</if>
            <if test="ntEvaluation != null">#{ntEvaluation},</if>
            <if test="surgicalTreatmentTime != null">#{surgicalTreatmentTime},</if>
            <if test="surgicalTreatmentResult != null">#{surgicalTreatmentResult},</if>
            <if test="patPlan != null">#{patPlan},</if>
            <if test="patCycle != null">#{patCycle},</if>
            <if test="patEvaluation != null">#{patEvaluation},</if>
            <if test="topicalTreatmentPlan != null">#{topicalTreatmentPlan},</if>
            <if test="topicalTreatmentCycle != null">#{topicalTreatmentCycle},</if>
            <if test="topicalTreatmentImage != null">#{topicalTreatmentImage},</if>
            <if test="topicalTreatmentImagReport != null">#{topicalTreatmentImagReport},</if>
            <if test="ttTumorMarkerExamination != null">#{ttTumorMarkerExamination},</if>
            <if test="ttEfficacyAssessment != null">#{ttEfficacyAssessment},</if>
            <if test="topicalTreatmentElse != null">#{topicalTreatmentElse},</if>
            <if test="firstLineTreatmentPlan != null">#{firstLineTreatmentPlan},</if>
            <if test="firstLineTreatmentCycle != null">#{firstLineTreatmentCycle},</if>
            <if test="firstLineTreatmentImage != null">#{firstLineTreatmentImage},</if>
            <if test="fltImageReport != null">#{fltImageReport},</if>
            <if test="fltTumorMarkerExamination != null">#{fltTumorMarkerExamination},</if>
            <if test="fltEfficacyAssessment != null">#{fltEfficacyAssessment},</if>
            <if test="firstLineTreatmentElse != null">#{firstLineTreatmentElse},</if>
            <if test="secondLineTreatmentPlan != null">#{secondLineTreatmentPlan},</if>
            <if test="secondLineTreatmentCycle != null">#{secondLineTreatmentCycle},</if>
            <if test="secondLineTreatmentImage != null">#{secondLineTreatmentImage},</if>
            <if test="sltImageReport != null">#{sltImageReport},</if>
            <if test="sltTumorMarkerExamination != null">#{sltTumorMarkerExamination},</if>
            <if test="sltEfficacyAssessment != null">#{sltEfficacyAssessment},</if>
            <if test="secondLineTreatmentElse != null">#{secondLineTreatmentElse},</if>
            <if test="thirdLineTreatmentPlan != null">#{thirdLineTreatmentPlan},</if>
            <if test="thirdLineTreatmentCycle != null">#{thirdLineTreatmentCycle},</if>
            <if test="thirdLineTreatmentImage != null">#{thirdLineTreatmentImage},</if>
            <if test="tltImageReport != null">#{tltImageReport},</if>
            <if test="tltTumorMarkerExamination != null">#{tltTumorMarkerExamination},</if>
            <if test="tltEfficacyAssessment != null">#{tltEfficacyAssessment},</if>
            <if test="thirdLineTreatmentElse != null">#{thirdLineTreatmentElse},</if>
            <if test="adverseReactionHandle != null">#{adverseReactionHandle},</if>
            <if test="caseSummaryAndReflection != null">#{caseSummaryAndReflection},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCaseCollectTable" parameterType="CaseCollectTable">
        update case_collect_table
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="age != null">age = #{age},</if>
            <if test="chiefComplaint != null">chief_complaint = #{chiefComplaint},</if>
            <if test="familyMedicalHistory != null">family_medical_history = #{familyMedicalHistory},</if>
            <if test="presentIllnessHistory != null">present_illness_history = #{presentIllnessHistory},</if>
            <if test="physicalExamination != null">physical_examination = #{physicalExamination},</if>
            <if test="bloodRoutineExamination != null">blood_routine_examination = #{bloodRoutineExamination},</if>
            <if test="biochemicalExamination != null">biochemical_examination = #{biochemicalExamination},</if>
            <if test="tumorMarker != null">tumor_marker = #{tumorMarker},</if>
            <if test="laboratoryExaminationElse != null">laboratory_examination_else = #{laboratoryExaminationElse},</if>
            <if test="petctImage != null">PETCT_image = #{petctImage},</if>
            <if test="petctReport != null">PETCT_report = #{petctReport},</if>
            <if test="ctImage != null">CT_image = #{ctImage},</if>
            <if test="ctReport != null">CT_report = #{ctReport},</if>
            <if test="mriImage != null">MRI_image = #{mriImage},</if>
            <if test="mriReport != null">MRI_report = #{mriReport},</if>
            <if test="elseImage != null">else_image = #{elseImage},</if>
            <if test="elseReport != null">else_report = #{elseReport},</if>
            <if test="pd1 != null">PD_1 = #{pd1},</if>
            <if test="geneticTest != null">genetic_test = #{geneticTest},</if>
            <if test="immunohistochemical != null">immunohistochemical = #{immunohistochemical},</if>
            <if test="elseExamination != null">else_examination = #{elseExamination},</if>
            <if test="pathologicDiagnosis != null">pathologic_diagnosis = #{pathologicDiagnosis},</if>
            <if test="neoadjuvantTreatmentOptions != null">neoadjuvant_treatment_options = #{neoadjuvantTreatmentOptions},</if>
            <if test="neoadjuvantTreatmentCycle != null">neoadjuvant_treatment_cycle = #{neoadjuvantTreatmentCycle},</if>
            <if test="ntEvaluation != null">nt_evaluation = #{ntEvaluation},</if>
            <if test="surgicalTreatmentTime != null">surgical_treatment_time = #{surgicalTreatmentTime},</if>
            <if test="surgicalTreatmentResult != null">surgical_treatment_result = #{surgicalTreatmentResult},</if>
            <if test="patPlan != null">pat_plan = #{patPlan},</if>
            <if test="patCycle != null">pat_cycle = #{patCycle},</if>
            <if test="patEvaluation != null">pat_evaluation = #{patEvaluation},</if>
            <if test="topicalTreatmentPlan != null">topical_treatment_plan = #{topicalTreatmentPlan},</if>
            <if test="topicalTreatmentCycle != null">topical_treatment_cycle = #{topicalTreatmentCycle},</if>
            <if test="topicalTreatmentImage != null">topical_treatment_image = #{topicalTreatmentImage},</if>
            <if test="topicalTreatmentImagReport != null">topical_treatment_imag_report = #{topicalTreatmentImagReport},</if>
            <if test="ttTumorMarkerExamination != null">tt_tumor_marker_examination = #{ttTumorMarkerExamination},</if>
            <if test="ttEfficacyAssessment != null">tt_efficacy_assessment = #{ttEfficacyAssessment},</if>
            <if test="topicalTreatmentElse != null">topical_treatment_else = #{topicalTreatmentElse},</if>
            <if test="firstLineTreatmentPlan != null">first_line_treatment_plan = #{firstLineTreatmentPlan},</if>
            <if test="firstLineTreatmentCycle != null">first_line_treatment_cycle = #{firstLineTreatmentCycle},</if>
            <if test="firstLineTreatmentImage != null">first_line_treatment_image = #{firstLineTreatmentImage},</if>
            <if test="fltImageReport != null">flt_image_report = #{fltImageReport},</if>
            <if test="fltTumorMarkerExamination != null">flt_tumor_marker_examination = #{fltTumorMarkerExamination},</if>
            <if test="fltEfficacyAssessment != null">flt_efficacy_assessment = #{fltEfficacyAssessment},</if>
            <if test="firstLineTreatmentElse != null">first_line_treatment_else = #{firstLineTreatmentElse},</if>
            <if test="secondLineTreatmentPlan != null">second_line_treatment_plan = #{secondLineTreatmentPlan},</if>
            <if test="secondLineTreatmentCycle != null">second_line_treatment_cycle = #{secondLineTreatmentCycle},</if>
            <if test="secondLineTreatmentImage != null">second_line_treatment_image = #{secondLineTreatmentImage},</if>
            <if test="sltImageReport != null">slt_image_report = #{sltImageReport},</if>
            <if test="sltTumorMarkerExamination != null">slt_tumor_marker_examination = #{sltTumorMarkerExamination},</if>
            <if test="sltEfficacyAssessment != null">slt_efficacy_assessment = #{sltEfficacyAssessment},</if>
            <if test="secondLineTreatmentElse != null">second_line_treatment_else = #{secondLineTreatmentElse},</if>
            <if test="thirdLineTreatmentPlan != null">third_line_treatment_plan = #{thirdLineTreatmentPlan},</if>
            <if test="thirdLineTreatmentCycle != null">third_line_treatment_cycle = #{thirdLineTreatmentCycle},</if>
            <if test="thirdLineTreatmentImage != null">third_line_treatment_image = #{thirdLineTreatmentImage},</if>
            <if test="tltImageReport != null">tlt_image_report = #{tltImageReport},</if>
            <if test="tltTumorMarkerExamination != null">tlt_tumor_marker_examination = #{tltTumorMarkerExamination},</if>
            <if test="tltEfficacyAssessment != null">tlt_efficacy_assessment = #{tltEfficacyAssessment},</if>
            <if test="thirdLineTreatmentElse != null">third_line_treatment_else = #{thirdLineTreatmentElse},</if>
            <if test="adverseReactionHandle != null">adverse_reaction_handle = #{adverseReactionHandle},</if>
            <if test="caseSummaryAndReflection != null">case_summary_and_reflection = #{caseSummaryAndReflection},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCaseCollectTableById" parameterType="String">
        delete from case_collect_table where id = #{id}
    </delete>

    <delete id="deleteCaseCollectTableByIds" parameterType="String">
        delete from case_collect_table where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>