<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ddi.mapper.ArchivesInformation2025Mapper">

    <resultMap type="ArchivesInformation2025" id="ArchivesInformation2025Result">
        <result property="id" column="id"/>
        <result property="companyCode" column="companyCode"/>
        <result property="companyName" column="companyName"/>
        <result property="hospitalCode" column="hospitalCode"/>
        <result property="hospitalName" column="hospitalName"/>
        <result property="secondaryDepartment" column="secondaryDepartment"/>
        <result property="thirdLevelDepartments" column="thirdLevelDepartments"/>
        <result property="fourthLevelDepartment" column="fourthLevelDepartment"/>
        <result property="fifthLevelDepartment" column="fifthLevelDepartment"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="ktnRegion1" column="ktnRegion1"/>
        <result property="ktnArea1" column="ktnArea1"/>
        <result property="ktnUnitPrice" column="ktnUnitPrice"/>
        <result property="ydfRegion1" column="ydfRegion1"/>
        <result property="ydfArea1" column="ydfArea1"/>
        <result property="ydfUnitPrice" column="ydfUnitPrice"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="ktnRegion2" column="ktnRegion2"/>
        <result property="ktnArea2" column="ktnArea2"/>
        <result property="ydfRegion2" column="ydfRegion2"/>
        <result property="ydfArea2" column="ydfArea2"/>
        <result property="isTargetTerminal" column="isTargetTerminal"/>
        <result property="primaryAttribute" column="primaryAttribute"/>
        <result property="companyOrganizationAlias" column="companyOrganizationAlias"/>
    </resultMap>

    <sql id="selectArchivesInformation2025Vo">
        select id,
               companyCode,
               companyName,
               hospitalCode,
               hospitalName,
               secondaryDepartment,
               thirdLevelDepartments,
               fourthLevelDepartment,
               fifthLevelDepartment,
               province,
               city,
               ktnRegion1,
               ktnArea1,
               ktnUnitPrice,
               ydfRegion1,
               ydfArea1,
               ydfUnitPrice,
               deleteStatus,
               ktnRegion2,
               ktnArea2,
               ydfRegion2,
               ydfArea2,
               isTargetTerminal,
               primaryAttribute,
               companyOrganizationAlias
        from archives_information2025
    </sql>

    <select id="selectArchivesInformation2025List" parameterType="ArchivesInformation2025"
            resultMap="ArchivesInformation2025Result">
        <include refid="selectArchivesInformation2025Vo"/>
        <where>
            <if test="companyCode != null  and companyCode != ''">and companyCode like concat('%', #{companyCode},
                '%')
            </if>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="hospitalCode != null  and hospitalCode != ''">and hospitalCode like concat('%', #{hospitalCode},
                '%')
            </if>
            <if test="hospitalName != null  and hospitalName != ''">and hospitalName like concat('%', #{hospitalName},
                '%')
            </if>
            <if test="secondaryDepartment != null  and secondaryDepartment != ''">and secondaryDepartment like
                concat('%', #{secondaryDepartment}, '%')
            </if>
            <if test="thirdLevelDepartments != null  and thirdLevelDepartments != ''">and thirdLevelDepartments like
                concat('%', #{thirdLevelDepartments}, '%')
            </if>
            <if test="fourthLevelDepartment != null  and fourthLevelDepartment != ''">and fourthLevelDepartment like
                concat('%', #{fourthLevelDepartment}, '%')
            </if>
            <if test="fifthLevelDepartment != null  and fifthLevelDepartment != ''">and fifthLevelDepartment like
                concat('%', #{fifthLevelDepartment}, '%')
            </if>
            <if test="province != null  and province != ''">and province like concat('%', #{province}, '%')</if>
            <if test="city != null  and city != ''">and city like concat('%', #{city}, '%')</if>
            <if test="ktnRegion1 != null  and ktnRegion1 != ''">and ktnRegion1 like concat('%', #{ktnRegion1}, '%')</if>
            <if test="ktnArea1 != null  and ktnArea1 != ''">and ktnArea1 like concat('%', #{ktnArea1}, '%')</if>
            <if test="ktnUnitPrice != null ">and ktnUnitPrice = #{ktnUnitPrice}</if>
            <if test="ydfRegion1 != null  and ydfRegion1 != ''">and ydfRegion1 like concat('%', #{ydfRegion1}, '%')</if>
            <if test="ydfArea1 != null  and ydfArea1 != ''">and ydfArea1 like concat('%', #{ydfArea1}, '%')</if>
            <if test="ydfUnitPrice != null ">and ydfUnitPrice = #{ydfUnitPrice}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="ktnRegion2 != null  and ktnRegion2 != ''">and ktnRegion2 like concat('%', #{ktnRegion2}, '%')</if>
            <if test="ktnArea2 != null  and ktnArea2 != ''">and ktnArea2 like concat('%', #{ktnArea2}, '%')</if>
            <if test="ydfRegion2 != null  and ydfRegion2 != ''">and ydfRegion2 like concat('%', #{ydfRegion2}, '%')</if>
            <if test="ydfArea2 != null  and ydfArea2 != ''">and ydfArea2 like concat('%', #{ydfArea2}, '%')</if>
            <if test="isTargetTerminal != null  and isTargetTerminal != ''">and isTargetTerminal like concat('%',
                #{isTargetTerminal}, '%')
            </if>
            <if test="primaryAttribute != null  and primaryAttribute != ''">and primaryAttribute like concat('%',
                #{primaryAttribute}, '%')
            </if>
            <if test="companyOrganizationAlias != null  and companyOrganizationAlias != ''">and companyOrganizationAlias
                like concat('%', #{companyOrganizationAlias}, '%')
            </if>
        </where>
    </select>

    <select id="selectArchivesInformation2025ById" parameterType="Integer" resultMap="ArchivesInformation2025Result">
        <include refid="selectArchivesInformation2025Vo"/>
        where id = #{id}
    </select>

    <insert id="insertArchivesInformation2025" parameterType="ArchivesInformation2025">
        insert into archives_information2025
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="companyName != null">companyName,</if>
            <if test="hospitalCode != null">hospitalCode,</if>
            <if test="hospitalName != null">hospitalName,</if>
            <if test="secondaryDepartment != null">secondaryDepartment,</if>
            <if test="thirdLevelDepartments != null">thirdLevelDepartments,</if>
            <if test="fourthLevelDepartment != null">fourthLevelDepartment,</if>
            <if test="fifthLevelDepartment != null">fifthLevelDepartment,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="ktnRegion1 != null">ktnRegion1,</if>
            <if test="ktnArea1 != null">ktnArea1,</if>
            <if test="ktnUnitPrice != null">ktnUnitPrice,</if>
            <if test="ydfRegion1 != null">ydfRegion1,</if>
            <if test="ydfArea1 != null">ydfArea1,</if>
            <if test="ydfUnitPrice != null">ydfUnitPrice,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="ktnRegion2 != null">ktnRegion2,</if>
            <if test="ktnArea2 != null">ktnArea2,</if>
            <if test="ydfRegion2 != null">ydfRegion2,</if>
            <if test="ydfArea2 != null">ydfArea2,</if>
            <if test="isTargetTerminal != null">isTargetTerminal,</if>
            <if test="primaryAttribute != null">primaryAttribute,</if>
            <if test="companyOrganizationAlias != null">companyOrganizationAlias,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="hospitalCode != null">#{hospitalCode},</if>
            <if test="hospitalName != null">#{hospitalName},</if>
            <if test="secondaryDepartment != null">#{secondaryDepartment},</if>
            <if test="thirdLevelDepartments != null">#{thirdLevelDepartments},</if>
            <if test="fourthLevelDepartment != null">#{fourthLevelDepartment},</if>
            <if test="fifthLevelDepartment != null">#{fifthLevelDepartment},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="ktnRegion1 != null">#{ktnRegion1},</if>
            <if test="ktnArea1 != null">#{ktnArea1},</if>
            <if test="ktnUnitPrice != null">#{ktnUnitPrice},</if>
            <if test="ydfRegion1 != null">#{ydfRegion1},</if>
            <if test="ydfArea1 != null">#{ydfArea1},</if>
            <if test="ydfUnitPrice != null">#{ydfUnitPrice},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="ktnRegion2 != null">#{ktnRegion2},</if>
            <if test="ktnArea2 != null">#{ktnArea2},</if>
            <if test="ydfRegion2 != null">#{ydfRegion2},</if>
            <if test="ydfArea2 != null">#{ydfArea2},</if>
            <if test="isTargetTerminal != null">#{isTargetTerminal},</if>
            <if test="primaryAttribute != null">#{primaryAttribute},</if>
            <if test="companyOrganizationAlias != null">#{companyOrganizationAlias},</if>
        </trim>
    </insert>

    <update id="updateArchivesInformation2025" parameterType="ArchivesInformation2025">
        update archives_information2025
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="hospitalCode != null">hospitalCode = #{hospitalCode},</if>
            <if test="hospitalName != null">hospitalName = #{hospitalName},</if>
            <if test="secondaryDepartment != null">secondaryDepartment = #{secondaryDepartment},</if>
            <if test="thirdLevelDepartments != null">thirdLevelDepartments = #{thirdLevelDepartments},</if>
            <if test="fourthLevelDepartment != null">fourthLevelDepartment = #{fourthLevelDepartment},</if>
            <if test="fifthLevelDepartment != null">fifthLevelDepartment = #{fifthLevelDepartment},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="ktnRegion1 != null">ktnRegion1 = #{ktnRegion1},</if>
            <if test="ktnArea1 != null">ktnArea1 = #{ktnArea1},</if>
            <if test="ktnUnitPrice != null">ktnUnitPrice = #{ktnUnitPrice},</if>
            <if test="ydfRegion1 != null">ydfRegion1 = #{ydfRegion1},</if>
            <if test="ydfArea1 != null">ydfArea1 = #{ydfArea1},</if>
            <if test="ydfUnitPrice != null">ydfUnitPrice = #{ydfUnitPrice},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
            <if test="ktnRegion2 != null">ktnRegion2 = #{ktnRegion2},</if>
            <if test="ktnArea2 != null">ktnArea2 = #{ktnArea2},</if>
            <if test="ydfRegion2 != null">ydfRegion2 = #{ydfRegion2},</if>
            <if test="ydfArea2 != null">ydfArea2 = #{ydfArea2},</if>
            <if test="isTargetTerminal != null">isTargetTerminal = #{isTargetTerminal},</if>
            <if test="primaryAttribute != null">primaryAttribute = #{primaryAttribute},</if>
            <if test="companyOrganizationAlias != null">companyOrganizationAlias = #{companyOrganizationAlias},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteArchivesInformation2025ById" parameterType="Integer">
        delete
        from archives_information2025
        where id = #{id}
    </delete>

    <delete id="deleteArchivesInformation2025ByIds" parameterType="String">
        delete from archives_information2025 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>