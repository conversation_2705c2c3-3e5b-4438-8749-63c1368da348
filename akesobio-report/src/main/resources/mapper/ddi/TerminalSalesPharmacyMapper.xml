<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ddi.mapper.TerminalSalesPharmacyMapper">

    <resultMap type="TerminalSalesPharmacy" id="TerminalSalesPharmacyResult">
        <result property="id" column="id"/>
        <result property="salesArea" column="salesArea"/>
        <result property="salesRegion" column="salesRegion"/>
        <result property="salesProvince" column="salesProvince"/>
        <result property="salesDate" column="salesDate"/>
        <result property="dailySalesVolume" column="dailySalesVolume"/>
        <result property="dailyRanking" column="dailyRanking"/>
        <result property="dailyGrowthRate" column="dailyGrowthRate"/>
        <result property="weeklySalesVolume" column="weeklySalesVolume"/>
        <result property="weeklyRanking" column="weeklyRanking"/>
        <result property="weeklyGrowthRate" column="weeklyGrowthRate"/>
        <result property="monthlySalesVolume" column="monthlySalesVolume"/>
        <result property="monthlyRanking" column="monthlyRanking"/>
        <result property="monthlyGrowthRate" column="monthlyGrowthRate"/>
        <result property="salesVolumeYear24" column="salesVolumeYear24"/>
        <result property="rankingYear24" column="rankingYear24"/>
        <result property="salesAfterListing" column="salesAfterListing"/>
        <result property="salesRankingAfterListing" column="salesRankingAfterListing"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="quarterlySales" column="quarterlySales"/>
        <result property="quarterlyRanking" column="quarterlyRanking"/>
        <result property="quarterlyGrowthRate" column="quarterlyGrowthRate"/>
        <result property="productName" column="productName"/>
        <result property="productCode" column="productCode"/>
        <result property="dailySalesVolumeAmount" column="dailySalesVolumeAmount"/>
        <result property="monthlySalesVolumeAmount" column="monthlySalesVolumeAmount"/>
        <result property="quarterlySalesAmount" column="quarterlySalesAmount"/>
        <result property="salesVolumeAmountYear24" column="salesVolumeAmountYear24"/>
        <result property="salesAfterListingAmount" column="salesAfterListingAmount"/>
        <result property="salesVolumeYear25" column="salesVolumeYear25"/>
        <result property="salesVolumeAmountYear25" column="salesVolumeAmountYear25"/>
        <result property="isTargetTerminal"    column="isTargetTerminal"    />
    </resultMap>

    <sql id="selectTerminalSalesPharmacyVo">
        select id,
               salesArea,
               salesRegion,
               salesProvince,
               salesDate,
               dailySalesVolume,
               dailyRanking,
               dailyGrowthRate,
               weeklySalesVolume,
               weeklyRanking,
               weeklyGrowthRate,
               monthlySalesVolume,
               monthlyRanking,
               monthlyGrowthRate,
               salesVolumeYear24,
               rankingYear24,
               salesAfterListing,
               salesRankingAfterListing,
               deleteStatus,
               quarterlySales,
               quarterlyRanking,
               quarterlyGrowthRate,
               productName,
               productCode,
               dailySalesVolumeAmount,
               monthlySalesVolumeAmount,
               quarterlySalesAmount,
               salesVolumeAmountYear24,
               salesAfterListingAmount,
               salesVolumeYear25,
               salesVolumeAmountYear25,
               isTargetTerminal
        from terminal_sales_pharmacy
    </sql>

    <select id="selectTerminalSalesPharmacyList" parameterType="TerminalSalesPharmacy"
            resultMap="TerminalSalesPharmacyResult">
        <include refid="selectTerminalSalesPharmacyVo"/>
        <where>
            <if test="salesArea != null  and salesArea != ''">and salesArea like concat('%', #{salesArea}, '%')</if>
            <if test="salesRegion != null  and salesRegion != ''">and salesRegion like concat('%', #{salesRegion},
                '%')
            </if>
            <if test="salesProvince != null  and salesProvince != ''">and salesProvince like concat('%',
                #{salesProvince}, '%')
            </if>
            <if test="salesDate != null ">and salesDate = #{salesDate}</if>
            <if test="dailySalesVolume != null ">and dailySalesVolume = #{dailySalesVolume}</if>
            <if test="dailyRanking != null ">and dailyRanking = #{dailyRanking}</if>
            <if test="dailyGrowthRate != null  and dailyGrowthRate != ''">and dailyGrowthRate = #{dailyGrowthRate}</if>
            <if test="weeklySalesVolume != null ">and weeklySalesVolume = #{weeklySalesVolume}</if>
            <if test="weeklyRanking != null ">and weeklyRanking = #{weeklyRanking}</if>
            <if test="weeklyGrowthRate != null  and weeklyGrowthRate != ''">and weeklyGrowthRate = #{weeklyGrowthRate}
            </if>
            <if test="monthlySalesVolume != null ">and monthlySalesVolume = #{monthlySalesVolume}</if>
            <if test="monthlyRanking != null ">and monthlyRanking = #{monthlyRanking}</if>
            <if test="monthlyGrowthRate != null  and monthlyGrowthRate != ''">and monthlyGrowthRate =
                #{monthlyGrowthRate}
            </if>
            <if test="salesVolumeYear24 != null ">and salesVolumeYear24 = #{salesVolumeYear24}</if>
            <if test="rankingYear24 != null ">and rankingYear24 = #{rankingYear24}</if>
            <if test="salesAfterListing != null ">and salesAfterListing = #{salesAfterListing}</if>
            <if test="salesRankingAfterListing != null ">and salesRankingAfterListing = #{salesRankingAfterListing}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="productName != null  and productName != ''">and productName like concat('%', #{productName},
                '%')
            </if>
            <if test="productCode != null  and productCode != ''">and productCode like concat('%', #{productCode},
                '%')
            </if>
            <if test="dailySalesVolumeAmount != null ">and dailySalesVolumeAmount = #{dailySalesVolumeAmount}</if>
            <if test="monthlySalesVolumeAmount != null ">and monthlySalesVolumeAmount = #{monthlySalesVolumeAmount}</if>
            <if test="quarterlySalesAmount != null ">and quarterlySalesAmount = #{quarterlySalesAmount}</if>
            <if test="salesVolumeAmountYear24 != null ">and salesVolumeAmountYear24 = #{salesVolumeAmountYear24}</if>
            <if test="salesAfterListingAmount != null ">and salesAfterListingAmount = #{salesAfterListingAmount}</if>
            <if test="isTargetTerminal != null  and isTargetTerminal != ''"> and isTargetTerminal = #{isTargetTerminal}</if>
            <if test="provinceList !=null and provinceList.size>0">
                and salesProvince in
                <foreach collection="provinceList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and deleteStatus ='0'
        </where>
    </select>

    <select id="selectTerminalSalesPharmacyById" parameterType="Integer" resultMap="TerminalSalesPharmacyResult">
        <include refid="selectTerminalSalesPharmacyVo"/>
        where id = #{id}
    </select>

    <insert id="insertTerminalSalesPharmacy" parameterType="TerminalSalesPharmacy">
        insert into terminal_sales_pharmacy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="salesArea != null">salesArea,</if>
            <if test="salesRegion != null">salesRegion,</if>
            <if test="salesProvince != null">salesProvince,</if>
            <if test="salesDate != null">salesDate,</if>
            <if test="dailySalesVolume != null">dailySalesVolume,</if>
            <if test="dailyRanking != null">dailyRanking,</if>
            <if test="dailyGrowthRate != null">dailyGrowthRate,</if>
            <if test="weeklySalesVolume != null">weeklySalesVolume,</if>
            <if test="weeklyRanking != null">weeklyRanking,</if>
            <if test="weeklyGrowthRate != null">weeklyGrowthRate,</if>
            <if test="monthlySalesVolume != null">monthlySalesVolume,</if>
            <if test="monthlyRanking != null">monthlyRanking,</if>
            <if test="monthlyGrowthRate != null">monthlyGrowthRate,</if>
            <if test="salesVolumeYear24 != null">salesVolumeYear24,</if>
            <if test="rankingYear24 != null">rankingYear24,</if>
            <if test="salesAfterListing != null">salesAfterListing,</if>
            <if test="salesRankingAfterListing != null">salesRankingAfterListing,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="quarterlySales != null">quarterlySales,</if>
            <if test="quarterlyRanking != null">quarterlyRanking,</if>
            <if test="quarterlyGrowthRate != null">quarterlyGrowthRate,</if>
            <if test="productName != null">productName,</if>
            <if test="productCode != null">productCode,</if>
            <if test="dailySalesVolumeAmount != null">dailySalesVolumeAmount,</if>
            <if test="monthlySalesVolumeAmount != null">monthlySalesVolumeAmount,</if>
            <if test="quarterlySalesAmount != null">quarterlySalesAmount,</if>
            <if test="salesVolumeAmountYear24 != null">salesVolumeAmountYear24,</if>
            <if test="salesAfterListingAmount != null">salesAfterListingAmount,</if>
            <if test="salesVolumeYear25 != null">salesVolumeYear25,</if>
            <if test="salesVolumeAmountYear25 != null">salesVolumeAmountYear25,</if>
            <if test="isTargetTerminal != null">isTargetTerminal,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="salesArea != null">#{salesArea},</if>
            <if test="salesRegion != null">#{salesRegion},</if>
            <if test="salesProvince != null">#{salesProvince},</if>
            <if test="salesDate != null">#{salesDate},</if>
            <if test="dailySalesVolume != null">#{dailySalesVolume},</if>
            <if test="dailyRanking != null">#{dailyRanking},</if>
            <if test="dailyGrowthRate != null">#{dailyGrowthRate},</if>
            <if test="weeklySalesVolume != null">#{weeklySalesVolume},</if>
            <if test="weeklyRanking != null">#{weeklyRanking},</if>
            <if test="weeklyGrowthRate != null">#{weeklyGrowthRate},</if>
            <if test="monthlySalesVolume != null">#{monthlySalesVolume},</if>
            <if test="monthlyRanking != null">#{monthlyRanking},</if>
            <if test="monthlyGrowthRate != null">#{monthlyGrowthRate},</if>
            <if test="salesVolumeYear24 != null">#{salesVolumeYear24},</if>
            <if test="rankingYear24 != null">#{rankingYear24},</if>
            <if test="salesAfterListing != null">#{salesAfterListing},</if>
            <if test="salesRankingAfterListing != null">#{salesRankingAfterListing},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="quarterlySales != null">#{quarterlySales},</if>
            <if test="quarterlyRanking != null">#{quarterlyRanking},</if>
            <if test="quarterlyGrowthRate != null">#{quarterlyGrowthRate},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productCode != null">#{productCode},</if>
            <if test="dailySalesVolumeAmount != null">#{dailySalesVolumeAmount},</if>
            <if test="monthlySalesVolumeAmount != null">#{monthlySalesVolumeAmount},</if>
            <if test="quarterlySalesAmount != null">#{quarterlySalesAmount},</if>
            <if test="salesVolumeAmountYear24 != null">#{salesVolumeAmountYear24},</if>
            <if test="salesAfterListingAmount != null">#{salesAfterListingAmount},</if>
            <if test="salesVolumeYear25 != null">#{salesVolumeYear25},</if>
            <if test="salesVolumeAmountYear25 != null">#{salesVolumeAmountYear25},</if>
            <if test="isTargetTerminal != null">#{isTargetTerminal},</if>
        </trim>
    </insert>

    <update id="updateTerminalSalesPharmacy" parameterType="TerminalSalesPharmacy">
        update terminal_sales_pharmacy
        <trim prefix="SET" suffixOverrides=",">
            <if test="salesArea != null">salesArea = #{salesArea},</if>
            <if test="salesRegion != null">salesRegion = #{salesRegion},</if>
            <if test="salesProvince != null">salesProvince = #{salesProvince},</if>
            <if test="salesDate != null">salesDate = #{salesDate},</if>
            <if test="dailySalesVolume != null">dailySalesVolume = #{dailySalesVolume},</if>
            <if test="dailyRanking != null">dailyRanking = #{dailyRanking},</if>
            <if test="dailyGrowthRate != null">dailyGrowthRate = #{dailyGrowthRate},</if>
            <if test="weeklySalesVolume != null">weeklySalesVolume = #{weeklySalesVolume},</if>
            <if test="weeklyRanking != null">weeklyRanking = #{weeklyRanking},</if>
            <if test="weeklyGrowthRate != null">weeklyGrowthRate = #{weeklyGrowthRate},</if>
            <if test="monthlySalesVolume != null">monthlySalesVolume = #{monthlySalesVolume},</if>
            <if test="monthlyRanking != null">monthlyRanking = #{monthlyRanking},</if>
            <if test="monthlyGrowthRate != null">monthlyGrowthRate = #{monthlyGrowthRate},</if>
            <if test="salesVolumeYear24 != null">salesVolumeYear24 = #{salesVolumeYear24},</if>
            <if test="rankingYear24 != null">rankingYear24 = #{rankingYear24},</if>
            <if test="salesAfterListing != null">salesAfterListing = #{salesAfterListing},</if>
            <if test="salesRankingAfterListing != null">salesRankingAfterListing = #{salesRankingAfterListing},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
            <if test="quarterlySales != null">quarterlySales = #{quarterlySales},</if>
            <if test="quarterlyRanking != null">quarterlyRanking = #{quarterlyRanking},</if>
            <if test="quarterlyGrowthRate != null">quarterlyGrowthRate = #{quarterlyGrowthRate},</if>
            <if test="productName != null">productName = #{productName},</if>
            <if test="productCode != null">productCode = #{productCode},</if>
            <if test="dailySalesVolumeAmount != null">dailySalesVolumeAmount = #{dailySalesVolumeAmount},</if>
            <if test="monthlySalesVolumeAmount != null">monthlySalesVolumeAmount = #{monthlySalesVolumeAmount},</if>
            <if test="quarterlySalesAmount != null">quarterlySalesAmount = #{quarterlySalesAmount},</if>
            <if test="salesVolumeAmountYear24 != null">salesVolumeAmountYear24 = #{salesVolumeAmountYear24},</if>
            <if test="salesAfterListingAmount != null">salesAfterListingAmount = #{salesAfterListingAmount},</if>
            <if test="salesVolumeYear25 != null">salesVolumeYear25 = #{salesVolumeYear25},</if>
            <if test="salesVolumeAmountYear25 != null">salesVolumeAmountYear25 = #{salesVolumeAmountYear25},</if>
            <if test="isTargetTerminal != null">isTargetTerminal = #{isTargetTerminal},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTerminalSalesPharmacyById" parameterType="Integer">
        delete
        from terminal_sales_pharmacy
        where id = #{id}
    </delete>

    <delete id="deleteTerminalSalesPharmacyByIds" parameterType="String">
        delete from terminal_sales_pharmacy where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updatePharmacyStatus" parameterType="TerminalSalesPharmacy">
        update terminal_sales_pharmacy
        set deleteStatus='1'
        where salesDate = #{salesDate}
    </update>
</mapper>