<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ddi.mapper.CustomerExecutionPlanMapper">

    <resultMap type="CustomerExecutionPlan" id="CustomerExecutionPlanResult">
        <result property="id" column="id"/>
        <result property="customerCode" column="customerCode"/>
        <result property="customerName" column="customerName"/>
        <result property="commercialGrade" column="commercialGrade"/>
        <result property="commercialProvince" column="commercialProvince"/>
        <result property="businessRepresentative" column="businessRepresentative"/>
        <result property="businessDistrict" column="businessDistrict"/>
        <result property="businessManager" column="businessManager"/>
        <result property="businessRegion" column="businessRegion"/>
        <result property="businessRegionManage" column="businessRegionManage"/>
        <result property="commercialDirector" column="commercialDirector"/>
        <result property="pharmacyLevel" column="pharmacyLevel"/>
        <result property="productName" column="productName"/>
    </resultMap>

    <sql id="selectCustomerExecutionPlanVo">
        select id,
               customerCode,
               customerName,
               commercialGrade,
               commercialProvince,
               businessRepresentative,
               businessDistrict,
               businessManager,
               businessRegion,
               businessRegionManage,
               commercialDirector,
               pharmacyLevel,
               productName
        from customer_execution_plan
    </sql>

    <select id="selectCustomerExecutionPlanList" parameterType="CustomerExecutionPlan"
            resultMap="CustomerExecutionPlanResult">
        <include refid="selectCustomerExecutionPlanVo"/>
        <where>
            <if test="customerCode != null  and customerCode != ''">and customerCode like concat('%', #{customerCode},
                '%')
            </if>
            <if test="customerName != null  and customerName != ''">and customerName like concat('%', #{customerName},
                '%')
            </if>
            <if test="commercialGrade != null  and commercialGrade != ''">and commercialGrade like concat('%',
                #{commercialGrade}, '%')
            </if>
            <if test="commercialProvince != null  and commercialProvince != ''">and commercialProvince like concat('%',
                #{commercialProvince}, '%')
            </if>
            <if test="businessRepresentative != null  and businessRepresentative != ''">and businessRepresentative like
                concat('%', #{businessRepresentative}, '%')
            </if>
            <if test="businessDistrict != null  and businessDistrict != ''">and businessDistrict like concat('%',
                #{businessDistrict}, '%')
            </if>
            <if test="businessManager != null  and businessManager != ''">and businessManager like concat('%',
                #{businessManager}, '%')
            </if>
            <if test="businessRegion != null  and businessRegion != ''">and businessRegion like concat('%',
                #{businessRegion}, '%')
            </if>
            <if test="businessRegionManage != null  and businessRegionManage != ''">and businessRegionManage like
                concat('%', #{businessRegionManage}, '%')
            </if>
            <if test="commercialDirector != null  and commercialDirector != ''">and commercialDirector like concat('%',
                #{commercialDirector}, '%')
            </if>
            <if test="pharmacyLevel != null  and pharmacyLevel != ''">and pharmacyLevel like concat('%',
                #{pharmacyLevel}, '%')
            </if>
            <if test="productName != null  and productName != ''">and productName like concat('%', #{productName},
                '%')
            </if>
        </where>
    </select>

    <select id="selectCustomerExecutionPlanById" parameterType="Integer" resultMap="CustomerExecutionPlanResult">
        <include refid="selectCustomerExecutionPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertCustomerExecutionPlan" parameterType="CustomerExecutionPlan">
        insert into customer_execution_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerCode != null">customerCode,</if>
            <if test="customerName != null">customerName,</if>
            <if test="commercialGrade != null">commercialGrade,</if>
            <if test="commercialProvince != null">commercialProvince,</if>
            <if test="businessRepresentative != null">businessRepresentative,</if>
            <if test="businessDistrict != null">businessDistrict,</if>
            <if test="businessManager != null">businessManager,</if>
            <if test="businessRegion != null">businessRegion,</if>
            <if test="businessRegionManage != null">businessRegionManage,</if>
            <if test="commercialDirector != null">commercialDirector,</if>
            <if test="pharmacyLevel != null">pharmacyLevel,</if>
            <if test="productName != null">productName,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerCode != null">#{customerCode},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="commercialGrade != null">#{commercialGrade},</if>
            <if test="commercialProvince != null">#{commercialProvince},</if>
            <if test="businessRepresentative != null">#{businessRepresentative},</if>
            <if test="businessDistrict != null">#{businessDistrict},</if>
            <if test="businessManager != null">#{businessManager},</if>
            <if test="businessRegion != null">#{businessRegion},</if>
            <if test="businessRegionManage != null">#{businessRegionManage},</if>
            <if test="commercialDirector != null">#{commercialDirector},</if>
            <if test="pharmacyLevel != null">#{pharmacyLevel},</if>
            <if test="productName != null">#{productName},</if>
        </trim>
    </insert>

    <update id="updateCustomerExecutionPlan" parameterType="CustomerExecutionPlan">
        update customer_execution_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerCode != null">customerCode = #{customerCode},</if>
            <if test="customerName != null">customerName = #{customerName},</if>
            <if test="commercialGrade != null">commercialGrade = #{commercialGrade},</if>
            <if test="commercialProvince != null">commercialProvince = #{commercialProvince},</if>
            <if test="businessRepresentative != null">businessRepresentative = #{businessRepresentative},</if>
            <if test="businessDistrict != null">businessDistrict = #{businessDistrict},</if>
            <if test="businessManager != null">businessManager = #{businessManager},</if>
            <if test="businessRegion != null">businessRegion = #{businessRegion},</if>
            <if test="businessRegionManage != null">businessRegionManage = #{businessRegionManage},</if>
            <if test="commercialDirector != null">commercialDirector = #{commercialDirector},</if>
            <if test="pharmacyLevel != null">pharmacyLevel = #{pharmacyLevel},</if>
            <if test="productName != null">productName = #{productName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerExecutionPlanById" parameterType="Integer">
        delete
        from customer_execution_plan
        where id = #{id}
    </delete>

    <delete id="deleteCustomerExecutionPlanByIds" parameterType="String">
        delete from customer_execution_plan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>