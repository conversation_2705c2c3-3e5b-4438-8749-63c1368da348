<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ddi.mapper.DdiBusinessMonthProcurement2025Mapper">

    <resultMap type="DdiBusinessMonthProcurement2025" id="DdiBusinessMonthProcurement2025Result">
        <result property="id" column="id"/>
        <result property="executionMonth" column="execution_month"/>
        <result property="businessMonth" column="business_month"/>
        <result property="businessCoding" column="business_coding"/>
        <result property="businessName" column="business_name"/>
        <result property="purchaseDate" column="purchase_date"/>
        <result property="vendorCode" column="vendor_code"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="standardProductCodes" column="standard_product_codes"/>
        <result property="standardProductName" column="standard_product_name"/>
        <result property="standardProductSpecification" column="standard_product_specification"/>
        <result property="standardUnits" column="standard_units"/>
        <result property="standardQuantity" column="standard_quantity"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="amount" column="amount"/>
        <result property="standardLotNumber" column="standard_lot_number"/>
        <result property="upstreamVendorCode" column="upstream_vendor_code"/>
        <result property="upstreamVendorName" column="upstream_vendor_name"/>
        <result property="productCode" column="product_code"/>
        <result property="productName" column="product_name"/>
        <result property="productSpecifications" column="product_specifications"/>
        <result property="manufacturer" column="manufacturer"/>
        <result property="quantityUnits" column="quantity_units"/>
        <result property="quantity" column="quantity"/>
        <result property="productUnitPrice" column="product_unit_price"/>
        <result property="productAmount" column="product_amount"/>
        <result property="productLotNumber" column="product_lot_number"/>
        <result property="expirationDate" column="expiration_date"/>
        <result property="dataType" column="data_type"/>
        <result property="maintenanceStatus" column="maintenance_status"/>
        <result property="creationTime" column="creation_time"/>
        <result property="updated" column="updated"/>
        <result property="deletionTime" column="deletion_time"/>
        <result property="deleteStatus" column="delete_status"/>
        <result property="commercialGrade" column="commercialGrade"/>
        <result property="commercialProvince" column="commercialProvince"/>
        <result property="secondaryDepartment" column="secondaryDepartment"/>
        <result property="thirdLevelDepartments" column="thirdLevelDepartments"/>
        <result property="fourthLevelDepartment" column="fourthLevelDepartment"/>
        <result property="fifthLevelDepartment" column="fifthLevelDepartment"/>
    </resultMap>


    <select id="selectKtnDdiBusinessMonthProcurement2025List" parameterType="DdiBusinessMonthProcurement2025"
            resultMap="DdiBusinessMonthProcurement2025Result">
        SELECT
        a.*,
        b.customerName,
        b.commercialGrade,
        b.commercialProvince,
        b.businessRepresentative,
        b.businessDistrict,
        b.businessManager,
        b.businessRegion,
        b.businessRegionManage,
        b.commercialDirector ,
        c.secondaryDepartment,
        c.thirdLevelDepartments,
        ( CASE c.isTargetTerminal WHEN '是' THEN c.ktnRegion1 WHEN '否' THEN c.fourthLevelDepartment ELSE '' END )
        'fourthLevelDepartment',
        ( CASE c.isTargetTerminal WHEN '是' THEN c.ktnArea1 WHEN '否' THEN c.fifthLevelDepartment ELSE '' END )
        'fifthLevelDepartment'
        FROM
        ddi_month_procurement a
        LEFT JOIN ddi_customer_information b ON b.customerCode= a.business_coding
        LEFT JOIN archives_information2025 c ON c.companyCode= a.business_coding
        <where>
            <if test="executionMonth != null  and executionMonth != ''">and execution_month like concat('%',
                #{executionMonth}, '%')
            </if>
            <if test="businessMonth != null  and businessMonth != ''">and business_month like concat('%',
                #{businessMonth}, '%')
            </if>
            <if test="businessCoding != null  and businessCoding != ''">and business_coding like concat('%',
                #{businessCoding}, '%')
            </if>
            <if test="businessName != null  and businessName != ''">and business_name like concat('%', #{businessName},
                '%')
            </if>
            <if test="params.beginPurchaseDate != null and params.beginPurchaseDate != '' and params.endPurchaseDate != null and params.endPurchaseDate != ''">
                and purchase_date between #{params.beginPurchaseDate} and #{params.endPurchaseDate}
            </if>
            <if test="vendorCode != null  and vendorCode != ''">and vendor_code like concat('%', #{vendorCode}, '%')
            </if>
            <if test="vendorName != null  and vendorName != ''">and vendor_name like concat('%', #{vendorName}, '%')
            </if>
            <if test="standardProductCodes != null  and standardProductCodes != ''">and standard_product_codes like
                concat('%', #{standardProductCodes}, '%')
            </if>
            <if test="standardProductName != null  and standardProductName != ''">and standard_product_name like
                concat('%', #{standardProductName}, '%')
            </if>
            <if test="standardProductSpecification != null  and standardProductSpecification != ''">and
                standard_product_specification like concat('%', #{standardProductSpecification}, '%')
            </if>
            <if test="standardUnits != null  and standardUnits != ''">and standard_units like concat('%',
                #{standardUnits}, '%')
            </if>
            <if test="standardQuantity != null ">and standard_quantity = #{standardQuantity}</if>
            <if test="unitPrice != null ">and unit_price = #{unitPrice}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="standardLotNumber != null  and standardLotNumber != ''">and standard_lot_number like concat('%',
                #{standardLotNumber}, '%')
            </if>
            <if test="upstreamVendorCode != null  and upstreamVendorCode != ''">and upstream_vendor_code like
                concat('%', #{upstreamVendorCode}, '%')
            </if>
            <if test="upstreamVendorName != null  and upstreamVendorName != ''">and upstream_vendor_name like
                concat('%', #{upstreamVendorName}, '%')
            </if>
            <if test="productCode != null  and productCode != ''">and product_code like concat('%', #{productCode},
                '%')
            </if>
            <if test="productName != null  and productName != ''">and product_name like concat('%', #{productName},
                '%')
            </if>
            <if test="productSpecifications != null  and productSpecifications != ''">and product_specifications like
                concat('%', #{productSpecifications}, '%')
            </if>
            <if test="manufacturer != null  and manufacturer != ''">and manufacturer like concat('%', #{manufacturer},
                '%')
            </if>
            <if test="quantityUnits != null  and quantityUnits != ''">and quantity_units = #{quantityUnits}</if>
            <if test="quantity != null ">and quantity = #{quantity}</if>
            <if test="productUnitPrice != null ">and product_unit_price = #{productUnitPrice}</if>
            <if test="productAmount != null ">and product_amount = #{productAmount}</if>
            <if test="productLotNumber != null  and productLotNumber != ''">and product_lot_number like concat('%',
                #{productLotNumber}, '%')
            </if>
            <if test="params.beginExpirationDate != null and params.beginExpirationDate != '' and params.endExpirationDate != null and params.endExpirationDate != ''">
                and expiration_date between #{params.beginExpirationDate} and #{params.endExpirationDate}
            </if>
            <if test="dataType != null  and dataType != ''">and data_type like concat('%', #{dataType}, '%')</if>
            <if test="maintenanceStatus != null  and maintenanceStatus != ''">and maintenance_status =
                #{maintenanceStatus}
            </if>
            <if test="creationTime != null ">and creation_time = #{creationTime}</if>
            <if test="updated != null ">and updated = #{updated}</if>
            <if test="deletionTime != null ">and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null ">and delete_status = #{deleteStatus}</if>
            <if test="commercialGrade != null  and commercialGrade != ''">and commercialGrade like concat('%',
                #{commercialGrade}, '%')
            </if>
            <if test="commercialProvince != null  and commercialProvince != ''">and commercialProvince like concat('%',
                #{commercialProvince}, '%')
            </if>
            <if test="provinceList !=null and provinceList.size>0">
                and commercialProvince in
                <foreach collection="provinceList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="secondaryDepartment != null  and secondaryDepartment != ''">and secondaryDepartment like
                concat('%', #{secondaryDepartment}, '%')
            </if>
            <if test="thirdLevelDepartments != null  and thirdLevelDepartments != ''">and thirdLevelDepartments like
                concat('%', #{thirdLevelDepartments}, '%')
            </if>
            <if test="fourthLevelDepartment != null  and fourthLevelDepartment != ''">and ktnRegion1 like
                concat('%', #{fourthLevelDepartment}, '%')
            </if>
            <if test="fifthLevelDepartment != null  and fifthLevelDepartment != ''">and ktnArea1 like
                concat('%', #{fifthLevelDepartment}, '%')
            </if>

            <if test="directorAreaList !=null and directorAreaList.size>0">
                and thirdLevelDepartments in
                <foreach collection="directorAreaList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="regionList !=null and regionList.size>0">
                and ktnRegion1 in
                <foreach collection="regionList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="areaList !=null and areaList.size>0">
                and ktnArea1 in
                <foreach collection="areaList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and a.delete_status ='0' and b.commercialGrade in ('一级商','二级商') and a.purchase_date >= '2025-01-01'
        </where>
    </select>

    <select id="selectYdfDdiBusinessMonthProcurement2025List" parameterType="DdiBusinessMonthProcurement2025"
            resultMap="DdiBusinessMonthProcurement2025Result">
        SELECT
        a.*,
        b.customerName,
        b.commercialGrade,
        b.commercialProvince,
        b.businessRepresentative,
        b.businessDistrict,
        b.businessManager,
        b.businessRegion,
        b.businessRegionManage,
        b.commercialDirector ,
        c.secondaryDepartment,
        c.thirdLevelDepartments,
        ( CASE c.isTargetTerminal WHEN '是' THEN c.ydfRegion1 WHEN '否' THEN c.fourthLevelDepartment ELSE '' END )
        'fourthLevelDepartment',
        ( CASE c.isTargetTerminal WHEN '是' THEN c.ydfArea1 WHEN '否' THEN c.fifthLevelDepartment ELSE '' END )
        'fifthLevelDepartment'
        FROM
        ddi_month_procurement a
        LEFT JOIN ddi_customer_information b ON b.customerCode= a.business_coding
        LEFT JOIN archives_information2025 c ON c.companyCode= a.business_coding
        <where>
            <if test="executionMonth != null  and executionMonth != ''">and execution_month like concat('%',
                #{executionMonth}, '%')
            </if>
            <if test="businessMonth != null  and businessMonth != ''">and business_month like concat('%',
                #{businessMonth}, '%')
            </if>
            <if test="businessCoding != null  and businessCoding != ''">and business_coding like concat('%',
                #{businessCoding}, '%')
            </if>
            <if test="businessName != null  and businessName != ''">and business_name like concat('%', #{businessName},
                '%')
            </if>
            <if test="params.beginPurchaseDate != null and params.beginPurchaseDate != '' and params.endPurchaseDate != null and params.endPurchaseDate != ''">
                and purchase_date between #{params.beginPurchaseDate} and #{params.endPurchaseDate}
            </if>
            <if test="vendorCode != null  and vendorCode != ''">and vendor_code like concat('%', #{vendorCode}, '%')
            </if>
            <if test="vendorName != null  and vendorName != ''">and vendor_name like concat('%', #{vendorName}, '%')
            </if>
            <if test="standardProductCodes != null  and standardProductCodes != ''">and standard_product_codes like
                concat('%', #{standardProductCodes}, '%')
            </if>
            <if test="standardProductName != null  and standardProductName != ''">and standard_product_name like
                concat('%', #{standardProductName}, '%')
            </if>
            <if test="standardProductSpecification != null  and standardProductSpecification != ''">and
                standard_product_specification like concat('%', #{standardProductSpecification}, '%')
            </if>
            <if test="standardUnits != null  and standardUnits != ''">and standard_units like concat('%',
                #{standardUnits}, '%')
            </if>
            <if test="standardQuantity != null ">and standard_quantity = #{standardQuantity}</if>
            <if test="unitPrice != null ">and unit_price = #{unitPrice}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="standardLotNumber != null  and standardLotNumber != ''">and standard_lot_number like concat('%',
                #{standardLotNumber}, '%')
            </if>
            <if test="upstreamVendorCode != null  and upstreamVendorCode != ''">and upstream_vendor_code like
                concat('%', #{upstreamVendorCode}, '%')
            </if>
            <if test="upstreamVendorName != null  and upstreamVendorName != ''">and upstream_vendor_name like
                concat('%', #{upstreamVendorName}, '%')
            </if>
            <if test="productCode != null  and productCode != ''">and product_code like concat('%', #{productCode},
                '%')
            </if>
            <if test="productName != null  and productName != ''">and product_name like concat('%', #{productName},
                '%')
            </if>
            <if test="productSpecifications != null  and productSpecifications != ''">and product_specifications like
                concat('%', #{productSpecifications}, '%')
            </if>
            <if test="manufacturer != null  and manufacturer != ''">and manufacturer like concat('%', #{manufacturer},
                '%')
            </if>
            <if test="quantityUnits != null  and quantityUnits != ''">and quantity_units = #{quantityUnits}</if>
            <if test="quantity != null ">and quantity = #{quantity}</if>
            <if test="productUnitPrice != null ">and product_unit_price = #{productUnitPrice}</if>
            <if test="productAmount != null ">and product_amount = #{productAmount}</if>
            <if test="productLotNumber != null  and productLotNumber != ''">and product_lot_number like concat('%',
                #{productLotNumber}, '%')
            </if>
            <if test="params.beginExpirationDate != null and params.beginExpirationDate != '' and params.endExpirationDate != null and params.endExpirationDate != ''">
                and expiration_date between #{params.beginExpirationDate} and #{params.endExpirationDate}
            </if>
            <if test="dataType != null  and dataType != ''">and data_type like concat('%', #{dataType}, '%')</if>
            <if test="maintenanceStatus != null  and maintenanceStatus != ''">and maintenance_status =
                #{maintenanceStatus}
            </if>
            <if test="creationTime != null ">and creation_time = #{creationTime}</if>
            <if test="updated != null ">and updated = #{updated}</if>
            <if test="deletionTime != null ">and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null ">and delete_status = #{deleteStatus}</if>
            <if test="commercialGrade != null  and commercialGrade != ''">and commercialGrade like concat('%',
                #{commercialGrade}, '%')
            </if>
            <if test="commercialProvince != null  and commercialProvince != ''">and commercialProvince like concat('%',
                #{commercialProvince}, '%')
            </if>
            <if test="provinceList !=null and provinceList.size>0">
                and commercialProvince in
                <foreach collection="provinceList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="secondaryDepartment != null  and secondaryDepartment != ''">and secondaryDepartment like
                concat('%', #{secondaryDepartment}, '%')
            </if>
            <if test="thirdLevelDepartments != null  and thirdLevelDepartments != ''">and thirdLevelDepartments like
                concat('%', #{thirdLevelDepartments}, '%')
            </if>
            <if test="fourthLevelDepartment != null  and fourthLevelDepartment != ''">and ydfRegion1 like
                concat('%', #{fourthLevelDepartment}, '%')
            </if>
            <if test="fifthLevelDepartment != null  and fifthLevelDepartment != ''">and ydfArea1 like
                concat('%', #{fifthLevelDepartment}, '%')
            </if>

            <if test="directorAreaList !=null and directorAreaList.size>0">
                and thirdLevelDepartments in
                <foreach collection="directorAreaList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="regionList !=null and regionList.size>0">
                and ydfRegion1 in
                <foreach collection="regionList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="areaList !=null and areaList.size>0">
                and ydfArea1 in
                <foreach collection="areaList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and a.delete_status ='0' and b.commercialGrade in ('一级商','二级商') and a.purchase_date >= '2025-01-01'
        </where>
    </select>

</mapper>