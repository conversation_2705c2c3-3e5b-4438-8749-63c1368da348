<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ddi.mapper.TerminalSalesOfficeMapper">

    <resultMap type="TerminalSalesOffice" id="TerminalSalesOfficeResult">
        <result property="id" column="id"/>
        <result property="salesArea" column="salesArea"/>
        <result property="salesRegion" column="salesRegion"/>
        <result property="salesAddress" column="salesAddress"/>
        <result property="salesDate" column="salesDate"/>
        <result property="dailySalesVolume" column="dailySalesVolume"/>
        <result property="dailyRanking" column="dailyRanking"/>
        <result property="dailyGrowthRate" column="dailyGrowthRate"/>
        <result property="weeklySalesVolume" column="weeklySalesVolume"/>
        <result property="weeklyRanking" column="weeklyRanking"/>
        <result property="weeklyGrowthRate" column="weeklyGrowthRate"/>
        <result property="monthlySalesVolume" column="monthlySalesVolume"/>
        <result property="monthlyRanking" column="monthlyRanking"/>
        <result property="monthlyGrowthRate" column="monthlyGrowthRate"/>
        <result property="salesVolumeYear24" column="salesVolumeYear24"/>
        <result property="rankingYear24" column="rankingYear24"/>
        <result property="salesAfterListing" column="salesAfterListing"/>
        <result property="salesRankingAfterListing" column="salesRankingAfterListing"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="quarterlySales" column="quarterlySales"/>
        <result property="quarterlyRanking" column="quarterlyRanking"/>
        <result property="quarterlyGrowthRate" column="quarterlyGrowthRate"/>

    </resultMap>

    <sql id="selectTerminalSalesOfficeVo">
        select id,
               salesArea,
               salesRegion,
               salesAddress,
               salesDate,
               dailySalesVolume,
               dailyRanking,
               dailyGrowthRate,
               weeklySalesVolume,
               weeklyRanking,
               weeklyGrowthRate,
               monthlySalesVolume,
               monthlyRanking,
               monthlyGrowthRate,
               salesVolumeYear24,
               rankingYear24,
               salesAfterListing,
               salesRankingAfterListing,
               deleteStatus,
               quarterlySales,
               quarterlyRanking,
               quarterlyGrowthRate
        from terminal_sales_office
    </sql>

    <select id="selectTerminalSalesOfficeList" parameterType="TerminalSalesOffice"
            resultMap="TerminalSalesOfficeResult">
        <include refid="selectTerminalSalesOfficeVo"/>
        <where>
            <if test="salesArea != null  and salesArea != ''">and salesArea like concat('%', #{salesArea}, '%')</if>
            <if test="salesRegion != null  and salesRegion != ''">and salesRegion like concat('%', #{salesRegion},
                '%')
            </if>
            <if test="salesAddress != null  and salesAddress != ''">and salesAddress like concat('%', #{salesAddress},
                '%')
            </if>
            <if test="salesDate != null ">and salesDate = #{salesDate}</if>
            <if test="dailySalesVolume != null ">and dailySalesVolume = #{dailySalesVolume}</if>
            <if test="dailyRanking != null ">and dailyRanking = #{dailyRanking}</if>
            <if test="dailyGrowthRate != null  and dailyGrowthRate != ''">and dailyGrowthRate = #{dailyGrowthRate}</if>
            <if test="weeklySalesVolume != null ">and weeklySalesVolume = #{weeklySalesVolume}</if>
            <if test="weeklyRanking != null ">and weeklyRanking = #{weeklyRanking}</if>
            <if test="weeklyGrowthRate != null  and weeklyGrowthRate != ''">and weeklyGrowthRate = #{weeklyGrowthRate}
            </if>
            <if test="monthlySalesVolume != null ">and monthlySalesVolume = #{monthlySalesVolume}</if>
            <if test="monthlyRanking != null ">and monthlyRanking = #{monthlyRanking}</if>
            <if test="monthlyGrowthRate != null  and monthlyGrowthRate != ''">and monthlyGrowthRate =
                #{monthlyGrowthRate}
            </if>
            <if test="salesVolumeYear24 != null ">and salesVolumeYear24 = #{salesVolumeYear24}</if>
            <if test="rankingYear24 != null ">and rankingYear24 = #{rankingYear24}</if>
            <if test="salesAfterListing != null ">and salesAfterListing = #{salesAfterListing}</if>
            <if test="salesRankingAfterListing != null ">and salesRankingAfterListing = #{salesRankingAfterListing}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="addressList !=null and addressList.size>0">
                and salesAddress in
                <foreach collection="addressList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and deleteStatus ='0'
        </where>
    </select>

    <select id="selectTerminalSalesOfficeById" parameterType="Integer" resultMap="TerminalSalesOfficeResult">
        <include refid="selectTerminalSalesOfficeVo"/>
        where id = #{id}
    </select>

    <insert id="insertTerminalSalesOffice" parameterType="TerminalSalesOffice">
        insert into terminal_sales_office
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="salesArea != null">salesArea,</if>
            <if test="salesRegion != null">salesRegion,</if>
            <if test="salesAddress != null">salesAddress,</if>
            <if test="salesDate != null">salesDate,</if>
            <if test="dailySalesVolume != null">dailySalesVolume,</if>
            <if test="dailyRanking != null">dailyRanking,</if>
            <if test="dailyGrowthRate != null">dailyGrowthRate,</if>
            <if test="weeklySalesVolume != null">weeklySalesVolume,</if>
            <if test="weeklyRanking != null">weeklyRanking,</if>
            <if test="weeklyGrowthRate != null">weeklyGrowthRate,</if>
            <if test="monthlySalesVolume != null">monthlySalesVolume,</if>
            <if test="monthlyRanking != null">monthlyRanking,</if>
            <if test="monthlyGrowthRate != null">monthlyGrowthRate,</if>
            <if test="salesVolumeYear24 != null">salesVolumeYear24,</if>
            <if test="rankingYear24 != null">rankingYear24,</if>
            <if test="salesAfterListing != null">salesAfterListing,</if>
            <if test="salesRankingAfterListing != null">salesRankingAfterListing,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="quarterlySales != null">quarterlySales,</if>
            <if test="quarterlyRanking != null">quarterlyRanking,</if>
            <if test="quarterlyGrowthRate != null">quarterlyGrowthRate,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="salesArea != null">#{salesArea},</if>
            <if test="salesRegion != null">#{salesRegion},</if>
            <if test="salesAddress != null">#{salesAddress},</if>
            <if test="salesDate != null">#{salesDate},</if>
            <if test="dailySalesVolume != null">#{dailySalesVolume},</if>
            <if test="dailyRanking != null">#{dailyRanking},</if>
            <if test="dailyGrowthRate != null">#{dailyGrowthRate},</if>
            <if test="weeklySalesVolume != null">#{weeklySalesVolume},</if>
            <if test="weeklyRanking != null">#{weeklyRanking},</if>
            <if test="weeklyGrowthRate != null">#{weeklyGrowthRate},</if>
            <if test="monthlySalesVolume != null">#{monthlySalesVolume},</if>
            <if test="monthlyRanking != null">#{monthlyRanking},</if>
            <if test="monthlyGrowthRate != null">#{monthlyGrowthRate},</if>
            <if test="salesVolumeYear24 != null">#{salesVolumeYear24},</if>
            <if test="rankingYear24 != null">#{rankingYear24},</if>
            <if test="salesAfterListing != null">#{salesAfterListing},</if>
            <if test="salesRankingAfterListing != null">#{salesRankingAfterListing},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="quarterlySales != null">#{quarterlySales},</if>
            <if test="quarterlyRanking != null">#{quarterlyRanking},</if>
            <if test="quarterlyGrowthRate != null">#{quarterlyGrowthRate},</if>
        </trim>
    </insert>

    <update id="updateTerminalSalesOffice" parameterType="TerminalSalesOffice">
        update terminal_sales_office
        <trim prefix="SET" suffixOverrides=",">
            <if test="salesArea != null">salesArea = #{salesArea},</if>
            <if test="salesRegion != null">salesRegion = #{salesRegion},</if>
            <if test="salesAddress != null">salesAddress = #{salesAddress},</if>
            <if test="salesDate != null">salesDate = #{salesDate},</if>
            <if test="dailySalesVolume != null">dailySalesVolume = #{dailySalesVolume},</if>
            <if test="dailyRanking != null">dailyRanking = #{dailyRanking},</if>
            <if test="dailyGrowthRate != null">dailyGrowthRate = #{dailyGrowthRate},</if>
            <if test="weeklySalesVolume != null">weeklySalesVolume = #{weeklySalesVolume},</if>
            <if test="weeklyRanking != null">weeklyRanking = #{weeklyRanking},</if>
            <if test="weeklyGrowthRate != null">weeklyGrowthRate = #{weeklyGrowthRate},</if>
            <if test="monthlySalesVolume != null">monthlySalesVolume = #{monthlySalesVolume},</if>
            <if test="monthlyRanking != null">monthlyRanking = #{monthlyRanking},</if>
            <if test="monthlyGrowthRate != null">monthlyGrowthRate = #{monthlyGrowthRate},</if>
            <if test="salesVolumeYear24 != null">salesVolumeYear24 = #{salesVolumeYear24},</if>
            <if test="rankingYear24 != null">rankingYear24 = #{rankingYear24},</if>
            <if test="salesAfterListing != null">salesAfterListing = #{salesAfterListing},</if>
            <if test="salesRankingAfterListing != null">salesRankingAfterListing = #{salesRankingAfterListing},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
            <if test="quarterlySales != null">quarterlySales = #{quarterlySales},</if>
            <if test="quarterlyRanking != null">quarterlyRanking = #{quarterlyRanking},</if>
            <if test="quarterlyGrowthRate != null">quarterlyGrowthRate = #{quarterlyGrowthRate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTerminalSalesOfficeById" parameterType="Integer">
        delete
        from terminal_sales_office
        where id = #{id}
    </delete>

    <delete id="deleteTerminalSalesOfficeByIds" parameterType="String">
        delete from terminal_sales_office where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateOfficeStatus" parameterType="TerminalSalesOffice">
        update terminal_sales_office
        set deleteStatus='1'
        where salesDate = #{salesDate}
    </update>
</mapper>