<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupWorkOrder.mapper.GroupWorkHoursSummaryMapper">

    <resultMap type="GroupWorkHoursSummary" id="GroupWorkHoursSummaryResult">
        <result property="name" column="name"/>
        <result property="jobNumber" column="jobNumber"/>
        <result property="totalStandardWorkingHours" column="totalStandardWorkingHours"/>
        <result property="totalCompletedWorkHours" column="totalCompletedWorkHours"/>
    </resultMap>

    <select id="selectGroupWorkHoursSummaryList" parameterType="GroupWorkHoursSummary"
            resultMap="GroupWorkHoursSummaryResult">
        select *
        from (
                 select
                 b.fd_name AS 'name',
                 b.fd_no AS 'jobNumber',
                 SUM(isnull(a.fd_3c17c051989c40, 0)) AS 'totalStandardWorkingHours',
                 SUM(convert(decimal(18,1),isnull(a.fd_3bfdfdd1255910,0))) as 'totalCompletedWorkHours'
                 from ekp_ITFWGD a
                 left join
                 hr_org_element b ON b.fd_id = a.fd_3ac85bf3d30d48
                 left join
                 hr_org_element c on c.fd_id = b.fd_parentorgid
                 <where>
                     <if test="params.beginDateCreated != null  and params.beginDateCreated != '' and params.endDateCreated != null  and params.endDateCreated != '' ">
                         and a.fd_3c17bf239d3370 between #{params.beginDateCreated} and #{params.endDateCreated}
                     </if>
                     <if test="name != null  and name != ''">and b.fd_name like concat('%', #{name}, '%')</if>
                     <if test="jobNumber != null  and jobNumber != ''">and b.fd_no like concat('%', #{jobNumber}, '%')</if>
                     and
                     b.fd_no is not null
                     and
                     (c.fd_name='康方药业' or c.fd_name='康融广东')
                 </where>
                 group by
                 b.fd_name, b.fd_no
             ) d
    </select>
</mapper>