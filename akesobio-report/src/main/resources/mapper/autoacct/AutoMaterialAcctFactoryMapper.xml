<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoMaterialAcctFactoryMapper">
    
    <resultMap type="AutoMaterialAcctFactory" id="AutoMaterialAcctFactoryResult">
        <result property="id"                column="id" />
        <result property="factoryCode"       column="factory_code" />
        <result property="factoryName"       column="factory_name" />
        <result property="status"           column="status" />
        <result property="sortOrder"        column="sort_order" />
        <result property="creationTime"     column="creation_time" />
        <result property="createdBy"        column="created_by" />
        <result property="updated"          column="updated" />
        <result property="updater"          column="updater" />
        <result property="deletionTime"     column="deletion_time" />
        <result property="deleteStatus"     column="delete_status" />
    </resultMap>

    <sql id="selectAutoMaterialAcctFactoryVo">
        select id, factory_code, factory_name, status, sort_order, 
               creation_time, created_by, updated, updater, deletion_time, delete_status 
        from auto_material_acct_factory
    </sql>

    <select id="selectAutoMaterialAcctFactoryList" parameterType="AutoMaterialAcctFactory" resultMap="AutoMaterialAcctFactoryResult">
        <include refid="selectAutoMaterialAcctFactoryVo"/>
        <where>  
            <if test="factoryCode != null and factoryCode != ''"> and factory_code like concat('%', #{factoryCode}, '%')</if>
            <if test="factoryName != null and factoryName != ''"> and factory_name like concat('%', #{factoryName}, '%')</if>
            <if test="status != null"> and status = #{status}</if>
            <if test="factoryList != null">
                and factory_code in
                <foreach collection="factoryList" item="factory" open="(" separator="," close=")">
                    #{factory}
                </foreach>
            </if>
            and delete_status = 0
        </where>
        order by sort_order asc, creation_time desc
    </select>
    
    <select id="selectAutoMaterialAcctFactoryById" parameterType="Long" resultMap="AutoMaterialAcctFactoryResult">
        <include refid="selectAutoMaterialAcctFactoryVo"/>
        where id = #{id} and delete_status = 0
    </select>

    <select id="selectEnabledFactories" resultMap="AutoMaterialAcctFactoryResult">
        <include refid="selectAutoMaterialAcctFactoryVo"/>
        where status = 1 and delete_status = 0
        order by sort_order asc
    </select>
        
    <insert id="insertAutoMaterialAcctFactory" parameterType="AutoMaterialAcctFactory" useGeneratedKeys="true" keyProperty="id">
        insert into auto_material_acct_factory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="factoryCode != null and factoryCode != ''">factory_code,</if>
            <if test="factoryName != null and factoryName != ''">factory_name,</if>
            <if test="status != null">status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="factoryCode != null and factoryCode != ''">#{factoryCode},</if>
            <if test="factoryName != null and factoryName != ''">#{factoryName},</if>
            <if test="status != null">#{status},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
         </trim>
    </insert>

    <update id="updateAutoMaterialAcctFactory" parameterType="AutoMaterialAcctFactory">
        update auto_material_acct_factory
        <trim prefix="SET" suffixOverrides=",">
            <if test="factoryCode != null and factoryCode != ''">factory_code = #{factoryCode},</if>
            <if test="factoryName != null and factoryName != ''">factory_name = #{factoryName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updated != null">updated = #{updated},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="deletionTime != null">deletion_time = #{deletionTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAutoMaterialAcctFactoryById" parameterType="Long">
        update auto_material_acct_factory set delete_status = 1, deletion_time = unix_timestamp() * 1000 where id = #{id}
    </update>

    <update id="deleteAutoMaterialAcctFactoryByIds" parameterType="String">
        update auto_material_acct_factory set delete_status = 1, deletion_time = unix_timestamp() * 1000 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>