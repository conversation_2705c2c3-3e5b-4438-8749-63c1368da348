<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoMaterialAcctPurchaseNotreturnMapper">
    
    <resultMap type="AutoMaterialAcctPurchaseNotreturn" id="AutoMaterialAcctPurchaseNotreturnResult">
        <result property="id"    column="id"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDesc"    column="material_desc"    />
        <result property="manufacturer"    column="manufacturer"    />
        <result property="articleNumber"    column="article_number"    />
        <result property="unit"    column="unit"    />
        <result property="specification"    column="specification"    />
        <result property="purchasingUnit"    column="purchasing_unit"    />
        <result property="purchasingUnitConversion"    column="purchasing_unit_conversion"    />
        <result property="purchasingUnitActualOrder"    column="purchasing_unit_actual_order"    />
        <result property="planArrivalTime"    column="plan_arrival_time"    />
        <result property="convertUnitUnreturnedQuantity"    column="convert_unit_unreturned_quantity"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updated"    column="updated"    />
        <result property="updater"    column="updater"    />
        <result property="deletionTime"    column="deletion_time"    />
        <result property="deleteStatus"    column="delete_status"    />
        <result property="factoryCode"    column="factory_code"    />
        <result property="planDate"    column="plan_date"    />
        <result property="material"    column="material"    />
    </resultMap>

    <sql id="selectAutoMaterialAcctPurchaseNotreturnVo">
        select id, material_code, material_desc, manufacturers, article_number, unit, specification, purchasing_unit, purchasing_unit_conversion, purchasing_unit_actual_order, plan_arrival_time, convert_unit_unreturned_quantity, creation_time, created_by, updated, updater, deletion_time, delete_status, factory_code, plan_date,material from auto_material_acct_purchase_notreturn
    </sql>

    <select id="selectAutoMaterialAcctPurchaseNotreturnList" parameterType="AutoMaterialAcctPurchaseNotreturn" resultMap="AutoMaterialAcctPurchaseNotreturnResult">
        <include refid="selectAutoMaterialAcctPurchaseNotreturnVo"/>
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="factoryList != null ">
                and factory_code in
                <foreach collection="factoryList" item="factory" open="(" separator="," close=")">
                    #{factory}
                </foreach>
            </if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDesc != null  and materialDesc != ''"> and material_desc = #{materialDesc}</if>
            <if test="manufacturers != null  and manufacturers != ''"> and manufacturers = #{manufacturers}</if>
            <if test="articleNumber != null  and articleNumber != ''"> and article_number = #{articleNumber}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="purchasingUnit != null  and purchasingUnit != ''"> and purchasing_unit = #{purchasingUnit}</if>
            <if test="purchasingUnitConversion != null  and purchasingUnitConversion != ''"> and purchasing_unit_conversion = #{purchasingUnitConversion}</if>
            <if test="purchasingUnitActualOrder != null  and purchasingUnitActualOrder != ''"> and purchasing_unit_actual_order = #{purchasingUnitActualOrder}</if>
            <if test="planArrivalTime != null "> and plan_arrival_time = #{planArrivalTime}</if>
            <if test="convertUnitUnreturnedQuantity != null  and convertUnitUnreturnedQuantity != ''"> and convert_unit_unreturned_quantity = #{convertUnitUnreturnedQuantity}</if>
            <if test="creationTime != null "> and creation_time = #{creationTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updated != null "> and updated = #{updated}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="deletionTime != null "> and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
            <if test="factoryCode != null  and factoryCode != ''"> and factory_code = #{factoryCode}</if>
            <if test="planDate != null  and planDate != ''"> and plan_date = #{planDate}</if>
            <if test="material != null  and material != ''"> and material = #{material}</if>
        </where>
    </select>


    <select id="selectAutoMaterialAcctPurchaseNotreturnTestList" parameterType="AutoMaterialAcctPurchaseNotreturn" resultMap="AutoMaterialAcctPurchaseNotreturnResult">
        select id, material_code, material_desc, manufacturers, article_number, unit, specification, purchasing_unit, purchasing_unit_conversion, purchasing_unit_actual_order, plan_arrival_time, convert_unit_unreturned_quantity, creation_time, created_by, updated, updater, deletion_time, delete_status, factory_code, plan_date,material from auto_material_acct_purchase_notreturn_copy1
        <where>
            <if test="factoryList != null ">
                and factory_code in
                <foreach collection="factoryList" item="factory" open="(" separator="," close=")">
                    #{factory}
                </foreach>
            </if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDesc != null  and materialDesc != ''"> and material_desc = #{materialDesc}</if>
            <if test="manufacturers != null  and manufacturers != ''"> and manufacturers = #{manufacturers}</if>
            <if test="articleNumber != null  and articleNumber != ''"> and article_number = #{articleNumber}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="purchasingUnit != null  and purchasingUnit != ''"> and purchasing_unit = #{purchasingUnit}</if>
            <if test="purchasingUnitConversion != null  and purchasingUnitConversion != ''"> and purchasing_unit_conversion = #{purchasingUnitConversion}</if>
            <if test="purchasingUnitActualOrder != null  and purchasingUnitActualOrder != ''"> and purchasing_unit_actual_order = #{purchasingUnitActualOrder}</if>
            <if test="planArrivalTime != null "> and plan_arrival_time = #{planArrivalTime}</if>
            <if test="convertUnitUnreturnedQuantity != null  and convertUnitUnreturnedQuantity != ''"> and convert_unit_unreturned_quantity = #{convertUnitUnreturnedQuantity}</if>
            <if test="creationTime != null "> and creation_time = #{creationTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updated != null "> and updated = #{updated}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="deletionTime != null "> and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
            <if test="factoryCode != null  and factoryCode != ''"> and factory_code = #{factoryCode}</if>
            <if test="planDate != null  and planDate != ''"> and plan_date = #{planDate}</if>
            <if test="material != null  and material != ''"> and material = #{material}</if>
        </where>
    </select>



    <select id="selectAutoMaterialAcctPurchaseNotreturnById" parameterType="Long" resultMap="AutoMaterialAcctPurchaseNotreturnResult">
        <include refid="selectAutoMaterialAcctPurchaseNotreturnVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAutoMaterialAcctPurchaseNotreturn" parameterType="AutoMaterialAcctPurchaseNotreturn">
        insert into auto_material_acct_purchase_notreturn
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDesc != null">material_desc,</if>
            <if test="manufacturers != null">manufacturers,</if>
            <if test="articleNumber != null">article_number,</if>
            <if test="unit != null">unit,</if>
            <if test="specification != null">specification,</if>
            <if test="purchasingUnit != null">purchasing_unit,</if>
            <if test="purchasingUnitConversion != null">purchasing_unit_conversion,</if>
            <if test="purchasingUnitActualOrder != null">purchasing_unit_actual_order,</if>
            <if test="planArrivalTime != null">plan_arrival_time,</if>
            <if test="convertUnitUnreturnedQuantity != null">convert_unit_unreturned_quantity,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="factoryCode != null">factory_code,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="material != null">material,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDesc != null">#{materialDesc},</if>
            <if test="manufacturers != null">#{manufacturers},</if>
            <if test="articleNumber != null">#{articleNumber},</if>
            <if test="unit != null">#{unit},</if>
            <if test="specification != null">#{specification},</if>
            <if test="purchasingUnit != null">#{purchasingUnit},</if>
            <if test="purchasingUnitConversion != null">#{purchasingUnitConversion},</if>
            <if test="purchasingUnitActualOrder != null">#{purchasingUnitActualOrder},</if>
            <if test="planArrivalTime != null">#{planArrivalTime},</if>
            <if test="convertUnitUnreturnedQuantity != null">#{convertUnitUnreturnedQuantity},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="factoryCode != null">#{factoryCode},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="material != null">#{material},</if>
         </trim>
    </insert>


    <update id="updateAutoMaterialAcctPurchaseNotreturn" parameterType="AutoMaterialAcctPurchaseNotreturn">
        update auto_material_acct_purchase_notreturn
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDesc != null">material_desc = #{materialDesc},</if>
           <if test="manufacturers != null">manufacturers = #{manufacturers},</if>
            <if test="articleNumber != null">article_number = #{articleNumber},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="purchasingUnit != null">purchasing_unit = #{purchasingUnit},</if>
            <if test="purchasingUnitConversion != null">purchasing_unit_conversion = #{purchasingUnitConversion},</if>
            <if test="purchasingUnitActualOrder != null">purchasing_unit_actual_order = #{purchasingUnitActualOrder},</if>
            <if test="planArrivalTime != null">plan_arrival_time = #{planArrivalTime},</if>
            <if test="convertUnitUnreturnedQuantity != null">convert_unit_unreturned_quantity = #{convertUnitUnreturnedQuantity},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updated != null">updated = #{updated},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="deletionTime != null">deletion_time = #{deletionTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="factoryCode != null">factory_code = #{factoryCode},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="material != null">material = #{material},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoMaterialAcctPurchaseNotreturnById" parameterType="Long">
        delete from auto_material_acct_purchase_notreturn where id = #{id}
    </delete>

    <delete id="deleteAutoMaterialAcctPurchaseNotreturnByIds" parameterType="String">
        delete from auto_material_acct_purchase_notreturn where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertAutoMaterialAcctPurchaseNotreturnCpoy1" parameterType="AutoMaterialAcctPurchaseNotreturn">
        INSERT INTO
        auto_material_acct_purchase_notreturn_copy1
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDesc != null">material_desc,</if>
            <if test="manufacturers != null">manufacturers,</if>
            <if test="articleNumber != null">article_number,</if>
            <if test="unit != null">unit,</if>
            <if test="specification != null">specification,</if>
            <if test="purchasingUnit != null">purchasing_unit,</if>
            <if test="purchasingUnitConversion != null">purchasing_unit_conversion,</if>
            <if test="purchasingUnitActualOrder != null">purchasing_unit_actual_order,</if>
            <if test="planArrivalTime != null">plan_arrival_time,</if>
            <if test="convertUnitUnreturnedQuantity != null">convert_unit_unreturned_quantity,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="factoryCode != null">factory_code,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="material != null">material,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDesc != null">#{materialDesc},</if>
            <if test="manufacturers != null">#{manufacturers},</if>
            <if test="articleNumber != null">#{articleNumber},</if>
            <if test="unit != null">#{unit},</if>
            <if test="specification != null">#{specification},</if>
            <if test="purchasingUnit != null">#{purchasingUnit},</if>
            <if test="purchasingUnitConversion != null">#{purchasingUnitConversion},</if>
            <if test="purchasingUnitActualOrder != null">#{purchasingUnitActualOrder},</if>
            <if test="planArrivalTime != null">#{planArrivalTime},</if>
            <if test="convertUnitUnreturnedQuantity != null">#{convertUnitUnreturnedQuantity},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="factoryCode != null">#{factoryCode},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="material != null">#{material},</if>
        </trim>
    </insert>

    <delete id="truncateTableAcctPurchaseNotreturnCpoy1" >
        TRUNCATE TABLE auto_material_acct_purchase_notreturn_copy1
    </delete>

    <delete id="truncateTableAcctPurchaseNotreturn" >
        TRUNCATE TABLE auto_material_acct_purchase_notreturn
    </delete>

</mapper>