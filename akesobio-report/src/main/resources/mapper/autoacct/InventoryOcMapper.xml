<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.InventoryOcMapper">


    <delete id="truncateTableAutoMaterialAcctInventoryOc" >
        TRUNCATE TABLE auto_material_acct_inventory_oc;
    </delete>

    <insert id="batchInsertAutoMaterialAcctInventoryOc" parameterType="java.util.List">
        insert into auto_material_acct_inventory_oc (
        id, factory, material, material_desc, packing_specifications, batch, production_batches, non_restricted_inventory, basic_unit, quality_inspection, frozen, shelf_expiration_date, recent_receipt_date, manufacture_date, old_material_number, storage_location, storage_location_desc, raw_material_batch, supplier_lots, manufacturers,article_number, creation_time, created_by, updated, updater, deletion_time, delete_status
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.factory}, #{item.material}, #{item.materialDesc}, #{item.packingSpecifications}, #{item.batch}, #{item.productionBatches}, #{item.nonRestrictedInventory}, #{item.basicUnit}, #{item.qualityInspection}, #{item.frozen}, #{item.shelfExpirationDate}, #{item.recentReceiptDate}, #{item.manufactureDate}, #{item.oldMaterialNumber}, #{item.storageLocation}, #{item.storageLocationDesc}, #{item.rawMaterialBatch}, #{item.supplierLots}, #{item.manufacturers}, #{item.articleNumber}, #{item.creationTime}, #{item.createdBy}, #{item.updated}, #{item.updater}, #{item.deletionTime}, #{item.deleteStatus}
            )
        </foreach>
    </insert>

</mapper>