<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoMaterialAcctOtherRequirementMapper">

    <resultMap type="AutoMaterialAcctOtherRequirement" id="AutoMaterialAcctOtherRequirementResult">
        <result property="id"    column="id"    />
        <result property="materialCode"    column="material_code"    />
        <result property="basicUnit"    column="basic_unit"    />
        <result property="specification"    column="specification"    />
        <result property="requiredQuantity"    column="required_quantity"    />
        <result property="requirementDesc"    column="requirement_desc"    />
        <result property="requirementPersonnel"    column="requirement_personnel"    />
        <result property="requirementSource"    column="requirement_source"    />
        <result property="planCompletionTime"    column="plan_completion_time"    />
        <result property="progressStatus"    column="progress_status"    />
        <result property="latestRequiredQuantity"    column="latest_required_quantity"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updated"    column="updated"    />
        <result property="updater"    column="updater"    />
        <result property="deletionTime"    column="deletion_time"    />
        <result property="deleteStatus"    column="delete_status"    />
        <result property="factoryCode"    column="factory_code"    />
        <result property="planDate"    column="plan_date"    />
    </resultMap>

    <sql id="selectAutoMaterialAcctOtherRequirementVo">
        select id, material_code, basic_unit, specification, required_quantity, requirement_desc, requirement_personnel, requirement_source, plan_completion_time, progress_status, latest_required_quantity, creation_time, created_by, updated, updater, deletion_time, delete_status, factory_code, plan_date from auto_material_acct_other_requirement
    </sql>

    <select id="selectAutoMaterialAcctOtherRequirementList" parameterType="AutoMaterialAcctOtherRequirement" resultMap="AutoMaterialAcctOtherRequirementResult">
        <include refid="selectAutoMaterialAcctOtherRequirementVo"/>
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="basicUnit != null  and basicUnit != ''"> and basic_unit = #{basicUnit}</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="requiredQuantity != null "> and required_quantity = #{requiredQuantity}</if>
            <if test="requirementDesc != null  and requirementDesc != ''"> and requirement_desc = #{requirementDesc}</if>
            <if test="requirementPersonnel != null  and requirementPersonnel != ''"> and requirement_personnel = #{requirementPersonnel}</if>
            <if test="requirementSource != null  and requirementSource != ''"> and requirement_source = #{requirementSource}</if>
            <if test="planCompletionTime != null "> and plan_completion_time = #{planCompletionTime}</if>
            <if test="progressStatus != null  and progressStatus != ''"> and progress_status = #{progressStatus}</if>
            <if test="latestRequiredQuantity != null "> and latest_required_quantity = #{latestRequiredQuantity}</if>
            <if test="creationTime != null "> and creation_time = #{creationTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updated != null "> and updated = #{updated}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="deletionTime != null "> and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
            <if test="factoryCode != null  and factoryCode != ''"> and factory_code = #{factoryCode}</if>
            <if test="planDate != null  and planDate != ''"> and plan_date = #{planDate}</if>
        </where>
    </select>

    <select id="selectAutoMaterialAcctOtherRequirementById" parameterType="Long" resultMap="AutoMaterialAcctOtherRequirementResult">
        <include refid="selectAutoMaterialAcctOtherRequirementVo"/>
        where id = #{id}
    </select>

    <insert id="insertAutoMaterialAcctOtherRequirement" parameterType="AutoMaterialAcctOtherRequirement">
        insert into auto_material_acct_other_requirement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="basicUnit != null">basic_unit,</if>
            <if test="specification != null">specification,</if>
            <if test="requiredQuantity != null">required_quantity,</if>
            <if test="requirementDesc != null">requirement_desc,</if>
            <if test="requirementPersonnel != null">requirement_personnel,</if>
            <if test="requirementSource != null">requirement_source,</if>
            <if test="planCompletionTime != null">plan_completion_time,</if>
            <if test="progressStatus != null">progress_status,</if>
            <if test="latestRequiredQuantity != null">latest_required_quantity,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="factoryCode != null">factory_code,</if>
            <if test="planDate != null">plan_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="basicUnit != null">#{basicUnit},</if>
            <if test="specification != null">#{specification},</if>
            <if test="requiredQuantity != null">#{requiredQuantity},</if>
            <if test="requirementDesc != null">#{requirementDesc},</if>
            <if test="requirementPersonnel != null">#{requirementPersonnel},</if>
            <if test="requirementSource != null">#{requirementSource},</if>
            <if test="planCompletionTime != null">#{planCompletionTime},</if>
            <if test="progressStatus != null">#{progressStatus},</if>
            <if test="latestRequiredQuantity != null">#{latestRequiredQuantity},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="factoryCode != null">#{factoryCode},</if>
            <if test="planDate != null">#{planDate},</if>
        </trim>
    </insert>

    <update id="updateAutoMaterialAcctOtherRequirement" parameterType="AutoMaterialAcctOtherRequirement">
        update auto_material_acct_other_requirement
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="basicUnit != null">basic_unit = #{basicUnit},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="requiredQuantity != null">required_quantity = #{requiredQuantity},</if>
            <if test="requirementDesc != null">requirement_desc = #{requirementDesc},</if>
            <if test="requirementPersonnel != null">requirement_personnel = #{requirementPersonnel},</if>
            <if test="requirementSource != null">requirement_source = #{requirementSource},</if>
            <if test="planCompletionTime != null">plan_completion_time = #{planCompletionTime},</if>
            <if test="progressStatus != null">progress_status = #{progressStatus},</if>
            <if test="latestRequiredQuantity != null">latest_required_quantity = #{latestRequiredQuantity},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updated != null">updated = #{updated},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="deletionTime != null">deletion_time = #{deletionTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="factoryCode != null">factory_code = #{factoryCode},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoMaterialAcctOtherRequirementById" parameterType="Long">
        delete from auto_material_acct_other_requirement where id = #{id}
    </delete>

    <delete id="deleteAutoMaterialAcctOtherRequirementByIds" parameterType="String">
        delete from auto_material_acct_other_requirement where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>