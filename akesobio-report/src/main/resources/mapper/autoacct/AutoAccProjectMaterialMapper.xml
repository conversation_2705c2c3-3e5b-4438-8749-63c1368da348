<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoAccProjectMaterialMapper">
    
    <resultMap type="AutoAccProjectMaterial" id="AutoAccProjectMaterialResult">
        <result property="factoryCode"    column="factory_code"    />
        <result property="materialCode"    column="material_code"    />
        <result property="singleBatchActualDemand"    column="single_batch_actual_demand"    />
        <result property="projectNumber"    column="project_number"    />
        <result property="id"    column="id"    />
    </resultMap>

    <sql id="selectAutoAccProjectMaterialVo">
        select factory_code, material_code, single_batch_actual_demand, project_number, id from auto_acc_project_material
    </sql>

    <select id="selectAutoAccProjectMaterialList" parameterType="AutoAccProjectMaterial" resultMap="AutoAccProjectMaterialResult">
        <include refid="selectAutoAccProjectMaterialVo"/>
        <where>  
            <if test="id != null"> and id = #{id}</if>
            <if test="factoryCode != null  and factoryCode != ''"> and factory_code = #{factoryCode}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="singleBatchActualDemand != null  and singleBatchActualDemand != ''"> and single_batch_actual_demand = #{singleBatchActualDemand}</if>
            <if test="projectNumber != null  and projectNumber != ''"> and project_number = #{projectNumber}</if>
        </where>
    </select>
    
    <select id="selectAutoAccProjectMaterialById" parameterType="Long" resultMap="AutoAccProjectMaterialResult">
        <include refid="selectAutoAccProjectMaterialVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAutoAccProjectMaterial" parameterType="AutoAccProjectMaterial">
        insert into auto_acc_project_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="factoryCode != null">factory_code,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="singleBatchActualDemand != null">single_batch_actual_demand,</if>
            <if test="projectNumber != null">project_number,</if>
            <if test="id != null">id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="factoryCode != null">#{factoryCode},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="singleBatchActualDemand != null">#{singleBatchActualDemand},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="id != null">#{id},</if>
         </trim>
    </insert>

    <update id="updateAutoAccProjectMaterial" parameterType="AutoAccProjectMaterial">
        update auto_acc_project_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="factoryCode != null">factory_code = #{factoryCode},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="singleBatchActualDemand != null">single_batch_actual_demand = #{singleBatchActualDemand},</if>
            <if test="projectNumber != null">project_number = #{projectNumber},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoAccProjectMaterialById" parameterType="Long">
        delete from auto_acc_project_material where id = #{id}
    </delete>

    <delete id="deleteAutoAccProjectMaterialByIds" parameterType="String">
        delete from auto_acc_project_material where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>