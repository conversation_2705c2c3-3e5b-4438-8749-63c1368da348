<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoMaterialAcctMpmInfoMapper">
    
    <resultMap type="MpmInfo" id="AutoMaterialAcctMpmInfoResult">
        <result property="id"    column="id"    />
        <result property="mpmCode"    column="mpm_code"    />
        <result property="mpmName"    column="mpm_name"    />
        <result property="discrepancyInformation"    column="discrepancy_information"    />
        <result property="remark"    column="remark"    />
        <result property="param1"    column="param1"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updated"    column="updated"    />
        <result property="updater"    column="updater"    />
        <result property="deletionTime"    column="deletion_time"    />
        <result property="deleteStatus"    column="delete_status"    />
        <result property="factoryCode"    column="factory_code"    />
    </resultMap>

    <sql id="selectAutoMaterialAcctMpmInfoVo">
        select id, mpm_code, mpm_name, discrepancy_information, remark, param1, creation_time, created_by, updated, updater, deletion_time, delete_status,factory_code,product_name,product_code from auto_material_acct_mpm_info
    </sql>

    <select id="selectAutoMaterialAcctMpmInfoList" parameterType="MpmInfo" resultMap="AutoMaterialAcctMpmInfoResult">
        <include refid="selectAutoMaterialAcctMpmInfoVo"/>
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="factoryList != null ">
                and factory_code in
                <foreach collection="factoryList" item="factory" open="(" separator="," close=")">
                    #{factory}
                </foreach>
            </if>
            <if test="mpmCode != null  and mpmCode != ''"> and mpm_code = #{mpmCode}</if>
            <if test="mpmName != null  and mpmName != ''"> and mpm_name like concat('%', #{mpmName}, '%')</if>
            <if test="discrepancyInformation != null  and discrepancyInformation != ''"> and discrepancy_information = #{discrepancyInformation}</if>
            <if test="param1 != null  and param1 != ''"> and param1 = #{param1}</if>
            <if test="creationTime != null "> and creation_time = #{creationTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updated != null "> and updated = #{updated}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="deletionTime != null "> and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
            <if test="factoryCode != null "> and factory_code = #{factoryCode}</if>
            <if test="productName != null" > and product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null" > and product_code = #{productCode}</if>
        </where>
    </select>
    
    <select id="selectAutoMaterialAcctMpmInfoById" parameterType="Long" resultMap="AutoMaterialAcctMpmInfoResult">
        <include refid="selectAutoMaterialAcctMpmInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAutoMaterialAcctMpmInfo" parameterType="MpmInfo">
        insert into auto_material_acct_mpm_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="mpmCode != null">mpm_code,</if>
            <if test="mpmName != null">mpm_name,</if>
            <if test="discrepancyInformation != null">discrepancy_information,</if>
            <if test="remark != null">remark,</if>
            <if test="param1 != null">param1,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="factoryCode != null">factory_code,</if>
            <if test="productName != null">product_name,</if>
            <if test="productCode != null">product_code,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="mpmCode != null">#{mpmCode},</if>
            <if test="mpmName != null">#{mpmName},</if>
            <if test="discrepancyInformation != null">#{discrepancyInformation},</if>
            <if test="remark != null">#{remark},</if>
            <if test="param1 != null">#{param1},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="factoryCode != null">#{factoryCode},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productCode != null">#{productCode},</if>
        </trim>
    </insert>

    <update id="updateAutoMaterialAcctMpmInfo" parameterType="MpmInfo">
        update auto_material_acct_mpm_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="mpmCode != null">mpm_code = #{mpmCode},</if>
            <if test="mpmName != null">mpm_name = #{mpmName},</if>
            <if test="discrepancyInformation != null">discrepancy_information = #{discrepancyInformation},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="param1 != null">param1 = #{param1},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updated != null">updated = #{updated},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="deletionTime != null">deletion_time = #{deletionTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="factoryCode != null">factory_code = #{factoryCode},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="productCode != null">product_code = #{productCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoMaterialAcctMpmInfoById" parameterType="Long">
        delete from auto_material_acct_mpm_info where id = #{id}
    </delete>

    <delete id="deleteAutoMaterialAcctMpmInfoByIds" parameterType="String">
        delete from auto_material_acct_mpm_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>