<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoMaterialAcctBomMaterialRequireMapper">

    <resultMap type="BomMaterialRequire" id="AutoMaterialAcctBomMaterialRequireResult">
        <result property="id"    column="id"    />
        <result property="prodPlanId"    column="prod_plan_id"    />
        <result property="mpmCode"    column="mpm_code"    />
        <result property="mpmName"    column="mpm_name"    />
        <result property="productionProcess"    column="production_process"    />
        <result property="componentSapCode"    column="component_sap_code"    />
        <result property="componentDescription"    column="component_description"    />
        <result property="units"    column="units"    />
        <result property="batchDosage"    column="batch_dosage"    />
        <result property="materialPlanCodeCheck"    column="material_plan_code_check"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updated"    column="updated"    />
        <result property="updater"    column="updater"    />
        <result property="deletionTime"    column="deletion_time"    />
        <result property="deleteStatus"    column="delete_status"    />
        <result property="planDate"    column="plan_date"    />
        <result property="prodBatchValue"    column="prod_batch_value"    />
        <result property="prodValue"    column="prod_value"    />
        <result property="factoryCode"    column="factory_code"    />
        <result property="material"    column="material"    />

    </resultMap>

    <sql id="selectAutoMaterialAcctBomMaterialRequireVo">
        select id, prod_plan_id, mpm_code, mpm_name, production_process, component_sap_code, component_description, units, batch_dosage, material_plan_code_check, creation_time, created_by, updated, updater, deletion_time, delete_status, plan_date, prod_batch_value, prod_value,factory_code,material from auto_material_acct_material_require
    </sql>

    <select id="selectAutoMaterialAcctBomMaterialRequireList" parameterType="BomMaterialRequire" resultMap="AutoMaterialAcctBomMaterialRequireResult">
        <include refid="selectAutoMaterialAcctBomMaterialRequireVo"/>
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="factoryList != null ">
                and factory_code in
                <foreach collection="factoryList" item="factory" open="(" separator="," close=")">
                    #{factory}
                </foreach>
            </if>
            <if test="prodPlanId != null "> and prod_plan_id = #{prodPlanId}</if>
            <if test="mpmCode != null  and mpmCode != ''"> and mpm_code = #{mpmCode}</if>
            <if test="mpmName != null  and mpmName != ''"> and mpm_name like concat('%', #{mpmName}, '%')</if>
            <if test="productionProcess != null  and productionProcess != ''"> and production_process = #{productionProcess}</if>
            <if test="componentSapCode != null  and componentSapCode != ''"> and component_sap_code = #{componentSapCode}</if>
            <if test="componentDescription != null  and componentDescription != ''"> and component_description = #{componentDescription}</if>
            <if test="units != null  and units != ''"> and units = #{units}</if>
            <if test="batchDosage != null  and batchDosage != ''"> and batch_dosage = #{batchDosage}</if>
            <if test="materialPlanCodeCheck != null  and materialPlanCodeCheck != ''"> and material_plan_code_check = #{materialPlanCodeCheck}</if>
            <if test="creationTime != null "> and creation_time = #{creationTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updated != null "> and updated = #{updated}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="deletionTime != null "> and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
            <if test="planDate != null  and planDate != ''"> and plan_date = #{planDate}</if>
            <if test="prodBatchValue != null "> and prod_batch_value = #{prodBatchValue}</if>
            <if test="prodValue != null "> and prod_value = #{prodValue}</if>
            <if test="factoryCode != null "> and factory_code = #{factoryCode}</if>
             <if test="material != null  and material != ''"> and material = #{material}</if>

        </where>
    </select>

    <select id="selectAutoMaterialAcctBomMaterialRequireById" parameterType="Long" resultMap="AutoMaterialAcctBomMaterialRequireResult">
        <include refid="selectAutoMaterialAcctBomMaterialRequireVo"/>
        where id = #{id}
    </select>

    <insert id="insertAutoMaterialAcctBomMaterialRequire" parameterType="BomMaterialRequire">
        insert into auto_material_acct_material_require
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="prodPlanId != null">prod_plan_id,</if>
            <if test="mpmCode != null">mpm_code,</if>
            <if test="mpmName != null">mpm_name,</if>
            <if test="productionProcess != null">production_process,</if>
            <if test="componentSapCode != null">component_sap_code,</if>
            <if test="componentDescription != null">component_description,</if>
            <if test="units != null">units,</if>
            <if test="batchDosage != null">batch_dosage,</if>
            <if test="materialPlanCodeCheck != null">material_plan_code_check,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="prodBatchValue != null">prod_batch_value,</if>
            <if test="prodValue != null">prod_value,</if>
            <if test="factoryCode != null">factory_code,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="prodPlanId != null">#{prodPlanId},</if>
            <if test="mpmCode != null">#{mpmCode},</if>
            <if test="mpmName != null">#{mpmName},</if>
            <if test="productionProcess != null">#{productionProcess},</if>
            <if test="componentSapCode != null">#{componentSapCode},</if>
            <if test="componentDescription != null">#{componentDescription},</if>
            <if test="units != null">#{units},</if>
            <if test="batchDosage != null">#{batchDosage},</if>
            <if test="materialPlanCodeCheck != null">#{materialPlanCodeCheck},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="prodBatchValue != null">#{prodBatchValue},</if>
            <if test="prodValue != null">#{prodValue},</if>
            <if test="factoryCode != null">#{factoryCode},</if>

        </trim>
    </insert>

    <update id="updateAutoMaterialAcctBomMaterialRequire" parameterType="BomMaterialRequire">
        update auto_material_acct_material_require
        <trim prefix="SET" suffixOverrides=",">
            <if test="prodPlanId != null">prod_plan_id = #{prodPlanId},</if>
            <if test="mpmCode != null">mpm_code = #{mpmCode},</if>
            <if test="mpmName != null">mpm_name = #{mpmName},</if>
            <if test="productionProcess != null">production_process = #{productionProcess},</if>
            <if test="componentSapCode != null">component_sap_code = #{componentSapCode},</if>
            <if test="componentDescription != null">component_description = #{componentDescription},</if>
            <if test="units != null">units = #{units},</if>
            <if test="batchDosage != null">batch_dosage = #{batchDosage},</if>
            <if test="materialPlanCodeCheck != null">material_plan_code_check = #{materialPlanCodeCheck},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updated != null">updated = #{updated},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="deletionTime != null">deletion_time = #{deletionTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="prodBatchValue != null">prod_batch_value = #{prodBatchValue},</if>
            <if test="prodValue != null">prod_value = #{prodValue},</if>
            <if test="factoryCode != null">factory_code = #{factoryCode},</if>

        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoMaterialAcctBomMaterialRequireById" parameterType="Long">
        delete from auto_material_acct_material_require where id = #{id}
    </delete>

    <delete id="deleteAutoMaterialAcctBomMaterialRequireByIds" parameterType="String">
        delete from auto_material_acct_material_require where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>