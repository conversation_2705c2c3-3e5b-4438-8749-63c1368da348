<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoMaterialAcctMaterialPlanMapper">

    <resultMap type="AutoMaterialAcctMaterialPlan" id="AutoMaterialAcctMaterialPlanResult">
        <result property="id"    column="id"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDesc"    column="material_desc"    />
        <result property="catalogNumber"    column="catalog_number"    />
        <result property="factory"    column="factory"    />
        <result property="unit"    column="unit"    />
        <result property="specification"    column="specification"    />
        <result property="remark"    column="remark"    />
        <result property="factoryCode"    column="factory_code"    />
        <result property="procurementCycle"    column="procurement_cycle"    />
        <result property="inspectionCycle"    column="inspection_cycle"    />
        <result property="processCycles"    column="process_cycles"    />
        <result property="expirationDate"    column="expiration_date"    />
        <result property="orderRules"    column="order_rules"    />
        <result property="qualifiedInventory"    column="qualified_inventory"    />
        <result property="verifiedInventory"    column="verified_inventory"    />
        <result property="safetyStock"    column="safety_stock"    />
        <result property="otherRequirements"    column="other_requirements"    />
        <result property="otherRequirementsDesc"    column="other_requirements_desc"    />
        <result property="soonExpiredQuantity"    column="soon_expired_quantity"    />
        <result property="expirationDateBak"    column="expiration_date_bak"    />
        <result property="soonExpiredQuantityNotExpected"    column="soon_expired_quantity_not_expected"    />
        <result property="underApproval"    column="under_approval"    />
        <result property="remarkDesc"    column="remark_desc"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updated"    column="updated"    />
        <result property="updater"    column="updater"    />
        <result property="deletionTime"    column="deletion_time"    />
        <result property="deleteStatus"    column="delete_status"    />
        <result property="planDate"    column="plan_date"    />
        <result property="purchaseNotreturn"    column="purchase_notreturn"    />
        <result property="purchaseNotreturnDesc"    column="purchase_notreturn_desc"    />
    </resultMap>

    <sql id="selectAutoMaterialAcctMaterialPlanVo">
        select id, material_code, material_desc, catalog_number, factory, unit, specification, remark, factory_code, procurement_cycle, inspection_cycle, process_cycles, expiration_date, order_rules, qualified_inventory, verified_inventory, safety_stock, other_requirements, other_requirements_desc, soon_expired_quantity, expiration_date_bak, soon_expired_quantity_not_expected, under_approval, remark_desc, creation_time, created_by, updated, updater, deletion_time, delete_status, plan_date, purchase_notreturn, purchase_notreturn_desc from auto_material_acct_material_plan
    </sql>

    <select id="selectAutoMaterialAcctMaterialPlanList" parameterType="AutoMaterialAcctMaterialPlan" resultMap="AutoMaterialAcctMaterialPlanResult">
        <include refid="selectAutoMaterialAcctMaterialPlanVo"/>
        <where>
            <if test="factoryList != null ">
                and factory_code in
                <foreach collection="factoryList" item="factory" open="(" separator="," close=")">
                    #{factory}
                </foreach>
            </if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDesc != null  and materialDesc != ''"> and material_desc = #{materialDesc}</if>
            <if test="catalogNumber != null  and catalogNumber != ''"> and catalog_number = #{catalogNumber}</if>
            <if test="factory != null  and factory != ''"> and factory = #{factory}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="factoryCode != null  and factoryCode != ''"> and factory_code = #{factoryCode}</if>
            <if test="procurementCycle != null "> and procurement_cycle = #{procurementCycle}</if>
            <if test="inspectionCycle != null "> and inspection_cycle = #{inspectionCycle}</if>
            <if test="processCycles != null "> and process_cycles = #{processCycles}</if>
            <if test="expirationDate != null "> and expiration_date = #{expirationDate}</if>
            <if test="orderRules != null  and orderRules != ''"> and order_rules = #{orderRules}</if>
            <if test="qualifiedInventory != null "> and qualified_inventory = #{qualifiedInventory}</if>
            <if test="verifiedInventory != null "> and verified_inventory = #{verifiedInventory}</if>
            <if test="safetyStock != null "> and safety_stock = #{safetyStock}</if>
            <if test="otherRequirements != null  and otherRequirements != ''"> and other_requirements = #{otherRequirements}</if>
            <if test="otherRequirementsDesc != null  and otherRequirementsDesc != ''"> and other_requirements_desc = #{otherRequirementsDesc}</if>
            <if test="soonExpiredQuantity != null "> and soon_expired_quantity = #{soonExpiredQuantity}</if>
            <if test="expirationDateBak != null "> and expiration_date_bak = #{expirationDateBak}</if>
            <if test="soonExpiredQuantityNotExpected != null "> and soon_expired_quantity_not_expected = #{soonExpiredQuantityNotExpected}</if>
            <if test="underApproval != null "> and under_approval = #{underApproval}</if>
            <if test="remarkDesc != null  and remarkDesc != ''"> and remark_desc = #{remarkDesc}</if>
            <if test="creationTime != null "> and creation_time = #{creationTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updated != null "> and updated = #{updated}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="deletionTime != null "> and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
            <if test="planDate != null  and planDate != ''"> and plan_date = #{planDate}</if>
            <if test="purchaseNotreturn != null "> and purchase_notreturn = #{purchaseNotreturn}</if>
            <if test="purchaseNotreturnDesc != null  and purchaseNotreturnDesc != ''"> and purchase_notreturn_desc = #{purchaseNotreturnDesc}</if>
        </where>
    </select>

    <select id="selectAutoMaterialAcctMaterialPlanById" parameterType="Long" resultMap="AutoMaterialAcctMaterialPlanResult">
        <include refid="selectAutoMaterialAcctMaterialPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertAutoMaterialAcctMaterialPlan" parameterType="AutoMaterialAcctMaterialPlan">
        insert into auto_material_acct_material_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDesc != null">material_desc,</if>
            <if test="catalogNumber != null">catalog_number,</if>
            <if test="factory != null">factory,</if>
            <if test="unit != null">unit,</if>
            <if test="specification != null">specification,</if>
            <if test="remark != null">remark,</if>
            <if test="factoryCode != null">factory_code,</if>
            <if test="procurementCycle != null">procurement_cycle,</if>
            <if test="inspectionCycle != null">inspection_cycle,</if>
            <if test="processCycles != null">process_cycles,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="orderRules != null">order_rules,</if>
            <if test="qualifiedInventory != null">qualified_inventory,</if>
            <if test="verifiedInventory != null">verified_inventory,</if>
            <if test="safetyStock != null">safety_stock,</if>
            <if test="otherRequirements != null">other_requirements,</if>
            <if test="otherRequirementsDesc != null">other_requirements_desc,</if>
            <if test="soonExpiredQuantity != null">soon_expired_quantity,</if>
            <if test="expirationDateBak != null">expiration_date_bak,</if>
            <if test="soonExpiredQuantityNotExpected != null">soon_expired_quantity_not_expected,</if>
            <if test="underApproval != null">under_approval,</if>
            <if test="remarkDesc != null">remark_desc,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="purchaseNotreturn != null">purchase_notreturn,</if>
            <if test="purchaseNotreturnDesc != null">purchase_notreturn_desc,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDesc != null">#{materialDesc},</if>
            <if test="catalogNumber != null">#{catalogNumber},</if>
            <if test="factory != null">#{factory},</if>
            <if test="unit != null">#{unit},</if>
            <if test="specification != null">#{specification},</if>
            <if test="remark != null">#{remark},</if>
            <if test="factoryCode != null">#{factoryCode},</if>
            <if test="procurementCycle != null">#{procurementCycle},</if>
            <if test="inspectionCycle != null">#{inspectionCycle},</if>
            <if test="processCycles != null">#{processCycles},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="orderRules != null">#{orderRules},</if>
            <if test="qualifiedInventory != null">#{qualifiedInventory},</if>
            <if test="verifiedInventory != null">#{verifiedInventory},</if>
            <if test="safetyStock != null">#{safetyStock},</if>
            <if test="otherRequirements != null">#{otherRequirements},</if>
            <if test="otherRequirementsDesc != null">#{otherRequirementsDesc},</if>
            <if test="soonExpiredQuantity != null">#{soonExpiredQuantity},</if>
            <if test="expirationDateBak != null">#{expirationDateBak},</if>
            <if test="soonExpiredQuantityNotExpected != null">#{soonExpiredQuantityNotExpected},</if>
            <if test="underApproval != null">#{underApproval},</if>
            <if test="remarkDesc != null">#{remarkDesc},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="purchaseNotreturn != null">#{purchaseNotreturn},</if>
            <if test="purchaseNotreturnDesc != null">#{purchaseNotreturnDesc},</if>
        </trim>
    </insert>

    <update id="updateAutoMaterialAcctMaterialPlan" parameterType="AutoMaterialAcctMaterialPlan">
        update auto_material_acct_material_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDesc != null">material_desc = #{materialDesc},</if>
            <if test="catalogNumber != null">catalog_number = #{catalogNumber},</if>
            <if test="factory != null">factory = #{factory},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="factoryCode != null">factory_code = #{factoryCode},</if>
            <if test="procurementCycle != null">procurement_cycle = #{procurementCycle},</if>
            <if test="inspectionCycle != null">inspection_cycle = #{inspectionCycle},</if>
            <if test="processCycles != null">process_cycles = #{processCycles},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="orderRules != null">order_rules = #{orderRules},</if>
            <if test="qualifiedInventory != null">qualified_inventory = #{qualifiedInventory},</if>
            <if test="verifiedInventory != null">verified_inventory = #{verifiedInventory},</if>
            <if test="safetyStock != null">safety_stock = #{safetyStock},</if>
            <if test="otherRequirements != null">other_requirements = #{otherRequirements},</if>
            <if test="otherRequirementsDesc != null">other_requirements_desc = #{otherRequirementsDesc},</if>
            <if test="soonExpiredQuantity != null">soon_expired_quantity = #{soonExpiredQuantity},</if>
            <if test="expirationDateBak != null">expiration_date_bak = #{expirationDateBak},</if>
            <if test="soonExpiredQuantityNotExpected != null">soon_expired_quantity_not_expected = #{soonExpiredQuantityNotExpected},</if>
            <if test="underApproval != null">under_approval = #{underApproval},</if>
            <if test="remarkDesc != null">remark_desc = #{remarkDesc},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updated != null">updated = #{updated},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="deletionTime != null">deletion_time = #{deletionTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="purchaseNotreturn != null">purchase_notreturn = #{purchaseNotreturn},</if>
            <if test="purchaseNotreturnDesc != null">purchase_notreturn_desc = #{purchaseNotreturnDesc},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoMaterialAcctMaterialPlanById" parameterType="Long">
        delete from auto_material_acct_material_plan where id = #{id}
    </delete>

    <delete id="deleteAutoMaterialAcctMaterialPlanByIds" parameterType="String">
        delete from auto_material_acct_material_plan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>