<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.EkpMapper">
    
    <select id="getAllCG04" resultType="com.akesobio.report.autoacct.domain.CG04" parameterType="com.akesobio.report.autoacct.domain.CG04">
        SELECT * FROM material_CG04
        <where>
            formState = '待审'
            <if test="serialNo!= null and serialNo!= ''"> and serialno = #{serialNo} </if>
            <if test="fdFactNodeName!= null and fdFactNodeName!= ''"> and fd_fact_node_name = #{fdFactNodeName} </if>
            <if test="materialCode!= null and materialCode!= ''"> and material_code = #{materialCode} </if>
            <if test="materialName != null and materialName != ''"> and material_name = #{materialName} </if>
            <if test="quantity != null and quantity != ''"> and quantity = #{quantity} </if>
            <if test="unit != null and unit != ''"> and unit = #{unit} </if>
            <if test="brand != null and brand != ''" > and brand = #{brand} </if>
            <if test="itemNo != null and itemNo != ''"> and item_no = #{itemNo} </if>
            <if test="specificationModel != null and specificationModel != ''"> and specification_model = #{specificationModel} </if>
            <if test="unit != null and unit != ''"> and unit = #{unit} </if>
            <if test="basicMeasurement != null and basicMeasurement != ''"> and basic_measurement = #{basicMeasurement} </if>
            <if test="deliveryDate != null"> and delivery_date = #{deliveryDate} </if>
            <if test="factory != null and factory != ''"> and factory = #{factory} </if>
        </where>
    </select>


</mapper>