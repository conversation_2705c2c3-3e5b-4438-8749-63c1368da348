<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoAccReceivedInventoryMapper">
    
    <resultMap type="AutoAccReceivedInventory" id="AutoAccReceivedInventoryResult">
        <result property="id"    column="id"    />
        <result property="factory"    column="factory"    />
        <result property="material"    column="material"    />
        <result property="materialDesc"    column="material_desc"    />
        <result property="packingSpecifications"    column="packing_specifications"    />
        <result property="batch"    column="batch"    />
        <result property="productionBatches"    column="production_batches"    />
        <result property="nonRestrictedInventory"    column="non_restricted_inventory"    />
        <result property="basicUnit"    column="basic_unit"    />
        <result property="qualityInspection"    column="quality_inspection"    />
        <result property="frozen"    column="frozen"    />
        <result property="shelfExpirationDate"    column="shelf_expiration_date"    />
        <result property="recentReceiptDate"    column="recent_receipt_date"    />
        <result property="manufactureDate"    column="manufacture_date"    />
        <result property="oldMaterialNumber"    column="old_material_number"    />
        <result property="storageLocation"    column="storage_location"    />
        <result property="storageLocationDesc"    column="storage_location_desc"    />
        <result property="rawMaterialBatch"    column="raw_material_batch"    />
        <result property="supplierLots"    column="supplier_lots"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updated"    column="updated"    />
        <result property="updater"    column="updater"    />
        <result property="deletionTime"    column="deletion_time"    />
        <result property="deleteStatus"    column="delete_status"    />
        <result property="planDate"    column="plan_date"    />
        <result property="manufacturers"    column="manufacturers"    />
        <result property="articleNumber"    column="article_number"    />
        <result property="materialStandardName"    column="material_standard_name"    />
        <result property="productMonth"    column="product_month"    />
        <result property="mpmCode"    column="mpm_code"    />
    </resultMap>



    <delete id="truncateAutoAccReceivedInventory" >
        TRUNCATE TABLE auto_acc_received_inventory;
    </delete>
    <insert id="batchInsertAutoAccReceivedInventory" parameterType="java.util.List">
        insert into auto_acc_received_inventory (
          factory, material, material_desc, packing_specifications, batch, production_batches, non_restricted_inventory, basic_unit, quality_inspection, frozen, shelf_expiration_date, recent_receipt_date, manufacture_date, old_material_number, storage_location, storage_location_desc, raw_material_batch, supplier_lots, creation_time, created_by, updated, updater, deletion_time, delete_status, plan_date, manufacturers, article_number, material_standard_name,order_number
        )
        values
        <foreach collection="list" item="item" separator=",">
           (
            #{item.factory},
            #{item.material},
            #{item.materialDesc},
            #{item.packingSpecifications},
            #{item.batch},
            #{item.productionBatches},
            #{item.nonRestrictedInventory},
            #{item.basicUnit},
            #{item.qualityInspection},
            #{item.frozen},
            #{item.shelfExpirationDate},
            #{item.recentReceiptDate},
            #{item.manufactureDate},
            #{item.oldMaterialNumber},
            #{item.storageLocation},
            #{item.storageLocationDesc},
            #{item.rawMaterialBatch},
            #{item.supplierLots},
            #{item.creationTime},
            #{item.createdBy},
            #{item.updated},
            #{item.updater}, #{item.deletionTime}, #{item.deleteStatus}, #{item.planDate}, #{item.manufacturers}, #{item.articleNumber}, #{item.materialStandardName}, #{item.orderNumber}
            )
        </foreach>
    </insert>

    <sql id="selectAutoAccReceivedInventoryVo">
        select id, factory, material, material_desc, packing_specifications, batch, production_batches, non_restricted_inventory, basic_unit, quality_inspection, frozen, shelf_expiration_date, recent_receipt_date, manufacture_date, old_material_number, storage_location, storage_location_desc, raw_material_batch, supplier_lots, creation_time, created_by, updated, updater, deletion_time, delete_status, plan_date, manufacturers, article_number, material_standard_name, order_number from auto_acc_received_inventory
    </sql>

    <select id="selectAutoAccReceivedInventoryList" parameterType="AutoAccReceivedInventory" resultMap="AutoAccReceivedInventoryResult">
        <include refid="selectAutoAccReceivedInventoryVo"/>
        <where>  
            <if test="factory != null  and factory != ''"> and factory = #{factory}</if>
            <if test="material != null  and material != ''"> and material = #{material}</if>
            <if test="materialDesc != null  and materialDesc != ''"> and material_desc = #{materialDesc}</if>
            <if test="packingSpecifications != null  and packingSpecifications != ''"> and packing_specifications = #{packingSpecifications}</if>
            <if test="batch != null  and batch != ''"> and batch = #{batch}</if>
            <if test="productionBatches != null  and productionBatches != ''"> and production_batches = #{productionBatches}</if>
            <if test="nonRestrictedInventory != null  and nonRestrictedInventory != ''"> and non_restricted_inventory = #{nonRestrictedInventory}</if>
            <if test="basicUnit != null  and basicUnit != ''"> and basic_unit = #{basicUnit}</if>
            <if test="qualityInspection != null  and qualityInspection != ''"> and quality_inspection = #{qualityInspection}</if>
            <if test="frozen != null  and frozen != ''"> and frozen = #{frozen}</if>
            <if test="shelfExpirationDate != null "> and shelf_expiration_date = #{shelfExpirationDate}</if>
            <if test="recentReceiptDate != null "> and recent_receipt_date = #{recentReceiptDate}</if>
            <if test="manufactureDate != null "> and manufacture_date = #{manufactureDate}</if>
            <if test="oldMaterialNumber != null  and oldMaterialNumber != ''"> and old_material_number = #{oldMaterialNumber}</if>
            <if test="storageLocation != null  and storageLocation != ''"> and storage_location = #{storageLocation}</if>
            <if test="storageLocationDesc != null  and storageLocationDesc != ''"> and storage_location_desc = #{storageLocationDesc}</if>
            <if test="rawMaterialBatch != null  and rawMaterialBatch != ''"> and raw_material_batch = #{rawMaterialBatch}</if>
            <if test="supplierLots != null  and supplierLots != ''"> and supplier_lots = #{supplierLots}</if>
            <if test="creationTime != null "> and creation_time = #{creationTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updated != null "> and updated = #{updated}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="deletionTime != null "> and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
            <if test="planDate != null  and planDate != ''"> and plan_date = #{planDate}</if>
            <if test="manufacturers != null  and manufacturers != ''"> and manufacturers = #{manufacturers}</if>
            <if test="articleNumber != null  and articleNumber != ''"> and article_number = #{articleNumber}</if>
            <if test="materialStandardName != null  and materialStandardName != ''"> and material_standard_name like concat('%', #{materialStandardName}, '%')</if>
<!--            <if test="productMonth != null "> and product_month = #{productMonth}</if>-->
<!--            <if test="mpmCode != null  and mpmCode != ''"> and mpm_code = #{mpmCode}</if>-->
        </where>
    </select>
    
    <select id="selectAutoAccReceivedInventoryById" parameterType="Long" resultMap="AutoAccReceivedInventoryResult">
        <include refid="selectAutoAccReceivedInventoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAutoAccReceivedInventory" parameterType="AutoAccReceivedInventory">
        insert into auto_acc_received_inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="factory != null">factory,</if>
            <if test="material != null">material,</if>
            <if test="materialDesc != null">material_desc,</if>
            <if test="packingSpecifications != null">packing_specifications,</if>
            <if test="batch != null">batch,</if>
            <if test="productionBatches != null">production_batches,</if>
            <if test="nonRestrictedInventory != null">non_restricted_inventory,</if>
            <if test="basicUnit != null">basic_unit,</if>
            <if test="qualityInspection != null">quality_inspection,</if>
            <if test="frozen != null">frozen,</if>
            <if test="shelfExpirationDate != null">shelf_expiration_date,</if>
            <if test="recentReceiptDate != null">recent_receipt_date,</if>
            <if test="manufactureDate != null">manufacture_date,</if>
            <if test="oldMaterialNumber != null">old_material_number,</if>
            <if test="storageLocation != null">storage_location,</if>
            <if test="storageLocationDesc != null">storage_location_desc,</if>
            <if test="rawMaterialBatch != null">raw_material_batch,</if>
            <if test="supplierLots != null">supplier_lots,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="manufacturers != null">manufacturers,</if>
            <if test="articleNumber != null">article_number,</if>
            <if test="materialStandardName != null">material_standard_name,</if>
<!--            <if test="productMonth != null">product_month,</if>-->
<!--            <if test="mpmCode != null">mpm_code,</if>-->
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="factory != null">#{factory},</if>
            <if test="material != null">#{material},</if>
            <if test="materialDesc != null">#{materialDesc},</if>
            <if test="packingSpecifications != null">#{packingSpecifications},</if>
            <if test="batch != null">#{batch},</if>
            <if test="productionBatches != null">#{productionBatches},</if>
            <if test="nonRestrictedInventory != null">#{nonRestrictedInventory},</if>
            <if test="basicUnit != null">#{basicUnit},</if>
            <if test="qualityInspection != null">#{qualityInspection},</if>
            <if test="frozen != null">#{frozen},</if>
            <if test="shelfExpirationDate != null">#{shelfExpirationDate},</if>
            <if test="recentReceiptDate != null">#{recentReceiptDate},</if>
            <if test="manufactureDate != null">#{manufactureDate},</if>
            <if test="oldMaterialNumber != null">#{oldMaterialNumber},</if>
            <if test="storageLocation != null">#{storageLocation},</if>
            <if test="storageLocationDesc != null">#{storageLocationDesc},</if>
            <if test="rawMaterialBatch != null">#{rawMaterialBatch},</if>
            <if test="supplierLots != null">#{supplierLots},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="manufacturers != null">#{manufacturers},</if>
            <if test="articleNumber != null">#{articleNumber},</if>
            <if test="materialStandardName != null">#{materialStandardName},</if>
<!--            <if test="productMonth != null">#{productMonth},</if>-->
<!--            <if test="mpmCode != null">#{mpmCode},</if>-->
         </trim>
    </insert>

    <update id="updateAutoAccReceivedInventory" parameterType="AutoAccReceivedInventory">
        update auto_acc_received_inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="factory != null">factory = #{factory},</if>
            <if test="material != null">material = #{material},</if>
            <if test="materialDesc != null">material_desc = #{materialDesc},</if>
            <if test="packingSpecifications != null">packing_specifications = #{packingSpecifications},</if>
            <if test="batch != null">batch = #{batch},</if>
            <if test="productionBatches != null">production_batches = #{productionBatches},</if>
            <if test="nonRestrictedInventory != null">non_restricted_inventory = #{nonRestrictedInventory},</if>
            <if test="basicUnit != null">basic_unit = #{basicUnit},</if>
            <if test="qualityInspection != null">quality_inspection = #{qualityInspection},</if>
            <if test="frozen != null">frozen = #{frozen},</if>
            <if test="shelfExpirationDate != null">shelf_expiration_date = #{shelfExpirationDate},</if>
            <if test="recentReceiptDate != null">recent_receipt_date = #{recentReceiptDate},</if>
            <if test="manufactureDate != null">manufacture_date = #{manufactureDate},</if>
            <if test="oldMaterialNumber != null">old_material_number = #{oldMaterialNumber},</if>
            <if test="storageLocation != null">storage_location = #{storageLocation},</if>
            <if test="storageLocationDesc != null">storage_location_desc = #{storageLocationDesc},</if>
            <if test="rawMaterialBatch != null">raw_material_batch = #{rawMaterialBatch},</if>
            <if test="supplierLots != null">supplier_lots = #{supplierLots},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updated != null">updated = #{updated},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="deletionTime != null">deletion_time = #{deletionTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="manufacturers != null">manufacturers = #{manufacturers},</if>
            <if test="articleNumber != null">article_number = #{articleNumber},</if>
            <if test="materialStandardName != null">material_standard_name = #{materialStandardName},</if>
<!--            <if test="productMonth != null">product_month = #{productMonth},</if>-->
<!--            <if test="mpmCode != null">mpm_code = #{mpmCode},</if>-->
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoAccReceivedInventoryById" parameterType="Long">
        delete from auto_acc_received_inventory where id = #{id}
    </delete>

    <delete id="deleteAutoAccReceivedInventoryByIds" parameterType="String">
        delete from auto_acc_received_inventory where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>