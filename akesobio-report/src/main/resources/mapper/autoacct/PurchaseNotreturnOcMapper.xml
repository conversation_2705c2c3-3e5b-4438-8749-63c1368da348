<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.PurchaseNotreturnOcMapper">

    <delete id="truncateTableAutoMaterialAcctPurchaseNotreturnOc" >
        TRUNCATE TABLE auto_material_acct_purchase_notreturn_oc;
    </delete>

    <insert id="batchInsertAutoMaterialAcctPurchaseNotreturnOc" parameterType="java.util.List">
        insert into auto_material_acct_purchase_notreturn_oc (
        id, material_code, material_desc, manufacturers, article_number, unit, specification, purchasing_unit, purchasing_unit_conversion, purchasing_unit_actual_order, plan_arrival_time, convert_unit_unreturned_quantity, creation_time, created_by, updated, updater, deletion_time, delete_status, factory_code, plan_date,material
        )
        values
        <foreach collection="list" item="item" separator=",">
        (
            #{item.id},
            #{item.materialCode},
            #{item.materialDesc},
            #{item.manufacturers},
            #{item.articleNumber},
            #{item.unit},
            #{item.specification},
            #{item.purchasingUnit},
            #{item.purchasingUnitConversion},
            #{item.purchasingUnitActualOrder},
            #{item.planArrivalTime},
            #{item.convertUnitUnreturnedQuantity},
            #{item.creationTime},
            #{item.createdBy},
            #{item.updated},
            #{item.updater},
            #{item.deletionTime},
            #{item.deleteStatus},
            #{item.factoryCode},
            #{item.planDate},
            #{item.material}
        )
        </foreach>
    </insert>

</mapper>