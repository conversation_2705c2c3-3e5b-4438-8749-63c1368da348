<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoMaterialAcctSafetyInventoryMapper">
    
    <resultMap type="AutoMaterialAcctSafetyInventory" id="AutoMaterialAcctSafetyInventoryResult">
        <result property="id"    column="id"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDesc"    column="material_desc"    />
        <result property="itemNumber"    column="item_number"    />
        <result property="factory"    column="factory"    />
        <result property="unit"    column="unit"    />
        <result property="specification"    column="specification"    />
        <result property="procurementCycle"    column="procurement_cycle"    />
        <result property="safetyStockQuantity"    column="safety_stock_quantity"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updated"    column="updated"    />
        <result property="updater"    column="updater"    />
        <result property="deletionTime"    column="deletion_time"    />
        <result property="deleteStatus"    column="delete_status"    />
        <result property="factoryCode"    column="factory_code"    />
    </resultMap>

    <sql id="selectAutoMaterialAcctSafetyInventoryVo">
        select id, material_code, material_desc, item_number, factory, unit, specification, procurement_cycle, safety_stock_quantity, creation_time, created_by, updated, updater, deletion_time, delete_status,factory_code from auto_material_acct_safety_inventory
    </sql>

    <select id="selectAutoMaterialAcctSafetyInventoryList" parameterType="AutoMaterialAcctSafetyInventory" resultMap="AutoMaterialAcctSafetyInventoryResult">
        <include refid="selectAutoMaterialAcctSafetyInventoryVo"/>
        <where>  
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDesc != null  and materialDesc != ''"> and material_desc = #{materialDesc}</if>
            <if test="itemNumber != null  and itemNumber != ''"> and item_number = #{itemNumber}</if>
            <if test="factory != null  and factory != ''"> and factory = #{factory}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="procurementCycle != null "> and procurement_cycle = #{procurementCycle}</if>
            <if test="safetyStockQuantity != null "> and safety_stock_quantity = #{safetyStockQuantity}</if>
            <if test="creationTime != null "> and creation_time = #{creationTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updated != null "> and updated = #{updated}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="deletionTime != null "> and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
        </where>
    </select>
    
    <select id="selectAutoMaterialAcctSafetyInventoryById" parameterType="Long" resultMap="AutoMaterialAcctSafetyInventoryResult">
        <include refid="selectAutoMaterialAcctSafetyInventoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAutoMaterialAcctSafetyInventory" parameterType="AutoMaterialAcctSafetyInventory">
        insert into auto_material_acct_safety_inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDesc != null">material_desc,</if>
            <if test="itemNumber != null">item_number,</if>
            <if test="factory != null">factory,</if>
            <if test="unit != null">unit,</if>
            <if test="specification != null">specification,</if>
            <if test="procurementCycle != null">procurement_cycle,</if>
            <if test="safetyStockQuantity != null">safety_stock_quantity,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="factoryCode != null">factory_code,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDesc != null">#{materialDesc},</if>
            <if test="itemNumber != null">#{itemNumber},</if>
            <if test="factory != null">#{factory},</if>
            <if test="unit != null">#{unit},</if>
            <if test="specification != null">#{specification},</if>
            <if test="procurementCycle != null">#{procurementCycle},</if>
            <if test="safetyStockQuantity != null">#{safetyStockQuantity},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="factoryCode != null">#{factoryCode},</if>
        </trim>
    </insert>

    <update id="updateAutoMaterialAcctSafetyInventory" parameterType="AutoMaterialAcctSafetyInventory">
        update auto_material_acct_safety_inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDesc != null">material_desc = #{materialDesc},</if>
            <if test="itemNumber != null">item_number = #{itemNumber},</if>
            <if test="factory != null">factory = #{factory},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="procurementCycle != null">procurement_cycle = #{procurementCycle},</if>
            <if test="safetyStockQuantity != null">safety_stock_quantity = #{safetyStockQuantity},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updated != null">updated = #{updated},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="deletionTime != null">deletion_time = #{deletionTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="factoryCode != null">factory_code = #{factoryCode},</if>

        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoMaterialAcctSafetyInventoryById" parameterType="Long">
        delete from auto_material_acct_safety_inventory where id = #{id}
    </delete>

    <delete id="deleteAutoMaterialAcctSafetyInventoryByIds" parameterType="String">
        delete from auto_material_acct_safety_inventory where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>