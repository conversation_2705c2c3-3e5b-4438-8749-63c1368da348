<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoMaterialAcctAboutExpireMaterialMapper">
    
    <resultMap type="AutoMaterialAcctAboutExpireMaterial" id="AutoMaterialAcctAboutExpireMaterialResult">
        <result property="id"    column="id"    />
        <result property="materialCode"    column="material_code"    />
        <result property="oldMaterialCode"    column="old_material_code"    />
        <result property="materialDesc"    column="material_desc"    />
        <result property="manufacturers"    column="manufacturers"    />
        <result property="batch"    column="batch"    />
        <result property="rawMaterialBatch"    column="raw_material_batch"    />
        <result property="supplierBatch"    column="supplier_batch"    />
        <result property="productionBatches"    column="production_batches"    />
        <result property="quantity"    column="quantity"    />
        <result property="basicUnit"    column="basic_unit"    />
        <result property="shelfExpirationDate"    column="shelf_expiration_date"    />
        <result property="manufactureDate"    column="manufacture_date"    />
        <result property="lastMoveDate"    column="last_move_date"    />
        <result property="storageLocation"    column="storage_location"    />
        <result property="description"    column="description"    />
        <result property="locationNumber"    column="location_number"    />
        <result property="nearExpirationDate"    column="near_expiration_date"    />
        <result property="sluggish"    column="sluggish"    />
        <result property="estimateUnableQuantity"    column="estimate_unable_quantity"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updated"    column="updated"    />
        <result property="updater"    column="updater"    />
        <result property="deletionTime"    column="deletion_time"    />
        <result property="deleteStatus"    column="delete_status"    />
    </resultMap>

    <sql id="selectAutoMaterialAcctAboutExpireMaterialVo">
        select id, material_code, old_material_code, material_desc, manufacturers, batch, raw_material_batch, supplier_batch, production_batches, quantity, basic_unit, shelf_expiration_date, manufacture_date, last_move_date, storage_location, description, location_number, near_expiration_date, sluggish, estimate_unable_quantity, creation_time, created_by, updated, updater, deletion_time, delete_status from auto_material_acct_about_expire_material
    </sql>

    <select id="selectAutoMaterialAcctAboutExpireMaterialList" parameterType="AutoMaterialAcctAboutExpireMaterial" resultMap="AutoMaterialAcctAboutExpireMaterialResult">
        <include refid="selectAutoMaterialAcctAboutExpireMaterialVo"/>
        <where>  
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="oldMaterialCode != null  and oldMaterialCode != ''"> and old_material_code = #{oldMaterialCode}</if>
            <if test="materialDesc != null  and materialDesc != ''"> and material_desc = #{materialDesc}</if>
            <if test="manufacturers != null  and manufacturers != ''"> and manufacturers = #{manufacturers}</if>
            <if test="batch != null  and batch != ''"> and batch = #{batch}</if>
            <if test="rawMaterialBatch != null  and rawMaterialBatch != ''"> and raw_material_batch = #{rawMaterialBatch}</if>
            <if test="supplierBatch != null  and supplierBatch != ''"> and supplier_batch = #{supplierBatch}</if>
            <if test="productionBatches != null  and productionBatches != ''"> and production_batches = #{productionBatches}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="basicUnit != null  and basicUnit != ''"> and basic_unit = #{basicUnit}</if>
            <if test="shelfExpirationDate != null "> and shelf_expiration_date = #{shelfExpirationDate}</if>
            <if test="manufactureDate != null "> and manufacture_date = #{manufactureDate}</if>
            <if test="lastMoveDate != null "> and last_move_date = #{lastMoveDate}</if>
            <if test="storageLocation != null  and storageLocation != ''"> and storage_location = #{storageLocation}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="locationNumber != null  and locationNumber != ''"> and location_number = #{locationNumber}</if>
            <if test="nearExpirationDate != null "> and near_expiration_date = #{nearExpirationDate}</if>
            <if test="sluggish != null  and sluggish != ''"> and sluggish = #{sluggish}</if>
            <if test="estimateUnableQuantity != null "> and estimate_unable_quantity = #{estimateUnableQuantity}</if>
            <if test="creationTime != null "> and creation_time = #{creationTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updated != null "> and updated = #{updated}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="deletionTime != null "> and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
        </where>
    </select>
    
    <select id="selectAutoMaterialAcctAboutExpireMaterialById" parameterType="Long" resultMap="AutoMaterialAcctAboutExpireMaterialResult">
        <include refid="selectAutoMaterialAcctAboutExpireMaterialVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAutoMaterialAcctAboutExpireMaterial" parameterType="AutoMaterialAcctAboutExpireMaterial">
        insert into auto_material_acct_about_expire_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="oldMaterialCode != null">old_material_code,</if>
            <if test="materialDesc != null">material_desc,</if>
            <if test="manufacturers != null">manufacturers,</if>
            <if test="batch != null">batch,</if>
            <if test="rawMaterialBatch != null">raw_material_batch,</if>
            <if test="supplierBatch != null">supplier_batch,</if>
            <if test="productionBatches != null">production_batches,</if>
            <if test="quantity != null">quantity,</if>
            <if test="basicUnit != null">basic_unit,</if>
            <if test="shelfExpirationDate != null">shelf_expiration_date,</if>
            <if test="manufactureDate != null">manufacture_date,</if>
            <if test="lastMoveDate != null">last_move_date,</if>
            <if test="storageLocation != null">storage_location,</if>
            <if test="description != null">description,</if>
            <if test="locationNumber != null">location_number,</if>
            <if test="nearExpirationDate != null">near_expiration_date,</if>
            <if test="sluggish != null">sluggish,</if>
            <if test="estimateUnableQuantity != null">estimate_unable_quantity,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="oldMaterialCode != null">#{oldMaterialCode},</if>
            <if test="materialDesc != null">#{materialDesc},</if>
            <if test="manufacturers != null">#{manufacturers},</if>
            <if test="batch != null">#{batch},</if>
            <if test="rawMaterialBatch != null">#{rawMaterialBatch},</if>
            <if test="supplierBatch != null">#{supplierBatch},</if>
            <if test="productionBatches != null">#{productionBatches},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="basicUnit != null">#{basicUnit},</if>
            <if test="shelfExpirationDate != null">#{shelfExpirationDate},</if>
            <if test="manufactureDate != null">#{manufactureDate},</if>
            <if test="lastMoveDate != null">#{lastMoveDate},</if>
            <if test="storageLocation != null">#{storageLocation},</if>
            <if test="description != null">#{description},</if>
            <if test="locationNumber != null">#{locationNumber},</if>
            <if test="nearExpirationDate != null">#{nearExpirationDate},</if>
            <if test="sluggish != null">#{sluggish},</if>
            <if test="estimateUnableQuantity != null">#{estimateUnableQuantity},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
         </trim>
    </insert>

    <update id="updateAutoMaterialAcctAboutExpireMaterial" parameterType="AutoMaterialAcctAboutExpireMaterial">
        update auto_material_acct_about_expire_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="oldMaterialCode != null">old_material_code = #{oldMaterialCode},</if>
            <if test="materialDesc != null">material_desc = #{materialDesc},</if>
            <if test="manufacturers != null">manufacturers = #{manufacturers},</if>
            <if test="batch != null">batch = #{batch},</if>
            <if test="rawMaterialBatch != null">raw_material_batch = #{rawMaterialBatch},</if>
            <if test="supplierBatch != null">supplier_batch = #{supplierBatch},</if>
            <if test="productionBatches != null">production_batches = #{productionBatches},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="basicUnit != null">basic_unit = #{basicUnit},</if>
            <if test="shelfExpirationDate != null">shelf_expiration_date = #{shelfExpirationDate},</if>
            <if test="manufactureDate != null">manufacture_date = #{manufactureDate},</if>
            <if test="lastMoveDate != null">last_move_date = #{lastMoveDate},</if>
            <if test="storageLocation != null">storage_location = #{storageLocation},</if>
            <if test="description != null">description = #{description},</if>
            <if test="locationNumber != null">location_number = #{locationNumber},</if>
            <if test="nearExpirationDate != null">near_expiration_date = #{nearExpirationDate},</if>
            <if test="sluggish != null">sluggish = #{sluggish},</if>
            <if test="estimateUnableQuantity != null">estimate_unable_quantity = #{estimateUnableQuantity},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updated != null">updated = #{updated},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="deletionTime != null">deletion_time = #{deletionTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoMaterialAcctAboutExpireMaterialById" parameterType="Long">
        delete from auto_material_acct_about_expire_material where id = #{id}
    </delete>

    <delete id="deleteAutoMaterialAcctAboutExpireMaterialByIds" parameterType="String">
        delete from auto_material_acct_about_expire_material where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>