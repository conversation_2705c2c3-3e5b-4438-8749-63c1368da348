<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.InventoryOccupationDetailMapper">
    
    <resultMap type="InventoryOccupationDetail" id="InventoryOccupationDetailResult">
        <result property="id"    column="id"    />
        <result property="prodPlanId"    column="prod_plan_id"    />
        <result property="material"    column="material"    />
        <result property="requiredQuantity"    column="required_quantity"    />
        <result property="supplyMaterialCode"    column="supply_material_code"    />
        <result property="category"    column="category"    />
        <result property="batchNumberOrPurchaseBacklogOrGap"    column="batch_number_or_purchase_backlog_or_gap"    />
        <result property="supplyQuantity"    column="supply_quantity"    />
        <result property="batchRemaining"    column="batch_remaining"    />
        <result property="inventoryRemaining"    column="inventory_remaining"    />
        <result property="qualifiedRemaining"    column="qualified_remaining"    />
        <result property="pendingInspectionRemaining"    column="pending_inspection_remaining"    />
        <result property="purchaseBacklogRemaining"    column="purchase_backlog_remaining"    />
        <result property="gap"    column="gap"    />
        <result property="expirationDateOrArrivalDate"    column="expiration_date_or_arrival_date"    />
        <result property="receivedMaterialQuantity"    column="received_material_quantity"    />
        <result property="receivedMaterialRemaining"    column="received_material_remaining"    />
    </resultMap>

    <sql id="selectInventoryOccupationDetailVo">
        select id, prod_plan_id, material, required_quantity, supply_material_code, category, batch_number_or_purchase_backlog_or_gap,
               supply_quantity, batch_remaining, inventory_remaining, qualified_remaining, pending_inspection_remaining, purchase_backlog_remaining,
               gap, expiration_date_or_arrival_date,received_material_quantity,received_material_remaining from inventory_occupation_detail
    </sql>

    <select id="selectInventoryOccupationDetailList" parameterType="InventoryOccupationDetail" resultMap="InventoryOccupationDetailResult">
        <include refid="selectInventoryOccupationDetailVo"/>
--         select p.factory_code,p.product_month, p.mpm_code,p.mpm_name,p.workshop,p.product_line,p.produc_batch, i.* from inventory_occupation_detail as i
--         left join auto_material_acct_prod_plan p
--         on p.id = i.prod_plan_id
        <where>  

            <if test="material != null  and material != ''"> and material = #{material}</if>
            <if test="requiredQuantity != null "> and required_quantity = #{requiredQuantity}</if>
            <if test="supplyMaterialCode != null  and supplyMaterialCode != ''"> and supply_material_code = #{supplyMaterialCode}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="batchNumberOrPurchaseBacklogOrGap != null  and batchNumberOrPurchaseBacklogOrGap != ''"> and batch_number_or_purchase_backlog_or_gap = #{batchNumberOrPurchaseBacklogOrGap}</if>
            <if test="supplyQuantity != null "> and supply_quantity = #{supplyQuantity}</if>
            <if test="batchRemaining != null "> and batch_remaining = #{batchRemaining}</if>
            <if test="inventoryRemaining != null "> and inventory_remaining = #{inventoryRemaining}</if>
            <if test="qualifiedRemaining != null "> and qualified_remaining = #{qualifiedRemaining}</if>
            <if test="pendingInspectionRemaining != null "> and pending_inspection_remaining = #{pendingInspectionRemaining}</if>
            <if test="purchaseBacklogRemaining != null "> and purchase_backlog_remaining = #{purchaseBacklogRemaining}</if>
            <if test="gap != null "> and gap = #{gap}</if>
            <if test="expirationDateOrArrivalDate != null "> and expiration_date_or_arrival_date = #{expirationDateOrArrivalDate}</if>
        </where>
    </select>

    <select id="selectInventoryOccupationDetailVoList" parameterType="InventoryOccupationDetailVo" resultType="InventoryOccupationDetailVo">
        <!--        <include refid="selectInventoryOccupationDetailVo"/>-->
        select p.factory_code,p.product_month, p.mpm_code,p.mpm_name,p.workshop,p.product_line,p.produc_batch, i.* from inventory_occupation_detail as i
        left join auto_material_acct_prod_plan p
        on p.id = i.prod_plan_id
        <where>
            <if test="factoryList != null ">
                and p.factory_code in
                <foreach collection="factoryList" item="factory" open="(" separator="," close=")">
                    #{factory}
                </foreach>
            </if>
            <if test="factoryCode != null  and factoryCode != ''"> and p.factory_code = #{factoryCode}</if>
            <if test="productMonth != null  "> and product_month = #{productMonth}</if>
            <if test="mpmCode != null  and mpmCode != ''"> and p.mpm_code = #{mpmCode}</if>
            <if test="mpmCodes != null">
                and p.mpm_code in
                <foreach item="mpmCode" collection="mpmCodes" open="(" separator="," close=")">
                    #{mpmCode}
                </foreach>
            </if>
            <if test="mpmName != null  and mpmName != ''"> and p.mpm_name = #{mpmName}</if>
<!--            <if test="workshop != null  and workshop != ''"> and p.workshop = #{workshop}</if>-->
            <if test="productLine != null  and productLine != ''"> and p.product_line = #{productLine}</if>
<!--            <if test="producBatch != null  and producBatch != ''"> and p.produc_batch = #{producBatch}</if>-->
            <if test="material != null  and material != ''"> and material = #{material}</if>
             <if test="materials != null">
                 and material in
                 <foreach item="material" collection="materials" open="(" separator="," close=")">
                     #{material}
                 </foreach>
             </if>
            <if test="requiredQuantity != null "> and required_quantity = #{requiredQuantity}</if>
            <if test="supplyMaterialCode != null  and supplyMaterialCode != ''"> and supply_material_code = #{supplyMaterialCode}</if>
            <if test="categoryList != null">
               and category in
               <foreach item="category" collection="categoryList" open="(" separator="," close=")">
                   #{category}
               </foreach>
            </if>
            <if test="taskIds != null">
                and task_id in
                <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            </if>
            <if test="batchNumberOrPurchaseBacklogOrGap != null  and batchNumberOrPurchaseBacklogOrGap != ''"> and batch_number_or_purchase_backlog_or_gap = #{batchNumberOrPurchaseBacklogOrGap}</if>
            <if test="supplyQuantity != null "> and supply_quantity = #{supplyQuantity}</if>
            <if test="batchRemaining != null "> and batch_remaining = #{batchRemaining}</if>
            <if test="inventoryRemaining != null "> and inventory_remaining = #{inventoryRemaining}</if>
            <if test="qualifiedRemaining != null "> and qualified_remaining = #{qualifiedRemaining}</if>
            <if test="pendingInspectionRemaining != null "> and pending_inspection_remaining = #{pendingInspectionRemaining}</if>
            <if test="purchaseBacklogRemaining != null "> and purchase_backlog_remaining = #{purchaseBacklogRemaining}</if>
            <if test="gap != null "> and gap = #{gap}</if>
            <if test="expirationDateOrArrivalDate != null "> and expiration_date_or_arrival_date = #{expirationDateOrArrivalDate}</if>
        </where>
        ORDER BY
        p.factory_code asc, i.material asc,p.product_month asc
    </select>
    
    <select id="selectInventoryOccupationDetailById" parameterType="Long" resultMap="InventoryOccupationDetailResult">
        <include refid="selectInventoryOccupationDetailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertInventoryOccupationDetail" parameterType="InventoryOccupationDetail">
        insert into inventory_occupation_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="prodPlanId != null">prod_plan_id,</if>
            <if test="material != null">material,</if>
            <if test="requiredQuantity != null">required_quantity,</if>
            <if test="supplyMaterialCode != null">supply_material_code,</if>
            <if test="category != null">category,</if>
            <if test="batchNumberOrPurchaseBacklogOrGap != null">batch_number_or_purchase_backlog_or_gap,</if>
            <if test="supplyQuantity != null">supply_quantity,</if>
            <if test="batchRemaining != null">batch_remaining,</if>
            <if test="inventoryRemaining != null">inventory_remaining,</if>
            <if test="qualifiedRemaining != null">qualified_remaining,</if>
            <if test="pendingInspectionRemaining != null">pending_inspection_remaining,</if>
            <if test="purchaseBacklogRemaining != null">purchase_backlog_remaining,</if>
            <if test="gap != null">gap,</if>
            <if test="expirationDateOrArrivalDate != null">expiration_date_or_arrival_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="prodPlanId != null">#{prodPlanId},</if>
            <if test="material != null">#{material},</if>
            <if test="requiredQuantity != null">#{requiredQuantity},</if>
            <if test="supplyMaterialCode != null">#{supplyMaterialCode},</if>
            <if test="category != null">#{category},</if>
            <if test="batchNumberOrPurchaseBacklogOrGap != null">#{batchNumberOrPurchaseBacklogOrGap},</if>
            <if test="supplyQuantity != null">#{supplyQuantity},</if>
            <if test="batchRemaining != null">#{batchRemaining},</if>
            <if test="inventoryRemaining != null">#{inventoryRemaining},</if>
            <if test="qualifiedRemaining != null">#{qualifiedRemaining},</if>
            <if test="pendingInspectionRemaining != null">#{pendingInspectionRemaining},</if>
            <if test="purchaseBacklogRemaining != null">#{purchaseBacklogRemaining},</if>
            <if test="gap != null">#{gap},</if>
            <if test="expirationDateOrArrivalDate != null">#{expirationDateOrArrivalDate},</if>
         </trim>
    </insert>

    <update id="updateInventoryOccupationDetail" parameterType="InventoryOccupationDetail">
        update inventory_occupation_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="prodPlanId != null">prod_plan_id = #{prodPlanId},</if>
            <if test="material != null">material = #{material},</if>
            <if test="requiredQuantity != null">required_quantity = #{requiredQuantity},</if>
            <if test="supplyMaterialCode != null">supply_material_code = #{supplyMaterialCode},</if>
            <if test="category != null">category = #{category},</if>
            <if test="batchNumberOrPurchaseBacklogOrGap != null">batch_number_or_purchase_backlog_or_gap = #{batchNumberOrPurchaseBacklogOrGap},</if>
            <if test="supplyQuantity != null">supply_quantity = #{supplyQuantity},</if>
            <if test="batchRemaining != null">batch_remaining = #{batchRemaining},</if>
            <if test="inventoryRemaining != null">inventory_remaining = #{inventoryRemaining},</if>
            <if test="qualifiedRemaining != null">qualified_remaining = #{qualifiedRemaining},</if>
            <if test="pendingInspectionRemaining != null">pending_inspection_remaining = #{pendingInspectionRemaining},</if>
            <if test="purchaseBacklogRemaining != null">purchase_backlog_remaining = #{purchaseBacklogRemaining},</if>
            <if test="gap != null">gap = #{gap},</if>
            <if test="expirationDateOrArrivalDate != null">expiration_date_or_arrival_date = #{expirationDateOrArrivalDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryOccupationDetailById" parameterType="Long">
        delete from inventory_occupation_detail where id = #{id}
    </delete>

    <delete id="deleteInventoryOccupationDetailByIds" parameterType="String">
        delete from inventory_occupation_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTaskIds" resultType="map">
        SELECT task_id,task_time
        FROM inventory_occupation_detail
        GROUP BY task_id,task_time
        HAVING COUNT(*) > 1
        ORDER BY task_time DESC
    </select>
</mapper>