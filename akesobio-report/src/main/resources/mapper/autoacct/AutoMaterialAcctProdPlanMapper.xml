<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoMaterialAcctProdPlanMapper">
    
    <resultMap type="ProdPlan" id="AutoMaterialAcctProdPlanResult">
        <result property="id"    column="id"    />
        <result property="mpmCode"    column="mpm_code"    />
        <result property="mpmName"    column="mpm_name"    />
        <result property="workshop"    column="workshop"    />
        <result property="auditors"    column="auditors"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updated"    column="updated"    />
        <result property="updater"    column="updater"    />
        <result property="deletionTime"    column="deletion_time"    />
        <result property="deleteStatus"    column="delete_status"    />
        <result property="productLine"    column="product_line"    />
        <result property="productMonth"    column="product_month"    />
        <result property="producBatch"    column="produc_batch"    />
        <result property="factoryCode"    column="factory_code"    />
        <result property="productionState"    column="production_state"    />

    </resultMap>

    <sql id="selectAutoMaterialAcctProdPlanVo">
        select id, mpm_code, mpm_name, workshop, auditors, creation_time, created_by, updated, updater, deletion_time, delete_status, product_line, product_month, produc_batch,factory_code,production_state from auto_material_acct_prod_plan
    </sql>

    <select id="selectAutoMaterialAcctProdPlanList" parameterType="ProdPlan" resultMap="AutoMaterialAcctProdPlanResult">
        <include refid="selectAutoMaterialAcctProdPlanVo"/>
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="factoryList != null ">
                and factory_code in
                <foreach collection="factoryList" item="factory" open="(" separator="," close=")">
                    #{factory}
                </foreach>
            </if>
            <if test="mpmCode != null  and mpmCode != ''"> and mpm_code = #{mpmCode}</if>
            <if test="mpmName != null  and mpmName != ''"> and mpm_name like concat('%', #{mpmName}, '%')</if>
            <if test="workshop != null  and workshop != ''"> and workshop = #{workshop}</if>
            <if test="auditors != null  and auditors != ''"> and auditors = #{auditors}</if>
            <if test="creationTime != null "> and creation_time = #{creationTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updated != null "> and updated = #{updated}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="deletionTime != null "> and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
            <if test="productLine != null  and productLine != ''"> and product_line = #{productLine}</if>
            <if test="productMonth != null "> and product_month = #{productMonth}</if>
            <if test="producBatch != null  and producBatch != ''"> and produc_batch = #{producBatch}</if>
            <if test="factoryCode != null  and factoryCode != ''"> and factory_code = #{factoryCode}</if>

        </where>
    </select>
    
    <select id="selectAutoMaterialAcctProdPlanById" parameterType="Long" resultMap="AutoMaterialAcctProdPlanResult">
        <include refid="selectAutoMaterialAcctProdPlanVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAutoMaterialAcctProdPlan" parameterType="ProdPlan">
        insert into auto_material_acct_prod_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="mpmCode != null">mpm_code,</if>
            <if test="mpmName != null">mpm_name,</if>
            <if test="workshop != null">workshop,</if>
            <if test="auditors != null">auditors,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="productLine != null">product_line,</if>
            <if test="productMonth != null">product_month,</if>
            <if test="producBatch != null">produc_batch,</if>
            <if test="factoryCode != null">factory_code,</if>
            <if test="productionState != null">production_state,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="mpmCode != null">#{mpmCode},</if>
            <if test="mpmName != null">#{mpmName},</if>
            <if test="workshop != null">#{workshop},</if>
            <if test="auditors != null">#{auditors},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="productLine != null">#{productLine},</if>
            <if test="productMonth != null">#{productMonth},</if>
            <if test="producBatch != null">#{producBatch},</if>
            <if test="factoryCode != null">#{factoryCode},</if>
            <if test="productionState != null">#{productionState},</if>
        </trim>
    </insert>

    <update id="updateAutoMaterialAcctProdPlan" parameterType="ProdPlan">
        update auto_material_acct_prod_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="mpmCode != null">mpm_code = #{mpmCode},</if>
            <if test="mpmName != null">mpm_name = #{mpmName},</if>
            <if test="workshop != null">workshop = #{workshop},</if>
            <if test="auditors != null">auditors = #{auditors},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updated != null">updated = #{updated},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="deletionTime != null">deletion_time = #{deletionTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="productLine != null">product_line = #{productLine},</if>
            <if test="productMonth != null">product_month = #{productMonth},</if>
            <if test="producBatch != null">produc_batch = #{producBatch},</if>
            <if test="factoryCode != null">factory_code = #{factoryCode},</if>
            <if test="productionState != null">production_state = #{productionState},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoMaterialAcctProdPlanById" parameterType="Long">
        delete from auto_material_acct_prod_plan where id = #{id}
    </delete>

    <delete id="deleteAutoMaterialAcctProdPlanByIds" parameterType="String">
        delete from auto_material_acct_prod_plan where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>