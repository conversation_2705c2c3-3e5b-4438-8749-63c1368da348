<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoAccReceivedMaterialsMapper">

    <resultMap type="AutoAccReceivedMaterials" id="AutoAccReceivedMaterialsResult">
        <result property="sign"    column="sign"    />
        <result property="choice "    column="choice "    />
        <result property="orderNumber"    column="order_number"    />
        <result property="high"    column="high"    />
        <result property="id"    column="id"    />
        <result property="factoryCode"    column="factory_code"    />
        <result property="mpmCode"    column="mpm_code"    />
        <result property="productMonth"    column="product_month"    />
    </resultMap>

    <sql id="selectAutoAccReceivedMaterialsVo">
        select sign, choice , order_number, high, id, factory_code, mpm_code, product_month from auto_acc_received_materials
    </sql>

    <select id="selectAutoAccReceivedMaterialsList" parameterType="AutoAccReceivedMaterials" resultMap="AutoAccReceivedMaterialsResult">
        <include refid="selectAutoAccReceivedMaterialsVo"/>
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="sign != null  and sign != ''"> and sign = #{sign}</if>
            <if test="choice  != null  and choice != ''"> and choice  = #{choice }</if>
            <if test="orderNumber != null  and orderNumber != ''"> and order_number = #{orderNumber}</if>
            <if test="high != null  and high != ''"> and high = #{high}</if>
            <if test="factoryCode != null  and factoryCode != ''"> and factory_code = #{factoryCode}</if>
            <if test="mpmCode != null  and mpmCode != ''"> and mpm_code = #{mpmCode}</if>
            <if test="productMonth != null "> and product_month = #{productMonth}</if>
        </where>
    </select>

    <select id="selectAutoAccReceivedMaterialsById" parameterType="Long" resultMap="AutoAccReceivedMaterialsResult">
        <include refid="selectAutoAccReceivedMaterialsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAutoAccReceivedMaterials" parameterType="AutoAccReceivedMaterials">
        insert into auto_acc_received_materials
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sign != null">sign,</if>
            <if test="choice  != null">choice ,</if>
            <if test="orderNumber != null">order_number,</if>
            <if test="high != null">high,</if>
            <if test="id != null">id,</if>
            <if test="factoryCode != null">factory_code,</if>
            <if test="mpmCode != null">mpm_code,</if>
            <if test="productMonth != null">product_month,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sign != null">#{sign},</if>
            <if test="choice  != null">#{choice },</if>
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="high != null">#{high},</if>
            <if test="id != null">#{id},</if>
            <if test="factoryCode != null">#{factoryCode},</if>
            <if test="mpmCode != null">#{mpmCode},</if>
            <if test="productMonth != null">#{productMonth},</if>
        </trim>
    </insert>

    <update id="updateAutoAccReceivedMaterials" parameterType="AutoAccReceivedMaterials">
        update auto_acc_received_materials
        <trim prefix="SET" suffixOverrides=",">
            <if test="sign != null">sign = #{sign},</if>
            <if test="choice  != null">choice  = #{choice },</if>
            <if test="orderNumber != null">order_number = #{orderNumber},</if>
            <if test="high != null">high = #{high},</if>
            <if test="factoryCode != null">factory_code = #{factoryCode},</if>
            <if test="mpmCode != null">mpm_code = #{mpmCode},</if>
            <if test="productMonth != null">product_month = #{productMonth},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoAccReceivedMaterialsById" parameterType="Long">
        delete from auto_acc_received_materials where id = #{id}
    </delete>

    <delete id="deleteAutoAccReceivedMaterialsByIds" parameterType="String">
        delete from auto_acc_received_materials where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>