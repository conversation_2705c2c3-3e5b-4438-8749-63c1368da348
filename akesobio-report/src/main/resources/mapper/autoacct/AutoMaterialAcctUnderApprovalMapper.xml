<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.autoacct.mapper.AutoMaterialAcctUnderApprovalMapper">
    
    <resultMap type="AutoMaterialAcctUnderApproval" id="AutoMaterialAcctUnderApprovalResult">
        <result property="id"    column="id"    />
        <result property="materialCode"    column="material_code"    />
        <result property="factory"    column="factory"    />
        <result property="factoryCode"    column="factory_code"    />
        <result property="difference"    column="difference"    />
        <result property="orderRule"    column="order_rule"    />
        <result property="basicUnit"    column="basic_unit"    />
        <result property="specification"    column="specification"    />
        <result property="purchasingUnit"    column="purchasing_unit"    />
        <result property="purchasingUnitConversion"    column="purchasing_unit_conversion"    />
        <result property="purchasingUnitTheoryOrder"    column="purchasing_unit_theory_order"    />
        <result property="purchasingUnitActualOrder"    column="purchasing_unit_actual_order"    />
        <result property="arrivalTime"    column="arrival_time"    />
        <result property="arrivalExpirationRequirement"    column="arrival_expiration_requirement"    />
        <result property="remark"    column="remark"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updated"    column="updated"    />
        <result property="updater"    column="updater"    />
        <result property="deletionTime"    column="deletion_time"    />
        <result property="deleteStatus"    column="delete_status"    />
    </resultMap>

    <sql id="selectAutoMaterialAcctUnderApprovalVo">
        select id, material_code, factory, factory_code, difference, order_rule, basic_unit, specification, purchasing_unit, purchasing_unit_conversion, purchasing_unit_theory_order, purchasing_unit_actual_order, arrival_time, arrival_expiration_requirement, remark, creation_time, created_by, updated, updater, deletion_time, delete_status from auto_material_acct_under_approval
    </sql>

    <select id="selectAutoMaterialAcctUnderApprovalList" parameterType="AutoMaterialAcctUnderApproval" resultMap="AutoMaterialAcctUnderApprovalResult">
        <include refid="selectAutoMaterialAcctUnderApprovalVo"/>
        <where>  
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="factory != null  and factory != ''"> and factory = #{factory}</if>
            <if test="factoryCode != null  and factoryCode != ''"> and factory_code = #{factoryCode}</if>
            <if test="difference != null  and difference != ''"> and difference = #{difference}</if>
            <if test="orderRule != null  and orderRule != ''"> and order_rule = #{orderRule}</if>
            <if test="basicUnit != null  and basicUnit != ''"> and basic_unit = #{basicUnit}</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="purchasingUnit != null  and purchasingUnit != ''"> and purchasing_unit = #{purchasingUnit}</if>
            <if test="purchasingUnitConversion != null  and purchasingUnitConversion != ''"> and purchasing_unit_conversion = #{purchasingUnitConversion}</if>
            <if test="purchasingUnitTheoryOrder != null  and purchasingUnitTheoryOrder != ''"> and purchasing_unit_theory_order = #{purchasingUnitTheoryOrder}</if>
            <if test="purchasingUnitActualOrder != null  and purchasingUnitActualOrder != ''"> and purchasing_unit_actual_order = #{purchasingUnitActualOrder}</if>
            <if test="arrivalTime != null "> and arrival_time = #{arrivalTime}</if>
            <if test="arrivalExpirationRequirement != null  and arrivalExpirationRequirement != ''"> and arrival_expiration_requirement = #{arrivalExpirationRequirement}</if>
            <if test="creationTime != null "> and creation_time = #{creationTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updated != null "> and updated = #{updated}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="deletionTime != null "> and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
        </where>
    </select>
    
    <select id="selectAutoMaterialAcctUnderApprovalById" parameterType="Long" resultMap="AutoMaterialAcctUnderApprovalResult">
        <include refid="selectAutoMaterialAcctUnderApprovalVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAutoMaterialAcctUnderApproval" parameterType="AutoMaterialAcctUnderApproval">
        insert into auto_material_acct_under_approval
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="factory != null">factory,</if>
            <if test="factoryCode != null">factory_code,</if>
            <if test="difference != null">difference,</if>
            <if test="orderRule != null">order_rule,</if>
            <if test="basicUnit != null">basic_unit,</if>
            <if test="specification != null">specification,</if>
            <if test="purchasingUnit != null">purchasing_unit,</if>
            <if test="purchasingUnitConversion != null">purchasing_unit_conversion,</if>
            <if test="purchasingUnitTheoryOrder != null">purchasing_unit_theory_order,</if>
            <if test="purchasingUnitActualOrder != null">purchasing_unit_actual_order,</if>
            <if test="arrivalTime != null">arrival_time,</if>
            <if test="arrivalExpirationRequirement != null">arrival_expiration_requirement,</if>
            <if test="remark != null">remark,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updated != null">updated,</if>
            <if test="updater != null">updater,</if>
            <if test="deletionTime != null">deletion_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="factory != null">#{factory},</if>
            <if test="factoryCode != null">#{factoryCode},</if>
            <if test="difference != null">#{difference},</if>
            <if test="orderRule != null">#{orderRule},</if>
            <if test="basicUnit != null">#{basicUnit},</if>
            <if test="specification != null">#{specification},</if>
            <if test="purchasingUnit != null">#{purchasingUnit},</if>
            <if test="purchasingUnitConversion != null">#{purchasingUnitConversion},</if>
            <if test="purchasingUnitTheoryOrder != null">#{purchasingUnitTheoryOrder},</if>
            <if test="purchasingUnitActualOrder != null">#{purchasingUnitActualOrder},</if>
            <if test="arrivalTime != null">#{arrivalTime},</if>
            <if test="arrivalExpirationRequirement != null">#{arrivalExpirationRequirement},</if>
            <if test="remark != null">#{remark},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updated != null">#{updated},</if>
            <if test="updater != null">#{updater},</if>
            <if test="deletionTime != null">#{deletionTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
         </trim>
    </insert>

    <update id="updateAutoMaterialAcctUnderApproval" parameterType="AutoMaterialAcctUnderApproval">
        update auto_material_acct_under_approval
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="factory != null">factory = #{factory},</if>
            <if test="factoryCode != null">factory_code = #{factoryCode},</if>
            <if test="difference != null">difference = #{difference},</if>
            <if test="orderRule != null">order_rule = #{orderRule},</if>
            <if test="basicUnit != null">basic_unit = #{basicUnit},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="purchasingUnit != null">purchasing_unit = #{purchasingUnit},</if>
            <if test="purchasingUnitConversion != null">purchasing_unit_conversion = #{purchasingUnitConversion},</if>
            <if test="purchasingUnitTheoryOrder != null">purchasing_unit_theory_order = #{purchasingUnitTheoryOrder},</if>
            <if test="purchasingUnitActualOrder != null">purchasing_unit_actual_order = #{purchasingUnitActualOrder},</if>
            <if test="arrivalTime != null">arrival_time = #{arrivalTime},</if>
            <if test="arrivalExpirationRequirement != null">arrival_expiration_requirement = #{arrivalExpirationRequirement},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updated != null">updated = #{updated},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="deletionTime != null">deletion_time = #{deletionTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoMaterialAcctUnderApprovalById" parameterType="Long">
        delete from auto_material_acct_under_approval where id = #{id}
    </delete>

    <delete id="deleteAutoMaterialAcctUnderApprovalByIds" parameterType="String">
        delete from auto_material_acct_under_approval where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>