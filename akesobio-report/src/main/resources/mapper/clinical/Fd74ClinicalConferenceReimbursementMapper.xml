<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.Fd74ClinicalConferenceReimbursementMapper">

    <resultMap type="Fd74ClinicalConferenceReimbursement" id="Fd74ClinicalConferenceReimbursementResult">
        <result property="id" column="id"/>
        <result property="oaReimbursementNumber" column="oaReimbursementNumber"/>
        <result property="dateCreated" column="dateCreated"/>
        <result property="reimbursementCategory" column="reimbursementCategory"/>
        <result property="companyCode" column="companyCode"/>
        <result property="companyName" column="companyName"/>
        <result property="companyEntity" column="companyEntity"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="reimbursementPersonNumber" column="reimbursementPersonNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="applicantNumber" column="applicantNumber"/>
        <result property="applicantPosition" column="applicantPosition"/>
        <result property="department" column="department"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="relatedMeetingApplication" column="relatedMeetingApplication"/>
        <result property="businessPeriod" column="businessPeriod"/>
        <result property="startDate" column="startDate"/>
        <result property="endDate" column="endDate"/>
        <result property="meetingName" column="meetingName"/>
        <result property="meetingType" column="meetingType"/>
        <result property="meetingCategory" column="meetingCategory"/>
        <result property="meetingFormat" column="meetingFormat"/>
        <result property="companyRole" column="companyRole"/>
        <result property="objectOriented" column="objectOriented"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="peopleCovered" column="peopleCovered"/>
        <result property="signNumber" column="signNumber"/>
        <result property="expertsNumber" column="expertsNumber"/>
        <result property="meetingMaterials" column="meetingMaterials"/>
        <result property="invitation" column="invitation"/>
        <result property="schedule" column="schedule"/>
        <result property="attendanceSheet" column="attendanceSheet"/>
        <result property="photo" column="photo"/>
        <result property="paymentMethod" column="paymentMethod"/>
        <result property="loanBalance" column="loanBalance"/>
        <result property="isWhetherLoan" column="isWhetherLoan"/>
        <result property="totalAmount" column="totalAmount"/>
        <result property="conferenceBudgetAmount" column="conferenceBudgetAmount"/>
        <result property="differenceAmount" column="differenceAmount"/>
        <result property="differenceExplanation" column="differenceExplanation"/>
        <result property="serviceDescription" column="serviceDescription"/>
        <result property="excludingTaxAmount" column="excludingTaxAmount"/>
        <result property="specialInvoiceTaxAmount" column="specialInvoiceTaxAmount"/>
        <result property="reimbursementAmount" column="reimbursementAmount"/>
        <result property="currency" column="currency"/>
        <result property="projectNo" column="projectNo"/>
        <result property="projectNos" column="projectNos"/>
        <result property="centerName" column="centerName"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="voucherPushDate" column="voucherPushDate"/>
        <result property="fd20ID" column="fd20ID"/>
        <result property="fd20Number" column="fd20Number"/>
        <result property="fd20Name" column="fd20Name"/>
        <result property="fd20Amount" column="fd20Amount"/>
        <result property="fd20ServiceDescription" column="fd20ServiceDescription"/>
        <result property="executionDate" column="executionDate"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="fdId" column="fdId"/>
    </resultMap>

    <sql id="selectFd74ClinicalConferenceReimbursementVo">
        select id,
               oaReimbursementNumber,
               dateCreated,
               reimbursementCategory,
               companyCode,
               companyName,
               companyEntity,
               reimbursementPerson,
               reimbursementPersonNumber,
               applicant,
               applicantNumber,
               applicantPosition,
               department,
               costCenterCode,
               costCenterName,
               relatedMeetingApplication,
               businessPeriod,
               startDate,
               endDate,
               meetingName,
               meetingType,
               meetingCategory,
               meetingFormat,
               companyRole,
               objectOriented,
               province,
               city,
               peopleCovered,
               signNumber,
               expertsNumber,
               meetingMaterials,
               invitation,
               schedule,
               attendanceSheet,
               photo,
               paymentMethod,
               loanBalance,
               isWhetherLoan,
               totalAmount,
               conferenceBudgetAmount,
               differenceAmount,
               differenceExplanation,
               serviceDescription,
               excludingTaxAmount,
               specialInvoiceTaxAmount,
               reimbursementAmount,
               currency,
               projectNo,
               projectNos,
               centerName,
               documentStatus,
               currentSession,
               sapPayNumber,
               voucherPushDate,
               fd20ID,
               fd20Number,
               fd20Name,
               fd20Amount,
               fd20ServiceDescription,
               executionDate,
               deleteStatus,
               fdId
        from fd74_clinical_conference_reimbursement
    </sql>

    <select id="selectFd74ClinicalConferenceReimbursementList" parameterType="Fd74ClinicalConferenceReimbursement"
            resultMap="Fd74ClinicalConferenceReimbursementResult">
        <include refid="selectFd74ClinicalConferenceReimbursementVo"/>
        <where>
            <if test="oaReimbursementNumber != null  and oaReimbursementNumber != ''">and oaReimbursementNumber like
                concat('%', #{oaReimbursementNumber}, '%')
            </if>
            <if test="params.beginDateCreated != null and params.beginDateCreated != '' and params.endDateCreated != null and params.endDateCreated != ''">
                and dateCreated between #{params.beginDateCreated} and #{params.endDateCreated}
            </if>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''">and reimbursementCategory like
                concat('%', #{reimbursementCategory}, '%')
            </if>
            <if test="companyCode != null  and companyCode != ''">and companyCode like concat('%', #{companyCode},
                '%')
            </if>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="companyEntity != null  and companyEntity != ''">and companyEntity like concat('%',
                #{companyEntity}, '%')
            </if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''">and reimbursementPerson like
                concat('%', #{reimbursementPerson}, '%')
            </if>
            <if test="reimbursementPersonNumber != null  and reimbursementPersonNumber != ''">and
                reimbursementPersonNumber = #{reimbursementPersonNumber}
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="applicantNumber != null  and applicantNumber != ''">and applicantNumber = #{applicantNumber}</if>
            <if test="applicantPosition != null  and applicantPosition != ''">and applicantPosition =
                #{applicantPosition}
            </if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="costCenterCode != null  and costCenterCode != ''">and costCenterCode = #{costCenterCode}</if>
            <if test="costCenterName != null  and costCenterName != ''">and costCenterName like concat('%',
                #{costCenterName}, '%')
            </if>
            <if test="relatedMeetingApplication != null  and relatedMeetingApplication != ''">and
                relatedMeetingApplication = #{relatedMeetingApplication}
            </if>
            <if test="businessPeriod != null  and businessPeriod != ''">and businessPeriod = #{businessPeriod}</if>
            <if test="startDate != null  and startDate != ''">and startDate = #{startDate}</if>
            <if test="endDate != null  and endDate != ''">and endDate = #{endDate}</if>
            <if test="meetingName != null  and meetingName != ''">and meetingName like concat('%', #{meetingName},
                '%')
            </if>
            <if test="meetingType != null  and meetingType != ''">and meetingType = #{meetingType}</if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
            <if test="meetingFormat != null  and meetingFormat != ''">and meetingFormat = #{meetingFormat}</if>
            <if test="companyRole != null  and companyRole != ''">and companyRole = #{companyRole}</if>
            <if test="objectOriented != null  and objectOriented != ''">and objectOriented = #{objectOriented}</if>
            <if test="province != null  and province != ''">and province = #{province}</if>
            <if test="city != null  and city != ''">and city = #{city}</if>
            <if test="peopleCovered != null  and peopleCovered != ''">and peopleCovered = #{peopleCovered}</if>
            <if test="signNumber != null  and signNumber != ''">and signNumber = #{signNumber}</if>
            <if test="expertsNumber != null  and expertsNumber != ''">and expertsNumber = #{expertsNumber}</if>
            <if test="meetingMaterials != null  and meetingMaterials != ''">and meetingMaterials = #{meetingMaterials}
            </if>
            <if test="invitation != null  and invitation != ''">and invitation = #{invitation}</if>
            <if test="schedule != null  and schedule != ''">and schedule = #{schedule}</if>
            <if test="attendanceSheet != null  and attendanceSheet != ''">and attendanceSheet = #{attendanceSheet}</if>
            <if test="photo != null  and photo != ''">and photo = #{photo}</if>
            <if test="paymentMethod != null  and paymentMethod != ''">and paymentMethod = #{paymentMethod}</if>
            <if test="loanBalance != null  and loanBalance != ''">and loanBalance = #{loanBalance}</if>
            <if test="isWhetherLoan != null  and isWhetherLoan != ''">and isWhetherLoan = #{isWhetherLoan}</if>
            <if test="totalAmount != null  and totalAmount != ''">and totalAmount = #{totalAmount}</if>
            <if test="conferenceBudgetAmount != null  and conferenceBudgetAmount != ''">and conferenceBudgetAmount =
                #{conferenceBudgetAmount}
            </if>
            <if test="differenceAmount != null  and differenceAmount != ''">and differenceAmount = #{differenceAmount}
            </if>
            <if test="differenceExplanation != null  and differenceExplanation != ''">and differenceExplanation =
                #{differenceExplanation}
            </if>
            <if test="serviceDescription != null  and serviceDescription != ''">and serviceDescription =
                #{serviceDescription}
            </if>
            <if test="excludingTaxAmount != null  and excludingTaxAmount != ''">and excludingTaxAmount =
                #{excludingTaxAmount}
            </if>
            <if test="specialInvoiceTaxAmount != null  and specialInvoiceTaxAmount != ''">and specialInvoiceTaxAmount =
                #{specialInvoiceTaxAmount}
            </if>
            <if test="reimbursementAmount != null  and reimbursementAmount != ''">and reimbursementAmount =
                #{reimbursementAmount}
            </if>
            <if test="currency != null  and currency != ''">and currency = #{currency}</if>
            <if test="projectNo != null  and projectNo != ''">and projectNo like concat('%', #{projectNo}, '%')</if>
            <if test="projectNos != null  and projectNos != ''">and projectNos like concat('%', #{projectNos}, '%')</if>

            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="sapPayNumber != null  and sapPayNumber != ''">and sapPayNumber = #{sapPayNumber}</if>
            <if test="voucherPushDate != null  and voucherPushDate != ''">and voucherPushDate = #{voucherPushDate}</if>
            <if test="fd20ID != null  and fd20ID != ''">and fd20ID = #{fd20ID}</if>
            <if test="fd20Number != null  and fd20Number != ''">and fd20Number like concat('%', #{fd20Number}, '%')</if>
            <if test="fd20Name != null  and fd20Name != ''">and fd20Name like concat('%', #{fd20Name}, '%')</if>
            <if test="fd20Amount != null  and fd20Amount != ''">and fd20Amount = #{fd20Amount}</if>
            <if test="fd20ServiceDescription != null  and fd20ServiceDescription != ''">and fd20ServiceDescription =
                #{fd20ServiceDescription}
            </if>
            <if test="executionDate != null ">and executionDate = #{executionDate}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="permissionProjectNo != null and permissionProjectNo.size() > 0">
                and projectNos in (
                <foreach collection="permissionProjectNo" item="e" separator=",">
                    #{e.projectNos}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="selectFd74ClinicalConferenceReimbursementById" parameterType="Integer"
            resultMap="Fd74ClinicalConferenceReimbursementResult">
        <include refid="selectFd74ClinicalConferenceReimbursementVo"/>
        where id = #{id}
    </select>

    <insert id="insertFd74ClinicalConferenceReimbursement" parameterType="Fd74ClinicalConferenceReimbursement">
        insert into fd74_clinical_conference_reimbursement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="oaReimbursementNumber != null">oaReimbursementNumber,</if>
            <if test="dateCreated != null">dateCreated,</if>
            <if test="reimbursementCategory != null">reimbursementCategory,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyEntity != null">companyEntity,</if>
            <if test="reimbursementPerson != null">reimbursementPerson,</if>
            <if test="reimbursementPersonNumber != null">reimbursementPersonNumber,</if>
            <if test="applicant != null">applicant,</if>
            <if test="applicantNumber != null">applicantNumber,</if>
            <if test="applicantPosition != null">applicantPosition,</if>
            <if test="department != null">department,</if>
            <if test="costCenterCode != null">costCenterCode,</if>
            <if test="costCenterName != null">costCenterName,</if>
            <if test="relatedMeetingApplication != null">relatedMeetingApplication,</if>
            <if test="businessPeriod != null">businessPeriod,</if>
            <if test="startDate != null">startDate,</if>
            <if test="endDate != null">endDate,</if>
            <if test="meetingName != null">meetingName,</if>
            <if test="meetingType != null">meetingType,</if>
            <if test="meetingCategory != null">meetingCategory,</if>
            <if test="meetingFormat != null">meetingFormat,</if>
            <if test="companyRole != null">companyRole,</if>
            <if test="objectOriented != null">objectOriented,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="peopleCovered != null">peopleCovered,</if>
            <if test="signNumber != null">signNumber,</if>
            <if test="expertsNumber != null">expertsNumber,</if>
            <if test="meetingMaterials != null">meetingMaterials,</if>
            <if test="invitation != null">invitation,</if>
            <if test="schedule != null">schedule,</if>
            <if test="attendanceSheet != null">attendanceSheet,</if>
            <if test="photo != null">photo,</if>
            <if test="paymentMethod != null">paymentMethod,</if>
            <if test="loanBalance != null">loanBalance,</if>
            <if test="isWhetherLoan != null">isWhetherLoan,</if>
            <if test="totalAmount != null">totalAmount,</if>
            <if test="conferenceBudgetAmount != null">conferenceBudgetAmount,</if>
            <if test="differenceAmount != null">differenceAmount,</if>
            <if test="differenceExplanation != null">differenceExplanation,</if>
            <if test="serviceDescription != null">serviceDescription,</if>
            <if test="excludingTaxAmount != null">excludingTaxAmount,</if>
            <if test="specialInvoiceTaxAmount != null">specialInvoiceTaxAmount,</if>
            <if test="reimbursementAmount != null">reimbursementAmount,</if>
            <if test="currency != null">currency,</if>
            <if test="projectNo != null">projectNo,</if>
            <if test="projectNos != null">projectNos,</if>
            <if test="centerName != null">centerName,</if>
            <if test="documentStatus != null">documentStatus,</if>
            <if test="currentSession != null">currentSession,</if>
            <if test="sapPayNumber != null">sapPayNumber,</if>
            <if test="voucherPushDate != null">voucherPushDate,</if>
            <if test="fd20ID != null">fd20ID,</if>
            <if test="fd20Number != null">fd20Number,</if>
            <if test="fd20Name != null">fd20Name,</if>
            <if test="fd20Amount != null">fd20Amount,</if>
            <if test="fd20ServiceDescription != null">fd20ServiceDescription,</if>
            <if test="executionDate != null">executionDate,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="fdId != null">fdId,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="oaReimbursementNumber != null">#{oaReimbursementNumber},</if>
            <if test="dateCreated != null">#{dateCreated},</if>
            <if test="reimbursementCategory != null">#{reimbursementCategory},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyEntity != null">#{companyEntity},</if>
            <if test="reimbursementPerson != null">#{reimbursementPerson},</if>
            <if test="reimbursementPersonNumber != null">#{reimbursementPersonNumber},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="applicantNumber != null">#{applicantNumber},</if>
            <if test="applicantPosition != null">#{applicantPosition},</if>
            <if test="department != null">#{department},</if>
            <if test="costCenterCode != null">#{costCenterCode},</if>
            <if test="costCenterName != null">#{costCenterName},</if>
            <if test="relatedMeetingApplication != null">#{relatedMeetingApplication},</if>
            <if test="businessPeriod != null">#{businessPeriod},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="meetingName != null">#{meetingName},</if>
            <if test="meetingType != null">#{meetingType},</if>
            <if test="meetingCategory != null">#{meetingCategory},</if>
            <if test="meetingFormat != null">#{meetingFormat},</if>
            <if test="companyRole != null">#{companyRole},</if>
            <if test="objectOriented != null">#{objectOriented},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="peopleCovered != null">#{peopleCovered},</if>
            <if test="signNumber != null">#{signNumber},</if>
            <if test="expertsNumber != null">#{expertsNumber},</if>
            <if test="meetingMaterials != null">#{meetingMaterials},</if>
            <if test="invitation != null">#{invitation},</if>
            <if test="schedule != null">#{schedule},</if>
            <if test="attendanceSheet != null">#{attendanceSheet},</if>
            <if test="photo != null">#{photo},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="loanBalance != null">#{loanBalance},</if>
            <if test="isWhetherLoan != null">#{isWhetherLoan},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="conferenceBudgetAmount != null">#{conferenceBudgetAmount},</if>
            <if test="differenceAmount != null">#{differenceAmount},</if>
            <if test="differenceExplanation != null">#{differenceExplanation},</if>
            <if test="serviceDescription != null">#{serviceDescription},</if>
            <if test="excludingTaxAmount != null">#{excludingTaxAmount},</if>
            <if test="specialInvoiceTaxAmount != null">#{specialInvoiceTaxAmount},</if>
            <if test="reimbursementAmount != null">#{reimbursementAmount},</if>
            <if test="currency != null">#{currency},</if>
            <if test="projectNo != null">#{projectNo},</if>
            <if test="projectNos != null">#{projectNos},</if>
            <if test="centerName != null">#{centerName},</if>
            <if test="documentStatus != null">#{documentStatus},</if>
            <if test="currentSession != null">#{currentSession},</if>
            <if test="sapPayNumber != null">#{sapPayNumber},</if>
            <if test="voucherPushDate != null">#{voucherPushDate},</if>
            <if test="fd20ID != null">#{fd20ID},</if>
            <if test="fd20Number != null">#{fd20Number},</if>
            <if test="fd20Name != null">#{fd20Name},</if>
            <if test="fd20Amount != null">#{fd20Amount},</if>
            <if test="fd20ServiceDescription != null">#{fd20ServiceDescription},</if>
            <if test="executionDate != null">#{executionDate},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="fdId != null">#{fdId},</if>
        </trim>
    </insert>

    <update id="updateFd74ClinicalConferenceReimbursement" parameterType="Fd74ClinicalConferenceReimbursement">
        update fd74_clinical_conference_reimbursement
        <trim prefix="SET" suffixOverrides=",">
            <if test="oaReimbursementNumber != null">oaReimbursementNumber = #{oaReimbursementNumber},</if>
            <if test="dateCreated != null">dateCreated = #{dateCreated},</if>
            <if test="reimbursementCategory != null">reimbursementCategory = #{reimbursementCategory},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyEntity != null">companyEntity = #{companyEntity},</if>
            <if test="reimbursementPerson != null">reimbursementPerson = #{reimbursementPerson},</if>
            <if test="reimbursementPersonNumber != null">reimbursementPersonNumber = #{reimbursementPersonNumber},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="applicantNumber != null">applicantNumber = #{applicantNumber},</if>
            <if test="applicantPosition != null">applicantPosition = #{applicantPosition},</if>
            <if test="department != null">department = #{department},</if>
            <if test="costCenterCode != null">costCenterCode = #{costCenterCode},</if>
            <if test="costCenterName != null">costCenterName = #{costCenterName},</if>
            <if test="relatedMeetingApplication != null">relatedMeetingApplication = #{relatedMeetingApplication},</if>
            <if test="businessPeriod != null">businessPeriod = #{businessPeriod},</if>
            <if test="startDate != null">startDate = #{startDate},</if>
            <if test="endDate != null">endDate = #{endDate},</if>
            <if test="meetingName != null">meetingName = #{meetingName},</if>
            <if test="meetingType != null">meetingType = #{meetingType},</if>
            <if test="meetingCategory != null">meetingCategory = #{meetingCategory},</if>
            <if test="meetingFormat != null">meetingFormat = #{meetingFormat},</if>
            <if test="companyRole != null">companyRole = #{companyRole},</if>
            <if test="objectOriented != null">objectOriented = #{objectOriented},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="peopleCovered != null">peopleCovered = #{peopleCovered},</if>
            <if test="signNumber != null">signNumber = #{signNumber},</if>
            <if test="expertsNumber != null">expertsNumber = #{expertsNumber},</if>
            <if test="meetingMaterials != null">meetingMaterials = #{meetingMaterials},</if>
            <if test="invitation != null">invitation = #{invitation},</if>
            <if test="schedule != null">schedule = #{schedule},</if>
            <if test="attendanceSheet != null">attendanceSheet = #{attendanceSheet},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="paymentMethod != null">paymentMethod = #{paymentMethod},</if>
            <if test="loanBalance != null">loanBalance = #{loanBalance},</if>
            <if test="isWhetherLoan != null">isWhetherLoan = #{isWhetherLoan},</if>
            <if test="totalAmount != null">totalAmount = #{totalAmount},</if>
            <if test="conferenceBudgetAmount != null">conferenceBudgetAmount = #{conferenceBudgetAmount},</if>
            <if test="differenceAmount != null">differenceAmount = #{differenceAmount},</if>
            <if test="differenceExplanation != null">differenceExplanation = #{differenceExplanation},</if>
            <if test="serviceDescription != null">serviceDescription = #{serviceDescription},</if>
            <if test="excludingTaxAmount != null">excludingTaxAmount = #{excludingTaxAmount},</if>
            <if test="specialInvoiceTaxAmount != null">specialInvoiceTaxAmount = #{specialInvoiceTaxAmount},</if>
            <if test="reimbursementAmount != null">reimbursementAmount = #{reimbursementAmount},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="projectNo != null">projectNo = #{projectNo},</if>
            <if test="projectNos != null">projectNos = #{projectNos},</if>
            <if test="centerName != null">centerName = #{centerName},</if>
            <if test="documentStatus != null">documentStatus = #{documentStatus},</if>
            <if test="currentSession != null">currentSession = #{currentSession},</if>
            <if test="sapPayNumber != null">sapPayNumber = #{sapPayNumber},</if>
            <if test="voucherPushDate != null">voucherPushDate = #{voucherPushDate},</if>
            <if test="fd20ID != null">fd20ID = #{fd20ID},</if>
            <if test="fd20Number != null">fd20Number = #{fd20Number},</if>
            <if test="fd20Name != null">fd20Name = #{fd20Name},</if>
            <if test="fd20Amount != null">fd20Amount = #{fd20Amount},</if>
            <if test="fd20ServiceDescription != null">fd20ServiceDescription = #{fd20ServiceDescription},</if>
            <if test="executionDate != null">executionDate = #{executionDate},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFd74ClinicalConferenceReimbursementById" parameterType="Integer">
        delete
        from fd74_clinical_conference_reimbursement
        where id = #{id}
    </delete>

    <delete id="deleteFd74ClinicalConferenceReimbursementByIds" parameterType="String">
        delete from fd74_clinical_conference_reimbursement where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAllFd74ClinicalConferenceReimbursement" parameterType="Integer">
        delete
        from fd74_clinical_conference_reimbursement
    </delete>
</mapper>