<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalTrialMaterialsMapper">

    <resultMap type="ClinicalTrialMaterials" id="ClinicalTrialMaterialsResult">
        <result property="requestNo" column="requestNo"/>
        <result property="creationTime" column="creationTime"/>
        <result property="processName" column="processName"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="centerNo" column="centerNo"/>
        <result property="centerName" column="centerName"/>
        <result property="applicant" column="applicant"/>
        <result property="applicationReason" column="applicationReason"/>
        <result property="supplyMethod" column="supplyMethod"/>
        <result property="materialCategory" column="materialCategory"/>
        <result property="materialName" column="materialName"/>
        <result property="productCode" column="productCode"/>
        <result property="productName" column="productName"/>
        <result property="number" column="number"/>
        <result property="unit" column="unit"/>
        <result property="expectedDeliveryTime" column="expectedDeliveryTime"/>
        <result property="recipient" column="recipient"/>
        <result property="city" column="city"/>
        <result property="detailedShippingAddress" column="detailedShippingAddress"/>
        <result property="endTime" column="endTime"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="currentProcessor" column="currentProcessor"/>
        <result property="department" column="department"/>
        <result property="principalIinvestigator" column="principalIinvestigator"/>
        <result property="waybillNumber" column="waybillNumber"/>
        <result property="remark" column="remark"/>
        <result property="null1" column="null1"/>
        <result property="null2" column="null2"/>
        <result property="null3" column="null3"/>
        <result property="null4" column="null4"/>
        <result property="null5" column="null5"/>
        <result property="null6" column="null6"/>
        <result property="null7" column="null7"/>
        <result property="null8" column="null8"/>
        <result property="null9" column="null9"/>
        <result property="null10" column="null10"/>
        <result property="null11" column="null11"/>
        <result property="fDId" column="fDId"/>
        <result property="model" column="model"/>
    </resultMap>

    <sql id="selectClinicalTrialMaterialsVo">
        select requestNo,
               creationTime,
               processName,
               projectNumber,
               centerNo,
               centerName,
               applicant,
               applicationReason,
               supplyMethod,
               materialCategory,
               materialName,
               productCode,
               productName,
               number,
               unit,
               expectedDeliveryTime,
               recipient,
               city,
               detailedShippingAddress,
               endTime,
               documentStatus,
               currentSession,
               currentProcessor,
               department,
               principalIinvestigator,
               waybillNumber,
               remark,
               null1,
               null2,
               null3,
               null4,
               null5,
               null6,
               null7,
               null8,
               null9,
               null10,
               null11,
               fDId,
               model
        from clinical_trial_materials
    </sql>

    <select id="selectClinicalTrialMaterialsList" parameterType="ClinicalTrialMaterials"
            resultMap="ClinicalTrialMaterialsResult">
        <include refid="selectClinicalTrialMaterialsVo"/>
        <where>
            <if test="requestNo != null  and requestNo != ''">and requestNo like concat('%', #{requestNo}, '%')</if>
            <if test="params.beginCreationTime != null and params.beginCreationTime != '' and params.endCreationTime != null and params.endCreationTime != ''">
                and creationTime between #{params.beginCreationTime} and #{params.endCreationTime}
            </if>
            <if test="processName != null  and processName != ''">and processName like concat('%', #{processName},
                '%')
            </if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="centerNo != null  and centerNo != ''">and centerNo like concat('%', #{centerNo}, '%')</if>
            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="applicationReason != null  and applicationReason != ''">and applicationReason =
                #{applicationReason}
            </if>
            <if test="supplyMethod != null  and supplyMethod != ''">and supplyMethod like concat('%', #{supplyMethod},
                '%')
            </if>
            <if test="materialCategory != null  and materialCategory != ''">and materialCategory like concat('%',
                #{materialCategory}, '%')
            </if>
            <if test="materialName != null  and materialName != ''">and materialName like concat('%', #{materialName},
                '%')
            </if>
            <if test="productCode != null  and productCode != ''">and productCode like concat('%', #{productCode},
                '%')
            </if>
            <if test="productName != null  and productName != ''">and productName like concat('%', #{productName},
                '%')
            </if>
            <if test="number != null  and number != ''">and number = #{number}</if>
            <if test="unit != null  and unit != ''">and unit = #{unit}</if>
            <if test="recipient != null  and recipient != ''">and recipient like concat('%', #{recipient}, '%')</if>
            <if test="city != null  and city != ''">and city like concat('%', #{city}, '%')</if>
            <if test="detailedShippingAddress != null  and detailedShippingAddress != ''">and detailedShippingAddress
                like concat('%', #{detailedShippingAddress}, '%')
            </if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''">
                and endTime between #{params.beginEndTime} and #{params.endEndTime}
            </if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="currentProcessor != null  and currentProcessor != ''">and currentProcessor like concat('%',
                #{currentProcessor}, '%')
            </if>
            and documentStatus !='废弃'
        </where>
        order by creationTime desc
    </select>
</mapper>