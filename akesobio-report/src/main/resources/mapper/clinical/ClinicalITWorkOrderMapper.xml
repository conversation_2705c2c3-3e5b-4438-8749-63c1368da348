<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalITWorkOrderMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalITWorkOrder" id="ClinicalITWorkOrderResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectClinicalITWorkOrderList">
        SELECT id,
               oddNumber,
               applicationName,
               jobNumber,
               department,
               position,
               description,
               serviceType
        FROM clinical_it_work_order
    </sql>

    <!-- 方法 -->
    <select id="queryClinicalITWorkOrderList" parameterType="ClinicalITWorkOrder" resultMap="ClinicalITWorkOrderResult">
        <include refid="selectClinicalITWorkOrderList"/>
        <where>
            department LIKE '%临床运行部门_临床事务部_项目组' and serviceType = '权限类服务'
            <if test="oddNumber != null and oddNumber != ''">and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="applicationName != null and applicationName != ''">and applicationName like concat('%',
                #{applicationName}, '%')
            </if>
            <if test="jobNumber != null and jobNumber != ''">and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="department != null and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="description != null and description != ''">and description like concat('%', #{description}, '%')
            </if>
            <if test="serviceType != null and serviceType != ''">and serviceType like concat('%', #{serviceType}, '%')
            </if>
            <!--            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>-->
            <!--            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>-->
        </where>
        ORDER BY oddNumber DESC
    </select>
</mapper>