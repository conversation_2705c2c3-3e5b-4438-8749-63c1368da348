<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.Fd74ClinicalConferenceReimbursementOldMapper">
    <resultMap type="Fd74ClinicalConferenceReimbursementOld" id="Fd74ClinicalConferenceReimbursementOldResult">
        <result property="oaReimbursementNumber" column="oaReimbursementNumber"/>
        <result property="dateCreated" column="dateCreated"/>
        <result property="reimbursementCategory" column="reimbursementCategory"/>
        <result property="companyCode" column="companyCode"/>
        <result property="companyName" column="companyName"/>
        <result property="companyEntity" column="companyEntity"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="reimbursementPersonNumber" column="reimbursementPersonNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="applicantNumber" column="applicantNumber"/>
        <result property="applicantPosition" column="applicantPosition"/>
        <result property="department" column="department"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="relatedMeetingApplication" column="relatedMeetingApplication"/>
        <result property="businessPeriod" column="businessPeriod"/>
        <result property="startDate" column="startDate"/>
        <result property="endDate" column="endDate"/>
        <result property="meetingName" column="meetingName"/>
        <result property="meetingType" column="meetingType"/>
        <result property="meetingCategory" column="meetingCategory"/>
        <result property="meetingFormat" column="meetingFormat"/>
        <result property="companyRole" column="companyRole"/>
        <result property="objectOriented" column="objectOriented"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="peopleCovered" column="peopleCovered"/>
        <result property="signNumber" column="signNumber"/>
        <result property="expertsNumber" column="expertsNumber"/>
        <result property="meetingMaterials" column="meetingMaterials"/>
        <result property="invitation" column="invitation"/>
        <result property="schedule" column="schedule"/>
        <result property="attendanceSheet" column="attendanceSheet"/>
        <result property="photo" column="photo"/>
        <result property="paymentMethod" column="paymentMethod"/>
        <result property="loanBalance" column="loanBalance"/>
        <result property="isWhetherLoan" column="isWhetherLoan"/>
        <result property="totalAmount" column="totalAmount"/>
        <result property="conferenceBudgetAmount" column="conferenceBudgetAmount"/>
        <result property="differenceAmount" column="differenceAmount"/>
        <result property="differenceExplanation" column="differenceExplanation"/>
        <result property="serviceDescription" column="serviceDescription"/>
        <result property="excludingTaxAmount" column="excludingTaxAmount"/>
        <result property="specialInvoiceTaxAmount" column="specialInvoiceTaxAmount"/>
        <result property="reimbursementAmount" column="reimbursementAmount"/>
        <result property="currency" column="currency"/>
        <result property="projectNo" column="projectNo"/>
        <result property="projectNos" column="projectNos"/>
        <result property="centerName" column="centerName"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="voucherPushDate" column="voucherPushDate"/>
        <result property="fdId" column="fdId"/>
    </resultMap>
    <select id="selectFd74" parameterType="Fd74ClinicalConferenceReimbursementOld"
            resultMap="Fd74ClinicalConferenceReimbursementOldResult">
        SELECT--FD74
              a.fd_id 'fdId', a.serialno 'oaReimbursementNumber', a.fd_3bda9a3d4dcab6 'dateCreated', NULL 'reimbursementCategory', a.code_CO 'companyCode', a.fd_3bda9ee477fe46_text 'companyName', a.fd_3bda9ee477fe46_text 'companyEntity', b.fd_name 'reimbursementPerson', a.fd_3ab974b9c3d6f0 'reimbursementPersonNumber', c.fd_name 'applicant', NULL 'applicantNumber', a.fd_3bdb36401127fc 'applicantPosition', a.fd_3bda9a4e538620 'department', a.cost_center_code_text 'costCenterCode', a.cost_name_center 'costCenterName', a.fd_3bda9abf99730c_text 'relatedMeetingApplication', TRIM(CONCAT(a.fd_3bda9b0b101dd6, '年', a.fd_3bda9b1271de30)) + '月' AS 'businessPeriod', a.fd_3bda9c925d4cd4 'startDate', a.fd_3bda9c90a225a8 'endDate', a.fd_3bda9c7b86a3b4 'meetingName', a.fd_3bda9cd9743454 'meetingType', a.fd_3bda9cbc49cee8 'meetingCategory', a.fd_3bda9cc3494aba 'meetingFormat', a.fd_3bda9cbfcc8020 'companyRole', a.fd_3bee6425916eca 'objectOriented', a.fd_3bda9ca484bd94 'province', a.fd_3bda9cd3ac8be6_text 'city', a.fd_3bda9c8ee711a6 'peopleCovered', a.fd_3bdcc7a0cd9efe 'signNumber', a.fd_3bda9c83a40efe 'expertsNumber', a.fd_3bda99a8b9a2b8 'meetingMaterials', ( CASE WHEN CHARINDEX('邀请函', a.fd_3bda99a8b9a2b8) > 0 THEN '是' ELSE '' END ) 'invitation', ( CASE WHEN CHARINDEX('日程表', a.fd_3bda99a8b9a2b8) > 0 THEN '是' ELSE '' END ) 'schedule', ( CASE WHEN CHARINDEX('签到表', a.fd_3bda99a8b9a2b8) > 0 THEN '是' ELSE '' END ) 'attendanceSheet', ( CASE WHEN CHARINDEX('照片', a.fd_3bda99a8b9a2b8) > 0 THEN '是' ELSE '' END ) 'photo', a.fd_3bda9cc56afa98 'paymentMethod', a.fd_3bdc288af21e46 'loanBalance', ( CASE a.whether_offset WHEN '0' THEN '否' WHEN '1' THEN '是' ELSE '不对劲' END ) 'isWhetherLoan', a.fd_3d1f33bb1e0802 'totalAmount', a.fd_3bda9cf0595844 'conferenceBudgetAmount', a.fd_3bdcc7cfd74364 'differenceAmount', a.fd_3bda99c234f71a 'differenceExplanation', d.fd_3bdb991ad95ce2 'serviceDescription', d.fd_3bdd9e9290914c 'excludingTaxAmount', d.fd_3bdb58a94b8528 'specialInvoiceTaxAmount', d.fd_3bdb58a2f1b942 'reimbursementAmount', a.fd_3be0134499038e_text 'currency', a.item_number_text 'projectNo',a.fd_3d5eacadefe526 as 'projectNos', a.fd_3bdaa6c45dd7b6_text 'centerName', (
            CASE
                f.doc_status
                WHEN '10' THEN
                    '草稿'
                WHEN '20' THEN
                    '待审'
                WHEN '11' THEN
                    '驳回'
                WHEN '00' THEN
                    '废弃'
                WHEN '30' THEN
                    '结束'
                WHEN '31' THEN
                    '已反馈'
                ELSE '不对劲'
                END
            ) 'documentStatus', e.fd_fact_node_name 'currentSession', a.sap_document_number 'sapPayNumber', a.sap_credential_push_time 'voucherPushDate'
        FROM ekp_cw_hyfy a
                 LEFT JOIN hr_org_element b ON b.fd_id = a.apply_reimbursement
                 LEFT JOIN hr_org_element c ON c.fd_id = a.fd_3bda9a43128cdc
                 LEFT JOIN ekp_kp_cw_hyfy_99dc245476 d ON d.fd_parent_id = a.fd_id
                 LEFT JOIN [dbo].[modeling_model_main] f
        ON a.fd_id = f.fd_id
            LEFT JOIN [dbo].[lbpm_node] e ON a.fd_id = e.fd_process_id
        WHERE
            a.fd_3bda9a4e538620 LIKE '%临床运行%'
          AND a.fd_3bda9a3d4dcab6 >= '2025-01-01'
    </select>
</mapper>