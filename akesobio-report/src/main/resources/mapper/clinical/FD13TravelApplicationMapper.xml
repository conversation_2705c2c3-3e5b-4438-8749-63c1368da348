<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.FD13TravelApplicationMapper">
    <resultMap type="FD13TravelApplication" id="FD13TravelApplicationResult">
        <result property="fd13ID" column="fd13ID"/>
        <result property="fd13Number" column="fd13Number"/>
        <result property="fd13Name" column="fd13Name"/>
        <result property="fd13Amount" column="fd13Amount"/>
        <result property="fd13Location" column="fd13Location"/>
        <result property="fd13ServiceDescription" column="fd13ServiceDescription"/>
    </resultMap>
    <select id="selectFd13" parameterType="FD13TravelApplication"
            resultMap="FD13TravelApplicationResult">
        SELECT
        fd13ID,
        fd13Number,
        fd13Name,
        fd13Amount,
        fd13Location,
        fd13ServiceDescription
        FROM
        (
        SELECT
        a.fd_id 'fd13ID',
        a.fd_danHao 'fd13Number',
        'FD13差旅申请' 'fd13Name',
        NULL 'fd13Amount',
        b.fd_muDeChengShi 'fd13Location',
        b.fd_chaLvRenWu 'fd13ServiceDescription'
        FROM
        ekp_clsq_2024 a
        LEFT JOIN ekp_clsq_detail_2024 b ON b.fd_parent_id= a.fd_id
        WHERE
        a.fd_suoShuBuMen LIKE '%临床运行%'
        AND a.fd_danHao IS NOT NULL UNION ALL
        SELECT
        a.fd_id 'fd13ID',
        a.fd_danHao 'fd13Number',
        '外出单' 'fd13Name',
        NULL 'fd13Amount',
        b.fd_waiChuDiDian 'fd13Location',
        b.fd_waiChuShiYou 'fd13ServiceDescription'
        FROM
        ekp_wcd_zb a
        LEFT JOIN ekp_wcd_detial b ON b.fd_parent_id= a.fd_id
        WHERE
        a.fd_suoShuBuMen LIKE '%临床运行%'
        AND a.fd_danHao IS NOT NULL
        ) b
        <where>
            <if test="fd13Number != null  and fd13Number != ''">and fd13Number = #{fd13Number}</if>
        </where>
    </select>
</mapper>