<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalSystemPermissionMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalSystemPermission" id="ClinicalSystemPermissionResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectClinicalSystemPermissionList">
        SELECT id,
               applicationDate,
               oddNumber,
               applicationName,
               jobNumber,
               department,
               position,
               projectNumber,
               projectBranch,
               applicationType,
               applicationAim,
               involveSystem,
               name,
               email,
               permissionRole,
               centerNumber,
               centerName,
               pi,
               sectionDepartment,
               permissionOperate,
               applicationReason,
               submitDate,
               documentStatus,
               currentSession,
               currentProcessor,
               nodeArrivalTime,
               nodeApprovalTime,
               operationType,
               approver,
               approvalOpinion,
               (case
                   when xxzddDate5 is null then xxzddDate13
                   else xxzddDate5
                end) as xxzddDate,
               (case
                   when xxzDate5 is null then xxzDate13
                   else xxzDate5
                end) as xxzDate
        FROM clinical_system_permission
    </sql>

    <!-- 方法 -->
    <select id="queryClinicalSystemPermissionList" parameterType="ClinicalSystemPermission"
            resultMap="ClinicalSystemPermissionResult">
        <include refid="selectClinicalSystemPermissionList"/>
        <where>
            department IN ('项目组','信息组')
            <if test="applicationName != null and applicationName != ''">and applicationName like concat('%',
                #{applicationName}, '%')
            </if>
            <if test="oddNumber != null and oddNumber != ''">and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="jobNumber != null and jobNumber != ''">and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="department != null and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="projectNumber != null and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="projectBranch != null and projectBranch != ''">and projectBranch like concat('%',
                #{projectBranch}, '%')
            </if>
            <if test="applicationType != null and applicationType != ''">and applicationType like concat('%',
                #{applicationType}, '%')
            </if>
            <if test="centerNumber != null and centerNumber != ''">and centerNumber like concat('%', #{centerNumber},
                '%')
            </if>
            <if test="centerName != null and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="currentSession != null and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="documentStatus != null and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>