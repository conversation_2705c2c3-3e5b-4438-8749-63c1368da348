<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.Fd73ClinicalTravelReimbursementOldMapper">
    <resultMap type="Fd73ClinicalTravelReimbursementOld" id="Fd73ClinicalTravelReimbursementOldResult">
        <result property="oaReimbursementNumber" column="oaReimbursementNumber"/>
        <result property="dateCreated" column="dateCreated"/>
        <result property="reimbursementCategory" column="reimbursementCategory"/>
        <result property="companyCode" column="companyCode"/>
        <result property="companyName" column="companyName"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="reimbursementPersonNumber" column="reimbursementPersonNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="applicantNumber" column="applicantNumber"/>
        <result property="applicantPosition" column="applicantPosition"/>
        <result property="department" column="department"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="relatedApprovalForm" column="relatedApprovalForm"/>
        <result property="businessPeriod" column="businessPeriod"/>
        <result property="departureDate" column="departureDate"/>
        <result property="returnDate" column="returnDate"/>
        <result property="businessDay" column="businessDay"/>
        <result property="colleagues" column="colleagues"/>
        <result property="loanBalance" column="loanBalance"/>
        <result property="isWhetherLoan" column="isWhetherLoan"/>
        <result property="totalAmount" column="totalAmount"/>
        <result property="expenseAccountCode" column="expenseAccountCode"/>
        <result property="expenseAccountName" column="expenseAccountName"/>
        <result property="serviceDescription" column="serviceDescription"/>
        <result property="invoiceNumber" column="invoiceNumber"/>
        <result property="excludingTaxAmount" column="excludingTaxAmount"/>
        <result property="specialInvoiceTaxAmount" column="specialInvoiceTaxAmount"/>
        <result property="reimbursementAmount" column="reimbursementAmount"/>
        <result property="currency" column="currency"/>
        <result property="projectNo" column="projectNo"/>
        <result property="projectNos" column="projectNos"/>
        <result property="centerName" column="centerName"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="voucherPushDate" column="voucherPushDate"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="fdId" column="fdId"/>
    </resultMap>
    <select id="selectFd73" parameterType="Fd73ClinicalTravelReimbursementOld"
            resultMap="Fd73ClinicalTravelReimbursementOldResult">
        SELECT--FD73
              a.fd_id 'fdId', a.serialno 'oaReimbursementNumber', a.application_date 'dateCreated', NULL 'reimbursementCategory', a.code_CO 'companyCode', a.bear_cost_text 'companyName', b.fd_name 'reimbursementPerson', a.fd_3ab974b9c3d6f0 'reimbursementPersonNumber', c.fd_name 'applicant', a.job_number 'applicantNumber', a.job_title 'applicantPosition', a.department 'department', a.cost_center_code_text 'costCenterCode', a.cost_name_center 'costCenterName', a.association_form 'relatedApprovalForm', a.fd_3bb0234b0104f4 + '-' + a.fd_3bb02356c2f6a4 'businessPeriod', a.fd_3a3bbc08cebf2e 'departureDate', a.fd_3a3bbc0ced7134 'returnDate', a.fd_3ab9483f90530a 'businessDay', (
            STUFF(
                    (SELECT ';' + sys_org_person_id
                     FROM ekporg_180e01c5b01c95228f23 g
                     WHERE a.fd_id = g.fd_parent_id FOR xml path ('') ),
		1,
		1,
		''
	)
            ) 'colleagues', a.fd_3a45cf66283b08 'loanBalance', ( CASE a.whether_offset WHEN '0' THEN '否' WHEN '1' THEN '是' ELSE '不对劲' END ) 'isWhetherLoan', a.fd_3d1f3345b84e42 'totalAmount', d.debit_code_text 'expenseAccountCode', d.debit_name 'expenseAccountName', d.invoice_type 'serviceDescription', d.invoice_no_baiwang 'invoiceNumber', d.business_description 'excludingTaxAmount', d.fd_3a3bbef9d4e540 'specialInvoiceTaxAmount', d.fd_3abc64c2d8e6ac 'reimbursementAmount', a.fd_3a3c5a391655da_text 'currency', a.item_number_text 'projectNo',a.item_name as 'projectNos', a.fd_3ab7e967054356 'centerName', (
            CASE
                f.doc_status
                WHEN '10' THEN
                    '草稿'
                WHEN '20' THEN
                    '待审'
                WHEN '11' THEN
                    '驳回'
                WHEN '00' THEN
                    '废弃'
                WHEN '30' THEN
                    '结束'
                WHEN '31' THEN
                    '已反馈'
                ELSE '不对劲'
                END
            ) 'documentStatus', e.fd_fact_node_name 'currentSession', a.sap_document_number 'sapPayNumber', a.sap_credential_push_time 'voucherPushDate', a.application_subject 'applicationSubject'
        FROM ekp_travelNew a
                 LEFT JOIN hr_org_element b ON b.fd_id = a.apply_reimbursement
                 LEFT JOIN hr_org_element c ON c.fd_id = a.applicant
                 LEFT JOIN ekp__travelNew_6f62fb1b5a d ON d.fd_parent_id = a.fd_id
                 LEFT JOIN [dbo].[modeling_model_main] f
        ON a.fd_id = f.fd_id
            LEFT JOIN [dbo].[lbpm_node] e ON a.fd_id = e.fd_process_id
        WHERE
            a.department LIKE '%临床运行%'
          AND a.application_date >= '2025-01-01'
    </select>
</mapper>