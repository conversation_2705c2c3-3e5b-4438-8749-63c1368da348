<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalTransferMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalTransfer" id="ClinicalTransferResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectClinicalTransferList">
        SELECT id,
               oddNumber,
               applicationName,
               applicationDate,
               jobNumber,
               department,
               position,
               departmentx,
               positionx,
               transferDate,
               directSupervisor,
               departmentLeaders,
               transferReason,
               documentStatus,
               currentSession,
               currentProcessor,
               dczsDate,
               dcbmDate,
               dcbmfgDate,
               drzsDate,
               drbmDate,
               drbmfgDate,
               hrDate
        FROM clinical_transfer
    </sql>

    <!-- 方法 -->
    <select id="queryClinicalTransferList" parameterType="ClinicalTransfer" resultMap="ClinicalTransferResult">
        <include refid="selectClinicalTransferList"/>
        <where>
            <if test="department != null and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="departmentx != null and departmentx != ''">and departmentx like concat('%', #{departmentx}, '%')
            </if>
            <if test="position != null and position != ''">and position like concat('%', #{position}, '%')</if>
            <if test="positionx != null and positionx != ''">and positionx like concat('%', #{positionx}, '%')</if>
            <if test="applicationName != null and applicationName != ''">and applicationName like concat('%',
                #{applicationName}, '%')
            </if>
            <if test="jobNumber != null and jobNumber != ''">and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="oddNumber != null and oddNumber != ''">and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="documentStatus != null and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
            <if test="startDate1 != null and startDate1 != ''">and transferDate &gt;= #{startDate1}</if>
            <if test="endDate1 != null and endDate1 != ''">and transferDate &lt;= #{endDate1}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>