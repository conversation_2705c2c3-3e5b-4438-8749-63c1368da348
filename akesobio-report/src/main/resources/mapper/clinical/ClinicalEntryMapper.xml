<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalEntryMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalEntry" id="ClinicalEntryResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectClinicalEntryList">
        SELECT id,
               applicationDate,
               oddNumber,
               jobNumber,
               applicant,
               sex,
               mailbox,
               department,
               position,
               workLocation,
               joinedDate,
               documentStatus,
               nodeId,
               currentSession,
               nodeArrivalTime,
               transferringPerson,
               currentProcessor,
               nodeApprovalTime,
               operationType,
               examiner,
               approvalOpinion
        FROM clinical_entry
    </sql>

    <!-- 方法 -->
    <select id="queryClinicalEntryList" parameterType="ClinicalEntry" resultMap="ClinicalEntryResult">
        <include refid="selectClinicalEntryList"/>
        <where>
            <if test="applicant != null and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="jobNumber != null and jobNumber != ''">and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="oddNumber != null and oddNumber != ''">and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="department != null and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="position != null and position != ''">and position like concat('%', #{position}, '%')</if>
            <if test="documentStatus != null and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentSession != null and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="currentProcessor != null and currentProcessor != ''">and currentProcessor like concat('%',
                #{currentProcessor}, '%')
            </if>
            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>