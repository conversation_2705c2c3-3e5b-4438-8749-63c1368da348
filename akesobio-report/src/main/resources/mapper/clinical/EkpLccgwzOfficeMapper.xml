<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.EkpLccgwzOfficeMapper">
    
    <resultMap type="EkpLccgwzOffice" id="EkpLccgwzOfficeResult">
        <result property="id"    column="id"    />
        <result property="formName"    column="form_name"    />
        <result property="applicationDate"    column="application_date"    />
        <result property="approvalCompletionTime"    column="approval_completion_time"    />
        <result property="applicant"    column="applicant"    />
        <result property="costAttribution"    column="cost_attribution"    />
        <result property="projectNumber"    column="project_number"    />
        <result property="centerName"    column="center_name"    />
        <result property="officeSuppliesName"    column="office_supplies_name"    />
        <result property="commodityCode"    column="commodity_code"    />
        <result property="commodityName"    column="commodity_name"    />
        <result property="number"    column="number"    />
        <result property="units"    column="units"    />
        <result property="budgetUnitPrice"    column="budget_unit_price"    />
        <result property="totalBudget"    column="total_budget"    />
        <result property="remarks"    column="remarks"    />
        <result property="city"    column="city"    />
        <result property="recipientInfo"    column="recipient_info"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="currentNode"    column="current_node"    />
        <result property="arrivalTime"    column="arrival_time"    />
        <result property="nodeName"    column="node_name"    />
        <result property="nodePerson"    column="node_person"    />
        <result property="currentHandler"    column="current_handler"    />
        <result property="recipient"    column="recipient"    />
        <result property="jsPhone"    column="jsPhone"    />
    </resultMap>

    <sql id="selectEkpLccgwzOfficeVo">
        select id, serial_number, form_name, application_date, approval_completion_time, applicant, cost_attribution, project_number, center_name, office_supplies_name, commodity_code, commodity_name, number, units, budget_unit_price, total_budget, remarks, city, recipient_info,doc_status, flow_end_date, current_node, current_handler,
               arrival_time,
               node_name,
               node_person,
               recipient,
               jsPhone
        from ekp_lccgwz_office
    </sql>

    <select id="selectEkpLccgwzOfficeList" parameterType="EkpLccgwzOffice" resultMap="EkpLccgwzOfficeResult">
        <include refid="selectEkpLccgwzOfficeVo"/>
        <where>  
            <if test="formName != null  and formName != ''"> and form_name like concat('%', #{formName}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and application_date between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>

            <!--            <if test="applicationDate != null  and applicationDate != ''"> and application_date = #{applicationDate}</if>-->
<!--            <if test="approvalCompletionTime != null  and approvalCompletionTime != ''"> and approval_completion_time = #{approvalCompletionTime}</if>-->
            <if test="params.beginApprovalCompletionTime != null and params.beginApprovalCompletionTime != '' and params.endApprovalCompletionTime != null and params.endApprovalCompletionTime != ''"> and approval_completion_time between #{params.beginApprovalCompletionTime} and #{params.endApprovalCompletionTime}</if>

            <if test="applicant != null  and applicant != ''"> and applicant = #{applicant}</if>
            <if test="costAttribution != null  and costAttribution != ''"> and cost_attribution = #{costAttribution}</if>
            <if test="projectNumber != null  and projectNumber != ''"> and project_number = #{projectNumber}</if>
            <if test="centerName != null  and centerName != ''"> and center_name like concat('%', #{centerName}, '%')</if>
            <if test="officeSuppliesName != null  and officeSuppliesName != ''"> and office_supplies_name like concat('%', #{officeSuppliesName}, '%')</if>
            <if test="commodityCode != null  and commodityCode != ''"> and commodity_code = #{commodityCode}</if>
            <if test="commodityName != null  and commodityName != ''"> and commodity_name like concat('%', #{commodityName}, '%')</if>
            <if test="serialNumber != null  and serialNumber != ''"> and serial_number like concat('%', #{serialNumber}, '%')</if>
            <if test="currentNode != null  and currentNode != ''"> and current_node like concat('%', #{currentNode}, '%')</if>
            <if test="currentHandler != null  and currentHandler != ''"> and current_handler like concat('%', #{currentHandler}, '%')</if>
            <if test="number != null  and number != ''"> and number = #{number}</if>
            <if test="units != null  and units != ''"> and units = #{units}</if>
            <if test="budgetUnitPrice != null  and budgetUnitPrice != ''"> and budget_unit_price = #{budgetUnitPrice}</if>
            <if test="totalBudget != null  and totalBudget != ''"> and total_budget = #{totalBudget}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="recipientInfo != null  and recipientInfo != ''"> and recipient_info = #{recipientInfo}</if>
            <if test="docStatus != null  and docStatus != ''"> and doc_status = #{docStatus}</if>
            <if test="nodeName != null  and nodeName != ''"> and node_name like concat('%', #{nodeName}, '%')</if>
            <if test="nodePerson != null  and nodePerson != ''"> and node_person like concat('%', #{nodePerson}, '%')</if>

        </where>
        ORDER BY application_date DESC
    </select>
    
    <select id="selectEkpLccgwzOfficeById" parameterType="String" resultMap="EkpLccgwzOfficeResult">
        <include refid="selectEkpLccgwzOfficeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertEkpLccgwzOffice" parameterType="EkpLccgwzOffice">
        insert into ekp_lccgwz_office
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="formName != null">form_name,</if>
            <if test="applicationDate != null">application_date,</if>
            <if test="approvalCompletionTime != null">approval_completion_time,</if>
            <if test="applicant != null">applicant,</if>
            <if test="costAttribution != null">cost_attribution,</if>
            <if test="projectNumber != null">project_number,</if>
            <if test="centerName != null">center_name,</if>
            <if test="officeSuppliesName != null">office_supplies_name,</if>
            <if test="commodityCode != null">commodity_code,</if>
            <if test="commodityName != null">commodity_name,</if>
            <if test="number != null">number,</if>
            <if test="units != null">units,</if>
            <if test="budgetUnitPrice != null">budget_unit_price,</if>
            <if test="totalBudget != null">total_budget,</if>
            <if test="remarks != null">remarks,</if>
            <if test="city != null">city,</if>
            <if test="recipientInfo != null">recipient_info,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="formName != null">#{formName},</if>
            <if test="applicationDate != null">#{applicationDate},</if>
            <if test="approvalCompletionTime != null">#{approvalCompletionTime},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="costAttribution != null">#{costAttribution},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="centerName != null">#{centerName},</if>
            <if test="officeSuppliesName != null">#{officeSuppliesName},</if>
            <if test="commodityCode != null">#{commodityCode},</if>
            <if test="commodityName != null">#{commodityName},</if>
            <if test="number != null">#{number},</if>
            <if test="units != null">#{units},</if>
            <if test="budgetUnitPrice != null">#{budgetUnitPrice},</if>
            <if test="totalBudget != null">#{totalBudget},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="city != null">#{city},</if>
            <if test="recipientInfo != null">#{recipientInfo},</if>
         </trim>
    </insert>

    <update id="updateEkpLccgwzOffice" parameterType="EkpLccgwzOffice">
        update ekp_lccgwz_office
        <trim prefix="SET" suffixOverrides=",">
            <if test="formName != null">form_name = #{formName},</if>
            <if test="applicationDate != null">application_date = #{applicationDate},</if>
            <if test="approvalCompletionTime != null">approval_completion_time = #{approvalCompletionTime},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="costAttribution != null">cost_attribution = #{costAttribution},</if>
            <if test="projectNumber != null">project_number = #{projectNumber},</if>
            <if test="centerName != null">center_name = #{centerName},</if>
            <if test="officeSuppliesName != null">office_supplies_name = #{officeSuppliesName},</if>
            <if test="commodityCode != null">commodity_code = #{commodityCode},</if>
            <if test="commodityName != null">commodity_name = #{commodityName},</if>
            <if test="number != null">number = #{number},</if>
            <if test="units != null">units = #{units},</if>
            <if test="budgetUnitPrice != null">budget_unit_price = #{budgetUnitPrice},</if>
            <if test="totalBudget != null">total_budget = #{totalBudget},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="city != null">city = #{city},</if>
            <if test="recipientInfo != null">recipient_info = #{recipientInfo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEkpLccgwzOfficeById" parameterType="String">
        delete from ekp_lccgwz_office where id = #{id}
    </delete>

    <delete id="deleteEkpLccgwzOfficeByIds" parameterType="String">
        delete from ekp_lccgwz_office where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>