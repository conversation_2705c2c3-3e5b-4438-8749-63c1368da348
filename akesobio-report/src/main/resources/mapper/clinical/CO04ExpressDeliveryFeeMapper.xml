<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.CO04ExpressDeliveryFeeMapper">
    <resultMap type="CO04ExpressDeliveryFee" id="CO04ExpressDeliveryFeeResult">
        <result property="co04ID" column="co04ID"/>
        <result property="co04Number" column="co04Number"/>
        <result property="co04Name" column="co04Name"/>
        <result property="co04CustomerName" column="co04CustomerName"/>
        <result property="co04ServiceDescription" column="co04ServiceDescription"/>
        <result property="co04EstimatedAmount" column="co04EstimatedAmount"/>
    </resultMap>
    <select id="selectCO04" parameterType="CO04ExpressDeliveryFee"
            resultMap="CO04ExpressDeliveryFeeResult">
        SELECT
        co04ID,
        co04Number,
        co04Name,
        co04CustomerName,
        co04ServiceDescription,
        co04EstimatedAmount
        FROM
        (
        SELECT
        a.fd_id 'co04ID',
        a.fd_danHao 'co04Number',
        'CO_04快递费' 'co04Name',
        b.fd_keHuXingMing 'co04CustomerName',
        b.fd_feiYongMiaoShu 'co04ServiceDescription',
        a.fd_shenQingJinEHeJi 'co04EstimatedAmount'
        FROM
        ekp_kdf a
        LEFT JOIN ekp_kdf_add b ON b.fd_parent_id= a.fd_id
        ) b
        <where>
            <if test="co04Number != null  and co04Number != ''">and co04Number = #{co04Number}</if>
        </where>
    </select>
</mapper>