<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.HrOrgElementMapper">
    <resultMap type="HrOrgElement" id="HrOrgElementResult">
        <result property="fdId" column="fdId"/>
        <result property="name" column="name"/>
        <result property="jobNumber" column="jobNumber"/>
    </resultMap>
    <select id="selectHrOrgElement" parameterType="HrOrgElement"
            resultMap="HrOrgElementResult">
        SELECT
        fdId,
        name,
        jobNumber
        FROM
        ( SELECT a.fd_id 'fdId', a.fd_name 'name', a.fd_no 'jobNumber' FROM hr_org_element a ) b
        <where>
            <if test="fdId != null  and fdId != ''">and fdId = #{fdId}</if>
        </where>
    </select>
</mapper>