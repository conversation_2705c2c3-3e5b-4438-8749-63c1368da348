<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.FD20MeetingApplicationMapper">
    <resultMap type="FD20MeetingApplication" id="FD20MeetingApplicationResult">
        <result property="fd20ID" column="fd20ID"/>
        <result property="fd20Number" column="fd20Number"/>
        <result property="fd20Name" column="fd20Name"/>
        <result property="fd20Amount" column="fd20Amount"/>
        <result property="fd20ServiceDescription" column="fd20ServiceDescription"/>
    </resultMap>
    <select id="selectFd20" parameterType="FD20MeetingApplication"
            resultMap="FD20MeetingApplicationResult">
        SELECT
        fd20ID,
        fd20Number,
        fd20Name,
        fd20Amount,
        fd20ServiceDescription
        FROM
        (
        SELECT
        a.fd_id 'fd20ID',
        a.fd_3bda815fccc716 'fd20Number',
        'FD20会议费用申请' 'fd20Name',
        a.fd_3bda7fc378f6f6 'fd20Amount',
        a.fd_3bda7fd87b008c 'fd20ServiceDescription'
        FROM
        ekp_cw_hyfybx a
        WHERE
        a.fd_3bda815fccc716 IS NOT NULL
        ) b
        <where>
            <if test="fd20Number != null  and fd20Number != ''">and fd20Number = #{fd20Number}</if>
        </where>
    </select>
</mapper>