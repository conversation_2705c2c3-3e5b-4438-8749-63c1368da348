<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.Fd72ClinicalExpenseReimbursementMapper">
    <resultMap type="Fd72ClinicalExpenseReimbursement" id="Fd72ClinicalExpenseReimbursementResult">
        <result property="id" column="id"/>
        <result property="oaReimbursementNumber" column="oaReimbursementNumber"/>
        <result property="dateCreated" column="dateCreated"/>
        <result property="reimbursementCategory" column="reimbursementCategory"/>
        <result property="companyCode" column="companyCode"/>
        <result property="companyName" column="companyName"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="reimbursementPersonNumber" column="reimbursementPersonNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="applicantNumber" column="applicantNumber"/>
        <result property="applicantPosition" column="applicantPosition"/>
        <result property="department" column="department"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="relatedApprovalForm" column="relatedApprovalForm"/>
        <result property="businessPeriod" column="businessPeriod"/>
        <result property="businessType" column="businessType"/>
        <result property="typeSegmentation" column="typeSegmentation"/>
        <result property="ceoBusiness" column="ceoBusiness"/>
        <result property="loanBalance" column="loanBalance"/>
        <result property="isWhetherLoan" column="isWhetherLoan"/>
        <result property="totalAmount" column="totalAmount"/>
        <result property="relatedApprovalAmount" column="relatedApprovalAmount"/>
        <result property="differenceAmount" column="differenceAmount"/>
        <result property="serviceDescription" column="serviceDescription"/>
        <result property="invoiceNumber" column="invoiceNumber"/>
        <result property="excludingTaxAmount" column="excludingTaxAmount"/>
        <result property="specialInvoiceTaxAmount" column="specialInvoiceTaxAmount"/>
        <result property="reimbursementAmount" column="reimbursementAmount"/>
        <result property="currency" column="currency"/>
        <result property="projectNo" column="projectNo"/>
        <result property="centerName" column="centerName"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="voucherPushDate" column="voucherPushDate"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="fd06ID" column="fd06ID"/>
        <result property="fd06Number" column="fd06Number"/>
        <result property="fd06Name" column="fd06Name"/>
        <result property="fd06HospitalityType" column="fd06HospitalityType"/>
        <result property="fd06ServiceDescription" column="fd06ServiceDescription"/>
        <result property="fd06EstimatedAmount" column="fd06EstimatedAmount"/>
        <result property="co04ID" column="co04ID"/>
        <result property="co04Number" column="co04Number"/>
        <result property="co04Name" column="co04Name"/>
        <result property="co04CustomerName" column="co04CustomerName"/>
        <result property="co04ServiceDescription" column="co04ServiceDescription"/>
        <result property="co04EstimatedAmount" column="co04EstimatedAmount"/>
        <result property="fd09ID" column="fd09ID"/>
        <result property="fd09Number" column="fd09Number"/>
        <result property="fd09Name" column="fd09Name"/>
        <result property="fd09ExpenseType" column="fd09ExpenseType"/>
        <result property="fd09CustomerName" column="fd09CustomerName"/>
        <result property="fd09SubjectID" column="fd09SubjectID"/>
        <result property="fd09VisitNumber" column="fd09VisitNumber"/>
        <result property="fd09AdvancePayment" column="fd09AdvancePayment"/>
        <result property="fd09QuanrongPayment" column="fd09QuanrongPayment"/>
        <result property="fd09ProtocolPayment" column="fd09ProtocolPayment"/>
        <result property="fd09CostDescription" column="fd09CostDescription"/>
        <result property="fd09ApplicationsAmount" column="fd09ApplicationsAmount"/>
        <result property="applicationFormId" column="applicationFormId"/>
        <result property="applicationFormNumber" column="applicationFormNumber"/>
        <result property="applicationFormName" column="applicationFormName"/>
        <result property="applicationFormType" column="applicationFormType"/>
        <result property="applicationFormDescribe" column="applicationFormDescribe"/>
        <result property="applicationFormAmount" column="applicationFormAmount"/>
        <result property="customerName" column="customerName"/>
        <result property="subjectId" column="subjectId"/>
        <result property="visitNumber" column="visitNumber"/>
        <result property="isAdvancePayment" column="isAdvancePayment"/>
        <result property="isQuanrongPayment" column="isQuanrongPayment"/>
        <result property="isProtocolPayment" column="isProtocolPayment"/>
        <result property="executionDate" column="executionDate"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="fdId" column="fdId"/>
    </resultMap>

    <sql id="selectFd72ClinicalExpenseReimbursementVo">
        select id,
               oaReimbursementNumber,
               dateCreated,
               reimbursementCategory,
               companyCode,
               companyName,
               reimbursementPerson,
               reimbursementPersonNumber,
               applicant,
               applicantNumber,
               applicantPosition,
               department,
               costCenterCode,
               costCenterName,
               relatedApprovalForm,
               businessPeriod,
               businessType,
               typeSegmentation,
               ceoBusiness,
               loanBalance,
               isWhetherLoan,
               totalAmount,
               relatedApprovalAmount,
               differenceAmount,
               serviceDescription,
               invoiceNumber,
               excludingTaxAmount,
               specialInvoiceTaxAmount,
               reimbursementAmount,
               currency,
               projectNo,
               projectNos,
               centerName,
               documentStatus,
               currentSession,
               sapPayNumber,
               voucherPushDate,
               applicationSubject,
               fd06ID,
               fd06Number,
               fd06Name,
               fd06HospitalityType,
               fd06ServiceDescription,
               fd06EstimatedAmount,
               co04ID,
               co04Number,
               co04Name,
               co04CustomerName,
               co04ServiceDescription,
               co04EstimatedAmount,
               fd09ID,
               fd09Number,
               fd09Name,
               fd09ExpenseType,
               fd09CustomerName,
               fd09SubjectID,
               fd09VisitNumber,
               fd09AdvancePayment,
               fd09QuanrongPayment,
               fd09ProtocolPayment,
               fd09CostDescription,
               fd09ApplicationsAmount,
               applicationFormId,
               applicationFormNumber,
               applicationFormName,
               applicationFormType,
               applicationFormDescribe,
               applicationFormAmount,
               customerName,
               subjectId,
               visitNumber,
               isAdvancePayment,
               isQuanrongPayment,
               isProtocolPayment,
               executionDate,
               deleteStatus,
               fdId
        from fd72_clinical_expense_reimbursement
    </sql>

    <select id="selectFd72ClinicalExpenseReimbursementList" parameterType="Fd72ClinicalExpenseReimbursement"
            resultMap="Fd72ClinicalExpenseReimbursementResult">
        <include refid="selectFd72ClinicalExpenseReimbursementVo"/>
        <where>
            <if test="oaReimbursementNumber != null  and oaReimbursementNumber != ''">and oaReimbursementNumber like
                concat('%', #{oaReimbursementNumber}, '%')
            </if>
            <if test="params.beginDateCreated != null and params.beginDateCreated != '' and params.endDateCreated != null and params.endDateCreated != ''">
                and dateCreated between #{params.beginDateCreated} and #{params.endDateCreated}
            </if>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''">and reimbursementCategory like
                concat('%', #{reimbursementCategory}, '%')
            </if>
            <if test="companyCode != null  and companyCode != ''">and companyCode like concat('%', #{companyCode},
                '%')
            </if>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''">and reimbursementPerson like
                concat('%', #{reimbursementPerson}, '%')
            </if>
            <if test="reimbursementPersonNumber != null  and reimbursementPersonNumber != ''">and
                reimbursementPersonNumber = #{reimbursementPersonNumber}
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="applicantNumber != null  and applicantNumber != ''">and applicantNumber = #{applicantNumber}</if>
            <if test="applicantPosition != null  and applicantPosition != ''">and applicantPosition =
                #{applicantPosition}
            </if>
            <if test="department != null  and department != ''">and department = #{department}</if>
            <if test="costCenterCode != null  and costCenterCode != ''">and costCenterCode = #{costCenterCode}</if>
            <if test="costCenterName != null  and costCenterName != ''">and costCenterName like concat('%',
                #{costCenterName}, '%')
            </if>
            <if test="relatedApprovalForm != null  and relatedApprovalForm != ''">and relatedApprovalForm =
                #{relatedApprovalForm}
            </if>
            <if test="businessPeriod != null  and businessPeriod != ''">and businessPeriod = #{businessPeriod}</if>
            <if test="businessType != null  and businessType != ''">and businessType = #{businessType}</if>
            <if test="typeSegmentation != null  and typeSegmentation != ''">and typeSegmentation = #{typeSegmentation}
            </if>
            <if test="ceoBusiness != null  and ceoBusiness != ''">and ceoBusiness = #{ceoBusiness}</if>
            <if test="loanBalance != null  and loanBalance != ''">and loanBalance = #{loanBalance}</if>
            <if test="isWhetherLoan != null  and isWhetherLoan != ''">and isWhetherLoan = #{isWhetherLoan}</if>
            <if test="totalAmount != null  and totalAmount != ''">and totalAmount = #{totalAmount}</if>
            <if test="relatedApprovalAmount != null  and relatedApprovalAmount != ''">and relatedApprovalAmount =
                #{relatedApprovalAmount}
            </if>
            <if test="differenceAmount != null  and differenceAmount != ''">and differenceAmount = #{differenceAmount}
            </if>
            <if test="serviceDescription != null  and serviceDescription != ''">and serviceDescription =
                #{serviceDescription}
            </if>
            <if test="invoiceNumber != null  and invoiceNumber != ''">and invoiceNumber = #{invoiceNumber}</if>
            <if test="excludingTaxAmount != null  and excludingTaxAmount != ''">and excludingTaxAmount =
                #{excludingTaxAmount}
            </if>
            <if test="specialInvoiceTaxAmount != null  and specialInvoiceTaxAmount != ''">and specialInvoiceTaxAmount =
                #{specialInvoiceTaxAmount}
            </if>
            <if test="reimbursementAmount != null  and reimbursementAmount != ''">and reimbursementAmount =
                #{reimbursementAmount}
            </if>
            <if test="currency != null  and currency != ''">and currency = #{currency}</if>
            <if test="projectNo != null  and projectNo != ''">and projectNo like concat('%', #{projectNo}, '%')</if>
            <if test="projectNos != null  and projectNos != ''">and projectNos like concat('%', #{projectNos}, '%')</if>
            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="sapPayNumber != null  and sapPayNumber != ''">and sapPayNumber = #{sapPayNumber}</if>
            <if test="voucherPushDate != null  and voucherPushDate != ''">and voucherPushDate = #{voucherPushDate}</if>
            <if test="applicationSubject != null  and applicationSubject != ''">and applicationSubject =
                #{applicationSubject}
            </if>
            <if test="fd06ID != null  and fd06ID != ''">and fd06ID = #{fd06ID}</if>
            <if test="fd06Number != null  and fd06Number != ''">and fd06Number = #{fd06Number}</if>
            <if test="fd06Name != null  and fd06Name != ''">and fd06Name like concat('%', #{fd06Name}, '%')</if>
            <if test="fd06HospitalityType != null  and fd06HospitalityType != ''">and fd06HospitalityType =
                #{fd06HospitalityType}
            </if>
            <if test="fd06ServiceDescription != null  and fd06ServiceDescription != ''">and fd06ServiceDescription =
                #{fd06ServiceDescription}
            </if>
            <if test="fd06EstimatedAmount != null  and fd06EstimatedAmount != ''">and fd06EstimatedAmount =
                #{fd06EstimatedAmount}
            </if>
            <if test="co04ID != null  and co04ID != ''">and co04ID = #{co04ID}</if>
            <if test="co04Number != null  and co04Number != ''">and co04Number = #{co04Number}</if>
            <if test="co04Name != null  and co04Name != ''">and co04Name like concat('%', #{co04Name}, '%')</if>
            <if test="co04CustomerName != null  and co04CustomerName != ''">and co04CustomerName like concat('%',
                #{co04CustomerName}, '%')
            </if>
            <if test="co04ServiceDescription != null  and co04ServiceDescription != ''">and co04ServiceDescription =
                #{co04ServiceDescription}
            </if>
            <if test="co04EstimatedAmount != null  and co04EstimatedAmount != ''">and co04EstimatedAmount =
                #{co04EstimatedAmount}
            </if>
            <if test="fd09ID != null  and fd09ID != ''">and fd09ID = #{fd09ID}</if>
            <if test="fd09Number != null  and fd09Number != ''">and fd09Number = #{fd09Number}</if>
            <if test="fd09Name != null  and fd09Name != ''">and fd09Name like concat('%', #{fd09Name}, '%')</if>
            <if test="fd09ExpenseType != null  and fd09ExpenseType != ''">and fd09ExpenseType = #{fd09ExpenseType}</if>
            <if test="fd09CustomerName != null  and fd09CustomerName != ''">and fd09CustomerName like concat('%',
                #{fd09CustomerName}, '%')
            </if>
            <if test="fd09SubjectID != null  and fd09SubjectID != ''">and fd09SubjectID = #{fd09SubjectID}</if>
            <if test="fd09VisitNumber != null  and fd09VisitNumber != ''">and fd09VisitNumber = #{fd09VisitNumber}</if>
            <if test="fd09AdvancePayment != null  and fd09AdvancePayment != ''">and fd09AdvancePayment =
                #{fd09AdvancePayment}
            </if>
            <if test="fd09QuanrongPayment != null  and fd09QuanrongPayment != ''">and fd09QuanrongPayment =
                #{fd09QuanrongPayment}
            </if>
            <if test="fd09ProtocolPayment != null  and fd09ProtocolPayment != ''">and fd09ProtocolPayment =
                #{fd09ProtocolPayment}
            </if>
            <if test="fd09CostDescription != null  and fd09CostDescription != ''">and fd09CostDescription =
                #{fd09CostDescription}
            </if>
            <if test="fd09ApplicationsAmount != null  and fd09ApplicationsAmount != ''">and fd09ApplicationsAmount =
                #{fd09ApplicationsAmount}
            </if>
            <if test="applicationFormNumber != null  and applicationFormNumber != ''">and applicationFormNumber
                like concat('%', #{applicationFormNumber}, '%')
            </if>
            <if test="applicationFormName != null  and applicationFormName != ''">and applicationFormName
                like concat('%', #{applicationFormName}, '%')
            </if>
            <if test="executionDate != null ">and executionDate = #{executionDate}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="permissionProjectNo != null and permissionProjectNo.size() > 0">
                and projectNos in (
                <foreach collection="permissionProjectNo" item="e" separator=",">
                    #{e.projectNos}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="selectFd72ClinicalExpenseReimbursementById" parameterType="Integer"
            resultMap="Fd72ClinicalExpenseReimbursementResult">
        <include refid="selectFd72ClinicalExpenseReimbursementVo"/>
        where id = #{id}
    </select>

    <insert id="insertFd72ClinicalExpenseReimbursement" parameterType="Fd72ClinicalExpenseReimbursement">
        insert into fd72_clinical_expense_reimbursement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="oaReimbursementNumber != null">oaReimbursementNumber,</if>
            <if test="dateCreated != null">dateCreated,</if>
            <if test="reimbursementCategory != null">reimbursementCategory,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="companyName != null">companyName,</if>
            <if test="reimbursementPerson != null">reimbursementPerson,</if>
            <if test="reimbursementPersonNumber != null">reimbursementPersonNumber,</if>
            <if test="applicant != null">applicant,</if>
            <if test="applicantNumber != null">applicantNumber,</if>
            <if test="applicantPosition != null">applicantPosition,</if>
            <if test="department != null">department,</if>
            <if test="costCenterCode != null">costCenterCode,</if>
            <if test="costCenterName != null">costCenterName,</if>
            <if test="relatedApprovalForm != null">relatedApprovalForm,</if>
            <if test="businessPeriod != null">businessPeriod,</if>
            <if test="businessType != null">businessType,</if>
            <if test="typeSegmentation != null">typeSegmentation,</if>
            <if test="ceoBusiness != null">ceoBusiness,</if>
            <if test="loanBalance != null">loanBalance,</if>
            <if test="isWhetherLoan != null">isWhetherLoan,</if>
            <if test="totalAmount != null">totalAmount,</if>
            <if test="relatedApprovalAmount != null">relatedApprovalAmount,</if>
            <if test="differenceAmount != null">differenceAmount,</if>
            <if test="serviceDescription != null">serviceDescription,</if>
            <if test="invoiceNumber != null">invoiceNumber,</if>
            <if test="excludingTaxAmount != null">excludingTaxAmount,</if>
            <if test="specialInvoiceTaxAmount != null">specialInvoiceTaxAmount,</if>
            <if test="reimbursementAmount != null">reimbursementAmount,</if>
            <if test="currency != null">currency,</if>
            <if test="projectNo != null">projectNo,</if>
            <if test="projectNos != null">projectNos,</if>
            <if test="centerName != null">centerName,</if>
            <if test="documentStatus != null">documentStatus,</if>
            <if test="currentSession != null">currentSession,</if>
            <if test="sapPayNumber != null">sapPayNumber,</if>
            <if test="voucherPushDate != null">voucherPushDate,</if>
            <if test="applicationSubject != null">applicationSubject,</if>
            <if test="fd06ID != null">fd06ID,</if>
            <if test="fd06Number != null">fd06Number,</if>
            <if test="fd06Name != null">fd06Name,</if>
            <if test="fd06HospitalityType != null">fd06HospitalityType,</if>
            <if test="fd06ServiceDescription != null">fd06ServiceDescription,</if>
            <if test="fd06EstimatedAmount != null">fd06EstimatedAmount,</if>
            <if test="co04ID != null">co04ID,</if>
            <if test="co04Number != null">co04Number,</if>
            <if test="co04Name != null">co04Name,</if>
            <if test="co04CustomerName != null">co04CustomerName,</if>
            <if test="co04ServiceDescription != null">co04ServiceDescription,</if>
            <if test="co04EstimatedAmount != null">co04EstimatedAmount,</if>
            <if test="fd09ID != null">fd09ID,</if>
            <if test="fd09Number != null">fd09Number,</if>
            <if test="fd09Name != null">fd09Name,</if>
            <if test="fd09ExpenseType != null">fd09ExpenseType,</if>
            <if test="fd09CustomerName != null">fd09CustomerName,</if>
            <if test="fd09SubjectID != null">fd09SubjectID,</if>
            <if test="fd09VisitNumber != null">fd09VisitNumber,</if>
            <if test="fd09AdvancePayment != null">fd09AdvancePayment,</if>
            <if test="fd09QuanrongPayment != null">fd09QuanrongPayment,</if>
            <if test="fd09ProtocolPayment != null">fd09ProtocolPayment,</if>
            <if test="fd09CostDescription != null">fd09CostDescription,</if>
            <if test="fd09ApplicationsAmount != null">fd09ApplicationsAmount,</if>
            <if test="applicationFormId != null">applicationFormId,</if>
            <if test="applicationFormNumber != null">applicationFormNumber,</if>
            <if test="applicationFormName != null">applicationFormName,</if>
            <if test="applicationFormType != null">applicationFormType,</if>
            <if test="applicationFormDescribe != null">applicationFormDescribe,</if>
            <if test="applicationFormAmount != null">applicationFormAmount,</if>
            <if test="customerName != null">customerName,</if>
            <if test="subjectId != null">subjectId,</if>
            <if test="visitNumber != null">visitNumber,</if>
            <if test="isAdvancePayment != null">isAdvancePayment,</if>
            <if test="isQuanrongPayment != null">isQuanrongPayment,</if>
            <if test="isProtocolPayment != null">isProtocolPayment,</if>
            <if test="executionDate != null">executionDate,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="fdId != null">fdId,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="oaReimbursementNumber != null">#{oaReimbursementNumber},</if>
            <if test="dateCreated != null">#{dateCreated},</if>
            <if test="reimbursementCategory != null">#{reimbursementCategory},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="reimbursementPerson != null">#{reimbursementPerson},</if>
            <if test="reimbursementPersonNumber != null">#{reimbursementPersonNumber},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="applicantNumber != null">#{applicantNumber},</if>
            <if test="applicantPosition != null">#{applicantPosition},</if>
            <if test="department != null">#{department},</if>
            <if test="costCenterCode != null">#{costCenterCode},</if>
            <if test="costCenterName != null">#{costCenterName},</if>
            <if test="relatedApprovalForm != null">#{relatedApprovalForm},</if>
            <if test="businessPeriod != null">#{businessPeriod},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="typeSegmentation != null">#{typeSegmentation},</if>
            <if test="ceoBusiness != null">#{ceoBusiness},</if>
            <if test="loanBalance != null">#{loanBalance},</if>
            <if test="isWhetherLoan != null">#{isWhetherLoan},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="relatedApprovalAmount != null">#{relatedApprovalAmount},</if>
            <if test="differenceAmount != null">#{differenceAmount},</if>
            <if test="serviceDescription != null">#{serviceDescription},</if>
            <if test="invoiceNumber != null">#{invoiceNumber},</if>
            <if test="excludingTaxAmount != null">#{excludingTaxAmount},</if>
            <if test="specialInvoiceTaxAmount != null">#{specialInvoiceTaxAmount},</if>
            <if test="reimbursementAmount != null">#{reimbursementAmount},</if>
            <if test="currency != null">#{currency},</if>
            <if test="projectNo != null">#{projectNo},</if>
            <if test="projectNos != null">#{projectNos},</if>
            <if test="centerName != null">#{centerName},</if>
            <if test="documentStatus != null">#{documentStatus},</if>
            <if test="currentSession != null">#{currentSession},</if>
            <if test="sapPayNumber != null">#{sapPayNumber},</if>
            <if test="voucherPushDate != null">#{voucherPushDate},</if>
            <if test="applicationSubject != null">#{applicationSubject},</if>
            <if test="fd06ID != null">#{fd06ID},</if>
            <if test="fd06Number != null">#{fd06Number},</if>
            <if test="fd06Name != null">#{fd06Name},</if>
            <if test="fd06HospitalityType != null">#{fd06HospitalityType},</if>
            <if test="fd06ServiceDescription != null">#{fd06ServiceDescription},</if>
            <if test="fd06EstimatedAmount != null">#{fd06EstimatedAmount},</if>
            <if test="co04ID != null">#{co04ID},</if>
            <if test="co04Number != null">#{co04Number},</if>
            <if test="co04Name != null">#{co04Name},</if>
            <if test="co04CustomerName != null">#{co04CustomerName},</if>
            <if test="co04ServiceDescription != null">#{co04ServiceDescription},</if>
            <if test="co04EstimatedAmount != null">#{co04EstimatedAmount},</if>
            <if test="fd09ID != null">#{fd09ID},</if>
            <if test="fd09Number != null">#{fd09Number},</if>
            <if test="fd09Name != null">#{fd09Name},</if>
            <if test="fd09ExpenseType != null">#{fd09ExpenseType},</if>
            <if test="fd09CustomerName != null">#{fd09CustomerName},</if>
            <if test="fd09SubjectID != null">#{fd09SubjectID},</if>
            <if test="fd09VisitNumber != null">#{fd09VisitNumber},</if>
            <if test="fd09AdvancePayment != null">#{fd09AdvancePayment},</if>
            <if test="fd09QuanrongPayment != null">#{fd09QuanrongPayment},</if>
            <if test="fd09ProtocolPayment != null">#{fd09ProtocolPayment},</if>
            <if test="fd09CostDescription != null">#{fd09CostDescription},</if>
            <if test="fd09ApplicationsAmount != null">#{fd09ApplicationsAmount},</if>
            <if test="applicationFormId != null">#{applicationFormId},</if>
            <if test="applicationFormNumber != null">#{applicationFormNumber},</if>
            <if test="applicationFormName != null">#{applicationFormName},</if>
            <if test="applicationFormType != null">#{applicationFormType},</if>
            <if test="applicationFormDescribe != null">#{applicationFormDescribe},</if>
            <if test="applicationFormAmount != null">#{applicationFormAmount},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="subjectId != null">#{subjectId},</if>
            <if test="visitNumber != null">#{visitNumber},</if>
            <if test="isAdvancePayment != null">#{isAdvancePayment},</if>
            <if test="isQuanrongPayment != null">#{isQuanrongPayment},</if>
            <if test="isProtocolPayment != null">#{isProtocolPayment},</if>
            <if test="executionDate != null">#{executionDate},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="fdId != null">#{fdId},</if>
        </trim>
    </insert>

    <update id="updateFd72ClinicalExpenseReimbursement" parameterType="Fd72ClinicalExpenseReimbursement">
        update fd72_clinical_expense_reimbursement
        <trim prefix="SET" suffixOverrides=",">
            <if test="oaReimbursementNumber != null">oaReimbursementNumber = #{oaReimbursementNumber},</if>
            <if test="dateCreated != null">dateCreated = #{dateCreated},</if>
            <if test="reimbursementCategory != null">reimbursementCategory = #{reimbursementCategory},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="reimbursementPerson != null">reimbursementPerson = #{reimbursementPerson},</if>
            <if test="reimbursementPersonNumber != null">reimbursementPersonNumber = #{reimbursementPersonNumber},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="applicantNumber != null">applicantNumber = #{applicantNumber},</if>
            <if test="applicantPosition != null">applicantPosition = #{applicantPosition},</if>
            <if test="department != null">department = #{department},</if>
            <if test="costCenterCode != null">costCenterCode = #{costCenterCode},</if>
            <if test="costCenterName != null">costCenterName = #{costCenterName},</if>
            <if test="relatedApprovalForm != null">relatedApprovalForm = #{relatedApprovalForm},</if>
            <if test="businessPeriod != null">businessPeriod = #{businessPeriod},</if>
            <if test="businessType != null">businessType = #{businessType},</if>
            <if test="typeSegmentation != null">typeSegmentation = #{typeSegmentation},</if>
            <if test="ceoBusiness != null">ceoBusiness = #{ceoBusiness},</if>
            <if test="loanBalance != null">loanBalance = #{loanBalance},</if>
            <if test="isWhetherLoan != null">isWhetherLoan = #{isWhetherLoan},</if>
            <if test="totalAmount != null">totalAmount = #{totalAmount},</if>
            <if test="relatedApprovalAmount != null">relatedApprovalAmount = #{relatedApprovalAmount},</if>
            <if test="differenceAmount != null">differenceAmount = #{differenceAmount},</if>
            <if test="serviceDescription != null">serviceDescription = #{serviceDescription},</if>
            <if test="invoiceNumber != null">invoiceNumber = #{invoiceNumber},</if>
            <if test="excludingTaxAmount != null">excludingTaxAmount = #{excludingTaxAmount},</if>
            <if test="specialInvoiceTaxAmount != null">specialInvoiceTaxAmount = #{specialInvoiceTaxAmount},</if>
            <if test="reimbursementAmount != null">reimbursementAmount = #{reimbursementAmount},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="projectNo != null">projectNo = #{projectNo},</if>
            <if test="projectNos != null">projectNos = #{projectNos},</if>
            <if test="centerName != null">centerName = #{centerName},</if>
            <if test="documentStatus != null">documentStatus = #{documentStatus},</if>
            <if test="currentSession != null">currentSession = #{currentSession},</if>
            <if test="sapPayNumber != null">sapPayNumber = #{sapPayNumber},</if>
            <if test="voucherPushDate != null">voucherPushDate = #{voucherPushDate},</if>
            <if test="applicationSubject != null">applicationSubject = #{applicationSubject},</if>
            <if test="fd06ID != null">fd06ID = #{fd06ID},</if>
            <if test="fd06Number != null">fd06Number = #{fd06Number},</if>
            <if test="fd06Name != null">fd06Name = #{fd06Name},</if>
            <if test="fd06HospitalityType != null">fd06HospitalityType = #{fd06HospitalityType},</if>
            <if test="fd06ServiceDescription != null">fd06ServiceDescription = #{fd06ServiceDescription},</if>
            <if test="fd06EstimatedAmount != null">fd06EstimatedAmount = #{fd06EstimatedAmount},</if>
            <if test="co04ID != null">co04ID = #{co04ID},</if>
            <if test="co04Number != null">co04Number = #{co04Number},</if>
            <if test="co04Name != null">co04Name = #{co04Name},</if>
            <if test="co04CustomerName != null">co04CustomerName = #{co04CustomerName},</if>
            <if test="co04ServiceDescription != null">co04ServiceDescription = #{co04ServiceDescription},</if>
            <if test="co04EstimatedAmount != null">co04EstimatedAmount = #{co04EstimatedAmount},</if>
            <if test="fd09ID != null">fd09ID = #{fd09ID},</if>
            <if test="fd09Number != null">fd09Number = #{fd09Number},</if>
            <if test="fd09Name != null">fd09Name = #{fd09Name},</if>
            <if test="fd09ExpenseType != null">fd09ExpenseType = #{fd09ExpenseType},</if>
            <if test="fd09CustomerName != null">fd09CustomerName = #{fd09CustomerName},</if>
            <if test="fd09SubjectID != null">fd09SubjectID = #{fd09SubjectID},</if>
            <if test="fd09VisitNumber != null">fd09VisitNumber = #{fd09VisitNumber},</if>
            <if test="fd09AdvancePayment != null">fd09AdvancePayment = #{fd09AdvancePayment},</if>
            <if test="fd09QuanrongPayment != null">fd09QuanrongPayment = #{fd09QuanrongPayment},</if>
            <if test="fd09ProtocolPayment != null">fd09ProtocolPayment = #{fd09ProtocolPayment},</if>
            <if test="fd09CostDescription != null">fd09CostDescription = #{fd09CostDescription},</if>
            <if test="fd09ApplicationsAmount != null">fd09ApplicationsAmount = #{fd09ApplicationsAmount},</if>
            <if test="applicationFormId != null">applicationFormId = #{applicationFormId},</if>
            <if test="applicationFormNumber != null">applicationFormNumber = #{applicationFormNumber},</if>
            <if test="applicationFormName != null">applicationFormName = #{applicationFormName},</if>
            <if test="applicationFormType != null">applicationFormType = #{applicationFormType},</if>
            <if test="applicationFormDescribe != null">applicationFormDescribe = #{applicationFormDescribe},</if>
            <if test="applicationFormAmount != null">applicationFormAmount = #{applicationFormAmount},</if>
            <if test="customerName != null">customerName = #{customerName},</if>
            <if test="subjectId != null">subjectId = #{subjectId},</if>
            <if test="visitNumber != null">visitNumber = #{visitNumber},</if>
            <if test="isAdvancePayment != null">isAdvancePayment = #{isAdvancePayment},</if>
            <if test="isQuanrongPayment != null">isQuanrongPayment = #{isQuanrongPayment},</if>
            <if test="isProtocolPayment != null">isProtocolPayment = #{isProtocolPayment},</if>
            <if test="executionDate != null">executionDate = #{executionDate},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFd72ClinicalExpenseReimbursementById" parameterType="Integer">
        delete
        from fd72_clinical_expense_reimbursement
        where id = #{id}
    </delete>

    <delete id="deleteFd72ClinicalExpenseReimbursementByIds" parameterType="String">
        delete from fd72_clinical_expense_reimbursement where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAllFd72ClinicalExpenseReimbursement" parameterType="Integer">
        delete
        from fd72_clinical_expense_reimbursement
    </delete>
</mapper>