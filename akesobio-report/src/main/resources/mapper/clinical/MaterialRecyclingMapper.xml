<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.MaterialRecyclingMapper">
    
    <resultMap type="MaterialRecycling" id="MaterialRecyclingResult">
        <result property="requestNo"    column="requestNo"    />
        <result property="creationTime"    column="creationTime"    />
        <result property="processName"    column="processName"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="centerName"    column="centerName"    />
        <result property="applicant"    column="applicant"    />
        <result property="applicationReason"    column="applicationReason"    />
        <result property="recoveryReason"    column="recoveryReason"    />
        <result property="newMaterialName"    column="newMaterialName"    />
        <result property="brand"    column="brand"    />
        <result property="model"    column="model"    />
        <result property="batchNumber"    column="batchNumber"    />
        <result property="unit"    column="unit"    />
        <result property="number"    column="number"    />
        <result property="recoveryMode"    column="recoveryMode"    />
        <result property="expectedProcessingTime"    column="expectedProcessingTime"    />
        <result property="endTime"    column="endTime"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="currentProcessor"    column="currentProcessor"    />
    </resultMap>

    <sql id="selectMaterialRecyclingVo">
        select id, requestNo, creationTime, processName, projectNumber, centerName, applicant, applicationReason, recoveryReason, newMaterialName, brand, model, batchNumber, unit, number,recoveryMode, expectedProcessingTime, endTime, documentStatus, currentSession, currentProcessor from material_recycling
    </sql>

    <select id="selectMaterialRecyclingList" parameterType="MaterialRecycling" resultMap="MaterialRecyclingResult">
        <include refid="selectMaterialRecyclingVo"/>
        <where>  
            <if test="requestNo != null  and requestNo != ''"> and requestNo like concat('%', #{requestNo}, '%')</if>
            <if test="params.beginCreationTime != null and params.beginCreationTime != '' and params.endCreationTime != null and params.endCreationTime != ''"> and creationTime between #{params.beginCreationTime} and #{params.endCreationTime}</if>
            <if test="processName != null  and processName != ''"> and processName like concat('%', #{processName}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="centerName != null  and centerName != ''"> and centerName like concat('%', #{centerName}, '%')</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="applicationReason != null  and applicationReason != ''"> and applicationReason like concat('%', #{applicationReason}, '%')</if>
            <if test="recoveryReason != null  and recoveryReason != ''"> and recoveryReason = #{recoveryReason}</if>
            <if test="newMaterialName != null  and newMaterialName != ''"> and newMaterialName like concat('%', #{newMaterialName}, '%')</if>
            <if test="brand != null  and brand != ''"> and brand like concat('%', #{brand}, '%')</if>
            <if test="model != null  and model != ''"> and model like concat('%', #{model}, '%')</if>
            <if test="batchNumber != null  and batchNumber != ''"> and batchNumber like concat('%', #{batchNumber}, '%')</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="number != null  and number != ''"> and number = #{number}</if>
            <if test="recoveryMode != null  and recoveryMode != ''"> and recoveryMode = #{recoveryMode}</if>
            <if test="params.beginExpectedProcessingTime != null and params.beginExpectedProcessingTime != '' and params.endExpectedProcessingTime != null and params.endExpectedProcessingTime != ''"> and expectedProcessingTime between #{params.beginExpectedProcessingTime} and #{params.endExpectedProcessingTime}</if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''"> and endTime between #{params.beginEndTime} and #{params.endEndTime}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="currentProcessor != null  and currentProcessor != ''"> and currentProcessor like concat('%', #{currentProcessor}, '%')</if>
            and documentStatus !='废弃'
        </where>
        order by creationTime desc
    </select>
</mapper>