<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.SupervisionDepartmentMapper">
    
    <resultMap type="SupervisionDepartment" id="SupervisionDepartmentResult">
        <result property="singleNumber"    column="singleNumber"    />
        <result property="department"    column="department"    />
        <result property="creationTime"    column="creationTime"    />
        <result property="applicant"    column="applicant"    />
        <result property="workingDate"    column="workingDate"    />
        <result property="workingHours"    column="workingHours"    />
        <result property="workContent"    column="workContent"    />
        <result property="jobDetails"    column="jobDetails"    />
        <result property="remark"    column="remark"    />
        <result property="projectType"    column="projectType"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="centerName"    column="centerName"    />
        <result property="documentStatus"    column="documentStatus"    />
    </resultMap>

    <sql id="selectSupervisionDepartmentVo">
        select singleNumber, department, creationTime, applicant, workingDate, workingHours, workContent, jobDetails, remark, projectType, projectNumber, centerName, documentStatus from supervision_department
    </sql>

    <select id="selectSupervisionDepartmentList" parameterType="SupervisionDepartment" resultMap="SupervisionDepartmentResult">
        <include refid="selectSupervisionDepartmentVo"/>
        <where>  
            <if test="singleNumber != null  and singleNumber != ''"> and singleNumber like concat('%', #{singleNumber}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="params.beginCreationTime != null and params.beginCreationTime != '' and params.endCreationTime != null and params.endCreationTime != ''"> and creationTime between #{params.beginCreationTime} and #{params.endCreationTime}</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="params.beginWorkingDate != null and params.beginWorkingDate != '' and params.endWorkingDate != null and params.endWorkingDate != ''"> and workingDate between #{params.beginWorkingDate} and #{params.endWorkingDate}</if>
            <if test="workingHours != null  and workingHours != ''"> and workingHours = #{workingHours}</if>
            <if test="workContent != null  and workContent != ''"> and workContent = #{workContent}</if>
            <if test="jobDetails != null  and jobDetails != ''"> and jobDetails = #{jobDetails}</if>
            <if test="projectType != null  and projectType != ''"> and projectType = #{projectType}</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber = #{projectNumber}</if>
            <if test="centerName != null  and centerName != ''"> and centerName like concat('%', #{centerName}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus = #{documentStatus}</if>
        </where>
        order by creationTime desc
    </select>
</mapper>