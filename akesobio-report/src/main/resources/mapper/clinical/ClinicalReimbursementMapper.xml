<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalReimbursementMapper">

    <resultMap type="ClinicalReimbursement" id="ClinicalReimbursementResult">
        <result property="id" column="id"/>
        <result property="oaReimbursementNumber" column="oaReimbursementNumber"/>
        <result property="dateCreated" column="dateCreated"/>
        <result property="reimbursementCategory" column="reimbursementCategory"/>
        <result property="companyCode" column="companyCode"/>
        <result property="companyName" column="companyName"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="reimbursementPersonNumber" column="reimbursementPersonNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="applicantNumber" column="applicantNumber"/>
        <result property="applicantPosition" column="applicantPosition"/>
        <result property="department" column="department"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="relatedApprovalForm" column="relatedApprovalForm"/>
        <result property="businessPeriod" column="businessPeriod"/>
        <result property="travelDepartureDate" column="travelDepartureDate"/>
        <result property="travelReturnDate" column="travelReturnDate"/>
        <result property="businessDay" column="businessDay"/>
        <result property="colleagues" column="colleagues"/>
        <result property="businessType" column="businessType"/>
        <result property="typeSegmentation" column="typeSegmentation"/>
        <result property="ceoBusiness" column="ceoBusiness"/>
        <result property="meetingStartDate" column="meetingStartDate"/>
        <result property="meetingEndDate" column="meetingEndDate"/>
        <result property="meetingName" column="meetingName"/>
        <result property="meetingType" column="meetingType"/>
        <result property="meetingCategory" column="meetingCategory"/>
        <result property="meetingFormat" column="meetingFormat"/>
        <result property="companyRole" column="companyRole"/>
        <result property="objectOriented" column="objectOriented"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="peopleCovered" column="peopleCovered"/>
        <result property="signNumber" column="signNumber"/>
        <result property="expertsNumber" column="expertsNumber"/>
        <result property="meetingMaterials" column="meetingMaterials"/>
        <result property="invitation" column="invitation"/>
        <result property="schedule" column="schedule"/>
        <result property="attendanceSheet" column="attendanceSheet"/>
        <result property="photo" column="photo"/>
        <result property="paymentMethod" column="paymentMethod"/>
        <result property="loanBalance" column="loanBalance"/>
        <result property="isWhetherLoan" column="isWhetherLoan"/>
        <result property="totalAmount" column="totalAmount"/>
        <result property="relatedApprovalAmount" column="relatedApprovalAmount"/>
        <result property="differenceAmount" column="differenceAmount"/>
        <result property="differenceExplanation" column="differenceExplanation"/>
        <result property="expenseAccountCode" column="expenseAccountCode"/>
        <result property="expenseAccountName" column="expenseAccountName"/>
        <result property="serviceDescription" column="serviceDescription"/>
        <result property="invoiceNumber" column="invoiceNumber"/>
        <result property="excludingTaxAmount" column="excludingTaxAmount"/>
        <result property="specialInvoiceTaxAmount" column="specialInvoiceTaxAmount"/>
        <result property="reimbursementAmount" column="reimbursementAmount"/>
        <result property="currency" column="currency"/>
        <result property="projectNo" column="projectNo"/>
        <result property="centerName" column="centerName"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="voucherPushDate" column="voucherPushDate"/>
        <result property="expenseApplicationNumber" column="expenseApplicationNumber"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="applicationType" column="applicationType"/>
        <result property="secondaryType" column="secondaryType"/>
        <result property="applicationsAmount" column="applicationsAmount"/>
        <result property="detailedDescription" column="detailedDescription"/>
        <result property="executionDate" column="executionDate"/>
        <result property="deleteStatus" column="deleteStatus"/>
    </resultMap>

    <sql id="selectClinicalReimbursementVo">
        select id,
               oaReimbursementNumber,
               dateCreated,
               reimbursementCategory,
               companyCode,
               companyName,
               reimbursementPerson,
               reimbursementPersonNumber,
               applicant,
               applicantNumber,
               applicantPosition,
               department,
               costCenterCode,
               costCenterName,
               relatedApprovalForm,
               businessPeriod,
               travelDepartureDate,
               travelReturnDate,
               businessDay,
               colleagues,
               businessType,
               typeSegmentation,
               ceoBusiness,
               meetingStartDate,
               meetingEndDate,
               meetingName,
               meetingType,
               meetingCategory,
               meetingFormat,
               companyRole,
               objectOriented,
               province,
               city,
               peopleCovered,
               signNumber,
               expertsNumber,
               meetingMaterials,
               invitation,
               schedule,
               attendanceSheet,
               photo,
               paymentMethod,
               loanBalance,
               isWhetherLoan,
               totalAmount,
               relatedApprovalAmount,
               differenceAmount,
               differenceExplanation,
               expenseAccountCode,
               expenseAccountName,
               serviceDescription,
               invoiceNumber,
               excludingTaxAmount,
               specialInvoiceTaxAmount,
               reimbursementAmount,
               currency,
               projectNo,
               centerName,
               documentStatus,
               currentSession,
               sapPayNumber,
               voucherPushDate,
               expenseApplicationNumber,
               applicationSubject,
               applicationType,
               secondaryType,
               applicationsAmount,
               detailedDescription,
               executionDate,
               deleteStatus
        from clinical_reimbursement
    </sql>

    <select id="selectClinicalReimbursementList" parameterType="ClinicalReimbursement"
            resultMap="ClinicalReimbursementResult">
        <include refid="selectClinicalReimbursementVo"/>
        <where>
            <if test="oaReimbursementNumber != null  and oaReimbursementNumber != ''">and oaReimbursementNumber like
                concat('%', #{oaReimbursementNumber}, '%')
            </if>
            <if test="params.beginDateCreated != null and params.beginDateCreated != '' and params.endDateCreated != null and params.endDateCreated != ''">
                and dateCreated between #{params.beginDateCreated} and #{params.endDateCreated}
            </if>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''">and reimbursementCategory like
                concat('%', #{reimbursementCategory}, '%')
            </if>
            <if test="companyCode != null  and companyCode != ''">and companyCode like concat('%', #{companyCode},
                '%')
            </if>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''">and reimbursementPerson like
                concat('%', #{reimbursementPerson}, '%')
            </if>
            <if test="reimbursementPersonNumber != null  and reimbursementPersonNumber != ''">and
                reimbursementPersonNumber like concat('%', #{reimbursementPersonNumber}, '%')
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="applicantNumber != null  and applicantNumber != ''">and applicantNumber like concat('%',
                #{applicantNumber}, '%')
            </if>
            <if test="applicantPosition != null  and applicantPosition != ''">and applicantPosition like concat('%',
                #{applicantPosition}, '%')
            </if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="costCenterCode != null  and costCenterCode != ''">and costCenterCode like concat('%',
                #{costCenterCode}, '%')
            </if>
            <if test="costCenterName != null  and costCenterName != ''">and costCenterName like concat('%',
                #{costCenterName}, '%')
            </if>
            <if test="relatedApprovalForm != null  and relatedApprovalForm != ''">and relatedApprovalForm like
                concat('%', #{relatedApprovalForm}, '%')
            </if>
            <if test="businessPeriod != null  and businessPeriod != ''">and businessPeriod like concat('%',
                #{businessPeriod}, '%')
            </if>
            <if test="travelDepartureDate != null  and travelDepartureDate != ''">and travelDepartureDate =
                #{travelDepartureDate}
            </if>
            <if test="travelReturnDate != null  and travelReturnDate != ''">and travelReturnDate = #{travelReturnDate}
            </if>
            <if test="businessDay != null ">and businessDay = #{businessDay}</if>
            <if test="colleagues != null  and colleagues != ''">and colleagues = #{colleagues}</if>
            <if test="businessType != null  and businessType != ''">and businessType = #{businessType}</if>
            <if test="typeSegmentation != null  and typeSegmentation != ''">and typeSegmentation = #{typeSegmentation}
            </if>
            <if test="ceoBusiness != null  and ceoBusiness != ''">and ceoBusiness = #{ceoBusiness}</if>
            <if test="meetingStartDate != null  and meetingStartDate != ''">and meetingStartDate = #{meetingStartDate}
            </if>
            <if test="meetingEndDate != null  and meetingEndDate != ''">and meetingEndDate = #{meetingEndDate}</if>
            <if test="meetingName != null  and meetingName != ''">and meetingName like concat('%', #{meetingName},
                '%')
            </if>
            <if test="meetingType != null  and meetingType != ''">and meetingType = #{meetingType}</if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
            <if test="meetingFormat != null  and meetingFormat != ''">and meetingFormat = #{meetingFormat}</if>
            <if test="companyRole != null  and companyRole != ''">and companyRole = #{companyRole}</if>
            <if test="objectOriented != null  and objectOriented != ''">and objectOriented = #{objectOriented}</if>
            <if test="province != null  and province != ''">and province = #{province}</if>
            <if test="city != null  and city != ''">and city = #{city}</if>
            <if test="peopleCovered != null ">and peopleCovered = #{peopleCovered}</if>
            <if test="signNumber != null ">and signNumber = #{signNumber}</if>
            <if test="expertsNumber != null ">and expertsNumber = #{expertsNumber}</if>
            <if test="meetingMaterials != null  and meetingMaterials != ''">and meetingMaterials = #{meetingMaterials}
            </if>
            <if test="invitation != null  and invitation != ''">and invitation = #{invitation}</if>
            <if test="schedule != null  and schedule != ''">and schedule = #{schedule}</if>
            <if test="attendanceSheet != null  and attendanceSheet != ''">and attendanceSheet = #{attendanceSheet}</if>
            <if test="photo != null  and photo != ''">and photo = #{photo}</if>
            <if test="paymentMethod != null  and paymentMethod != ''">and paymentMethod = #{paymentMethod}</if>
            <if test="loanBalance != null ">and loanBalance = #{loanBalance}</if>
            <if test="isWhetherLoan != null  and isWhetherLoan != ''">and isWhetherLoan = #{isWhetherLoan}</if>
            <if test="totalAmount != null ">and totalAmount = #{totalAmount}</if>
            <if test="relatedApprovalAmount != null ">and relatedApprovalAmount = #{relatedApprovalAmount}</if>
            <if test="differenceAmount != null ">and differenceAmount = #{differenceAmount}</if>
            <if test="differenceExplanation != null  and differenceExplanation != ''">and differenceExplanation =
                #{differenceExplanation}
            </if>
            <if test="expenseAccountCode != null  and expenseAccountCode != ''">and expenseAccountCode =
                #{expenseAccountCode}
            </if>
            <if test="expenseAccountName != null  and expenseAccountName != ''">and expenseAccountName like concat('%',
                #{expenseAccountName}, '%')
            </if>
            <if test="serviceDescription != null  and serviceDescription != ''">and serviceDescription =
                #{serviceDescription}
            </if>
            <if test="invoiceNumber != null  and invoiceNumber != ''">and invoiceNumber = #{invoiceNumber}</if>
            <if test="excludingTaxAmount != null ">and excludingTaxAmount = #{excludingTaxAmount}</if>
            <if test="specialInvoiceTaxAmount != null ">and specialInvoiceTaxAmount = #{specialInvoiceTaxAmount}</if>
            <if test="reimbursementAmount != null ">and reimbursementAmount like concat('%', #{reimbursementAmount},
                '%')
            </if>
            <if test="currency != null  and currency != ''">and currency = #{currency}</if>
            <if test="projectNo != null  and projectNo != ''">and projectNo = #{projectNo}</if>
            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="sapPayNumber != null  and sapPayNumber != ''">and sapPayNumber like concat('%', #{sapPayNumber},
                '%')
            </if>
            <if test="voucherPushDate != null  and voucherPushDate != ''">and voucherPushDate = #{voucherPushDate}</if>
            <if test="expenseApplicationNumber != null  and expenseApplicationNumber != ''">and expenseApplicationNumber
                like concat('%', #{expenseApplicationNumber}, '%')
            </if>
            <if test="applicationSubject != null  and applicationSubject != ''">and applicationSubject like concat('%',
                #{applicationSubject}, '%')
            </if>
            <if test="applicationType != null  and applicationType != ''">and applicationType = #{applicationType}</if>
            <if test="secondaryType != null  and secondaryType != ''">and secondaryType = #{secondaryType}</if>
            <if test="applicationsAmount != null ">and applicationsAmount = #{applicationsAmount}</if>
            <if test="detailedDescription != null  and detailedDescription != ''">and detailedDescription =
                #{detailedDescription}
            </if>
            <if test="executionDate != null ">and executionDate = #{executionDate}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
        </where>
    </select>

    <select id="selectClinicalReimbursementById" parameterType="Integer" resultMap="ClinicalReimbursementResult">
        <include refid="selectClinicalReimbursementVo"/>
        where id = #{id}
    </select>

    <insert id="insertClinicalReimbursement" parameterType="ClinicalReimbursement">
        insert into clinical_reimbursement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="oaReimbursementNumber != null">oaReimbursementNumber,</if>
            <if test="dateCreated != null">dateCreated,</if>
            <if test="reimbursementCategory != null">reimbursementCategory,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="companyName != null">companyName,</if>
            <if test="reimbursementPerson != null">reimbursementPerson,</if>
            <if test="reimbursementPersonNumber != null">reimbursementPersonNumber,</if>
            <if test="applicant != null">applicant,</if>
            <if test="applicantNumber != null">applicantNumber,</if>
            <if test="applicantPosition != null">applicantPosition,</if>
            <if test="department != null">department,</if>
            <if test="costCenterCode != null">costCenterCode,</if>
            <if test="costCenterName != null">costCenterName,</if>
            <if test="relatedApprovalForm != null">relatedApprovalForm,</if>
            <if test="businessPeriod != null">businessPeriod,</if>
            <if test="travelDepartureDate != null">travelDepartureDate,</if>
            <if test="travelReturnDate != null">travelReturnDate,</if>
            <if test="businessDay != null">businessDay,</if>
            <if test="colleagues != null">colleagues,</if>
            <if test="businessType != null">businessType,</if>
            <if test="typeSegmentation != null">typeSegmentation,</if>
            <if test="ceoBusiness != null">ceoBusiness,</if>
            <if test="meetingStartDate != null">meetingStartDate,</if>
            <if test="meetingEndDate != null">meetingEndDate,</if>
            <if test="meetingName != null">meetingName,</if>
            <if test="meetingType != null">meetingType,</if>
            <if test="meetingCategory != null">meetingCategory,</if>
            <if test="meetingFormat != null">meetingFormat,</if>
            <if test="companyRole != null">companyRole,</if>
            <if test="objectOriented != null">objectOriented,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="peopleCovered != null">peopleCovered,</if>
            <if test="signNumber != null">signNumber,</if>
            <if test="expertsNumber != null">expertsNumber,</if>
            <if test="meetingMaterials != null">meetingMaterials,</if>
            <if test="invitation != null">invitation,</if>
            <if test="schedule != null">schedule,</if>
            <if test="attendanceSheet != null">attendanceSheet,</if>
            <if test="photo != null">photo,</if>
            <if test="paymentMethod != null">paymentMethod,</if>
            <if test="loanBalance != null">loanBalance,</if>
            <if test="isWhetherLoan != null">isWhetherLoan,</if>
            <if test="totalAmount != null">totalAmount,</if>
            <if test="relatedApprovalAmount != null">relatedApprovalAmount,</if>
            <if test="differenceAmount != null">differenceAmount,</if>
            <if test="differenceExplanation != null">differenceExplanation,</if>
            <if test="expenseAccountCode != null">expenseAccountCode,</if>
            <if test="expenseAccountName != null">expenseAccountName,</if>
            <if test="serviceDescription != null">serviceDescription,</if>
            <if test="invoiceNumber != null">invoiceNumber,</if>
            <if test="excludingTaxAmount != null">excludingTaxAmount,</if>
            <if test="specialInvoiceTaxAmount != null">specialInvoiceTaxAmount,</if>
            <if test="reimbursementAmount != null">reimbursementAmount,</if>
            <if test="currency != null">currency,</if>
            <if test="projectNo != null">projectNo,</if>
            <if test="centerName != null">centerName,</if>
            <if test="documentStatus != null">documentStatus,</if>
            <if test="currentSession != null">currentSession,</if>
            <if test="sapPayNumber != null">sapPayNumber,</if>
            <if test="voucherPushDate != null">voucherPushDate,</if>
            <if test="expenseApplicationNumber != null">expenseApplicationNumber,</if>
            <if test="applicationSubject != null">applicationSubject,</if>
            <if test="applicationType != null">applicationType,</if>
            <if test="secondaryType != null">secondaryType,</if>
            <if test="applicationsAmount != null">applicationsAmount,</if>
            <if test="detailedDescription != null">detailedDescription,</if>
            <if test="executionDate != null">executionDate,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="oaReimbursementNumber != null">#{oaReimbursementNumber},</if>
            <if test="dateCreated != null">#{dateCreated},</if>
            <if test="reimbursementCategory != null">#{reimbursementCategory},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="reimbursementPerson != null">#{reimbursementPerson},</if>
            <if test="reimbursementPersonNumber != null">#{reimbursementPersonNumber},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="applicantNumber != null">#{applicantNumber},</if>
            <if test="applicantPosition != null">#{applicantPosition},</if>
            <if test="department != null">#{department},</if>
            <if test="costCenterCode != null">#{costCenterCode},</if>
            <if test="costCenterName != null">#{costCenterName},</if>
            <if test="relatedApprovalForm != null">#{relatedApprovalForm},</if>
            <if test="businessPeriod != null">#{businessPeriod},</if>
            <if test="travelDepartureDate != null">#{travelDepartureDate},</if>
            <if test="travelReturnDate != null">#{travelReturnDate},</if>
            <if test="businessDay != null">#{businessDay},</if>
            <if test="colleagues != null">#{colleagues},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="typeSegmentation != null">#{typeSegmentation},</if>
            <if test="ceoBusiness != null">#{ceoBusiness},</if>
            <if test="meetingStartDate != null">#{meetingStartDate},</if>
            <if test="meetingEndDate != null">#{meetingEndDate},</if>
            <if test="meetingName != null">#{meetingName},</if>
            <if test="meetingType != null">#{meetingType},</if>
            <if test="meetingCategory != null">#{meetingCategory},</if>
            <if test="meetingFormat != null">#{meetingFormat},</if>
            <if test="companyRole != null">#{companyRole},</if>
            <if test="objectOriented != null">#{objectOriented},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="peopleCovered != null">#{peopleCovered},</if>
            <if test="signNumber != null">#{signNumber},</if>
            <if test="expertsNumber != null">#{expertsNumber},</if>
            <if test="meetingMaterials != null">#{meetingMaterials},</if>
            <if test="invitation != null">#{invitation},</if>
            <if test="schedule != null">#{schedule},</if>
            <if test="attendanceSheet != null">#{attendanceSheet},</if>
            <if test="photo != null">#{photo},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="loanBalance != null">#{loanBalance},</if>
            <if test="isWhetherLoan != null">#{isWhetherLoan},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="relatedApprovalAmount != null">#{relatedApprovalAmount},</if>
            <if test="differenceAmount != null">#{differenceAmount},</if>
            <if test="differenceExplanation != null">#{differenceExplanation},</if>
            <if test="expenseAccountCode != null">#{expenseAccountCode},</if>
            <if test="expenseAccountName != null">#{expenseAccountName},</if>
            <if test="serviceDescription != null">#{serviceDescription},</if>
            <if test="invoiceNumber != null">#{invoiceNumber},</if>
            <if test="excludingTaxAmount != null">#{excludingTaxAmount},</if>
            <if test="specialInvoiceTaxAmount != null">#{specialInvoiceTaxAmount},</if>
            <if test="reimbursementAmount != null">#{reimbursementAmount},</if>
            <if test="currency != null">#{currency},</if>
            <if test="projectNo != null">#{projectNo},</if>
            <if test="centerName != null">#{centerName},</if>
            <if test="documentStatus != null">#{documentStatus},</if>
            <if test="currentSession != null">#{currentSession},</if>
            <if test="sapPayNumber != null">#{sapPayNumber},</if>
            <if test="voucherPushDate != null">#{voucherPushDate},</if>
            <if test="expenseApplicationNumber != null">#{expenseApplicationNumber},</if>
            <if test="applicationSubject != null">#{applicationSubject},</if>
            <if test="applicationType != null">#{applicationType},</if>
            <if test="secondaryType != null">#{secondaryType},</if>
            <if test="applicationsAmount != null">#{applicationsAmount},</if>
            <if test="detailedDescription != null">#{detailedDescription},</if>
            <if test="executionDate != null">#{executionDate},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
        </trim>
    </insert>

    <update id="updateClinicalReimbursement" parameterType="ClinicalReimbursement">
        update clinical_reimbursement
        <trim prefix="SET" suffixOverrides=",">
            <if test="oaReimbursementNumber != null">oaReimbursementNumber = #{oaReimbursementNumber},</if>
            <if test="dateCreated != null">dateCreated = #{dateCreated},</if>
            <if test="reimbursementCategory != null">reimbursementCategory = #{reimbursementCategory},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="reimbursementPerson != null">reimbursementPerson = #{reimbursementPerson},</if>
            <if test="reimbursementPersonNumber != null">reimbursementPersonNumber = #{reimbursementPersonNumber},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="applicantNumber != null">applicantNumber = #{applicantNumber},</if>
            <if test="applicantPosition != null">applicantPosition = #{applicantPosition},</if>
            <if test="department != null">department = #{department},</if>
            <if test="costCenterCode != null">costCenterCode = #{costCenterCode},</if>
            <if test="costCenterName != null">costCenterName = #{costCenterName},</if>
            <if test="relatedApprovalForm != null">relatedApprovalForm = #{relatedApprovalForm},</if>
            <if test="businessPeriod != null">businessPeriod = #{businessPeriod},</if>
            <if test="travelDepartureDate != null">travelDepartureDate = #{travelDepartureDate},</if>
            <if test="travelReturnDate != null">travelReturnDate = #{travelReturnDate},</if>
            <if test="businessDay != null">businessDay = #{businessDay},</if>
            <if test="colleagues != null">colleagues = #{colleagues},</if>
            <if test="businessType != null">businessType = #{businessType},</if>
            <if test="typeSegmentation != null">typeSegmentation = #{typeSegmentation},</if>
            <if test="ceoBusiness != null">ceoBusiness = #{ceoBusiness},</if>
            <if test="meetingStartDate != null">meetingStartDate = #{meetingStartDate},</if>
            <if test="meetingEndDate != null">meetingEndDate = #{meetingEndDate},</if>
            <if test="meetingName != null">meetingName = #{meetingName},</if>
            <if test="meetingType != null">meetingType = #{meetingType},</if>
            <if test="meetingCategory != null">meetingCategory = #{meetingCategory},</if>
            <if test="meetingFormat != null">meetingFormat = #{meetingFormat},</if>
            <if test="companyRole != null">companyRole = #{companyRole},</if>
            <if test="objectOriented != null">objectOriented = #{objectOriented},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="peopleCovered != null">peopleCovered = #{peopleCovered},</if>
            <if test="signNumber != null">signNumber = #{signNumber},</if>
            <if test="expertsNumber != null">expertsNumber = #{expertsNumber},</if>
            <if test="meetingMaterials != null">meetingMaterials = #{meetingMaterials},</if>
            <if test="invitation != null">invitation = #{invitation},</if>
            <if test="schedule != null">schedule = #{schedule},</if>
            <if test="attendanceSheet != null">attendanceSheet = #{attendanceSheet},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="paymentMethod != null">paymentMethod = #{paymentMethod},</if>
            <if test="loanBalance != null">loanBalance = #{loanBalance},</if>
            <if test="isWhetherLoan != null">isWhetherLoan = #{isWhetherLoan},</if>
            <if test="totalAmount != null">totalAmount = #{totalAmount},</if>
            <if test="relatedApprovalAmount != null">relatedApprovalAmount = #{relatedApprovalAmount},</if>
            <if test="differenceAmount != null">differenceAmount = #{differenceAmount},</if>
            <if test="differenceExplanation != null">differenceExplanation = #{differenceExplanation},</if>
            <if test="expenseAccountCode != null">expenseAccountCode = #{expenseAccountCode},</if>
            <if test="expenseAccountName != null">expenseAccountName = #{expenseAccountName},</if>
            <if test="serviceDescription != null">serviceDescription = #{serviceDescription},</if>
            <if test="invoiceNumber != null">invoiceNumber = #{invoiceNumber},</if>
            <if test="excludingTaxAmount != null">excludingTaxAmount = #{excludingTaxAmount},</if>
            <if test="specialInvoiceTaxAmount != null">specialInvoiceTaxAmount = #{specialInvoiceTaxAmount},</if>
            <if test="reimbursementAmount != null">reimbursementAmount = #{reimbursementAmount},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="projectNo != null">projectNo = #{projectNo},</if>
            <if test="centerName != null">centerName = #{centerName},</if>
            <if test="documentStatus != null">documentStatus = #{documentStatus},</if>
            <if test="currentSession != null">currentSession = #{currentSession},</if>
            <if test="sapPayNumber != null">sapPayNumber = #{sapPayNumber},</if>
            <if test="voucherPushDate != null">voucherPushDate = #{voucherPushDate},</if>
            <if test="expenseApplicationNumber != null">expenseApplicationNumber = #{expenseApplicationNumber},</if>
            <if test="applicationSubject != null">applicationSubject = #{applicationSubject},</if>
            <if test="applicationType != null">applicationType = #{applicationType},</if>
            <if test="secondaryType != null">secondaryType = #{secondaryType},</if>
            <if test="applicationsAmount != null">applicationsAmount = #{applicationsAmount},</if>
            <if test="detailedDescription != null">detailedDescription = #{detailedDescription},</if>
            <if test="executionDate != null">executionDate = #{executionDate},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteClinicalReimbursementById" parameterType="Integer">
        delete
        from clinical_reimbursement
        where id = #{id}
    </delete>

    <delete id="deleteClinicalReimbursementByIds" parameterType="String">
        delete from clinical_reimbursement where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAllClinicalReimbursement" parameterType="Integer">
        delete
        from clinical_reimbursement
    </delete>
</mapper>