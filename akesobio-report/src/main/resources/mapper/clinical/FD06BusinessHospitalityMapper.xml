<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.FD06BusinessHospitalityMapper">
    <resultMap type="FD06BusinessHospitality" id="FD06BusinessHospitalityResult">
        <result property="fd06ID" column="fd06ID"/>
        <result property="fd06Number" column="fd06Number"/>
        <result property="fd06Name" column="fd06Name"/>
        <result property="fd06HospitalityType" column="fd06HospitalityType"/>
        <result property="fd06ServiceDescription" column="fd06ServiceDescription"/>
        <result property="fd06EstimatedAmount" column="fd06EstimatedAmount"/>
    </resultMap>
    <select id="selectFD06" parameterType="FD06BusinessHospitality"
            resultMap="FD06BusinessHospitalityResult">
        SELECT
        fd06ID,
        fd06Number,
        fd06Name,
        fd06HospitalityType,
        fd06ServiceDescription,
        fd06EstimatedAmount
        FROM
        (
        SELECT
        a.fd_id 'fd06ID',
        a.fd_danHao 'fd06Number',
        ' FD06业务招待费申请' 'fd06Name',
        b.fd_zhaoDaiLeiXing 'fd06HospitalityType',
        b.fd_yeWuMiaoShu 'fd06ServiceDescription',
        a.fd_jinEHuiZong 'fd06EstimatedAmount'
        FROM
        ekp_ywzdf a
        LEFT JOIN ekp_ywzdf_add b ON b.fd_parent_id= a.fd_id
        ) b
        <where>
            <if test="fd06Number != null  and fd06Number != ''">and fd06Number = #{fd06Number}</if>
        </where>
    </select>
</mapper>