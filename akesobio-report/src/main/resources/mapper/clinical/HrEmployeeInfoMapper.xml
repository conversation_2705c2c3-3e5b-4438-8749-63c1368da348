<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.HrEmployeeInfoMapper">
    <!-- 实体类  -->
    <resultMap type="HrEmployeeInfo" id="HrEmployeeInfoResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectHrEmployeeInfoList">
      
    </sql>
    
    <!-- 方法 -->
    <select id="queryHrEmployeeInfoList" parameterType="HrEmployeeInfo" resultMap="HrEmployeeInfoResult">
        SELECT
            id,
            job_number,
            name,
            leader_name
        FROM hr_employee_info
    </select>
</mapper>