<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalOfficeSuppliesMapper">
    
    <resultMap type="ClinicalOfficeSupplies" id="ClinicalOfficeSuppliesResult">
        <result property="requestNo"    column="requestNo"    />
        <result property="creationTime"    column="creationTime"    />
        <result property="processName"    column="processName"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="centerName"    column="centerName"    />
        <result property="applicant"    column="applicant"    />
        <result property="applicationReason"    column="applicationReason"    />
        <result property="name"    column="name"    />
        <result property="brand"    column="brand"    />
        <result property="specifications"    column="specifications"    />
        <result property="unit"    column="unit"    />
        <result property="number"    column="number"    />
        <result property="expectedDeliveryTime"    column="expectedDeliveryTime"    />
        <result property="recipient"    column="recipient"    />
        <result property="telephone"    column="telephone"    />
        <result property="city"    column="city"    />
        <result property="detailedShippingAddress"    column="detailedShippingAddress"    />
        <result property="endTime"    column="endTime"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="currentProcessor"    column="currentProcessor"    />
        <result property="centerNumber"    column="centerNumber"    />
        <result property="department"    column="department"    />
        <result property="principalIinvestigator"    column="principalIinvestigator"    />
        <result property="budgetUnitPrice"    column="budgetUnitPrice"    />
        <result property="cost"    column="cost"    />
        <result property="remark"    column="remark"    />
        <result property="null1"    column="null1"    />
        <result property="null2"    column="null2"    />
    </resultMap>

    <sql id="selectClinicalOfficeSuppliesVo">
        select id, requestNo, creationTime, processName, projectNumber, centerName, applicant, applicationReason, name, brand, specifications, unit, number, expectedDeliveryTime, recipient, telephone, city, detailedShippingAddress, endTime, documentStatus, currentSession, currentProcessor,centerNumber,department,principalIinvestigator,budgetUnitPrice,cost,remark,null1,null2 from clinical_office_supplies
    </sql>

    <select id="selectClinicalOfficeSuppliesList" parameterType="ClinicalOfficeSupplies" resultMap="ClinicalOfficeSuppliesResult">
        <include refid="selectClinicalOfficeSuppliesVo"/>
        <where>  
            <if test="requestNo != null  and requestNo != ''"> and requestNo like concat('%', #{requestNo}, '%')</if>
            <if test="params.beginCreationTime != null and params.beginCreationTime != '' and params.endCreationTime != null and params.endCreationTime != ''"> and creationTime between #{params.beginCreationTime} and #{params.endCreationTime}</if>
            <if test="processName != null  and processName != ''"> and processName like concat('%', #{processName}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="centerName != null  and centerName != ''"> and centerName like concat('%', #{centerName}, '%')</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="applicationReason != null  and applicationReason != ''"> and applicationReason = #{applicationReason}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="brand != null  and brand != ''"> and brand like concat('%', #{brand}, '%')</if>
            <if test="specifications != null  and specifications != ''"> and specifications like concat('%', #{specifications}, '%')</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="number != null  and number != ''"> and number = #{number}</if>
            <if test="params.beginExpectedDeliveryTime != null and params.beginExpectedDeliveryTime != '' and params.endExpectedDeliveryTime != null and params.endExpectedDeliveryTime != ''"> and expectedDeliveryTime between #{params.beginExpectedDeliveryTime} and #{params.endExpectedDeliveryTime}</if>
            <if test="recipient != null  and recipient != ''"> and recipient like concat('%', #{recipient}, '%')</if>
            <if test="telephone != null  and telephone != ''"> and telephone like concat('%', #{telephone}, '%')</if>
            <if test="city != null  and city != ''"> and city like concat('%', #{city}, '%')</if>
            <if test="detailedShippingAddress != null  and detailedShippingAddress != ''"> and detailedShippingAddress like concat('%', #{detailedShippingAddress}, '%')</if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''"> and endTime between #{params.beginEndTime} and #{params.endEndTime}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="currentProcessor != null  and currentProcessor != ''"> and currentProcessor like concat('%', #{currentProcessor}, '%')</if>
             and documentStatus !='废弃'
        </where>
        order by creationTime desc
    </select>
</mapper>