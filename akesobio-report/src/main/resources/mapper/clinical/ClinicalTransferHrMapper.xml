<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalTransferHrMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalTransferHr" id="ClinicalTransferHrResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectClinicalTransferHrList">
        SELECT id,
               dh,
               name,
               loginName,
               entryDate,
               leaveDepartment,
               entryDepartment,
               leavePost,
               entryPost,
               transferDate,
               applicant,
               createTime,
               subject,
               currentSession,
               handlePerson
        FROM clinical_transfer_hr
    </sql>
    
    <!-- 方法 -->
    <select id="queryClinicalTransferHrList" parameterType="ClinicalTransferHr" resultMap="ClinicalTransferHrResult">
        <include refid="selectClinicalTransferHrList"/>
        <where>
            <if test="handlePerson != null and handlePerson != ''">AND handlePerson like concat('%',#{handlePerson},'%')</if>
            <if test="loginName != null and loginName != ''">AND loginName like concat('%',#{loginName},'%')</if>
            <if test="applicant != null and applicant != ''">AND applicant like concat('%',#{applicant},'%')</if>
            <if test="name != null and name != ''">AND name like concat('%',#{name},'%')</if>

<!--            <if test="startDate != null and startDate != ''">and CONVERT(VARCHAR(100),applicationDate,23) &gt;= #{startDate}</if>-->
<!--            <if test="endDate != null and endDate != ''">and CONVERT(VARCHAR(100),applicationDate,23) &lt;= #{endDate}</if>-->
        </where>
        ORDER BY createTime DESC
    </select>
</mapper>