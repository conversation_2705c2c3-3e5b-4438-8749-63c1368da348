<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.MaterialTeamApprovalNode2Mapper">
    <!-- 实体类  -->
    <resultMap type="MaterialTeamApprovalNode2" id="MaterialTeamApprovalNode2Result">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectMaterialTeamApprovalNode2List">
        SELECT
            id,
            oddNumber,
            tableName,
            currentSession,
            jobNumber,
            name,
            applicationDate,
            expectationDate,
            nodeArrivalTime,
            documentStatus,
            department
        FROM material_team_approval_node2
    </sql>
    
    <!-- 方法 -->
    <select id="queryMaterialTeamApprovalNode2List" parameterType="MaterialTeamApprovalNode2" resultMap="MaterialTeamApprovalNode2Result">
        <include refid="selectMaterialTeamApprovalNode2List"/>
        <where>  
            <if test="oddNumber != null and oddNumber != ''"> and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="tableName != null and tableName != ''"> and tableName like concat('%', #{tableName}, '%')</if>
            <if test="currentSession != null and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="jobNumber != null and jobNumber != ''"> and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="documentStatus != null and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="department != null and department != ''"> and department like concat('%', #{department}, '%')</if>

            <if test="startDate != null and startDate != ''">and CONVERT(VARCHAR(100),applicationDate,23) &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and CONVERT(VARCHAR(100),applicationDate,23) &lt;= #{endDate}</if>
            <if test="startDate1 != null and startDate1 != ''">and CONVERT(VARCHAR(100),expectationDate,23) &gt;= #{startDate1}</if>
            <if test="endDate1 != null and endDate1 != ''">and CONVERT(VARCHAR(100),expectationDate,23) &lt;= #{endDate1}</if>
            <if test="startDate2 != null and startDate2 != ''">and CONVERT(VARCHAR(100),nodeArrivalTime,23) &gt;= #{startDate2}</if>
            <if test="endDate2 != null and endDate2 != ''">and CONVERT(VARCHAR(100),nodeArrivalTime,23) &lt;= #{endDate2}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>