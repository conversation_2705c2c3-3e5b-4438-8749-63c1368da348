<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalDepartMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalDepart" id="ClinicalDepartResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectClinicalDepartList">
        SELECT id,
               applicationDate,
               oddNumber,
               jobNumber,
               applicant,
               department,
               position,
               documentStatus,
               currentSession,
               currentProcessor,
               nodeArrivalTime,
               lastDate,
               reasonForLeave,
               leaveDate,
               leaveRealDate,
               entryDate,
               zsDate,
               bmDate,
               bmjjDate,
               lcddDate,
               lcDate,
               cwDate,
               hrDate,
               zsName,
               bmName,
               lcName
        FROM clinical_depart
    </sql>

    <!-- 方法 -->
    <select id="queryClinicalDepartList" parameterType="ClinicalDepart" resultMap="ClinicalDepartResult">
        <include refid="selectClinicalDepartList"/>
        <where>
            <if test="documentStatus != null and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="applicant != null and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="jobNumber != null and jobNumber != ''">and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="oddNumber != null and oddNumber != ''">and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="position != null and position != ''">and position like concat('%', #{position}, '%')</if>
            <if test="department != null and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="currentSession != null and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="currentProcessor != null and currentProcessor != ''">and currentProcessor like concat('%',
                #{currentProcessor}, '%')
            </if>
            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
            <if test="startDate1 != null and startDate1 != ''">and leaveRealDate &gt;= #{startDate1}</if>
            <if test="endDate1 != null and endDate1 != ''">and leaveRealDate &lt;= #{endDate1}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>