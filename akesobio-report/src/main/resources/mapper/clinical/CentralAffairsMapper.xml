<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.CentralAffairsMapper">

    <resultMap type="CentralAffairs" id="CentralAffairsResult">
        <result property="oddNumbers" column="oddNumbers"/>
        <result property="projectNo" column="projectNo"/>
        <result property="state" column="state"/>
        <result property="plannedEndTime" column="plannedEndTime"/>
        <result property="cra" column="cra"/>
        <result property="filegroupConfirmation" column="filegroupConfirmation"/>
        <result property="materialTeamConfirmation" column="materialTeamConfirmation"/>
        <result property="expenseGroupConfirmation" column="expenseGroupConfirmation"/>
        <result property="informationGroupConfirmation" column="informationGroupConfirmation"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="currentProcessor" column="currentProcessor"/>
        <result property="applicationDate" column="applicationDate"/>
        <result property="applicant" column="applicant"/>
        <result property="projectDivision" column="projectDivision"/>
        <result property="centerNumber" column="centerNumber"/>
        <result property="centerName" column="centerName"/>
        <result property="department" column="department"/>
        <result property="nodeId" column="nodeId"/>
        <result property="nodeArrivalTime" column="nodeArrivalTime"/>
        <result property="transferringPerson" column="transferringPerson"/>
        <result property="nodeApprovalTime" column="nodeApprovalTime"/>
        <result property="operationType" column="operationType"/>
        <result property="examiner" column="examiner"/>
        <result property="approvalOpinion" column="approvalOpinion"/>
    </resultMap>

    <sql id="selectCentralAffairsVo">
        select
               id,
               oddNumbers,
               projectNo,
               state,
               plannedEndTime,
               cra,
               filegroupConfirmation,
               materialTeamConfirmation,
               expenseGroupConfirmation,
               informationGroupConfirmation,
               documentStatus,
               currentSession,
               currentProcessor,
               applicationDate,
               applicant,
               projectDivision,
               centerNumber,
               centerName,
               department,
               nodeId,
               nodeArrivalTime,
               transferringPerson,
               nodeApprovalTime,
               operationType,
               examiner,
               approvalOpinion
        from central_affairs
    </sql>

    <select id="selectCentralAffairsList" parameterType="CentralAffairs" resultMap="CentralAffairsResult">
        <include refid="selectCentralAffairsVo"/>
        <where>
            <if test="oddNumbers != null  and oddNumbers != ''">and oddNumbers like concat('%', #{oddNumbers}, '%')</if>
            <if test="projectNo != null  and projectNo != ''">and projectNo like concat('%', #{projectNo}, '%')</if>
            <if test="state != null  and state != ''">and state like concat('%', #{state}, '%')</if>
            <if test="params.beginPlannedEndTime != null and params.beginPlannedEndTime != '' and params.endPlannedEndTime != null and params.endPlannedEndTime != ''">
                and plannedEndTime between #{params.beginPlannedEndTime} and #{params.endPlannedEndTime}
            </if>
            <if test="cra != null  and cra != ''">and cra like concat('%', #{cra}, '%')</if>
            <if test="filegroupConfirmation != null  and filegroupConfirmation != ''">and filegroupConfirmation like
                concat('%', #{filegroupConfirmation}, '%')
            </if>
            <if test="materialTeamConfirmation != null  and materialTeamConfirmation != ''">and materialTeamConfirmation
                like concat('%', #{materialTeamConfirmation}, '%')
            </if>
            <if test="expenseGroupConfirmation != null  and expenseGroupConfirmation != ''">and expenseGroupConfirmation
                like concat('%', #{expenseGroupConfirmation}, '%')
            </if>
            <if test="informationGroupConfirmation != null  and informationGroupConfirmation != ''">and
                informationGroupConfirmation like concat('%', #{informationGroupConfirmation}, '%')
            </if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="currentProcessor != null  and currentProcessor != ''">and currentProcessor like concat('%',
                #{currentProcessor}, '%')
            </if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''">
                and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="projectDivision != null  and projectDivision != ''">and projectDivision like concat('%',
                #{projectDivision}, '%')
            </if>
            <if test="centerNumber != null  and centerNumber != ''">and centerNumber like concat('%', #{centerNumber},
                '%')
            </if>
            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="nodeId != null  and nodeId != ''">and nodeId like concat('%', #{nodeId}, '%')</if>
            <if test="params.beginNodeArrivalTime != null and params.beginNodeArrivalTime != '' and params.endNodeArrivalTime != null and params.endNodeArrivalTime != ''">
                and nodeArrivalTime between #{params.beginNodeArrivalTime} and #{params.endNodeArrivalTime}
            </if>
            <if test="transferringPerson != null  and transferringPerson != ''">and transferringPerson like concat('%',
                #{transferringPerson}, '%')
            </if>
            <if test="params.beginNodeApprovalTime != null and params.beginNodeApprovalTime != '' and params.endNodeApprovalTime != null and params.endNodeApprovalTime != ''">
                and nodeApprovalTime between #{params.beginNodeApprovalTime} and #{params.endNodeApprovalTime}
            </if>
            <if test="operationType != null  and operationType != ''">and operationType like concat('%',
                #{operationType}, '%')
            </if>
            <if test="examiner != null  and examiner != ''">and examiner like concat('%', #{examiner}, '%')</if>
            <if test="approvalOpinion != null  and approvalOpinion != ''">and approvalOpinion like concat('%',
                #{approvalOpinion}, '%')
            </if>
            and documentStatus !='废弃' and  documentStatus !='草稿'
        </where>
    </select>
</mapper>