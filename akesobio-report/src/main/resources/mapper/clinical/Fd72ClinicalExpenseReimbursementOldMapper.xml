<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.Fd72ClinicalExpenseReimbursementOldMapper">

    <resultMap type="Fd72ClinicalExpenseReimbursementOld" id="Fd72ClinicalExpenseReimbursementOldResult">
        <result property="oaReimbursementNumber" column="oaReimbursementNumber"/>
        <result property="dateCreated" column="dateCreated"/>
        <result property="reimbursementCategory" column="reimbursementCategory"/>
        <result property="companyCode" column="companyCode"/>
        <result property="companyName" column="companyName"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="reimbursementPersonNumber" column="reimbursementPersonNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="applicantNumber" column="applicantNumber"/>
        <result property="applicantPosition" column="applicantPosition"/>
        <result property="department" column="department"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="relatedApprovalForm" column="relatedApprovalForm"/>
        <result property="businessPeriod" column="businessPeriod"/>
        <result property="businessType" column="businessType"/>
        <result property="typeSegmentation" column="typeSegmentation"/>
        <result property="ceoBusiness" column="ceoBusiness"/>
        <result property="loanBalance" column="loanBalance"/>
        <result property="isWhetherLoan" column="isWhetherLoan"/>
        <result property="totalAmount" column="totalAmount"/>
        <result property="relatedApprovalAmount" column="relatedApprovalAmount"/>
        <result property="differenceAmount" column="differenceAmount"/>
        <result property="serviceDescription" column="serviceDescription"/>
        <result property="invoiceNumber" column="invoiceNumber"/>
        <result property="excludingTaxAmount" column="excludingTaxAmount"/>
        <result property="specialInvoiceTaxAmount" column="specialInvoiceTaxAmount"/>
        <result property="reimbursementAmount" column="reimbursementAmount"/>
        <result property="currency" column="currency"/>
        <result property="projectNo" column="projectNo"/>
        <result property="centerName" column="centerName"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="voucherPushDate" column="voucherPushDate"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="projectNos" column="projectNos"/>
        <result property="fdId" column="fdId"/>
    </resultMap>
    <select id="selectFd72" parameterType="Fd72ClinicalExpenseReimbursementOld"
            resultMap="Fd72ClinicalExpenseReimbursementOldResult">
        SELECT--FD72
              a.fd_id 'fdId', a.serialno 'oaReimbursementNumber', a.application_date 'dateCreated', NULL 'reimbursementCategory', a.code_CO 'companyCode', a.bear_cost_text 'companyName', b.fd_name 'reimbursementPerson', a.fd_3ab96f63333c80 'reimbursementPersonNumber', c.fd_name 'applicant', a.job_number 'applicantNumber', a.job_title 'applicantPosition', a.fd_3a4382f3d2e3e6 'department', a.cost_center_code_text 'costCenterCode', a.cost_name_center 'costCenterName', a.association_form 'relatedApprovalForm', a.fd_3bb022d04415de + '-' + a.fd_3bb022dfc444b2 'businessPeriod', a.fd_3a4ba29ef4eb08 'businessType', a.fd_3be678f8356c3c 'typeSegmentation', a.fd_3a438294a7e812 'ceoBusiness', a.fd_3a45cea6aef21a 'loanBalance', ( CASE a.whether_offset WHEN '0' THEN '否' WHEN '1' THEN '是' ELSE '' END ) 'isWhetherLoan', a.fd_3d1f32aa369c4e 'totalAmount', NULL 'relatedApprovalAmount', NULL 'differenceAmount', d.invoice_type 'serviceDescription', d.invoice_no_baiwang 'invoiceNumber', d.business_description 'excludingTaxAmount', d.fd_3a3bbef9d4e540 'specialInvoiceTaxAmount', d.fd_3ab7f19560420a 'reimbursementAmount', a.fd_3a3c5a391655da_text 'currency', a.item_number_text 'projectNo', a.fd_3ab7e967054356 'centerName', (
            CASE
                f.doc_status
                WHEN '10' THEN
                    '草稿'
                WHEN '20' THEN
                    '待审'
                WHEN '11' THEN
                    '驳回'
                WHEN '00' THEN
                    '废弃'
                WHEN '30' THEN
                    '结束'
                WHEN '31' THEN
                    '已反馈'
                ELSE '不对劲'
                END
            ) 'documentStatus',
                a.fd_3a50c39b7254f6 as 'projectNos',
                e.fd_fact_node_name 'currentSession', a.sap_document_number 'sapPayNumber', a.sap_credential_push_time 'voucherPushDate', a.application_subject 'applicationSubject'
        FROM ekp_expensereimbursementNew a
                 LEFT JOIN hr_org_element b ON b.fd_id = a.apply_reimbursement
                 LEFT JOIN hr_org_element c ON c.fd_id = a.applicant
                 LEFT JOIN ekp_rsementNew_e061d29310 d ON d.fd_parent_id = a.fd_id
                 LEFT JOIN [dbo].[modeling_model_main] f
        ON a.fd_id = f.fd_id
            LEFT JOIN [dbo].[lbpm_node] e ON a.fd_id = e.fd_process_id
        WHERE
            a.fd_3a4382f3d2e3e6 LIKE '%临床运行%'
          AND a.application_date >= '2025-01-01'
    </select>

</mapper>