<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalDataPermissionMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalDataPermission" id="ClinicalDataPermissionResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectClinicalDataPermissionList">
        SELECT
            id,
            name,
            job_number AS jobNumber,
            project_no AS projectNo,
            project_nos AS projectNos,
            create_time AS createTime,
            update_time AS updateTime
        FROM clinical_data_permission
    </sql>
    
    <!-- 方法 -->
    <select id="queryClinicalDataPermissionList" parameterType="ClinicalDataPermission" resultMap="ClinicalDataPermissionResult">
        <include refid="selectClinicalDataPermissionList"/>
        <where>  
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="jobNumber != null and jobNumber != ''"> and job_number like concat('%', #{jobNumber}, '%')</if>
            <if test="projectNo != null and projectNo != ''"> and project_no like concat('%', #{projectNo}, '%')</if>
            <if test="projectNos != null and projectNos != ''"> and project_nos like concat('%', #{projectNos}, '%')</if>

            <if test="startDate != null and startDate != ''">and CONVERT(VARCHAR(100),create_time,23) &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and CONVERT(VARCHAR(100),create_time,23) &lt;= #{endDate}</if>
            <if test="startDate1 != null and startDate1 != ''">and CONVERT(VARCHAR(100),update_time,23) &gt;= #{startDate1}</if>
            <if test="endDate1 != null and endDate1 != ''">and CONVERT(VARCHAR(100),update_time,23) &lt;= #{endDate1}</if>
        </where>
    </select>
    <!-- 查询单个 -->
    <select id="queryClinicalDataPermissionById" parameterType="String" resultType="ClinicalDataPermission">
        <include refid="selectClinicalDataPermissionList"/>
        WHERE id = #{id}
    </select>

    <insert id="insertClinicalDataPermission" useGeneratedKeys="true" parameterType="ClinicalDataPermission" keyProperty="id">
        INSERT INTO clinical_data_permission(
        <if test="name != null and name != ''">name,</if>
        <if test="jobNumber != null and jobNumber != ''">job_number,</if>
        <if test="projectNo != null and projectNo != ''">project_no,</if>
        <if test="projectNos != null and projectNos != ''">project_nos,</if>
        <!--            <if test="ids != null and ids.length > 0">id,</if>-->
        create_time,update_time
        )values
        (
        <if test="name != null and name != ''">#{name},</if>
        <if test="jobNumber != null and jobNumber != ''">#{jobNumber},</if>
        <if test="projectNo != null and projectNo != ''">#{projectNo},</if>
        <if test="projectNos != null and projectNos != ''">#{projectNos},</if>
        getdate(),getdate()
        )

        <!--        <foreach collection="ids" item="e" separator="," >-->
        <!--            (-->
        <!--            <if test="name != null and name != ''">#{name},</if>-->
        <!--            <if test="ids != null and ids.length > 0">#{e},</if>-->
        <!--            getdate(),getdate()-->
        <!--            )-->
        <!--        </foreach>-->
    </insert>

    <insert id="insertClinicalDataPermissionList" useGeneratedKeys="true" parameterType="List" keyProperty="id">
        INSERT INTO clinical_data_permission(
        name,
        job_number,
        project_nos,
        project_no,
        create_time,update_time
        )values
        <foreach collection="list" item="e" index="index" separator=",">
            (
            #{e.name},
            #{e.jobNumber},
            #{e.projectNos},
            #{e.projectNo},
            getdate(),getdate()
            )
        </foreach>
    </insert>
    <!-- 根据id修改 -->
    <update id="updateClinicalDataPermissionById" parameterType="ClinicalDataPermission">
        UPDATE clinical_data_permission
        <set>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="jobNumber != null and jobNumber != ''">job_number = #{jobNumber},</if>
            <if test="projectNo != null and projectNo != ''">project_no = #{projectNo},</if>
            <if test="projectNos != null and projectNos != ''">project_nos = #{projectNos},</if>
            update_time = getdate()
        </set>
        where id = #{id}
    </update>
    <!-- 替换项目号责任人 -->
    <update id="updateByNameJobNumber" parameterType="ClinicalDataPermission">
        UPDATE clinical_data_permission
        <set>
            <if test="name != null and name != ''">name = #{names},</if>
            <if test="jobNumber != null and jobNumber != ''">job_number = #{jobNumbers},</if>
            update_time = getdate()
        </set>
        where name = #{name} and job_number = #{jobNumber}
    </update>
</mapper>