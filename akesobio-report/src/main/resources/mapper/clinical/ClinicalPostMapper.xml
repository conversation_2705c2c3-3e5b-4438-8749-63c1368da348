<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalPostMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalPost" id="ClinicalPostResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectClinicalPostList">
        SELECT id,
               applicationDate,
               oddNumber,
               applicationName,
               jobNumber,
               department,
               position,
               senderName,
               projectNumber,
               receiveName,
               receiveAddress,
               promiseTimeType,
               deliveryId,
               packageCount,
               queryWaybillFreights,
               remark
        FROM clinical_post
    </sql>

    <!-- 方法 -->
    <select id="queryClinicalPostList" parameterType="ClinicalPost" resultMap="ClinicalPostResult">
        <include refid="selectClinicalPostList"/>
        <where>
            department like '%临床运行部门_临床事务部%'
            <if test="senderName != null and senderName != ''">and senderName like concat('%', #{senderName}, '%')</if>
            <if test="projectNumber != null and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="applicationName != null and applicationName != ''">and applicationName like concat('%',
                #{applicationName}, '%')
            </if>
            <if test="jobNumber != null and jobNumber != ''">and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="oddNumber != null and oddNumber != ''">and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>