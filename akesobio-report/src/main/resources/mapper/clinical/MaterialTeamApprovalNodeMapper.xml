<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.MaterialTeamApprovalNodeMapper">
    <!-- 实体类  -->
    <resultMap type="MaterialTeamApprovalNode" id="MaterialTeamApprovalNodeResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectMaterialTeamApprovalNodeList">
        SELECT
            id,
            oddNumber,
            jobNumber,
            name,
            tableName,
            nodeName,
            applicationDate,
            expectationDate,
            nodeDate,
            nodePassDate,
            operationType,
            currentSession,
            nodeArrivalTime,
            documentStatus
        FROM material_team_approval_nodes
    </sql>
    
    <!-- 方法 -->
    <select id="queryMaterialTeamApprovalNodeList" parameterType="MaterialTeamApprovalNode" resultMap="MaterialTeamApprovalNodeResult">
        <include refid="selectMaterialTeamApprovalNodeList"/>
        <where>
            jobNumber IN('A02367','A01564','A03874','A03611','A00299','A00681')
            <if test="tableName != null and tableName != ''"> and tableName like concat('%', #{tableName}, '%')</if>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="jobNumber != null and jobNumber != ''"> and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="oddNumber != null and oddNumber != ''"> and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="nodeName != null and nodeName != ''"> and nodeName like concat('%', #{nodeName}, '%')</if>
            <if test="operationType != null and operationType != ''"> and operationType like concat('%', #{operationType}, '%')</if>

            <if test="startDate != null and startDate != ''">and CONVERT(VARCHAR(100),applicationDate,23) &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and CONVERT(VARCHAR(100),applicationDate,23) &lt;= #{endDate}</if>

            <if test="startDate1 != null and startDate1 != ''">and CONVERT(VARCHAR(100),nodeDate,23) &gt;= #{startDate1}</if>
            <if test="endDate1 != null and endDate1 != ''">and CONVERT(VARCHAR(100),nodeDate,23) &lt;= #{endDate1}</if>

            <if test="startDate2 != null and startDate2 != ''">and CONVERT(VARCHAR(100),nodePassDate,23) &gt;= #{startDate2}</if>
            <if test="endDate2 != null and endDate2 != ''">and CONVERT(VARCHAR(100),nodePassDate,23) &lt;= #{endDate2}</if>

            <if test="startDate3 != null and startDate3 != ''">and CONVERT(VARCHAR(100),expectationDate,23) &gt;= #{startDate3}</if>
            <if test="endDate3 != null and endDate3 != ''">and CONVERT(VARCHAR(100),expectationDate,23) &lt;= #{endDate3}</if>
        </where>
        order by nodeDate desc
    </select>
</mapper>