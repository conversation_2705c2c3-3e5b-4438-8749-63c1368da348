<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ConfidentialityAgreementApprovalMapper">

    <resultMap type="com.akesobio.report.clinical.domain.ConfidentialityAgreementApproval" id="ConfidentialityAgreementApprovalResult">
        <result property="fdId" column="fdId"/>
        <result property="oddNumbers" column="oddNumbers"/>
        <result property="applicationDate" column="applicationDate"/>
        <result property="projectNo" column="projectNo"/>
        <result property="company" column="company"/>
        <result property="centerName" column="centerName"/>
        <result property="signatoryName" column="signatoryName"/>
        <result property="effectiveDate" column="effectiveDate"/>
        <result property="applicant" column="applicant"/>
        <result property="signDate" column="signDate"/>
        <result property="validityPeriod" column="validityPeriod"/>
        <result property="centerNumber" column="centerNumber"/>
        <result property="originalCopy" column="originalCopy"/>
        <result property="filingDate" column="filingDate"/>
        <result property="currentProcessor" column="currentProcessor"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="recipient" column="recipient"/>
        <result property="recipientDate" column="recipientDate"/>
        <result property="fileNumber" column="fileNumber"/>
    </resultMap>

    <sql id="selectConfidentialityAgreementApprovalVo">
        select fdId,
               oddNumbers,
               applicationDate,
               projectNo,
               company,
               centerName,
               signatoryName,
               effectiveDate,
               applicant,
               signDate,
               validityPeriod,
               centerNumber,
               originalCopy,
               filingDate,
               currentProcessor,
               documentStatus,
               currentSession,
               recipient,
               recipientDate,
               fileNumber
        from confidentiality_agreement_approval
    </sql>

    <select id="selectConfidentialityAgreementApprovalList" parameterType="ConfidentialityAgreementApproval"
            resultMap="ConfidentialityAgreementApprovalResult">
        <include refid="selectConfidentialityAgreementApprovalVo"/>
        <where>
            <if test="fdId != null  and fdId != ''">and fdId = #{fdId}</if>
            <if test="oddNumbers != null  and oddNumbers != ''">and oddNumbers like concat('%', #{oddNumbers}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''">
                and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}
            </if>
            <if test="projectNo != null  and projectNo != ''">and projectNo like concat('%', #{projectNo}, '%')</if>
            <if test="company != null  and company != ''">and company like concat('%', #{company}, '%')</if>
            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="signatoryName != null  and signatoryName != ''">and signatoryName like concat('%',
                #{signatoryName}, '%')
            </if>
            <if test="effectiveDate != null  and effectiveDate != ''">and effectiveDate like concat('%',
                #{effectiveDate}, '%')
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="params.beginSignDate != null and params.beginSignDate != '' and params.endSignDate != null and params.endSignDate != ''">
                and signDate between #{params.beginSignDate} and #{params.endSignDate}
            </if>
            <if test="validityPeriod != null  and validityPeriod != ''">and validityPeriod = #{validityPeriod}</if>
            <if test="centerNumber != null  and centerNumber != ''">and centerNumber like concat('%', #{centerNumber},
                '%')
            </if>
            <if test="originalCopy != null  and originalCopy != ''">and originalCopy like concat('%', #{originalCopy},
                '%')
            </if>
            <if test="params.beginFilingDate != null and params.beginFilingDate != '' and params.endFilingDate != null and params.endFilingDate != ''">
                and filingDate between #{params.beginFilingDate} and #{params.endFilingDate}
            </if>
            <if test="currentProcessor != null  and currentProcessor != ''">and currentProcessor like concat('%',
                #{currentProcessor}, '%')
            </if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="recipient != null  and recipient != ''">and recipient like concat('%', #{recipient}, '%')</if>
            <if test="params.beginRecipientDate != null and params.beginRecipientDate != '' and params.endRecipientDate != null and params.endRecipientDate != ''">
                and recipientDate between #{params.beginRecipientDate} and #{params.endRecipientDate}
            </if>
            <if test="fileNumber != null  and fileNumber != ''">and fileNumber like concat('%', #{fileNumber}, '%')</if>
        </where>
    </select>

</mapper>