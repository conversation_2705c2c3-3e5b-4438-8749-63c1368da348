<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalEntryHrMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalEntryHr" id="ClinicalEntryHrResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectClinicalEntryHrList">
        SELECT id,
               name,
               loginName,
               department,
               leaderName,
               post,
               workAddr,
               entryDate,
               entryAddr,
               hr,
               createTime,
               subject,
               currentSession,
               handlePerson
        FROM clinical_entry_hr
    </sql>
    
    <!-- 方法 -->
    <select id="queryClinicalEntryHrList" parameterType="ClinicalEntryHr" resultMap="ClinicalEntryHrResult">
        <include refid="selectClinicalEntryHrList"/>
        <where>
            <if test="handlePerson != null and handlePerson != ''">AND handlePerson like concat('%',#{handlePerson},'%')
            </if>
            <if test="hr != null and hr != ''">AND hr like concat('%',#{hr},'%')</if>
            <if test="name != null and name != ''">AND name like concat('%',#{name},'%')</if>
        </where>
        ORDER BY entryDate DESC
    </select>
</mapper>