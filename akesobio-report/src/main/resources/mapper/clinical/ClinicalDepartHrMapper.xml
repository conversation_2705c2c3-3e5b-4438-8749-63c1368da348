<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalDepartHrMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalDepartHr" id="ClinicalDepartHrResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectClinicalDepartHrList">
        SELECT id,
               name,
               loginName,
               department,
               leaderName,
               post,
               entryDate,
               leaveDate,
               realityLeaveDate,
               actualLeaveDate,
               applicant,
               createTime,
               subject,
               currentSession,
               handlePerson
        FROM clinical_depart_hr
    </sql>
    
    <!-- 方法 -->
    <select id="queryClinicalDepartHrList" parameterType="ClinicalDepartHr" resultMap="ClinicalDepartHrResult">
        <include refid="selectClinicalDepartHrList"/>
        <where>
            <if test="handlePerson != null and handlePerson != ''">AND handlePerson like
                concat('%',#{handlePerson},'%')
            </if>
            <if test="applicant != null and applicant != ''">AND applicant like concat('%',#{applicant},'%')</if>
            <if test="name != null and name != ''">AND name like concat('%',#{name},'%')</if>
            <if test="department != null and department != ''">AND department like concat('%',#{department},'%')
            </if>
            <if test="startDate != null  and startDate != ''">and CONVERT(VARCHAR(100),leaveDate,23) &gt;= #{startDate}</if>
            <if test="endDate != null  and endDate != ''">and CONVERT(VARCHAR(100),leaveDate,23) &lt;= #{endDate}</if>
            <if test="startDate1 != null  and startDate1 != ''">and CONVERT(VARCHAR(100),actualLeaveDate,23) &gt;= #{startDate1}</if>
            <if test="endDate1 != null  and endDate1 != ''">and CONVERT(VARCHAR(100),actualLeaveDate,23) &lt;= #{endDate1}</if>
        </where>
        ORDER BY leaveDate DESC
    </select>
</mapper>