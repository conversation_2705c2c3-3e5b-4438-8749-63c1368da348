<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalFolderMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalFolder" id="ClinicalFolderResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectClinicalFolderList">
        SELECT
            link,
            linkSuffix,
            id,
            applicationDate,
            oddNumber,
            name,
            projectNumber,
            projectBranch,
            matters,
            dispatchDate,
            sqPerson,
            email,
            sqPosition,
            sqDepartment,
            projectCenter,
            centerNumber,
            sqPermission,
            isPermission,
            cta,
            currentSession,
            currentProcessor,
            arrivalTime,
            documentStatus,
            lastDate,
            pmBeginDate,
            pmEndDate,
            wjjBeginDate,
            wjjEndDate,
            xxzBeginDate,
            xxzEndDate,
            craBeginDate,
            craEndDate,
            ctaBeginDate,
            ctaEndDate
        FROM clinical_folder
    </sql>
    
    <!-- 方法 -->
    <select id="queryClinicalFolderList" parameterType="ClinicalFolder" resultMap="ClinicalFolderResult">
        <include refid="selectClinicalFolderList"/>
        <where>  
            <if test="oddNumber != null and oddNumber != ''"> and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="projectNumber != null and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="projectBranch != null and projectBranch != ''"> and projectBranch like concat('%', #{projectBranch}, '%')</if>
            <if test="matters != null and matters != ''"> and matters like concat('%', #{matters}, '%')</if>
            <if test="sqPerson != null and sqPerson != ''"> and sqPerson like concat('%', #{sqPerson}, '%')</if>
            <if test="currentSession != null and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="documentStatus != null and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>

            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>