<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalMapper">

    <resultMap type="ClinicalAttendance" id="ClinicalAttendanceResult">
    </resultMap>
    <resultMap type="ClinicalAttendanceForWork" id="ClinicalAttendanceForWorkResult">
    </resultMap>
    <resultMap type="ClinicalAttendanceCheck" id="ClinicalAttendanceCheckResult">
    </resultMap>
    <resultMap type="ClinicalAttendanceShare" id="ClinicalAttendanceShareResult">
    </resultMap>

    <!--考勤数据 工时明细 汇总表1-->
    <sql id="selectClinicalAttendance">
        SELECT a.job_num         AS loginName,
               rtrim(a.username) AS name,
               a.company,
               a.department,
               a.today_date      AS workDate,
               a.work_date       AS workHours
        FROM AttendanceInfo a
                 LEFT JOIN [self_service].[DBO].hr_employee_info b
        ON a.job_num = b.job_number
    </sql>

    <select id="queryClinicalAttendance" parameterType="ClinicalAttendance" resultMap="ClinicalAttendanceResult">
        <include refid="selectClinicalAttendance"/>
        <where>
            (a.department IN ('CMCRA申报编辑','AST分析','DPT制剂','DST原液','DSP下游组','LS实验室支持小组',
            'USP上游组','AD分析组','FD制剂组','DPD纯化工艺小组','UPD细胞工艺小组','PMO项目管理',
            '稳定细胞株组','临床开发二部','临床开发三部','临床开发五部','临床开发一部','临床监查部北二区','临床监查部北三区','临床监查部北一区','临床监查部东二区','临床监查部东一区','临床监查部南二区',
            '临床监查部南一区','临床监查部西二区','临床监查部西一区','费用组','文件组','物资组','信息组',
            '临床项目三部','临床项目四部','临床项目一部','临床运行海外部','临床质量控制部','生物统计学部',
            '药政事务部','GCP QA组','R&amp;D QA组','生物统计部','数据管理部','统计编程部','临床促进部','药物警戒部',
            '药物安全交流','药物安全科学与流行病学','药物安全性检测','药物安全运行','药物安全质量标准与培训',
            '临床科学部','临床药理组','生物分析组','转化医学组','药物发现与临床前科学部','ADC研发组','单抗组',
            '单抗组&amp;生物分析组','分子与蛋白质科学组','临床前项目组','体内药理组','体外细胞组','医学发表部','临床情报与策略部')
            OR (
            b.dept_name_all LIKE concat('%','PDD工艺开发_理化组','%')
            OR b.dept_name_all LIKE concat('%','PDD工艺开发_生化组','%')
            OR b.dept_name_all LIKE concat('%','临床促进中心_北区','%')
            OR b.dept_name_all LIKE concat('%','临床促进中心_东区','%')
            OR b.dept_name_all LIKE concat('%','临床促进中心_南区','%')
            OR b.dept_name_all LIKE concat('%','临床事务部_项目组','%')
            )
            )
            <if test="startDate != null  and startDate != ''">and a.today_date &gt;= #{startDate}</if>
            <if test="endDate != null  and endDate != ''">and a.today_date &lt;= #{endDate}</if>
            <if test="name != null  and name != ''">and a.username LIKE concat('%',#{name},'%')</if>
            <if test="company != null  and company != ''">and a.company LIKE concat('%',#{company},'%')</if>
            <if test="department != null  and department != ''">and a.department LIKE concat('%',#{department},'%')</if>
        </where>
        ORDER BY a.today_date DESC
    </select>

    <!--报工数据-->
    <sql id="selectClinicalAttendance2">
        SELECT jobNumber AS loginName,
               rtrim(applicant) AS name,
               department,
               workDate,
               workHours AS workHour,
               projectNumber,
               documentStatus
        FROM clinicalHoursReport
    </sql>
    <!-- 报工数据 工时明细 -->
    <select id="queryClinicalHoursReport" parameterType="ClinicalAttendance"
            resultMap="ClinicalAttendanceForWorkResult">
        <include refid="selectClinicalAttendance2"/>
        <where>
            <if test="startDate != null  and startDate != ''">and workDate &gt;= #{startDate}</if>
            <if test="endDate != null  and endDate != ''">and workDate &lt;= #{endDate}</if>
            <if test="name != null  and name != ''">and applicant LIKE concat('%',#{name},'%')</if>
        </where>
        ORDER BY workDate DESC
    </select>
    <!-- 报工数据 汇总表1 -->
    <select id="queryClinicalHoursReportSummary" parameterType="ClinicalAttendance"
            resultMap="ClinicalAttendanceForWorkResult">
        <include refid="selectClinicalAttendance2"/>
        <where>
            (documentStatus LIKE concat('%','结束','%') or documentStatus LIKE concat('%','待审','%'))
            <if test="startDate != null  and startDate != ''">and workDate &gt;= #{startDate}</if>
            <if test="endDate != null  and endDate != ''">and workDate &lt;= #{endDate}</if>
            <if test="name != null  and name != ''">and applicant LIKE concat('%',#{name},'%')</if>
        </where>
        ORDER BY workDate DESC
    </select>

    <!--考勤数据 研发核算 汇总表1-->
    <sql id="selectClinicalAttendanceCheck">
        SELECT rtrim(a.username) AS name,
               MAX(a.job_num)    AS loginName,
               MAX(a.company) AS company,
               MAX(a.department) AS department,
               SUM(a.work_date)  AS workHours
        FROM AttendanceInfo a
                 LEFT JOIN [self_service].[DBO].hr_employee_info b
        ON a.job_num = b.job_number
    </sql>

    <select id="queryClinicalAttendanceCheck" parameterType="ClinicalAttendance"
            resultMap="ClinicalAttendanceCheckResult">
        <include refid="selectClinicalAttendanceCheck"/>
        <where>
            (a.department IN ('CMCRA申报编辑','AST分析','DPT制剂','DST原液','DSP下游组','LS实验室支持小组',
            'USP上游组','AD分析组','FD制剂组','DPD纯化工艺小组','UPD细胞工艺小组','PMO项目管理',
            '稳定细胞株组','临床开发二部','临床开发三部','临床开发五部','临床开发一部','临床监查部北二区','临床监查部北三区','临床监查部北一区','临床监查部东二区','临床监查部东一区','临床监查部南二区',
            '临床监查部南一区','临床监查部西二区','临床监查部西一区','费用组','文件组','物资组','信息组',
            '临床项目三部','临床项目四部','临床项目一部','临床运行海外部','临床质量控制部','生物统计学部',
            '药政事务部','GCP QA组','R&amp;D QA组','生物统计部','数据管理部','统计编程部','临床促进部','药物警戒部',
            '药物安全交流','药物安全科学与流行病学','药物安全性检测','药物安全运行','药物安全质量标准与培训',
            '临床科学部','临床药理组','生物分析组','转化医学组','药物发现与临床前科学部','ADC研发组','单抗组',
            '单抗组&amp;生物分析组','分子与蛋白质科学组','临床前项目组','体内药理组','体外细胞组','医学发表部','临床情报与策略部','稽查一组','稽查二组','稽查三组','质量体系组','疾病生物学组')
            OR (
            b.dept_name_all LIKE concat('%','PDD工艺开发_理化组','%')
            OR b.dept_name_all LIKE concat('%','PDD工艺开发_生化组','%')
            OR b.dept_name_all LIKE concat('%','临床促进中心_北区','%')
            OR b.dept_name_all LIKE concat('%','临床促进中心_东区','%')
            OR b.dept_name_all LIKE concat('%','临床促进中心_南区','%')
            OR b.dept_name_all LIKE concat('%','临床事务部_项目组','%')
            )
            )
            <if test="startDate != null  and startDate != ''">and a.today_date &gt;= #{startDate}</if>
            <if test="endDate != null  and endDate != ''">and a.today_date &lt;= #{endDate}</if>
            <if test="name != null  and name != ''">and a.username LIKE concat('%',#{name},'%')</if>
            <if test="company != null  and company != ''">and a.company LIKE concat('%',#{company},'%')</if>
        </where>
        GROUP BY a.username
    </select>

    <!--报工数据 研发核算 汇总表1-->
    <sql id="selectClinicalAttendanceForWork">
        SELECT rtrim(applicant) AS name,
               SUM(workHours) AS workHour
        FROM clinicalHoursReport
    </sql>

    <select id="queryClinicalAttendanceForWork" parameterType="ClinicalAttendance"
            resultMap="ClinicalAttendanceForWorkResult">
        <include refid="selectClinicalAttendanceForWork"/>
        <where>
            (documentStatus LIKE concat('%','结束','%') or documentStatus LIKE concat('%','待审','%'))
            <if test="startDate != null  and startDate != ''">and workDate &gt;= #{startDate}</if>
            <if test="endDate != null  and endDate != ''">and workDate &lt;= #{endDate}</if>
            <if test="name != null  and name != ''">and applicant LIKE concat('%',#{name},'%')</if>
        </where>
        GROUP BY applicant
    </select>

    <!-- 报工数据 核算组分摊 -->
    <sql id="selectClinicalAttendanceShare">
        SELECT jobNumber AS loginName,
               applicant AS name,
               department,
               workDate,
               workHours AS workHour,
               projectNumber,
               documentStatus
        FROM clinicalHoursReport
    </sql>

    <select id="queryClinicalAttendanceShare" parameterType="ClinicalAttendance"
            resultMap="ClinicalAttendanceShareResult">
        <include refid="selectClinicalAttendanceShare"/>
        <where>
            <if test="startDate != null  and startDate != ''">and workDate &gt;= #{startDate}</if>
            <if test="endDate != null  and endDate != ''">and workDate &lt;= #{endDate}</if>
            <if test="name != null  and name != ''">and applicant LIKE concat('%',#{name},'%')</if>
            <if test="department != null  and department != ''">and department LIKE concat('%',#{department},'%')</if>
        </where>
        ORDER BY workDate DESC
    </select>
</mapper>