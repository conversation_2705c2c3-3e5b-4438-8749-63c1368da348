<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.WeeklyReportMapper">
    
    <resultMap type="WeeklyReport" id="WeeklyReportResult">
        <result property="singleNumber"    column="singleNumber"    />
        <result property="reportingDate"    column="reportingDate"    />
        <result property="submitter"    column="submitter"    />
        <result property="firstDepartment"    column="firstDepartment"    />
        <result property="department"    column="department"    />
        <result property="jobTitle"    column="jobTitle"    />
        <result property="workingDate"    column="workingDate"    />
        <result property="directLeadership"    column="directLeadership"    />
        <result property="departmentHead"    column="departmentHead"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="workingHours"    column="workingHours"    />
        <result property="workContent"    column="workContent"    />
        <result property="processStatus"    column="processStatus"    />
    </resultMap>

    <sql id="selectWeeklyReportVo">
        select singleNumber, reportingDate, submitter, firstDepartment, department, jobTitle, workingDate, directLeadership, departmentHead, projectNumber, workingHours, workContent, processStatus from weeklyReport
    </sql>

    <select id="selectWeeklyReportList" parameterType="WeeklyReport" resultMap="WeeklyReportResult">
        <include refid="selectWeeklyReportVo"/>
        <where>  
            <if test="singleNumber != null  and singleNumber != ''"> and singleNumber like concat('%', #{singleNumber}, '%')</if>
            <if test="params.beginReportingDate != null and params.beginReportingDate != '' and params.endReportingDate != null and params.endReportingDate != ''"> and reportingDate between #{params.beginReportingDate} and #{params.endReportingDate}</if>
            <if test="submitter != null  and submitter != ''"> and submitter like concat('%', #{submitter}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="jobTitle != null  and jobTitle != ''"> and jobTitle = #{jobTitle}</if>
            <if test="params.beginWorkingDate != null and params.beginWorkingDate != '' and params.endWorkingDate != null and params.endWorkingDate != ''"> and workingDate between #{params.beginWorkingDate} and #{params.endWorkingDate}</if>
            <if test="directLeadership != null  and directLeadership != ''"> and directLeadership = #{directLeadership}</if>
            <if test="departmentHead != null  and departmentHead != ''"> and departmentHead = #{departmentHead}</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="workingHours != null  and workingHours != ''"> and workingHours = #{workingHours}</if>
            <if test="workContent != null  and workContent != ''"> and workContent = #{workContent}</if>
            <if test="processStatus != null  and processStatus != ''"> and processStatus like concat('%', #{processStatus}, '%')</if>
            <if test="objectList !=null and objectList.size()>0 ">
                and firstDepartment in
                <foreach collection="objectList"  item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by reportingDate desc
    </select>
</mapper>