<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.CompanyAndCodeMapper">
    <!-- 实体类 去掉Mapper后缀 -->
    <resultMap type="CompanyAndCode" id="CompanyAndCodeResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectCompanyAndCodeList">
        SELECT id, company, company_code, sort
        FROM company_and_code
    </sql>

    <!-- 方法 -->
    <select id="queryCompanyAndCode" parameterType="CompanyAndCode" resultMap="CompanyAndCodeResult">
        <include refid="selectCompanyAndCodeList"/>
        <where>
            <if test="company != null  and company != ''">and company like concat('%', #{company}, '%')</if>
            <if test="companyCode != null  and companyCode != ''">and company_code like concat('%', #{companyCode},
                '%')
            </if>
        </where>
    </select>

    <insert id="insertCompanyAndCode" parameterType="CompanyAndCode">
        insert into company_and_code (
        <if test="sort != null and sort != ''">sort,</if>
        <if test="companyCode != null and companyCode != ''">company_code,</if>
        <if test="company != null and company != ''">company</if>
        ) values (
        <if test="sort != null and sort != ''">#{sort},</if>
        <if test="companyCode != null and companyCode != ''">#{companyCode},</if>
        <if test="company != null and company != ''">#{company}</if>
        )
    </insert>
</mapper>