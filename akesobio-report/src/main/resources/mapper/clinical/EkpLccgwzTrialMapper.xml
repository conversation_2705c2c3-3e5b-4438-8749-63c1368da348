<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.EkpLccgwzTrialMapper">
    
    <resultMap type="EkpLccgwzTrial" id="EkpLccgwzTrialResult">
        <result property="formName"    column="form_name"    />
        <result property="basicInfo"    column="basic_info"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="projectNumber"    column="project_number"    />
        <result property="centerCode"    column="center_code"    />
        <result property="centerName"    column="center_name"    />
        <result property="technicalOffices"    column="technical_offices"    />
        <result property="principalInvestigator"    column="principal_investigator"    />
        <result property="applicant"    column="applicant"    />
        <result property="applicationDate"    column="application_date"    />
        <result property="signatory"    column="signatory"    />
        <result property="city"    column="city"    />
        <result property="recipientInfo"    column="recipient_info"    />
        <result property="materialName"    column="material_name"    />
        <result property="attribute"    column="attribute"    />
        <result property="clinicalMaterialBrand"    column="clinical_material_brand"    />
        <result property="model"    column="model"    />
        <result property="specifications"    column="specifications"    />
        <result property="equipmentNo"    column="equipment_no"    />
        <result property="productionDate"    column="production_date"    />
        <result property="number"    column="number"    />
        <result property="expectedDeliveryDate"    column="expected_delivery_date"    />
        <result property="modesOfSupply"    column="modes_of_supply"    />
        <result property="logisticsInformation"    column="logistics_information"    />
        <result property="budgetUnitPrice"    column="budget_unit_price"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="id"    column="id"    />
        <result property="remarks"    column="remarks"    />
        <result property="docStatus"    column="doc_status"    />
        <result property="flowEndDate"    column="flow_end_date"    />
        <result property="currentNode"    column="current_node"    />
        <result property="arrivalTime"    column="arrival_time"    />
        <result property="nodeName"    column="node_name"    />
        <result property="nodePerson"    column="node_person"    />
        <result property="currentHandler"    column="current_handler"    />
        <result property="recipient"    column="recipient"    />
        <result property="jsPhone"    column="jsPhone"    />
    </resultMap>

    <sql id="selectEkpLccgwzTrialVo">
        select form_name,
               basic_info,
               serial_number,
               project_number,
               center_code,
               center_name,
               technical_offices,
               principal_investigator,
               applicant,
               application_date,
               signatory,
               city,
               recipient_info,
               material_name,
               attribute,
               clinical_material_brand,
               model,
               specifications,
               equipment_no,
               production_date,
               number,
               expected_delivery_date,
               modes_of_supply,
               logistics_information,
               budget_unit_price,
               total_amount,
               id,
               remarks,
               doc_status,
               flow_end_date,
               current_node,
               arrival_time,
               node_name,
               node_person,
               current_handler,
               mailing_time,
               modes_of_supply_handler,
               mailing_time2,
               modes_of_supply_handler2,
               mailing_time3,
               modes_of_supply_handler3,
               mailing_time4,
               modes_of_supply_handler4,
               recipient,
               jsPhone
        from ekp_lccgwz_trial
    </sql>

    <select id="selectEkpLccgwzTrialList" parameterType="EkpLccgwzTrial" resultMap="EkpLccgwzTrialResult">
        <include refid="selectEkpLccgwzTrialVo"/>
        <where>  
            <if test="formName != null  and formName != ''"> and form_name like concat('%', #{formName}, '%')</if>
            <if test="basicInfo != null  and basicInfo != ''"> and basic_info like concat('%', #{basicInfo}, '%')</if>
            <if test="serialNumber != null  and serialNumber != ''"> and serial_number like concat('%', #{serialNumber}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and project_number like concat('%', #{projectNumber}, '%')</if>
            <if test="centerCode != null  and centerCode != ''"> and center_code like concat('%', #{centerCode}, '%')</if>
            <if test="centerName != null  and centerName != ''"> and center_name like concat('%', #{centerName}, '%')</if>
            <if test="technicalOffices != null  and technicalOffices != ''"> and technical_offices like concat('%', #{technicalOffices}, '%')</if>
            <if test="principalInvestigator != null  and principalInvestigator != ''"> and principal_investigator like concat('%', #{principalInvestigator}, '%')</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
<!--            <if test="applicationDate != null "> and application_date = #{applicationDate}</if>-->
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and application_date between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="signatory != null  and signatory != ''"> and signatory like concat('%', #{signatory}, '%')</if>
            <if test="city != null  and city != ''"> and city like concat('%', #{city}, '%')</if>
            <if test="recipientInfo != null  and recipientInfo != ''"> and recipient_info like concat('%', #{recipientInfo}, '%')</if>
            <if test="materialName != null  and materialName != ''"> and material_name like concat('%', #{materialName}, '%')</if>
            <if test="attribute != null  and attribute != ''"> and attribute like concat('%', #{attribute}, '%')</if>
            <if test="clinicalMaterialBrand != null  and clinicalMaterialBrand != ''"> and clinical_material_brand like concat('%', #{clinicalMaterialBrand}, '%')</if>
            <if test="model != null  and model != ''"> and model like concat('%', #{model}, '%')</if>
            <if test="specifications != null  and specifications != ''"> and specifications like concat('%', #{specifications}, '%')</if>
            <if test="equipmentNo != null  and equipmentNo != ''"> and equipment_no like concat('%', #{equipmentNo}, '%')</if>
<!--            <if test="productionDate != null  and productionDate != ''"> and production_date = #{productionDate}</if>-->
            <if test="params.beginProductionDate != null and params.beginProductionDate != '' and params.endProductionDate != null and params.endProductionDate != ''"> and production_date between #{params.beginProductionDate} and #{params.endProductionDate}</if>
            <if test="number != null  and number != ''"> and number like concat('%', #{number}, '%')</if>
            <if test="params.beginExpectedDeliveryDate != null and params.beginExpectedDeliveryDate != '' and params.endExpectedDeliveryDate != null and params.endExpectedDeliveryDate != ''"> and expected_delivery_date between #{params.beginExpectedDeliveryDate} and #{params.endExpectedDeliveryDate}</if>
            <!--            <if test="expectedDeliveryDate != null  and expectedDeliveryDate != ''"> and expected_delivery_date = #{expectedDeliveryDate}</if>-->
            <if test="modesOfSupply != null  and modesOfSupply != ''"> and modes_of_supply like concat('%', #{modesOfSupply}, '%')</if>
            <if test="logisticsInformation != null  and logisticsInformation != ''"> and logistics_information like concat('%', #{logisticsInformation}, '%')</if>
            <if test="budgetUnitPrice != null  and budgetUnitPrice != ''"> and budget_unit_price like concat('%', #{budgetUnitPrice}, '%')</if>
            <if test="totalAmount != null  and totalAmount != ''"> and total_amount like concat('%', #{totalAmount}, '%')</if>
            <if test="remarks != null  and remarks != ''"> and remarks like concat('%', #{remarks}, '%')</if>
            <if test="docStatus != null  and docStatus != ''"> and doc_status like concat('%', #{docStatus}, '%')</if>
            <if test="params.beginFlowEndDate != null and params.beginFlowEndDate != '' and params.endFlowEndDate != null and params.endFlowEndDate != ''"> and flow_end_date between #{params.beginFlowEndDate} and #{params.endFlowEndDate}</if>
<!--            <if test="flowEndDate != null  and flowEndDate != ''"> and flow_end_date = #{flowEndDate}</if>-->
            <if test="currentNode != null  and currentNode != ''"> and current_node like concat('%', #{currentNode}, '%')</if>
            <if test="currentHandler != null  and currentHandler != ''"> and current_handler like concat('%', #{currentHandler}, '%')</if>
            <if test="nodeName != null  and nodeName != ''"> and node_name like concat('%', #{nodeName}, '%')</if>
            <if test="nodePerson != null  and nodePerson != ''"> and node_person like concat('%', #{nodePerson}, '%')</if>
        </where>
        ORDER BY application_date DESC
    </select>
    
    <select id="selectEkpLccgwzTrialById" parameterType="String" resultMap="EkpLccgwzTrialResult">
        <include refid="selectEkpLccgwzTrialVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertEkpLccgwzTrial" parameterType="EkpLccgwzTrial">
        insert into ekp_lccgwz_trial
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formName != null">form_name,</if>
            <if test="basicInfo != null">basic_info,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="projectNumber != null">project_number,</if>
            <if test="centerCode != null">center_code,</if>
            <if test="centerName != null">center_name,</if>
            <if test="technicalOffices != null">technical_offices,</if>
            <if test="principalInvestigator != null">principal_investigator,</if>
            <if test="applicant != null">applicant,</if>
            <if test="applicationDate != null">application_date,</if>
            <if test="signatory != null">signatory,</if>
            <if test="city != null">city,</if>
            <if test="recipientInfo != null">recipient_info,</if>
            <if test="materialName != null">material_name,</if>
            <if test="attribute != null">attribute,</if>
            <if test="clinicalMaterialBrand != null">clinical_material_brand,</if>
            <if test="model != null">model,</if>
            <if test="specifications != null">specifications,</if>
            <if test="equipmentNo != null">equipment_no,</if>
            <if test="productionDate != null">production_date,</if>
            <if test="number != null">number,</if>
            <if test="expectedDeliveryDate != null">expected_delivery_date,</if>
            <if test="modesOfSupply != null">modes_of_supply,</if>
            <if test="logisticsInformation != null">logistics_information,</if>
            <if test="budgetUnitPrice != null">budget_unit_price,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="id != null">id,</if>
            <if test="remarks != null">remarks,</if>
            <if test="docStatus != null">doc_status,</if>
            <if test="flowEndDate != null">flow_end_date,</if>
            <if test="currentNode != null">current_node,</if>
            <if test="currentHandler != null">current_handler,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formName != null">#{formName},</if>
            <if test="basicInfo != null">#{basicInfo},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="centerCode != null">#{centerCode},</if>
            <if test="centerName != null">#{centerName},</if>
            <if test="technicalOffices != null">#{technicalOffices},</if>
            <if test="principalInvestigator != null">#{principalInvestigator},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="applicationDate != null">#{applicationDate},</if>
            <if test="signatory != null">#{signatory},</if>
            <if test="city != null">#{city},</if>
            <if test="recipientInfo != null">#{recipientInfo},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="attribute != null">#{attribute},</if>
            <if test="clinicalMaterialBrand != null">#{clinicalMaterialBrand},</if>
            <if test="model != null">#{model},</if>
            <if test="specifications != null">#{specifications},</if>
            <if test="equipmentNo != null">#{equipmentNo},</if>
            <if test="productionDate != null">#{productionDate},</if>
            <if test="number != null">#{number},</if>
            <if test="expectedDeliveryDate != null">#{expectedDeliveryDate},</if>
            <if test="modesOfSupply != null">#{modesOfSupply},</if>
            <if test="logisticsInformation != null">#{logisticsInformation},</if>
            <if test="budgetUnitPrice != null">#{budgetUnitPrice},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="id != null">#{id},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="docStatus != null">#{docStatus},</if>
            <if test="flowEndDate != null">#{flowEndDate},</if>
            <if test="currentNode != null">#{currentNode},</if>
            <if test="currentHandler != null">#{currentHandler},</if>
         </trim>
    </insert>

    <update id="updateEkpLccgwzTrial" parameterType="EkpLccgwzTrial">
        update ekp_lccgwz_trial
        <trim prefix="SET" suffixOverrides=",">
            <if test="formName != null">form_name = #{formName},</if>
            <if test="basicInfo != null">basic_info = #{basicInfo},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="projectNumber != null">project_number = #{projectNumber},</if>
            <if test="centerCode != null">center_code = #{centerCode},</if>
            <if test="centerName != null">center_name = #{centerName},</if>
            <if test="technicalOffices != null">technical_offices = #{technicalOffices},</if>
            <if test="principalInvestigator != null">principal_investigator = #{principalInvestigator},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="applicationDate != null">application_date = #{applicationDate},</if>
            <if test="signatory != null">signatory = #{signatory},</if>
            <if test="city != null">city = #{city},</if>
            <if test="recipientInfo != null">recipient_info = #{recipientInfo},</if>
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="attribute != null">attribute = #{attribute},</if>
            <if test="clinicalMaterialBrand != null">clinical_material_brand = #{clinicalMaterialBrand},</if>
            <if test="model != null">model = #{model},</if>
            <if test="specifications != null">specifications = #{specifications},</if>
            <if test="equipmentNo != null">equipment_no = #{equipmentNo},</if>
            <if test="productionDate != null">production_date = #{productionDate},</if>
            <if test="number != null">number = #{number},</if>
            <if test="expectedDeliveryDate != null">expected_delivery_date = #{expectedDeliveryDate},</if>
            <if test="modesOfSupply != null">modes_of_supply = #{modesOfSupply},</if>
            <if test="logisticsInformation != null">logistics_information = #{logisticsInformation},</if>
            <if test="budgetUnitPrice != null">budget_unit_price = #{budgetUnitPrice},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="docStatus != null">doc_status = #{docStatus},</if>
            <if test="flowEndDate != null">flow_end_date = #{flowEndDate},</if>
            <if test="currentNode != null">current_node = #{currentNode},</if>
            <if test="currentHandler != null">current_handler = #{currentHandler},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEkpLccgwzTrialById" parameterType="String">
        delete from ekp_lccgwz_trial where id = #{id}
    </delete>

    <delete id="deleteEkpLccgwzTrialByIds" parameterType="String">
        delete from ekp_lccgwz_trial where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>