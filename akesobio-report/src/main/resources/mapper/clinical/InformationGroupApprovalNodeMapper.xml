<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.InformationGroupApprovalNodeMapper">
    <!-- 实体类  -->
    <resultMap type="InformationGroupApprovalNode" id="InformationGroupApprovalNodeResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectInformationGroupApprovalNodeList">
        SELECT
            id,
            filePath,
            oddNumber,
            jobNumber,
            name,
            personId,
            department,
            zsName,
            jobNumbers,
            xxzName,
            tableName,
            nodeName,
            applicationDate,
            nodeDate,
            nodePassDate,
            operationType,
            documentStatus
        FROM information_group_approval_node
    </sql>
    
    <!-- 方法 -->
    <select id="queryInformationGroupApprovalNodeList" parameterType="InformationGroupApprovalNode" resultMap="InformationGroupApprovalNodeResult">
        <include refid="selectInformationGroupApprovalNodeList"/>
        <where>  
            <if test="oddNumber != null and oddNumber != ''"> and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="jobNumber != null and jobNumber != ''"> and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="department != null and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="jobNumbers != null and jobNumbers != ''"> and jobNumbers like concat('%', #{jobNumbers}, '%')</if>
            <if test="xxzName != null and xxzName != ''"> and xxzName like concat('%', #{xxzName}, '%')</if>
            <if test="tableName != null and tableName != ''"> and tableName like concat('%', #{tableName}, '%')</if>
            <if test="nodeName != null and nodeName != ''"> and nodeName like concat('%', #{nodeName}, '%')</if>
            <if test="operationType != null and operationType != ''"> and operationType like concat('%', #{operationType}, '%')</if>
            <if test="documentStatus != null and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>

            <if test="startDate != null and startDate != ''">and CONVERT(DATE,applicationDate,120) &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and CONVERT(DATE,applicationDate,120) &lt;= #{endDate}</if>
            <if test="startDate1 != null and startDate1 != ''">and CONVERT(DATE,nodeDate,120) &gt;= #{startDate1}</if>
            <if test="endDate1 != null and endDate1 != ''">and CONVERT(DATE,nodeDate,120) &lt;= #{endDate1}</if>
            <if test="startDate2 != null and startDate2 != ''">and CONVERT(DATE,nodePassDate,120) &gt;= #{startDate2}</if>
            <if test="endDate2 != null and endDate2 != ''">and CONVERT(DATE,nodePassDate,120) &lt;= #{endDate2}</if>
        </where>
        order by applicationDate desc
    </select>
</mapper>