<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ReimbursementApplicationMapper">
    <resultMap type="ReimbursementApplication" id="ReimbursementApplicationResult">
        <result property="expenseApplicationNumber"    column="expenseApplicationNumber"    />
        <result property="applicationSubject"    column="applicationSubject"    />
        <result property="applicationType"    column="applicationType"    />
        <result property="secondaryType"    column="secondaryType"    />
        <result property="applicationsAmount"    column="applicationsAmount"    />
        <result property="detailedDescription"    column="detailedDescription"    />
    </resultMap>

    <select id="selectReimbursementApplication" parameterType="ReimbursementApplication" resultMap="ReimbursementApplicationResult">
        SELECT
        expenseApplicationNumber,
        applicationSubject,
        applicationType,
        secondaryType,
        applicationsAmount,
        detailedDescription
        FROM
        (
        SELECT--业务招待费
        a.fd_danHao 'expenseApplicationNumber',--OA费用申请编号
        a.fd_shenQingZhuTi 'applicationSubject',--申请主题
        NULL 'applicationType',--申请类型
        NULL 'secondaryType',--二级类型
        a.fd_jinEHuiZong 'applicationsAmount',--申请金额
        NULL 'detailedDescription' --具体描述

        FROM
        ekp_ywzdf a
        WHERE
        a.fd_danHao!= '' UNION ALL
        SELECT--FD09其他费用申请
        a.fd_danHao 'expenseApplicationNumber',--OA费用申请编号
        NULL 'applicationSubject',--申请主题
        NULL 'applicationType',--申请类型
        NULL 'secondaryType',--二级类型
        a.fd_shenQingJinEHeJi 'applicationsAmount',--申请金额
        NULL 'detailedDescription' --具体描述

        FROM
        ekp_qtfysq a
        WHERE
        a.fd_danHao!= ''
        ) b
        <where>
            <if test="expenseApplicationNumber != null  and expenseApplicationNumber != ''"> and expenseApplicationNumber = #{expenseApplicationNumber}</if>
        </where>
    </select>
</mapper>