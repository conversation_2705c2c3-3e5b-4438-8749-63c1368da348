<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.EmploymentNoticeClinicalMapper">
    <!-- 实体类  -->
    <resultMap type="EmploymentNoticeClinical" id="EmploymentNoticeClinicalResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectEmploymentNoticeClinicalList">
        SELECT
            id,
            filePath,
            suffix,
            name,
            rank,
            department,
            sex,
            employeeType,
            officeLocation,
            reportLocation,
            serviceDate,
            phone,
            position,
            manager,
            trialPeriod,
            jobType
        FROM employment_notice_clinical
    </sql>
    
    <!-- 方法 -->
    <select id="queryEmploymentNoticeClinicalList" parameterType="EmploymentNoticeClinical" resultMap="EmploymentNoticeClinicalResult">
        <include refid="selectEmploymentNoticeClinicalList"/>
        <where>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="rank != null and rank != ''"> and rank like concat('%', #{rank}, '%')</if>
            <if test="department != null and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="sex != null and sex != ''"> and sex like concat('%', #{sex}, '%')</if>
            <if test="employeeType != null and employeeType != ''"> and employeeType like concat('%', #{employeeType}, '%')</if>
            <if test="officeLocation != null and officeLocation != ''"> and officeLocation like concat('%', #{officeLocation}, '%')</if>
            <if test="reportLocation != null and reportLocation != ''"> and reportLocation like concat('%', #{reportLocation}, '%')</if>
            <if test="phone != null and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="position != null and position != ''"> and position like concat('%', #{position}, '%')</if>
            <if test="manager != null and manager != ''"> and manager like concat('%', #{manager}, '%')</if>
            <if test="trialPeriod != null and trialPeriod != ''"> and trialPeriod like concat('%', #{trialPeriod}, '%')</if>
            <if test="jobType != null and jobType != ''"> and jobType like concat('%', #{jobType}, '%')</if>

            <if test="startDate != null and startDate != ''">and CONVERT(DATE,serviceDate,120) &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and CONVERT(DATE,serviceDate,120) &lt;= #{endDate}</if>
        </where>
        ORDER BY serviceDate DESC
    </select>
</mapper>