<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.FD09OtherExpenseApplicationMapper">
    <resultMap type="FD09OtherExpenseApplication" id="FD09OtherExpenseApplicationResult">
        <result property="fd09ID" column="fd09ID"/>
        <result property="fd09Number" column="fd09Number"/>
        <result property="fd09Name" column="fd09Name"/>
        <result property="fd09ExpenseType" column="fd09ExpenseType"/>
        <result property="fd09CustomerName" column="fd09CustomerName"/>
        <result property="fd09SubjectID" column="fd09SubjectID"/>
        <result property="fd09VisitNumber" column="fd09VisitNumber"/>
        <result property="fd09AdvancePayment" column="fd09AdvancePayment"/>
        <result property="fd09QuanrongPayment" column="fd09QuanrongPayment"/>
        <result property="fd09ProtocolPayment" column="fd09ProtocolPayment"/>
        <result property="fd09CostDescription" column="fd09CostDescription"/>
        <result property="fd09ApplicationsAmount" column="fd09ApplicationsAmount"/>
    </resultMap>
    <select id="selectFD09" parameterType="FD09OtherExpenseApplication"
            resultMap="FD09OtherExpenseApplicationResult">
        SELECT
        fd09ID,
        fd09Number,
        fd09Name,
        fd09ExpenseType,
        fd09CustomerName,
        fd09SubjectID,
        fd09VisitNumber,
        fd09AdvancePayment,
        fd09QuanrongPayment,
        fd09ProtocolPayment,
        fd09CostDescription,
        fd09ApplicationsAmount
        FROM
        (
        SELECT
        a.fd_id 'fd09ID',
        a.fd_danHao 'fd09Number',
        'FD09其他费用申请' 'fd09Name',
        b.fd_feiYongLeiXing 'fd09ExpenseType',
        b.fd_keHuXingMing 'fd09CustomerName',
        b.fd_shouShiZheBianHao 'fd09SubjectID',
        b.fd_fangShiBianHao 'fd09VisitNumber',
        b.fd_shiFuGongSiDianFu 'fd09AdvancePayment',
        b.fd_shiFuJingQuanRongZhiFu 'fd09QuanrongPayment',
        b.fd_shiFuJingYiYuanXieYiZhiFu 'fd09ProtocolPayment',
        b.fd_feiYongMiaoShu 'fd09CostDescription',
        b.fd_shenQingJinE 'fd09ApplicationsAmount'
        FROM
        ekp_qtfysq a
        LEFT JOIN ekp_qtfysq_add b ON b.fd_parent_id= a.fd_id
        ) b
        <where>
            <if test="fd09Number != null  and fd09Number != ''">and fd09Number = #{fd09Number}</if>
        </where>
    </select>
</mapper>