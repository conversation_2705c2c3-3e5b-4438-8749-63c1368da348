<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ProcessManagementMapper">
    <resultMap type="ProcessManagement" id="ProcessManagementResult">
        <result property="fdId"    column="fdId"    />
        <result property="oddNumbers"    column="oddNumbers"    />
    </resultMap>

    <select id="selectProcessManagement" parameterType="ProcessManagement" resultMap="ProcessManagementResult">
        SELECT
        fdId,
        oddNumbers
        FROM
        ( SELECT a.fd_id 'fdId', a.fd_number 'oddNumbers' FROM km_review_main a ) b
        <where>
            <if test="fdId != null  and fdId != ''"> and fdId = #{fdId}</if>
        </where>
    </select>
</mapper>