<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalTrialBatchMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalTrialBatch" id="ClinicalTrialBatchResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectClinicalTrialBatchList">
        SELECT id,
               nodeId,
               nodeName,
               name,
               department,
               actionKey,
               documentStatus,
               currentNodeName,
               createTime,
               subject
        FROM clinical_trial_batch
    </sql>

    <!-- 方法 -->
    <select id="queryClinicalTrialBatchList" parameterType="ClinicalTrialBatch" resultMap="ClinicalTrialBatchResult">
        <include refid="selectClinicalTrialBatchList"/>
        <where>
            <if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="documentStatus != null and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="subject != null and subject != ''">and subject like concat('%', #{subject}, '%')</if>
            <if test="startDate != null and startDate != ''">and createTime &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and createTime &lt;= #{endDate}</if>
        </where>
        ORDER BY createTime DESC
    </select>
</mapper>