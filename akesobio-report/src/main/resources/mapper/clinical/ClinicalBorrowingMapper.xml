<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalBorrowingMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalBorrowing" id="ClinicalBorrowingResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectClinicalBorrowingList">
        SELECT id,
               oddNumber,
               applicationDate,
               applicationName,
               jobNumber,
               department,
               position,
               pmName,
               swName,
               projectNumber,
               centerNumber,
               centerName,
               fileName,
               versionNumber,
               versionDate,
               fileType,
               borrowingReason,
               jyDate,
               jyzzDate,
               ghDate
        FROM clinical_borrowing
    </sql>

    <!-- 方法 -->
    <select id="queryClinicalBorrowingList" parameterType="ClinicalBorrowing" resultMap="ClinicalBorrowingResult">
        <include refid="selectClinicalBorrowingList"/>
        <where>
            <if test="applicationName != null and applicationName != ''">and applicationName like concat('%',
                #{applicationName}, '%')
            </if>
            <if test="oddNumber != null and oddNumber != ''">and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="jobNumber != null and jobNumber != ''">and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="department != null and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="projectNumber != null and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="centerNumber != null and centerNumber != ''">and centerNumber like concat('%', #{centerNumber},
                '%')
            </if>
            <if test="centerName != null and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>