<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.TravelOutMapper">

    <resultMap type="TravelOut" id="TravelOutResult">
        <result property="applicantName" column="applicantName"/>
        <result property="applicationDate" column="applicationDate"/>
        <result property="department" column="department"/>
        <result property="startTime" column="startTime"/>
        <result property="endTime" column="endTime"/>
        <result property="place" column="place"/>
        <result property="reason" column="reason"/>
    </resultMap>

    <select id="selectTravelOutList" parameterType="TravelOut" resultMap="TravelOutResult">
        select applicantName, applicationDate, department, startTime, endTime, place, reason
        from (SELECT a.username 'applicantName',a.today_date 'applicationDate',
        a.department 'department', a.start_workout_date 'startTime', a.end_workout_date 'endTime', a.destination
        'place', a.reason 'reason'
        FROM WorkOut a
        WHERE a.department LIKE '%临床项目三部%'
        UNION ALL
        SELECT a.username 'applicantName', a.today_date 'applicationDate', a.department
        'department', a.start_businesstrip_date 'startTime', a.end_businesstrip_date 'endTime', a.destination 'place',
        a.reason 'reason'
        FROM BusinessTrip a
        WHERE a.department LIKE '%临床项目三部%') b
        <where>
            <if test="applicantName != null  and applicantName != ''">and applicantName like concat('%',
                #{applicantName}, '%')
            </if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''">
                and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}
            </if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''">
                and startTime between #{params.beginStartTime} and #{params.endStartTime}
            </if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''">
                and endTime between #{params.beginEndTime} and #{params.endEndTime}
            </if>
            <if test="place != null  and place != ''">and place like concat('%', #{place}, '%')</if>
            <if test="reason != null  and reason != ''">and reason like concat('%', #{reason}, '%')</if>
        </where>
    </select>

</mapper>