<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.TravelApplicationMapper">
    <resultMap type="TravelApplication" id="TravelApplicationResult">
        <result property="travelApplicationNumber" column="travelApplicationNumber"/>
        <result property="travelDepartureDate" column="travelDepartureDate"/>
        <result property="travelReturnDate" column="travelReturnDate"/>
        <result property="businessDay" column="businessDay"/>
        <result property="colleagues" column="colleagues"/>
    </resultMap>

    <select id="selectTravelApplication" parameterType="TravelApplication" resultMap="TravelApplicationResult">
        SELECT--差旅申请
        travelApplicationNumber,
        travelDepartureDate,
        travelReturnDate,
        businessDay,
        colleagues
        FROM
        (
        SELECT
        a.fd_danHao 'travelApplicationNumber',--差旅申请单号
        b.fd_kaiShiRiQi 'travelDepartureDate',--出发日期
        b.fd_jieShuRiQi 'travelReturnDate',--返回日期
        NULL 'businessDay',--出差天数
        NULL 'colleagues' --同行人员
        FROM
        ekp_clsq_2024 a
        LEFT JOIN ekp_clsq_detail_2024 b ON b.fd_parent_id= a.fd_id
        --LEFT JOIN ekporg_tongXingRenYuan_1 e ON e.fd_parent_id= a.fd_id
        --LEFT JOIN sys_org_person f ON f.fd_id= e.sys_org_person_id
        WHERE
        a.fd_danHao!= ''
        ) b
        <where>
            <if test="travelApplicationNumber != null  and travelApplicationNumber != ''">and travelApplicationNumber = #{travelApplicationNumber}</if>
        </where>
    </select>
</mapper>