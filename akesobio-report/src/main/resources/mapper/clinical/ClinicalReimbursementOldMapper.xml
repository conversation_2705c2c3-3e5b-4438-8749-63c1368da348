<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalReimbursementOldMapper">
    <resultMap type="ClinicalReimbursementOld" id="ClinicalReimbursementOldResult">
        <result property="oaReimbursementNumber" column="oaReimbursementNumber"/>
        <result property="dateCreated" column="dateCreated"/>
        <result property="reimbursementCategory" column="reimbursementCategory"/>
        <result property="companyCode" column="companyCode"/>
        <result property="companyName" column="companyName"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="reimbursementPersonNumber" column="reimbursementPersonNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="applicantNumber" column="applicantNumber"/>
        <result property="applicantPosition" column="applicantPosition"/>
        <result property="department" column="department"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="relatedApprovalForm" column="relatedApprovalForm"/>
        <result property="businessPeriod" column="businessPeriod"/>
        <result property="travelDepartureDate" column="travelDepartureDate"/>
        <result property="travelReturnDate" column="travelReturnDate"/>
        <result property="businessDay" column="businessDay"/>
        <result property="colleagues" column="colleagues"/>
        <result property="businessType" column="businessType"/>
        <result property="typeSegmentation" column="typeSegmentation"/>
        <result property="ceoBusiness" column="ceoBusiness"/>
        <result property="meetingStartDate" column="meetingStartDate"/>
        <result property="meetingEndDate" column="meetingEndDate"/>
        <result property="meetingName" column="meetingName"/>
        <result property="meetingType" column="meetingType"/>
        <result property="meetingCategory" column="meetingCategory"/>
        <result property="meetingFormat" column="meetingFormat"/>
        <result property="companyRole" column="companyRole"/>
        <result property="objectOriented" column="objectOriented"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="peopleCovered" column="peopleCovered"/>
        <result property="signNumber" column="signNumber"/>
        <result property="expertsNumber" column="expertsNumber"/>
        <result property="meetingMaterials" column="meetingMaterials"/>
        <result property="invitation" column="invitation"/>
        <result property="schedule" column="schedule"/>
        <result property="attendanceSheet" column="attendanceSheet"/>
        <result property="photo" column="photo"/>
        <result property="paymentMethod" column="paymentMethod"/>
        <result property="loanBalance" column="loanBalance"/>
        <result property="isWhetherLoan" column="isWhetherLoan"/>
        <result property="totalAmount" column="totalAmount"/>
        <result property="relatedApprovalAmount" column="relatedApprovalAmount"/>
        <result property="differenceAmount" column="differenceAmount"/>
        <result property="differenceExplanation" column="differenceExplanation"/>
        <result property="expenseAccountCode" column="expenseAccountCode"/>
        <result property="expenseAccountName" column="expenseAccountName"/>
        <result property="serviceDescription" column="serviceDescription"/>
        <result property="invoiceNumber" column="invoiceNumber"/>
        <result property="excludingTaxAmount" column="excludingTaxAmount"/>
        <result property="specialInvoiceTaxAmount" column="specialInvoiceTaxAmount"/>
        <result property="reimbursementAmount" column="reimbursementAmount"/>
        <result property="currency" column="currency"/>
        <result property="projectNo" column="projectNo"/>
        <result property="centerName" column="centerName"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="voucherPushDate" column="voucherPushDate"/>
        <result property="expenseApplicationNumber" column="expenseApplicationNumber"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="applicationType" column="applicationType"/>
        <result property="secondaryType" column="secondaryType"/>
        <result property="applicationsAmount" column="applicationsAmount"/>
        <result property="detailedDescription" column="detailedDescription"/>
    </resultMap>

    <select id="selectClinicalReimbursementOld" parameterType="ClinicalReimbursementOld"
            resultMap="ClinicalReimbursementOldResult">
        SELECT --FD72
               a.serialno 'oaReimbursementNumber', a.application_date 'dateCreated', NULL 'reimbursementCategory', a.code_CO 'companyCode', a.bear_cost_text 'companyName', b.fd_name 'reimbursementPerson', a.fd_3ab96f63333c80 'reimbursementPersonNumber', c.fd_name 'applicant', a.job_number 'applicantNumber', a.job_title 'applicantPosition', a.fd_3a4382f3d2e3e6 'department', a.cost_center_code_text 'costCenterCode', a.cost_name_center 'costCenterName', a.association_form 'relatedApprovalForm', a.fd_3bb022d04415de + '-' + a.fd_3bb022dfc444b2 'businessPeriod', NULL 'travelDepartureDate', NULL 'travelReturnDate', NULL 'businessDay', NULL 'colleagues', a.fd_3a4ba29ef4eb08 'businessType', a.fd_3be678f8356c3c 'typeSegmentation', a.fd_3a438294a7e812 'ceoBusiness', NULL 'meetingStartDate', NULL 'meetingEndDate', NULL 'meetingName', NULL 'meetingType', NULL 'meetingCategory', NULL 'meetingFormat', NULL 'companyRole', NULL 'objectOriented', NULL 'province', NULL 'city', NULL 'peopleCovered', NULL 'signNumber', NULL 'expertsNumber', NULL 'meetingMaterials', NULL 'invitation', NULL 'schedule', NULL 'attendanceSheet', NULL 'photo', NULL 'paymentMethod', a.fd_3a45cc22fbe526 'loanBalance', a.whether_offset 'isWhetherLoan', a.fd_3d1f32aa369c4e 'totalAmount', NULL 'relatedApprovalAmount', NULL 'differenceAmount', NULL 'differenceExplanation', d.debit_code_text 'expenseAccountCode', d.debit_name 'expenseAccountName', d.invoice_type 'serviceDescription', d.invoice_no_baiwang 'invoiceNumber', d.business_description 'excludingTaxAmount', d.fd_3a3bbef9d4e540 'specialInvoiceTaxAmount', d.fd_3ab7f19560420a 'reimbursementAmount', a.fd_3a3c5a391655da_text 'currency', a.item_number_text 'projectNo', a.fd_3ab7e967054356 'centerName', (
            CASE
                f.doc_status
                WHEN '10' THEN
                    '草稿'
                WHEN '20' THEN
                    '待审'
                WHEN '11' THEN
                    '驳回'
                WHEN '00' THEN
                    '废弃'
                WHEN '30' THEN
                    '结束'
                WHEN '31' THEN
                    '已反馈'
                ELSE '不对劲'
                END
            ) 'documentStatus', e.fd_fact_node_name 'currentSession', a.sap_document_number 'sapPayNumber', a.sap_credential_push_time 'voucherPushDate', NULL 'expenseApplicationNumber', NULL 'applicationSubject', NULL 'applicationType', NULL 'secondaryType', NULL 'applicationsAmount', NULL 'detailedDescription'
        FROM ekp_expensereimbursementNew a
                 LEFT JOIN hr_org_element b ON b.fd_id = a.apply_reimbursement
                 LEFT JOIN hr_org_element c ON c.fd_id = a.applicant
                 LEFT JOIN ekp_rsementNew_e061d29310 d ON d.fd_parent_id = a.fd_id
                 LEFT JOIN [dbo].[modeling_model_main] f
        ON a.fd_id = f.fd_id
            LEFT JOIN [dbo].[lbpm_node] e ON a.fd_id = e.fd_process_id
        WHERE
            a.fd_3a4382f3d2e3e6 LIKE '%临床%'
          AND a.application_date >= '2024-12-01'

        UNION ALL

        SELECT --FD73
               a.serialno 'oaReimbursementNumber', a.application_date 'dateCreated', NULL 'reimbursementCategory', a.code_CO 'companyCode', a.bear_cost_text 'companyName', b.fd_name 'reimbursementPerson', a.fd_3ab974b9c3d6f0 'reimbursementPersonNumber', c.fd_name 'applicant', a.job_number 'applicantNumber', a.job_title 'applicantPosition', a.department 'department', a.cost_center_code_text 'costCenterCode', a.cost_name_center 'costCenterName', a.association_form 'relatedApprovalForm', a.fd_3bb0234b0104f4 + '-' + a.fd_3bb02356c2f6a4 'businessPeriod', NULL 'travelDepartureDate', NULL 'travelReturnDate', NULL 'businessDay', NULL 'colleagues', null 'businessType', null 'typeSegmentation', null 'ceoBusiness', NULL 'meetingStartDate', NULL 'meetingEndDate', NULL 'meetingName', NULL 'meetingType', NULL 'meetingCategory', NULL 'meetingFormat', NULL 'companyRole', NULL 'objectOriented', NULL 'province', NULL 'city', NULL 'peopleCovered', NULL 'signNumber', NULL 'expertsNumber', NULL 'meetingMaterials', NULL 'invitation', NULL 'schedule', NULL 'attendanceSheet', NULL 'photo', NULL 'paymentMethod', a.fd_3a45cf4007b68c 'loanBalance', a.whether_offset 'isWhetherLoan', a.fd_3d1f3345b84e42 'totalAmount', NULL 'relatedApprovalAmount', NULL 'differenceAmount', NULL 'differenceExplanation', d.debit_code_text 'expenseAccountCode', d.debit_name 'expenseAccountName', d.invoice_type 'serviceDescription', d.invoice_no_baiwang 'invoiceNumber', d.business_description 'excludingTaxAmount', d.fd_3a3bbef9d4e540 'specialInvoiceTaxAmount', d.fd_3abc64c2d8e6ac 'reimbursementAmount', a.fd_3a3c5a391655da_text 'currency', a.item_number_text 'projectNo', a.fd_3ab7e967054356 'centerName', (
            CASE
                f.doc_status
                WHEN '10' THEN
                    '草稿'
                WHEN '20' THEN
                    '待审'
                WHEN '11' THEN
                    '驳回'
                WHEN '00' THEN
                    '废弃'
                WHEN '30' THEN
                    '结束'
                WHEN '31' THEN
                    '已反馈'
                ELSE '不对劲'
                END
            ) 'documentStatus', e.fd_fact_node_name 'currentSession', a.sap_document_number 'sapPayNumber', a.sap_credential_push_time 'voucherPushDate', NULL 'expenseApplicationNumber', NULL 'applicationSubject', NULL 'applicationType', NULL 'secondaryType', NULL 'applicationsAmount', NULL 'detailedDescription'

        FROM ekp_travelNew a
                 LEFT JOIN hr_org_element b ON b.fd_id = a.apply_reimbursement
                 LEFT JOIN hr_org_element c ON c.fd_id = a.applicant
                 LEFT JOIN ekp__travelNew_6f62fb1b5a d ON d.fd_parent_id = a.fd_id
                 LEFT JOIN [dbo].[modeling_model_main] f
        ON a.fd_id = f.fd_id
            LEFT JOIN [dbo].[lbpm_node] e ON a.fd_id = e.fd_process_id
        WHERE
            a.department LIKE '%临床%'
          AND a.application_date >= '2024-12-01'
        UNION all

        SELECT--FD74
              a.serialno 'oaReimbursementNumber', a.fd_3bda9a3d4dcab6 'dateCreated', NULL 'reimbursementCategory', a.code_CO 'companyCode', a.fd_3bda9ee477fe46_text 'companyName', b.fd_name 'reimbursementPerson', a.fd_3ab974b9c3d6f0 'reimbursementPersonNumber', c.fd_name 'applicant', null 'applicantNumber', a.fd_3bdb36401127fc 'applicantPosition', a.fd_3bda9a4e538620 'department', a.cost_center_code_text 'costCenterCode', a.cost_name_center 'costCenterName', a.fd_3bda9abf99730c_text 'relatedApprovalForm', null 'businessPeriod', NULL 'travelDepartureDate', NULL 'travelReturnDate', NULL 'businessDay', NULL 'colleagues', null 'businessType', null 'typeSegmentation', null 'ceoBusiness', a.fd_3bda9c925d4cd4 'meetingStartDate', a.fd_3bda9c90a225a8 'meetingEndDate', a.fd_3bda9c7b86a3b4 'meetingName', a.fd_3bda9cd9743454 'meetingType', a.fd_3bda9cbc49cee8 'meetingCategory', a.fd_3bda9cc3494aba 'meetingFormat', a.fd_3bda9cbfcc8020 'companyRole', a.fd_3bee6425916eca 'objectOriented', a.fd_3bda9ca484bd94 'province', a.fd_3bda9cd3ac8be6_text 'city', a.fd_3bda9c8ee711a6 'peopleCovered', a.fd_3bdcc7a0cd9efe 'signNumber', a.fd_3bda9c83a40efe 'expertsNumber', a.fd_3bda99a8b9a2b8 'meetingMaterials', (case when CHARINDEX('邀请函', a.fd_3bda99a8b9a2b8) > 0 then '是' else '' end) 'invitation', (case when CHARINDEX('日程表', a.fd_3bda99a8b9a2b8) > 0 then '是' else '' end) 'schedule', (case when CHARINDEX('签到表', a.fd_3bda99a8b9a2b8) > 0 then '是' else '' end) 'attendanceSheet', (case when CHARINDEX('照片', a.fd_3bda99a8b9a2b8) > 0 then '是' else '' end) 'photo', a.fd_3bda9cc56afa98 'paymentMethod', a.fd_3bdc282c55813a 'loanBalance', a.whether_offset 'isWhetherLoan', a.fd_3d1f33bb1e0802 'totalAmount', NULL 'relatedApprovalAmount', a.fd_3bdcc7cfd74364 'differenceAmount', a.fd_3bda99c234f71a 'differenceExplanation', d.fd_3bddba4941f6e4_text 'expenseAccountCode', d.debit_name 'expenseAccountName', d.fd_3bdb991ad95ce2 'serviceDescription', d.invoice_no_baiwang 'invoiceNumber', d.fd_3bdd9e9290914c 'excludingTaxAmount', d.fd_3bdb58a94b8528 'specialInvoiceTaxAmount', d.fd_3bdb58a2f1b942 'reimbursementAmount', a.fd_3be0134499038e_text 'currency', a.item_number_text 'projectNo', a.fd_3bdaa6c45dd7b6_text 'centerName', (
            CASE
                f.doc_status
                WHEN '10' THEN
                    '草稿'
                WHEN '20' THEN
                    '待审'
                WHEN '11' THEN
                    '驳回'
                WHEN '00' THEN
                    '废弃'
                WHEN '30' THEN
                    '结束'
                WHEN '31' THEN
                    '已反馈'
                ELSE '不对劲'
                END
            ) 'documentStatus',e.fd_fact_node_name 'currentSession', a.sap_document_number 'sapPayNumber', a.sap_credential_push_time 'voucherPushDate', NULL 'expenseApplicationNumber', NULL 'applicationSubject', NULL 'applicationType', NULL 'secondaryType', NULL 'applicationsAmount', NULL 'detailedDescription'

        FROM ekp_cw_hyfy a
                 LEFT JOIN hr_org_element b ON b.fd_id = a.apply_reimbursement
                 LEFT JOIN hr_org_element c ON c.fd_id = a.fd_3bda9a43128cdc
                 LEFT JOIN ekp_kp_cw_hyfy_99dc245476 d ON d.fd_parent_id = a.fd_id
                 LEFT JOIN [dbo].[modeling_model_main] f
        ON a.fd_id = f.fd_id
            LEFT JOIN [dbo].[lbpm_node] e ON a.fd_id = e.fd_process_id
        WHERE
            a.fd_3bda9a4e538620 LIKE '%临床%'
          AND a.fd_3bda9a3d4dcab6 >= '2024-12-01'
    </select>
    <select id="selectClinicalReimbursementOld1" parameterType="ClinicalReimbursementOld"
            resultMap="ClinicalReimbursementOldResult">
        SELECT --FD73
               a.serialno 'oaReimbursementNumber', a.application_date 'dateCreated', NULL 'reimbursementCategory', a.code_CO 'companyCode', a.bear_cost_text 'companyName', b.fd_name 'reimbursementPerson', a.fd_3ab974b9c3d6f0 'reimbursementPersonNumber', c.fd_name 'applicant', a.job_number 'applicantNumber', a.job_title 'applicantPosition', a.department 'department', a.cost_center_code_text 'costCenterCode', a.cost_name_center 'costCenterName', a.association_form 'relatedApprovalForm', a.fd_3bb0234b0104f4 + '-' + a.fd_3bb02356c2f6a4 'businessPeriod', NULL 'travelDepartureDate', NULL 'travelReturnDate', NULL 'businessDay', NULL 'colleagues', null 'businessType', null 'typeSegmentation', null 'ceoBusiness', NULL 'meetingStartDate', NULL 'meetingEndDate', NULL 'meetingName', NULL 'meetingType', NULL 'meetingCategory', NULL 'meetingFormat', NULL 'companyRole', NULL 'objectOriented', NULL 'province', NULL 'city', NULL 'peopleCovered', NULL 'signNumber', NULL 'expertsNumber', NULL 'meetingMaterials', NULL 'invitation', NULL 'schedule', NULL 'attendanceSheet', NULL 'photo', NULL 'paymentMethod', a.fd_3a45cf4007b68c 'loanBalance', a.whether_offset 'isWhetherLoan', a.fd_3d1f3345b84e42 'totalAmount', NULL 'relatedApprovalAmount', NULL 'differenceAmount', NULL 'differenceExplanation', d.debit_code_text 'expenseAccountCode', d.debit_name 'expenseAccountName', d.invoice_type 'serviceDescription', d.invoice_no_baiwang 'invoiceNumber', d.business_description 'excludingTaxAmount', d.fd_3a3bbef9d4e540 'specialInvoiceTaxAmount', d.fd_3abc64c2d8e6ac 'reimbursementAmount', a.fd_3a3c5a391655da_text 'currency', a.item_number_text 'projectNo', a.fd_3ab7e967054356 'centerName', (
            CASE
                f.doc_status
                WHEN '10' THEN
                    '草稿'
                WHEN '20' THEN
                    '待审'
                WHEN '11' THEN
                    '驳回'
                WHEN '00' THEN
                    '废弃'
                WHEN '30' THEN
                    '结束'
                WHEN '31' THEN
                    '已反馈'
                ELSE '不对劲'
                END
            ) 'documentStatus', e.fd_fact_node_name 'currentSession', a.sap_document_number 'sapPayNumber', a.sap_credential_push_time 'voucherPushDate', NULL 'expenseApplicationNumber', NULL 'applicationSubject', NULL 'applicationType', NULL 'secondaryType', NULL 'applicationsAmount', NULL 'detailedDescription'

        FROM ekp_travelNew a
                 LEFT JOIN hr_org_element b ON b.fd_id = a.apply_reimbursement
                 LEFT JOIN hr_org_element c ON c.fd_id = a.applicant
                 LEFT JOIN ekp__travelNew_6f62fb1b5a d ON d.fd_parent_id = a.fd_id
                 LEFT JOIN [dbo].[modeling_model_main] f
        ON a.fd_id = f.fd_id
            LEFT JOIN [dbo].[lbpm_node] e ON a.fd_id = e.fd_process_id
        WHERE
            a.department LIKE '%临床%'
          AND a.application_date >= '2024-12-01'
          and a.serialno='FD73-20241201802'
    </select>
</mapper>