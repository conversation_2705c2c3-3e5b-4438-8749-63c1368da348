<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.Fd73ClinicalTravelReimbursementMapper">

    <resultMap type="Fd73ClinicalTravelReimbursement" id="Fd73ClinicalTravelReimbursementResult">
        <result property="id" column="id"/>
        <result property="oaReimbursementNumber" column="oaReimbursementNumber"/>
        <result property="dateCreated" column="dateCreated"/>
        <result property="reimbursementCategory" column="reimbursementCategory"/>
        <result property="companyCode" column="companyCode"/>
        <result property="companyName" column="companyName"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="reimbursementPersonNumber" column="reimbursementPersonNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="applicantNumber" column="applicantNumber"/>
        <result property="applicantPosition" column="applicantPosition"/>
        <result property="department" column="department"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="relatedApprovalForm" column="relatedApprovalForm"/>
        <result property="businessPeriod" column="businessPeriod"/>
        <result property="departureDate" column="departureDate"/>
        <result property="returnDate" column="returnDate"/>
        <result property="businessDay" column="businessDay"/>
        <result property="colleagues" column="colleagues"/>
        <result property="loanBalance" column="loanBalance"/>
        <result property="isWhetherLoan" column="isWhetherLoan"/>
        <result property="totalAmount" column="totalAmount"/>
        <result property="expenseAccountCode" column="expenseAccountCode"/>
        <result property="expenseAccountName" column="expenseAccountName"/>
        <result property="serviceDescription" column="serviceDescription"/>
        <result property="invoiceNumber" column="invoiceNumber"/>
        <result property="excludingTaxAmount" column="excludingTaxAmount"/>
        <result property="specialInvoiceTaxAmount" column="specialInvoiceTaxAmount"/>
        <result property="reimbursementAmount" column="reimbursementAmount"/>
        <result property="currency" column="currency"/>
        <result property="projectNo" column="projectNo"/>
        <result property="projectNos" column="projectNos"/>
        <result property="centerName" column="centerName"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="voucherPushDate" column="voucherPushDate"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="fd13ID" column="fd13ID"/>
        <result property="fd13Number" column="fd13Number"/>
        <result property="fd13Name" column="fd13Name"/>
        <result property="fd13Amount" column="fd13Amount"/>
        <result property="fd13Location" column="fd13Location"/>
        <result property="fd13ServiceDescription" column="fd13ServiceDescription"/>
        <result property="executionDate" column="executionDate"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="fdId" column="fdId"/>
    </resultMap>

    <sql id="selectFd73ClinicalTravelReimbursementVo">
        select id,
               oaReimbursementNumber,
               dateCreated,
               reimbursementCategory,
               companyCode,
               companyName,
               reimbursementPerson,
               reimbursementPersonNumber,
               applicant,
               applicantNumber,
               applicantPosition,
               department,
               costCenterCode,
               costCenterName,
               relatedApprovalForm,
               businessPeriod,
               departureDate,
               returnDate,
               businessDay,
               colleagues,
               loanBalance,
               isWhetherLoan,
               totalAmount,
               expenseAccountCode,
               expenseAccountName,
               serviceDescription,
               invoiceNumber,
               excludingTaxAmount,
               specialInvoiceTaxAmount,
               reimbursementAmount,
               currency,
               projectNo,
               projectNos,
               centerName,
               documentStatus,
               currentSession,
               sapPayNumber,
               voucherPushDate,
               applicationSubject,
               fd13ID,
               fd13Number,
               fd13Name,
               fd13Amount,
               fd13Location,
               fd13ServiceDescription,
               executionDate,
               deleteStatus,
               fdId
        from Fd73_clinical_travel_reimbursement
    </sql>

    <select id="selectFd73ClinicalTravelReimbursementList" parameterType="Fd73ClinicalTravelReimbursement"
            resultMap="Fd73ClinicalTravelReimbursementResult">
        <include refid="selectFd73ClinicalTravelReimbursementVo"/>
        <where>
            <if test="oaReimbursementNumber != null  and oaReimbursementNumber != ''">and oaReimbursementNumber like
                concat('%', #{oaReimbursementNumber}, '%')
            </if>
            <if test="params.beginDateCreated != null and params.beginDateCreated != '' and params.endDateCreated != null and params.endDateCreated != ''">
                and dateCreated between #{params.beginDateCreated} and #{params.endDateCreated}
            </if>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''">and reimbursementCategory like
                concat('%', #{reimbursementCategory}, '%')
            </if>
            <if test="companyCode != null  and companyCode != ''">and companyCode like concat('%', #{companyCode},
                '%')
            </if>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''">and reimbursementPerson like
                concat('%', #{reimbursementPerson}, '%')
            </if>
            <if test="reimbursementPersonNumber != null  and reimbursementPersonNumber != ''">and
                reimbursementPersonNumber = #{reimbursementPersonNumber}
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="applicantNumber != null  and applicantNumber != ''">and applicantNumber = #{applicantNumber}</if>
            <if test="applicantPosition != null  and applicantPosition != ''">and applicantPosition =
                #{applicantPosition}
            </if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="costCenterCode != null  and costCenterCode != ''">and costCenterCode = #{costCenterCode}</if>
            <if test="costCenterName != null  and costCenterName != ''">and costCenterName like concat('%',
                #{costCenterName}, '%')
            </if>
            <if test="relatedApprovalForm != null  and relatedApprovalForm != ''">and relatedApprovalForm =
                #{relatedApprovalForm}
            </if>
            <if test="businessPeriod != null  and businessPeriod != ''">and businessPeriod = #{businessPeriod}</if>
            <if test="departureDate != null  and departureDate != ''">and departureDate = #{departureDate}</if>
            <if test="returnDate != null  and returnDate != ''">and returnDate = #{returnDate}</if>
            <if test="businessDay != null  and businessDay != ''">and businessDay = #{businessDay}</if>
            <if test="colleagues != null  and colleagues != ''">and colleagues = #{colleagues}</if>
            <if test="loanBalance != null  and loanBalance != ''">and loanBalance = #{loanBalance}</if>
            <if test="isWhetherLoan != null  and isWhetherLoan != ''">and isWhetherLoan = #{isWhetherLoan}</if>
            <if test="totalAmount != null  and totalAmount != ''">and totalAmount = #{totalAmount}</if>
            <if test="expenseAccountCode != null  and expenseAccountCode != ''">and expenseAccountCode =
                #{expenseAccountCode}
            </if>
            <if test="expenseAccountName != null  and expenseAccountName != ''">and expenseAccountName like concat('%',
                #{expenseAccountName}, '%')
            </if>
            <if test="serviceDescription != null  and serviceDescription != ''">and serviceDescription =
                #{serviceDescription}
            </if>
            <if test="invoiceNumber != null  and invoiceNumber != ''">and invoiceNumber = #{invoiceNumber}</if>
            <if test="excludingTaxAmount != null  and excludingTaxAmount != ''">and excludingTaxAmount =
                #{excludingTaxAmount}
            </if>
            <if test="specialInvoiceTaxAmount != null  and specialInvoiceTaxAmount != ''">and specialInvoiceTaxAmount =
                #{specialInvoiceTaxAmount}
            </if>
            <if test="reimbursementAmount != null  and reimbursementAmount != ''">and reimbursementAmount =
                #{reimbursementAmount}
            </if>
            <if test="currency != null  and currency != ''">and currency = #{currency}</if>
            <if test="projectNo != null  and projectNo != ''">and projectNo like concat('%', #{projectNo}, '%')</if>
            <if test="projectNos != null  and projectNos != ''">and projectNos like concat('%', #{projectNos}, '%')</if>
            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="sapPayNumber != null  and sapPayNumber != ''">and sapPayNumber = #{sapPayNumber}</if>
            <if test="voucherPushDate != null  and voucherPushDate != ''">and voucherPushDate = #{voucherPushDate}</if>
            <if test="applicationSubject != null  and applicationSubject != ''">and applicationSubject like concat('%',
                #{applicationSubject}, '%')
            </if>
            <if test="fd13ID != null  and fd13ID != ''">and fd13ID = #{fd13ID}</if>
            <if test="fd13Number != null  and fd13Number != ''">and fd13Number like concat('%', #{fd13Number}, '%')</if>
            <if test="fd13Name != null  and fd13Name != ''">and fd13Name like concat('%', #{fd13Name}, '%')</if>
            <if test="fd13Amount != null  and fd13Amount != ''">and fd13Amount = #{fd13Amount}</if>
            <if test="fd13Location != null  and fd13Location != ''">and fd13Location = #{fd13Location}</if>
            <if test="fd13ServiceDescription != null  and fd13ServiceDescription != ''">and fd13ServiceDescription =
                #{fd13ServiceDescription}
            </if>
            <if test="executionDate != null ">and executionDate = #{executionDate}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="permissionProjectNo != null and permissionProjectNo.size() > 0">
                and projectNos in (
                <foreach collection="permissionProjectNo" item="e" separator=",">
                    #{e.projectNos}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="selectFd73ClinicalTravelReimbursementById" parameterType="Integer"
            resultMap="Fd73ClinicalTravelReimbursementResult">
        <include refid="selectFd73ClinicalTravelReimbursementVo"/>
        where id = #{id}
    </select>

    <insert id="insertFd73ClinicalTravelReimbursement" parameterType="Fd73ClinicalTravelReimbursement">
        insert into Fd73_clinical_travel_reimbursement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="oaReimbursementNumber != null">oaReimbursementNumber,</if>
            <if test="dateCreated != null">dateCreated,</if>
            <if test="reimbursementCategory != null">reimbursementCategory,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="companyName != null">companyName,</if>
            <if test="reimbursementPerson != null">reimbursementPerson,</if>
            <if test="reimbursementPersonNumber != null">reimbursementPersonNumber,</if>
            <if test="applicant != null">applicant,</if>
            <if test="applicantNumber != null">applicantNumber,</if>
            <if test="applicantPosition != null">applicantPosition,</if>
            <if test="department != null">department,</if>
            <if test="costCenterCode != null">costCenterCode,</if>
            <if test="costCenterName != null">costCenterName,</if>
            <if test="relatedApprovalForm != null">relatedApprovalForm,</if>
            <if test="businessPeriod != null">businessPeriod,</if>
            <if test="departureDate != null">departureDate,</if>
            <if test="returnDate != null">returnDate,</if>
            <if test="businessDay != null">businessDay,</if>
            <if test="colleagues != null">colleagues,</if>
            <if test="loanBalance != null">loanBalance,</if>
            <if test="isWhetherLoan != null">isWhetherLoan,</if>
            <if test="totalAmount != null">totalAmount,</if>
            <if test="expenseAccountCode != null">expenseAccountCode,</if>
            <if test="expenseAccountName != null">expenseAccountName,</if>
            <if test="serviceDescription != null">serviceDescription,</if>
            <if test="invoiceNumber != null">invoiceNumber,</if>
            <if test="excludingTaxAmount != null">excludingTaxAmount,</if>
            <if test="specialInvoiceTaxAmount != null">specialInvoiceTaxAmount,</if>
            <if test="reimbursementAmount != null">reimbursementAmount,</if>
            <if test="currency != null">currency,</if>
            <if test="projectNo != null">projectNo,</if>
            <if test="projectNos != null">projectNos,</if>
            <if test="centerName != null">centerName,</if>
            <if test="documentStatus != null">documentStatus,</if>
            <if test="currentSession != null">currentSession,</if>
            <if test="sapPayNumber != null">sapPayNumber,</if>
            <if test="voucherPushDate != null">voucherPushDate,</if>
            <if test="applicationSubject != null">applicationSubject,</if>
            <if test="fd13ID != null">fd13ID,</if>
            <if test="fd13Number != null">fd13Number,</if>
            <if test="fd13Name != null">fd13Name,</if>
            <if test="fd13Amount != null">fd13Amount,</if>
            <if test="fd13Location != null">fd13Location,</if>
            <if test="fd13ServiceDescription != null">fd13ServiceDescription,</if>
            <if test="executionDate != null">executionDate,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="fdId != null">fdId,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="oaReimbursementNumber != null">#{oaReimbursementNumber},</if>
            <if test="dateCreated != null">#{dateCreated},</if>
            <if test="reimbursementCategory != null">#{reimbursementCategory},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="reimbursementPerson != null">#{reimbursementPerson},</if>
            <if test="reimbursementPersonNumber != null">#{reimbursementPersonNumber},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="applicantNumber != null">#{applicantNumber},</if>
            <if test="applicantPosition != null">#{applicantPosition},</if>
            <if test="department != null">#{department},</if>
            <if test="costCenterCode != null">#{costCenterCode},</if>
            <if test="costCenterName != null">#{costCenterName},</if>
            <if test="relatedApprovalForm != null">#{relatedApprovalForm},</if>
            <if test="businessPeriod != null">#{businessPeriod},</if>
            <if test="departureDate != null">#{departureDate},</if>
            <if test="returnDate != null">#{returnDate},</if>
            <if test="businessDay != null">#{businessDay},</if>
            <if test="colleagues != null">#{colleagues},</if>
            <if test="loanBalance != null">#{loanBalance},</if>
            <if test="isWhetherLoan != null">#{isWhetherLoan},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="expenseAccountCode != null">#{expenseAccountCode},</if>
            <if test="expenseAccountName != null">#{expenseAccountName},</if>
            <if test="serviceDescription != null">#{serviceDescription},</if>
            <if test="invoiceNumber != null">#{invoiceNumber},</if>
            <if test="excludingTaxAmount != null">#{excludingTaxAmount},</if>
            <if test="specialInvoiceTaxAmount != null">#{specialInvoiceTaxAmount},</if>
            <if test="reimbursementAmount != null">#{reimbursementAmount},</if>
            <if test="currency != null">#{currency},</if>
            <if test="projectNo != null">#{projectNo},</if>
            <if test="projectNos != null">#{projectNos},</if>
            <if test="centerName != null">#{centerName},</if>
            <if test="documentStatus != null">#{documentStatus},</if>
            <if test="currentSession != null">#{currentSession},</if>
            <if test="sapPayNumber != null">#{sapPayNumber},</if>
            <if test="voucherPushDate != null">#{voucherPushDate},</if>
            <if test="applicationSubject != null">#{applicationSubject},</if>
            <if test="fd13ID != null">#{fd13ID},</if>
            <if test="fd13Number != null">#{fd13Number},</if>
            <if test="fd13Name != null">#{fd13Name},</if>
            <if test="fd13Amount != null">#{fd13Amount},</if>
            <if test="fd13Location != null">#{fd13Location},</if>
            <if test="fd13ServiceDescription != null">#{fd13ServiceDescription},</if>
            <if test="executionDate != null">#{executionDate},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="fdId != null">#{fdId},</if>
        </trim>
    </insert>

    <update id="updateFd73ClinicalTravelReimbursement" parameterType="Fd73ClinicalTravelReimbursement">
        update Fd73_clinical_travel_reimbursement
        <trim prefix="SET" suffixOverrides=",">
            <if test="oaReimbursementNumber != null">oaReimbursementNumber = #{oaReimbursementNumber},</if>
            <if test="dateCreated != null">dateCreated = #{dateCreated},</if>
            <if test="reimbursementCategory != null">reimbursementCategory = #{reimbursementCategory},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="reimbursementPerson != null">reimbursementPerson = #{reimbursementPerson},</if>
            <if test="reimbursementPersonNumber != null">reimbursementPersonNumber = #{reimbursementPersonNumber},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="applicantNumber != null">applicantNumber = #{applicantNumber},</if>
            <if test="applicantPosition != null">applicantPosition = #{applicantPosition},</if>
            <if test="department != null">department = #{department},</if>
            <if test="costCenterCode != null">costCenterCode = #{costCenterCode},</if>
            <if test="costCenterName != null">costCenterName = #{costCenterName},</if>
            <if test="relatedApprovalForm != null">relatedApprovalForm = #{relatedApprovalForm},</if>
            <if test="businessPeriod != null">businessPeriod = #{businessPeriod},</if>
            <if test="departureDate != null">departureDate = #{departureDate},</if>
            <if test="returnDate != null">returnDate = #{returnDate},</if>
            <if test="businessDay != null">businessDay = #{businessDay},</if>
            <if test="colleagues != null">colleagues = #{colleagues},</if>
            <if test="loanBalance != null">loanBalance = #{loanBalance},</if>
            <if test="isWhetherLoan != null">isWhetherLoan = #{isWhetherLoan},</if>
            <if test="totalAmount != null">totalAmount = #{totalAmount},</if>
            <if test="expenseAccountCode != null">expenseAccountCode = #{expenseAccountCode},</if>
            <if test="expenseAccountName != null">expenseAccountName = #{expenseAccountName},</if>
            <if test="serviceDescription != null">serviceDescription = #{serviceDescription},</if>
            <if test="invoiceNumber != null">invoiceNumber = #{invoiceNumber},</if>
            <if test="excludingTaxAmount != null">excludingTaxAmount = #{excludingTaxAmount},</if>
            <if test="specialInvoiceTaxAmount != null">specialInvoiceTaxAmount = #{specialInvoiceTaxAmount},</if>
            <if test="reimbursementAmount != null">reimbursementAmount = #{reimbursementAmount},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="projectNo != null">projectNo = #{projectNo},</if>
            <if test="projectNos != null">projectNos = #{projectNos},</if>
            <if test="centerName != null">centerName = #{centerName},</if>
            <if test="documentStatus != null">documentStatus = #{documentStatus},</if>
            <if test="currentSession != null">currentSession = #{currentSession},</if>
            <if test="sapPayNumber != null">sapPayNumber = #{sapPayNumber},</if>
            <if test="voucherPushDate != null">voucherPushDate = #{voucherPushDate},</if>
            <if test="applicationSubject != null">applicationSubject = #{applicationSubject},</if>
            <if test="fd13ID != null">fd13ID = #{fd13ID},</if>
            <if test="fd13Number != null">fd13Number = #{fd13Number},</if>
            <if test="fd13Name != null">fd13Name = #{fd13Name},</if>
            <if test="fd13Amount != null">fd13Amount = #{fd13Amount},</if>
            <if test="fd13Location != null">fd13Location = #{fd13Location},</if>
            <if test="fd13ServiceDescription != null">fd13ServiceDescription = #{fd13ServiceDescription},</if>
            <if test="executionDate != null">executionDate = #{executionDate},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFd73ClinicalTravelReimbursementById" parameterType="Integer">
        delete
        from Fd73_clinical_travel_reimbursement
        where id = #{id}
    </delete>

    <delete id="deleteFd73ClinicalTravelReimbursementByIds" parameterType="String">
        delete from Fd73_clinical_travel_reimbursement where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAllFd73ClinicalTravelReimbursement" parameterType="Integer">
        delete
        from Fd73_clinical_travel_reimbursement
    </delete>
</mapper>