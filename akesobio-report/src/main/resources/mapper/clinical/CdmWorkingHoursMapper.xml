<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.CdmWorkingHoursMapper">

    <resultMap type="CdmWorkingHours" id="CdmWorkingHoursResult">
        <result property="formName" column="formName"/>
        <result property="singleNumber" column="singleNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="creationTime" column="creationTime"/>
        <result property="workDate" column="workDate"/>
        <result property="projectClassification" column="projectClassification"/>
        <result property="publicProjectNumber" column="publicProjectNumber"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="workingHours" column="workingHours"/>
        <result property="workContent" column="workContent"/>
        <result property="jobTitle" column="jobTitle"/>
        <result property="directLeadership" column="directLeadership"/>
        <result property="departmentHead" column="departmentHead"/>
        <result property="documentStatus" column="documentStatus"/>
    </resultMap>

    <sql id="selectCdmWorkingHoursVo">
        select formName,
               singleNumber,
               applicant,
               creationTime,
               workDate,
               projectClassification,
               publicProjectNumber,
               projectNumber,
               workingHours,
               workContent,
               jobTitle,
               directLeadership,
               departmentHead,
               documentStatus
        from CDM_working_hours
    </sql>

    <select id="selectCdmWorkingHoursList" parameterType="CdmWorkingHours" resultMap="CdmWorkingHoursResult">
        <include refid="selectCdmWorkingHoursVo"/>
        <where>
            <if test="formName != null  and formName != ''">and formName like concat('%', #{formName}, '%')</if>
            <if test="singleNumber != null  and singleNumber != ''">and singleNumber like concat('%', #{singleNumber},
                '%')
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="params.beginCreationTime != null and params.beginCreationTime != '' and params.endCreationTime != null and params.endCreationTime != ''">
                and creationTime between #{params.beginCreationTime} and #{params.endCreationTime}
            </if>
            <if test="params.beginWorkDate != null and params.beginWorkDate != '' and params.endWorkDate != null and params.endWorkDate != ''">
                and workDate between #{params.beginWorkDate} and #{params.endWorkDate}
            </if>
            <if test="projectClassification != null  and projectClassification != ''">and projectClassification like
                concat('%', #{projectClassification}, '%')
            </if>
            <if test="publicProjectNumber != null  and publicProjectNumber != ''">and publicProjectNumber like
                concat('%', #{publicProjectNumber}, '%')
            </if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="workingHours != null ">and workingHours = #{workingHours}</if>
            <if test="workContent != null  and workContent != ''">and workContent like concat('%', #{workContent},
                '%')
            </if>
            <if test="jobTitle != null  and jobTitle != ''">and jobTitle like concat('%', #{jobTitle}, '%')</if>
            <if test="directLeadership != null  and directLeadership != ''">and directLeadership like concat('%',
                #{directLeadership}, '%')
            </if>
            <if test="departmentHead != null  and departmentHead != ''">and departmentHead like concat('%',
                #{departmentHead}, '%')
            </if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
        </where>
    </select>
</mapper>