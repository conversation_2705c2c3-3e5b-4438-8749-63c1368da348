<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalHoursReportMapper">

    <resultMap type="ClinicalHoursReport" id="ClinicalHoursReportResult">
        <result property="formType" column="formType"/>
        <result property="oddNumber" column="oddNumber"/>
        <result property="applicationDate" column="applicationDate"/>
        <result property="jobNumber" column="jobNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="department" column="department"/>
        <result property="post" column="post"/>
        <result property="workDate" column="workDate"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="workHours" column="workHours"/>
        <result property="workContent" column="workContent"/>
        <result property="documentStatus" column="documentStatus"/>
    </resultMap>

        <sql id="selectClinicalHoursReportVo" >
            select id, formType, oddNumber, applicationDate, jobNumber, applicant, department, post, workDate, projectNumber, workHours, workContent, documentStatus from clinicalHoursReport
        </sql>

        <select id="selectClinicalHoursReportList"  parameterType="ClinicalHoursReport" resultMap="ClinicalHoursReportResult">
            <include refid="selectClinicalHoursReportVo"/>
            <where>
                <if test="formType != null  and formType != ''"> and formType like concat('%', #{formType}, '%')</if>
                <if test="oddNumber != null  and oddNumber != ''"> and oddNumber like concat('%', #{oddNumber}, '%')</if>
                <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
                <if test="jobNumber != null  and jobNumber != ''"> and jobNumber like concat('%', #{jobNumber}, '%')</if>
                <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
                <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
                <if test="post != null  and post != ''"> and post = #{post}</if>
                <if test="params.beginWorkDate != null and params.beginWorkDate != '' and params.endWorkDate != null and params.endWorkDate != ''"> and workDate between #{params.beginWorkDate} and #{params.endWorkDate}</if>
                <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
                <if test="workHours != null  and workHours != ''"> and workHours = #{workHours}</if>
                <if test="workContent != null  and workContent != ''"> and workContent = #{workContent}</if>
                <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            </where>
            order by oddNumber desc
        </select>
</mapper>