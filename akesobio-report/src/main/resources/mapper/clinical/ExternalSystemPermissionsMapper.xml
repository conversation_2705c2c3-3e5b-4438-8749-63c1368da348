<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ExternalSystemPermissionsMapper">
    <!-- 实体类  -->
    <resultMap type="ExternalSystemPermissions" id="ExternalSystemPermissionsResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectExternalSystemPermissionsList">
        SELECT
            link,
            linkSuffix,
            id,
            applicationDate,
            oddNumber,
            name,
            projectNumber,
            projectBranch,
            externalSystem,
            ctaName,
            cta,
            cra,
            aOrDTime,
            currentSession,
            currentProcessor,
            arrivalTime,
            documentStatus,
            lastDate,
            pmBeginDate,
            pmEndDate,
            ctaBeginDate,
            ctaEndDate,
            applicantBeginDate,
            applicantEndDate
        FROM  external_system_permissions
    </sql>
    
    <!-- 方法 -->
    <select id="queryExternalSystemPermissionsList" parameterType="ExternalSystemPermissions" resultMap="ExternalSystemPermissionsResult">
        <include refid="selectExternalSystemPermissionsList"/>
        <where>
            <if test="oddNumber != null and oddNumber != ''"> and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="projectNumber != null and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="projectBranch != null and projectBranch != ''"> and projectBranch like concat('%', #{projectBranch}, '%')</if>
            <if test="externalSystem != null and externalSystem != ''"> and externalSystem like concat('%', #{externalSystem}, '%')</if>
            <if test="currentSession != null and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="documentStatus != null and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>

            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>