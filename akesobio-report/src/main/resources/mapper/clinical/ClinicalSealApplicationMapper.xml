<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalSealApplicationMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalSealApplication" id="ClinicalSealApplicationResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectClinicalSealApplicationList">
        SELECT id,
               subjectTitle,
               documentStatus,
               oddNumber,
               applicationDate,
               applicationName,
               jobNumber,
               department,
               position,
               subject,
               company,
               stampType,
               degree
        FROM clinical_seal_application
    </sql>

    <!-- 方法 -->
    <select id="queryClinicalSealApplicationList" parameterType="ClinicalSealApplication"
            resultMap="ClinicalSealApplicationResult">
        <include refid="selectClinicalSealApplicationList"/>
        <where>
            <if test="applicationName != null and applicationName != ''">and applicationName like concat('%',
                #{applicationName}, '%')
            </if>
            <if test="subject != null and subject != ''">and subject like concat('%', #{subject}, '%')</if>
            <if test="oddNumber != null and oddNumber != ''">and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="department != null and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="degree != null and degree != ''">and degree = #{degree}</if>
            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>