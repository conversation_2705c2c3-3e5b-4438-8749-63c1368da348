<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalMaterialDesignMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalMaterialDesign" id="ClinicalMaterialDesignResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectClinicalMaterialDesignList">
        SELECT id,
               applicationDate,
               oddNumber,
               applicationName,
               jobNumber,
               department,
               position,
               projectNumber,
               number,
               costDescription,
               makeType,
               outputForm,
               centerName,
               xmjlDate,
               expectationDate,
               sjzzzDate,
               sqrqrDate,
               xmzjDate,
               vpspDate,
               wlbxdzzDate
        FROM clinical_materialDesign
    </sql>

    <!-- 方法 -->
    <select id="queryClinicalMaterialDesignList" parameterType="ClinicalMaterialDesign"
            resultMap="ClinicalMaterialDesignResult">
        <include refid="selectClinicalMaterialDesignList"/>
        <where>
            department like '%临床运行部门_临床事务部%'
            <if test="applicationName != null and applicationName != ''">and applicationName like concat('%',
                #{applicationName}, '%')
            </if>
            <if test="jobNumber != null and jobNumber != ''">and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="oddNumber != null and oddNumber != ''">and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="centerName != null and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="makeType != null and makeType != ''">and makeType like concat('%', #{makeType}, '%')</if>
            <if test="outputForm != null and outputForm != ''">and outputForm like concat('%', #{outputForm}, '%')</if>
            <if test="projectNumber != null and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="department != null and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>