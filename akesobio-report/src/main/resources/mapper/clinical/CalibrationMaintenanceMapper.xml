<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.CalibrationMaintenanceMapper">
    
    <resultMap type="CalibrationMaintenance" id="CalibrationMaintenanceResult">
        <result property="oddNumbers"    column="oddNumbers"    />
        <result property="applicationDate"    column="applicationDate"    />
        <result property="applicant"    column="applicant"    />
        <result property="feeAttributable"    column="feeAttributable"    />
        <result property="projectNo"    column="projectNo"    />
        <result property="centerNumber"    column="centerNumber"    />
        <result property="centerName"    column="centerName"    />
        <result property="principalIinvestigator"    column="principalIinvestigator"    />
        <result property="department"    column="department"    />
        <result property="applicationReason"    column="applicationReason"    />
        <result property="materialName"    column="materialName"    />
        <result property="specificationModel"    column="specificationModel"    />
        <result property="equipmentNumber"    column="equipmentNumber"    />
        <result property="number"    column="number"    />
        <result property="calibrationMethod"    column="calibrationMethod"    />
        <result property="calibrationExpirationTime"    column="calibrationExpirationTime"    />
        <result property="equipmentOwnership"    column="equipmentOwnership"    />
        <result property="recipient"    column="recipient"    />
        <result property="city"    column="city"    />
        <result property="detailedShippingAddress"    column="detailedShippingAddress"    />
        <result property="calibrationDate"    column="calibrationDate"    />
        <result property="measurementCompany"    column="measurementCompany"    />
        <result property="paymentTime"    column="paymentTime"    />
        <result property="remarks"    column="remarks"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="currentProcessor"    column="currentProcessor"    />
        <result property="documentStatus"    column="documentStatus"    />
    </resultMap>

    <sql id="selectCalibrationMaintenanceVo">
        select id, oddNumbers, applicationDate, applicant, feeAttributable, projectNo, centerNumber, centerName, principalIinvestigator, department, applicationReason, materialName, specificationModel, equipmentNumber, number, calibrationMethod, calibrationExpirationTime, equipmentOwnership, recipient, city, detailedShippingAddress, calibrationDate, measurementCompany, paymentTime, remarks, currentSession, currentProcessor, documentStatus from calibration_maintenance
    </sql>

    <select id="selectCalibrationMaintenanceList" parameterType="CalibrationMaintenance" resultMap="CalibrationMaintenanceResult">
        <include refid="selectCalibrationMaintenanceVo"/>
        <where>  
            <if test="oddNumbers != null  and oddNumbers != ''"> and oddNumbers like concat('%', #{oddNumbers}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="feeAttributable != null  and feeAttributable != ''"> and feeAttributable like concat('%', #{feeAttributable}, '%')</if>
            <if test="projectNo != null  and projectNo != ''"> and projectNo like concat('%', #{projectNo}, '%')</if>
            <if test="centerNumber != null  and centerNumber != ''"> and centerNumber like concat('%', #{centerNumber}, '%')</if>
            <if test="centerName != null  and centerName != ''"> and centerName like concat('%', #{centerName}, '%')</if>
            <if test="principalIinvestigator != null  and principalIinvestigator != ''"> and principalIinvestigator like concat('%', #{principalIinvestigator}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="applicationReason != null  and applicationReason != ''"> and applicationReason like concat('%', #{applicationReason}, '%')</if>
            <if test="materialName != null  and materialName != ''"> and materialName like concat('%', #{materialName}, '%')</if>
            <if test="specificationModel != null  and specificationModel != ''"> and specificationModel like concat('%', #{specificationModel}, '%')</if>
            <if test="equipmentNumber != null  and equipmentNumber != ''"> and equipmentNumber like concat('%', #{equipmentNumber}, '%')</if>
            <if test="number != null "> and number = #{number}</if>
            <if test="calibrationMethod != null  and calibrationMethod != ''"> and calibrationMethod like concat('%', #{calibrationMethod}, '%')</if>
            <if test="params.beginCalibrationExpirationTime != null and params.beginCalibrationExpirationTime != '' and params.endCalibrationExpirationTime != null and params.endCalibrationExpirationTime != ''"> and calibrationExpirationTime between #{params.beginCalibrationExpirationTime} and #{params.endCalibrationExpirationTime}</if>
            <if test="equipmentOwnership != null  and equipmentOwnership != ''"> and equipmentOwnership like concat('%', #{equipmentOwnership}, '%')</if>
            <if test="recipient != null  and recipient != ''"> and recipient like concat('%', #{recipient}, '%')</if>
            <if test="city != null  and city != ''"> and city like concat('%', #{city}, '%')</if>
            <if test="detailedShippingAddress != null  and detailedShippingAddress != ''"> and detailedShippingAddress like concat('%', #{detailedShippingAddress}, '%')</if>
            <if test="calibrationDate != null "> and calibrationDate = #{calibrationDate}</if>
            <if test="measurementCompany != null  and measurementCompany != ''"> and measurementCompany = #{measurementCompany}</if>
            <if test="paymentTime != null "> and paymentTime = #{paymentTime}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="currentProcessor != null  and currentProcessor != ''"> and currentProcessor like concat('%', #{currentProcessor}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            and documentStatus !='废弃'
        </where>
    </select>
</mapper>