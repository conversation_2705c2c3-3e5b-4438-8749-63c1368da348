<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.clinical.mapper.ClinicalStampMapper">
    <!-- 实体类  -->
    <resultMap type="ClinicalStamp" id="ClinicalStampResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectClinicalStampList">
        SELECT
            id,
            filePath,
            oddNumber,
            applicationName,
            jobNumber,
            department,
            objectNumber,
            jlName,
            company,
            centerNumber,
            centerName,
            stampType,
            fileType,
            isSend,
            trackingNumber,
            isScannedCopy,
            name,
            email,
            fileId,
            outTrackingNumber,
            fileName,
            isFile,
            applicationDate,
            documentStatus,
            currentSession,
            currentProcessor,
            subject,
            approver,
            opinion,
            operationType,
            wjzDate,
            zsDate,
            bmDate,
            fzcDate,
            wjDate,
            wjzshName,
            wjzclName,
            degree,
            recipient,
            sjPhone,
            sjAdrees
        FROM clinical_stamp_application
    </sql>

    <!-- 方法 -->
    <select id="queryClinicalStampList" parameterType="ClinicalStamp" resultMap="ClinicalStampResult">
        <include refid="selectClinicalStampList"/>
        <where>
            (department LIKE '%临床监查部%' OR department LIKE '%临床质量管理部_稽查%' OR department LIKE '%商业运营部%' OR department LIKE '%临床开发%')
            <if test="applicationName != null and applicationName != ''">and applicationName like concat('%',
                #{applicationName}, '%')
            </if>
            <if test="oddNumber != null and oddNumber != ''">and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="jobNumber != null and jobNumber != ''">and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="documentStatus != null and documentStatus != ''">and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="company != null and company != ''">and company like concat('%', #{company}, '%')</if>
            <if test="degree != null and degree != ''">and degree = #{degree}</if>
            <if test="stampType != null and stampType != ''">and stampType like concat('%', #{stampType}, '%')</if>
            <if test="recipient != null and recipient != ''">and recipient like concat('%', #{recipient}, '%')</if>

            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
            <if test="startDate1 != null and startDate1 != ''">and wjzDate &gt;= #{startDate1}</if>
            <if test="endDate1 != null and endDate1 != ''">and wjzDate &lt;= #{endDate1}</if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>