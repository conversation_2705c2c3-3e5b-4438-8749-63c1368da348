<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.dms.mapper.OAApprovalRecordInfoMapper">

    <resultMap type="com.akesobio.report.dms.domain.OAApprovalRecordInfo" id="OAApprovalRecordInfoResult">
        <result property="id" column="id"/>
        <result property="operator" column="operator"/>
        <result property="nodeName" column="nodeName"/>
        <result property="receptionTime" column="receptionTime"/>
        <result property="code" column="code"/>
        <result property="currentProcessor" column="currentProcessor"/>
    </resultMap>

    <select id="selectOAApprovalRecordInfoList" parameterType="OAApprovalRecordInfo"
            resultMap="OAApprovalRecordInfoResult">
        SELECT
        id,
        operator,
        nodeName,
        receptionTime,
        code,
        currentProcessor
        FROM
        (
        SELECT
        a.fd_process_id 'id',
        a.fd_fact_node_name 'nodeName',
        b.fd_name 'operator',
        a.fd_create_time 'receptionTime',
        d.fd_number 'code',
        STUFF(
        (
        SELECT DISTINCT
        ',' + fd_name
        FROM
        sys_org_element
        WHERE
        fd_id IN ( SELECT fd_expected_id FROM lbpm_workitem WHERE fd_node_id = f.fd_id ) FOR XML PATH ( '' )
        ),
        1,
        1,
        ''
        ) 'currentProcessor'
        FROM
        lbpm_process s
        LEFT JOIN lbpm_audit_note a ON a.fd_process_id= s.fd_id
        LEFT JOIN sys_org_element b ON a.fd_handler_id= b.fd_id
        LEFT JOIN modeling_model_main d ON d.fd_id= s.fd_id
        LEFT JOIN lbpm_node f ON f.fd_process_id= s.fd_id
        WHERE
        a.fd_action_key IN (
        'handler_pass',
        'handler_refuse',
        'handler_abandon',
        'handler_commission',
        'handler_returnCommunicate',
        'handler_cancelCommunicate',
        'handler_additionSign',
        'handler_nodeSuspend',
        'handler_assign',
        'handler_assignCancel',
        'handler_assignRefuse',
        'handler_superRefuse',
        'drafter_submit',
        '_pocess_end',
        '_process_restart',
        '_process_send',
        'drafter_abandon'
        )
        ) b
        <where>
            <if test="id != null  and id != ''">and id = #{id}</if>
        </where>
    </select>
</mapper>