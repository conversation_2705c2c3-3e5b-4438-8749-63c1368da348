<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.dms.mapper.PurchaseOrderMapper">

    <resultMap type="com.akesobio.report.dms.domain.PurchaseOrder" id="PurchaseOrderResult">
        <result property="code" column="code"/>
        <result property="accountCode" column="accountCode"/>
        <result property="totalOrder" column="totalOrder"/>
        <result property="totalReceivable" column="totalReceivable"/>
        <result property="businessRepresentativeCode" column="businessRepresentativeCode"/>
        <result property="businessManagerCode" column="businessManagerCode"/>
        <result property="salesOrganizationCode" column="salesOrganizationCode"/>
        <result property="paymentTermsCode" column="paymentTermsCode"/>
        <result property="sapStatus" column="sapStatus"/>
        <result property="totalAllowance" column="totalAllowance"/>
        <result property="allowanceAmount" column="allowanceAmount"/>
        <result property="compensation" column="compensation"/>
        <result property="orderDate" column="orderDate"/>
        <result property="deliveryAddressCode" column="deliveryAddressCode"/>
        <result property="provinceName" column="provinceName"/>
        <result property="cityName" column="cityName"/>
        <result property="countyName" column="countyName"/>
        <result property="weekendTime" column="weekendTime"/>
        <result property="invoiceType" column="invoiceType"/>
        <result property="invoiceAddress" column="invoiceAddress"/>
        <result property="invoiceWay" column="invoiceWay"/>
        <result property="invoiceDemand" column="invoiceDemand"/>
        <result property="fdid" column="fdid"/>
        <result property="stash" column="stash"/>
        <result property="check" column="check"/>
        <result property="taxAmount" column="taxAmount"/>
        <result property="payment" column="payment"/>
        <result property="message" column="message"/>
        <result property="logisticsCompany" column="logisticsCompany"/>
        <result property="shipmentsTime" column="shipmentsTime"/>
        <result property="logisticsBill" column="logisticsBill"/>
        <result property="insureName" column="insureName"/>
        <result property="sapCode" column="sapCode"/>
        <result property="sapInvoice" column="sapInvoice"/>
        <result property="deliveryCode" column="deliveryCode"/>
        <result property="invoiceExpressInfo" column="invoiceExpressInfo"/>
        <result property="invoiceDate" column="invoiceDate"/>
        <result property="invoiceNumber" column="invoiceNumber"/>
        <result property="invoiceSign" column="invoiceSign"/>
        <result property="auditStatus" column="auditStatus"/>
        <result property="currentHandler" column="currentHandler"/>
        <result property="idea" column="idea"/>
        <collection property="details" ofType="com.akesobio.report.dms.domain.PurchaseOrderDetails">
            <result property="number" column="number"/>
            <result property="productCode" column="productCode"/>
            <result property="price" column="price"/>
            <result property="taxRate" column="taxRate"/>
            <result property="quantity" column="quantity"/>
            <result property="totalOrder" column="totalOrder1"/>
            <result property="allowance" column="allowance"/>
            <result property="compensation" column="compensation1"/>
            <result property="receivable" column="receivable"/>
        </collection>
    </resultMap>

    <select id="selectPurchaseOrderList" resultMap="PurchaseOrderResult">
        SELECT
            a.fd_3abf84e024cbfe 'code',
                a.fd_3ad00c8a3ee33a 'accountCode',
                a.fd_3ac07a41a0936e 'totalOrder',
                a.fd_3ad8af6c80c3ec 'totalReceivable',
                a.fd_3bd5eff7b30fba 'businessRepresentativeCode',
                a.fd_3ba92db45aeab8 'businessManagerCode',
                a.fd_3ad013e53bcf88 'salesOrganizationCode',
                a.fd_3d9dc9491ec9f6 'paymentTermsCode',
                a.fd_3ad2b85d4bf7d8 'sapStatus',
                a.fd_3ac92706221f44 'totalAllowance',
                a.fd_3ad4d5fc75f066 'allowanceAmount',
                a.tax_rate_makeup_1 'compensation',
                a.fd_3abf84ee12b8e8 'orderDate',
                NULL 'deliveryAddressCode',
                (CASE a.province WHEN '上海' THEN '上海市' WHEN '北京' THEN '北京市' WHEN '天津' THEN '天津市' WHEN '重庆' THEN '重庆市' ELSE a.province END) 'provinceName',
                a.city 'cityName',
                a.district 'countyName',
                a.fd_3bc3493ce57946 'weekendTime',
                CASE
                    a.fd_3bb0fc3a8f419e
                    WHEN '电子发票' THEN
                        10
                    WHEN '纸质发票' THEN
                        20 ELSE ''
                    END 'invoiceType',
                a.fd_3ad2c27addff84_text 'invoiceAddress',
                a.fd_3ac07acd70f50a 'invoiceWay',
                a.fd_3ac20f46db6f96 'invoiceDemand',
                a.fd_id 'fdid',
                CASE
                    WHEN a.fd_3c4ee03d202ea4 IS NOT NULL THEN
                        a.fd_3c4ee03d202ea4 ELSE
                    CASE
                        WHEN a.warehouse = '1000' THEN
                            '1000（中山康方）'
                        WHEN a.warehouse = '1020' THEN
                            '1020（康方天成）'
                        WHEN a.warehouse = '1030' THEN
                            '1030（康方赛诺）'
                        WHEN a.warehouse = '1050' THEN
                            '1050（康方药业）'
                        WHEN a.warehouse = '1060' THEN
                            '1060（康融东方）'
                        WHEN a.warehouse = '1070' THEN
                            '1070（康融广州）'
                        WHEN a.warehouse = '1080' THEN
                            '1080（康方隆跃）'
                        WHEN a.warehouse = '1200' THEN
                            '1200（康方添成）'
                        WHEN a.warehouse = '1230' THEN
                            '1230（康方汇科）'
                        WHEN a.warehouse = '1250' THEN
                            '1250（汇科广州）'
                        WHEN a.warehouse = '1610' THEN
                            '1610（广东泽晟）' ELSE a.warehouse
                        END
                    END 'stash',
                a.fd_3ac07a99c02d8a 'check',
                a.fd_3c5c4305c76b40 'taxAmount',
                a.fd_3c5c42a867013c 'payment',
                a.return_information 'message',
                a.fd_3b9ee91d72980c 'logisticsCompany',
                a.fd_rqr_pkp_time 'shipmentsTime',
                a.fd_entrust_no 'logisticsBill',
                CASE
                    a.safe_item
                    WHEN '投保' THEN
                        '是'
                    WHEN '不投保' THEN
                        '否' ELSE ''
                    END 'insureName',
                a.fd_3ad062abbb3798 'sapCode',
                a.invoice_no 'sapInvoice',
                a.delivery_no 'deliveryCode',
                a.fd_3ac07adb27c0c4 'invoiceExpressInfo',
                CAST(a.fd_3b2aa5a5d58606 AS DATE) 'invoiceDate',
                a.proof 'invoiceNumber',
                CASE
                    a.fd_3ad9aa40081e5c
                    WHEN '已签收' THEN
                        10 ELSE '20'
                    END 'invoiceSign',
                '40' 'auditStatus',
            NULL 'currentHandler',
                NULL 'idea',
                NULL 'number',
                b.fd_3ad056ad385a8e_text 'productCode',
                b.fd_3ac079dd52666a 'price',
                (CASE
                     WHEN b.fd_3ac079ddd91d84 IS NULL THEN ''
                     WHEN b.fd_3ac079ddd91d84 LIKE '%\%%' ESCAPE '\' THEN
                         REPLACE( b.fd_3ac079ddd91d84, '%', '' ) ELSE b.fd_3ac079ddd91d84
                END) 'taxRate',
                b.fd_3ac079dee74a7e 'quantity',
                b.fd_3ac079e7365382 'totalOrder1',
                b.fd_3ad2adc2fd2ab2 'allowance',
                b.fd_3cf87769bd936e 'compensation1',
                b.fd_3ad8af5f33843a 'receivable'
        FROM
            ekp_purchaseOrder a
                LEFT JOIN ekp_chaseOrder_79239994d4 b ON b.fd_parent_id= a.fd_id
                LEFT JOIN [dbo].[modeling_model_main] d ON a.fd_id = d.fd_id
        WHERE
            d.doc_status = '30'
    </select>
</mapper>