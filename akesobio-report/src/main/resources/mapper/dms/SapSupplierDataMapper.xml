<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.dms.mapper.SapSupplierDataMapper">

    <resultMap type="com.akesobio.report.dms.domain.SapSupplierData" id="SapSupplierDataResult">
        <result property="supplierName" column="supplierName"/>
        <result property="firstLevelMerchantCode" column="firstLevelMerchantCode"/>
        <result property="secondaryMerchantCode" column="secondaryMerchantCode"/>
    </resultMap>

    <select id="selectSapSupplierData" parameterType="SapSupplierData"
            resultMap="SapSupplierDataResult">
        SELECT
        supplierName,
        firstLevelMerchantCode,
        secondaryMerchantCode
        FROM
        ( SELECT a.name_org1 'supplierName', a.partner 'firstLevelMerchantCode', a.zejjxsbh 'secondaryMerchantCode' FROM
        SupplierMD a WHERE a.efficient= '1' ) b
        <where>
            <if test="firstLevelMerchantCode != null  and firstLevelMerchantCode != ''">and firstLevelMerchantCode like concat('%', #{firstLevelMerchantCode},
                '%')
            </if>
        </where>
    </select>
</mapper>