<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.SalesInvoiceReportMapper">

    <resultMap type="SalesInvoiceReport" id="SalesInvoiceReportResult">
        <result property="sid" column="sid"/>
        <result property="vbeln" column="vbeln"/>
        <result property="fksto" column="fksto"/>
        <result property="fkart" column="fkart"/>
        <result property="rfbsk" column="rfbsk"/>
        <result property="vtext" column="vtext"/>
        <result property="fkdat" column="fkdat"/>
        <result property="posnr" column="posnr"/>
        <result property="vkorgAuft" column="vkorg_auft"/>
        <result property="zxszzms" column="zxszzms"/>
        <result property="vtwegAuft" column="vtweg_auft"/>
        <result property="zfxqdms" column="zfxqdms"/>
        <result property="spara" column="spara"/>
        <result property="zcpzms" column="zcpzms"/>
        <result property="vkbur" column="vkbur"/>
        <result property="zxsbgsms" column="zxsbgsms"/>
        <result property="vkgrp" column="vkgrp"/>
        <result property="zxszms" column="zxszms"/>
        <result property="bzirkAuft" column="bzirk_auft"/>
        <result property="zxsdqs" column="zxsdqs"/>
        <result property="kunag" column="kunag"/>
        <result property="zsdfmc" column="zsdfmc"/>
        <result property="kunrg" column="kunrg"/>
        <result property="zfkfms" column="zfkfms"/>
        <result property="kunnr" column="kunnr"/>
        <result property="zsdfms" column="zsdfms"/>
        <result property="pstyv" column="pstyv"/>
        <result property="werks" column="werks"/>
        <result property="wadatIst" column="wadat_ist"/>
        <result property="podat" column="podat"/>
        <result property="matnr" column="matnr"/>
        <result property="arktx" column="arktx"/>
        <result property="fklmg" column="fklmg"/>
        <result property="mseh6" column="mseh6"/>
        <result property="zhsdj" column="zhsdj"/>
        <result property="waerk" column="waerk"/>
        <result property="netwr" column="netwr"/>
        <result property="mwsbp" column="mwsbp"/>
        <result property="zhsje" column="zhsje"/>
        <result property="vtext1" column="vtext1"/>
        <result property="vgbel" column="vgbel"/>
        <result property="vgpos" column="vgpos"/>
        <result property="aubel" column="aubel"/>
        <result property="aupos" column="aupos"/>
        <result property="sfakn" column="sfakn"/>
        <result property="zxsjg" column="zxsjg"/>
        <result property="zxszk" column="zxszk"/>
        <result property="zphjz" column="zphjz"/>
        <result property="zphse" column="zphse"/>
        <result property="zbcje" column="zbcje"/>
        <result property="ernam1" column="ernam1"/>
        <result property="erdat" column="erdat"/>
        <result property="lifnr1" column="lifnr1"/>
        <result property="name1" column="name1"/>
        <result property="lifnr2" column="lifnr2"/>
        <result property="name2" column="name2"/>
        <result property="lifnr3" column="lifnr3"/>
        <result property="name3" column="name3"/>
        <result property="zbclx" column="zbclx"/>
        <result property="zoadh" column="zoadh"/>
        <result property="znbph" column="znbph"/>
        <result property="bstnk" column="bstnk"/>
        <result property="charg" column="charg"/>
        <result property="bwtar" column="bwtar"/>
        <result property="vv003" column="vv003"/>
        <result property="htxt" column="htxt"/>
        <result property="zzjsbh1" column="zzjsbh1"/>
        <result property="zzjsbh2" column="zzjsbh2"/>
        <result property="zzjsrq" column="zzjsrq"/>
        <result property="zpdfAddr" column="zpdf_addr"/>
        <result property="zrecEmail" column="zrec_email"/>
    </resultMap>

    <sql id="selectSalesInvoiceReportVo">
        select sid,
               vbeln,
               fksto,
               fkart,
               rfbsk,
               vtext,
               fkdat,
               posnr,
               vkorg_auft,
               zxszzms,
               vtweg_auft,
               zfxqdms,
               spara,
               zcpzms,
               vkbur,
               zxsbgsms,
               vkgrp,
               zxszms,
               bzirk_auft,
               zxsdqs,
               kunag,
               zsdfmc,
               kunrg,
               zfkfms,
               kunnr,
               zsdfms,
               pstyv,
               werks,
               wadat_ist,
               podat,
               matnr,
               arktx,
               fklmg,
               mseh6,
               zhsdj,
               waerk,
               netwr,
               mwsbp,
               zhsje,
               vtext1,
               vgbel,
               vgpos,
               aubel,
               aupos,
               sfakn,
               zxsjg,
               zxszk,
               zphjz,
               zphse,
               zbcje,
               ernam1,
               erdat,
               lifnr1,
               name1,
               lifnr2,
               name2,
               lifnr3,
               name3,
               zbclx,
               zoadh,
               znbph,
               bstnk,
               charg,
               bwtar,
               vv003,
               htxt,
               zzjsbh1,
               zzjsbh2,
               zzjsrq,
               zpdf_addr,
               zrec_email
        from SalesInvoiceReport
    </sql>

    <select id="selectSalesInvoiceReportList" parameterType="SalesInvoiceReport" resultMap="SalesInvoiceReportResult">
        <include refid="selectSalesInvoiceReportVo"/>
        <where>
            <if test="vbeln != null  and vbeln != ''">and vbeln = #{vbeln}</if>
            <if test="fksto != null  and fksto != ''">and fksto = #{fksto}</if>
            <if test="fkart != null  and fkart != ''">and fkart = #{fkart}</if>
            <if test="rfbsk != null  and rfbsk != ''">and rfbsk = #{rfbsk}</if>
            <if test="vtext != null  and vtext != ''">and vtext = #{vtext}</if>
            <if test="fkdat != null  and fkdat != ''">and fkdat = #{fkdat}</if>
            <if test="posnr != null  and posnr != ''">and posnr = #{posnr}</if>
            <if test="vkorgAuft != null  and vkorgAuft != ''">and vkorg_auft = #{vkorgAuft}</if>
            <if test="zxszzms != null  and zxszzms != ''">and zxszzms = #{zxszzms}</if>
            <if test="vtwegAuft != null  and vtwegAuft != ''">and vtweg_auft = #{vtwegAuft}</if>
            <if test="zfxqdms != null  and zfxqdms != ''">and zfxqdms = #{zfxqdms}</if>
            <if test="spara != null  and spara != ''">and spara = #{spara}</if>
            <if test="zcpzms != null  and zcpzms != ''">and zcpzms = #{zcpzms}</if>
            <if test="vkbur != null  and vkbur != ''">and vkbur = #{vkbur}</if>
            <if test="zxsbgsms != null  and zxsbgsms != ''">and zxsbgsms = #{zxsbgsms}</if>
            <if test="vkgrp != null  and vkgrp != ''">and vkgrp = #{vkgrp}</if>
            <if test="zxszms != null  and zxszms != ''">and zxszms = #{zxszms}</if>
            <if test="bzirkAuft != null  and bzirkAuft != ''">and bzirk_auft = #{bzirkAuft}</if>
            <if test="zxsdqs != null  and zxsdqs != ''">and zxsdqs = #{zxsdqs}</if>
            <if test="kunag != null  and kunag != ''">and kunag = #{kunag}</if>
            <if test="zsdfmc != null  and zsdfmc != ''">and zsdfmc = #{zsdfmc}</if>
            <if test="kunrg != null  and kunrg != ''">and kunrg = #{kunrg}</if>
            <if test="zfkfms != null  and zfkfms != ''">and zfkfms = #{zfkfms}</if>
            <if test="kunnr != null  and kunnr != ''">and kunnr = #{kunnr}</if>
            <if test="zsdfms != null  and zsdfms != ''">and zsdfms = #{zsdfms}</if>
            <if test="pstyv != null  and pstyv != ''">and pstyv = #{pstyv}</if>
            <if test="werks != null  and werks != ''">and werks = #{werks}</if>
            <if test="wadatIst != null  and wadatIst != ''">and wadat_ist like concat('%',
                #{wadatIst}, '%')
            </if>
            <!--            <if test="wadatIst != null  and wadatIst != ''">and wadat_ist = #{wadatIst}</if>-->
            <if test="podat != null  and podat != ''">and podat = #{podat}</if>
            <if test="matnr != null  and matnr != ''">and matnr = #{matnr}</if>
            <if test="arktx != null  and arktx != ''">and arktx = #{arktx}</if>
            <if test="fklmg != null  and fklmg != ''">and fklmg = #{fklmg}</if>
            <if test="mseh6 != null  and mseh6 != ''">and mseh6 = #{mseh6}</if>
            <if test="zhsdj != null  and zhsdj != ''">and zhsdj = #{zhsdj}</if>
            <if test="waerk != null  and waerk != ''">and waerk = #{waerk}</if>
            <if test="netwr != null  and netwr != ''">and netwr = #{netwr}</if>
            <if test="mwsbp != null  and mwsbp != ''">and mwsbp = #{mwsbp}</if>
            <if test="zhsje != null  and zhsje != ''">and zhsje = #{zhsje}</if>
            <if test="vtext1 != null  and vtext1 != ''">and vtext1 = #{vtext1}</if>
            <if test="vgbel != null  and vgbel != ''">and vgbel = #{vgbel}</if>
            <if test="vgpos != null  and vgpos != ''">and vgpos = #{vgpos}</if>
            <if test="aubel != null  and aubel != ''">and aubel = #{aubel}</if>
            <if test="aupos != null  and aupos != ''">and aupos = #{aupos}</if>
            <if test="sfakn != null  and sfakn != ''">and sfakn = #{sfakn}</if>
            <if test="zxsjg != null  and zxsjg != ''">and zxsjg = #{zxsjg}</if>
            <if test="zxszk != null  and zxszk != ''">and zxszk = #{zxszk}</if>
            <if test="zphjz != null  and zphjz != ''">and zphjz = #{zphjz}</if>
            <if test="zphse != null  and zphse != ''">and zphse = #{zphse}</if>
            <if test="zbcje != null  and zbcje != ''">and zbcje = #{zbcje}</if>
            <if test="ernam1 != null  and ernam1 != ''">and ernam1 = #{ernam1}</if>
            <if test="erdat != null  and erdat != ''">and erdat = #{erdat}</if>
            <if test="lifnr1 != null  and lifnr1 != ''">and lifnr1 = #{lifnr1}</if>
            <if test="name1 != null  and name1 != ''">and name1 = #{name1}</if>
            <if test="lifnr2 != null  and lifnr2 != ''">and lifnr2 = #{lifnr2}</if>
            <if test="name2 != null  and name2 != ''">and name2 = #{name2}</if>
            <if test="lifnr3 != null  and lifnr3 != ''">and lifnr3 = #{lifnr3}</if>
            <if test="name3 != null  and name3 != ''">and name3 = #{name3}</if>
            <if test="zbclx != null  and zbclx != ''">and zbclx = #{zbclx}</if>
            <if test="zoadh != null  and zoadh != ''">and zoadh = #{zoadh}</if>
            <if test="znbph != null  and znbph != ''">and znbph = #{znbph}</if>
            <if test="bstnk != null  and bstnk != ''">and bstnk = #{bstnk}</if>
            <if test="charg != null  and charg != ''">and charg = #{charg}</if>
            <if test="bwtar != null  and bwtar != ''">and bwtar = #{bwtar}</if>
            <if test="vv003 != null  and vv003 != ''">and vv003 = #{vv003}</if>
            <if test="htxt != null  and htxt != ''">and htxt = #{htxt}</if>
            <if test="zzjsbh1 != null  and zzjsbh1 != ''">and zzjsbh1 = #{zzjsbh1}</if>
            <if test="zzjsbh2 != null  and zzjsbh2 != ''">and zzjsbh2 = #{zzjsbh2}</if>
            <if test="zzjsrq != null  and zzjsrq != ''">and zzjsrq = #{zzjsrq}</if>
            <if test="zpdfAddr != null  and zpdfAddr != ''">and zpdf_addr = #{zpdfAddr}</if>
            <if test="zrecEmail != null  and zrecEmail != ''">and zrec_email = #{zrecEmail}</if>
        </where>
    </select>

    <select id="selectSalesInvoiceReportBySid" parameterType="Long" resultMap="SalesInvoiceReportResult">
        <include refid="selectSalesInvoiceReportVo"/>
        where sid = #{sid}
    </select>

    <insert id="insertSalesInvoiceReport" parameterType="SalesInvoiceReport">
        insert into SalesInvoiceReport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="vbeln != null">vbeln,</if>
            <if test="fksto != null">fksto,</if>
            <if test="fkart != null">fkart,</if>
            <if test="rfbsk != null">rfbsk,</if>
            <if test="vtext != null">vtext,</if>
            <if test="fkdat != null">fkdat,</if>
            <if test="posnr != null">posnr,</if>
            <if test="vkorgAuft != null">vkorg_auft,</if>
            <if test="zxszzms != null">zxszzms,</if>
            <if test="vtwegAuft != null">vtweg_auft,</if>
            <if test="zfxqdms != null">zfxqdms,</if>
            <if test="spara != null">spara,</if>
            <if test="zcpzms != null">zcpzms,</if>
            <if test="vkbur != null">vkbur,</if>
            <if test="zxsbgsms != null">zxsbgsms,</if>
            <if test="vkgrp != null">vkgrp,</if>
            <if test="zxszms != null">zxszms,</if>
            <if test="bzirkAuft != null">bzirk_auft,</if>
            <if test="zxsdqs != null">zxsdqs,</if>
            <if test="kunag != null">kunag,</if>
            <if test="zsdfmc != null">zsdfmc,</if>
            <if test="kunrg != null">kunrg,</if>
            <if test="zfkfms != null">zfkfms,</if>
            <if test="kunnr != null">kunnr,</if>
            <if test="zsdfms != null">zsdfms,</if>
            <if test="pstyv != null">pstyv,</if>
            <if test="werks != null">werks,</if>
            <if test="wadatIst != null">wadat_ist,</if>
            <if test="podat != null">podat,</if>
            <if test="matnr != null">matnr,</if>
            <if test="arktx != null">arktx,</if>
            <if test="fklmg != null">fklmg,</if>
            <if test="mseh6 != null">mseh6,</if>
            <if test="zhsdj != null">zhsdj,</if>
            <if test="waerk != null">waerk,</if>
            <if test="netwr != null">netwr,</if>
            <if test="mwsbp != null">mwsbp,</if>
            <if test="zhsje != null">zhsje,</if>
            <if test="vtext1 != null">vtext1,</if>
            <if test="vgbel != null">vgbel,</if>
            <if test="vgpos != null">vgpos,</if>
            <if test="aubel != null">aubel,</if>
            <if test="aupos != null">aupos,</if>
            <if test="sfakn != null">sfakn,</if>
            <if test="zxsjg != null">zxsjg,</if>
            <if test="zxszk != null">zxszk,</if>
            <if test="zphjz != null">zphjz,</if>
            <if test="zphse != null">zphse,</if>
            <if test="zbcje != null">zbcje,</if>
            <if test="ernam1 != null">ernam1,</if>
            <if test="erdat != null">erdat,</if>
            <if test="lifnr1 != null">lifnr1,</if>
            <if test="name1 != null">name1,</if>
            <if test="lifnr2 != null">lifnr2,</if>
            <if test="name2 != null">name2,</if>
            <if test="lifnr3 != null">lifnr3,</if>
            <if test="name3 != null">name3,</if>
            <if test="zbclx != null">zbclx,</if>
            <if test="zoadh != null">zoadh,</if>
            <if test="znbph != null">znbph,</if>
            <if test="bstnk != null">bstnk,</if>
            <if test="charg != null">charg,</if>
            <if test="bwtar != null">bwtar,</if>
            <if test="vv003 != null">vv003,</if>
            <if test="htxt != null">htxt,</if>
            <if test="zzjsbh1 != null">zzjsbh1,</if>
            <if test="zzjsbh2 != null">zzjsbh2,</if>
            <if test="zzjsrq != null">zzjsrq,</if>
            <if test="zpdfAddr != null">zpdf_addr,</if>
            <if test="zrecEmail != null">zrec_email,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="vbeln != null">#{vbeln},</if>
            <if test="fksto != null">#{fksto},</if>
            <if test="fkart != null">#{fkart},</if>
            <if test="rfbsk != null">#{rfbsk},</if>
            <if test="vtext != null">#{vtext},</if>
            <if test="fkdat != null">#{fkdat},</if>
            <if test="posnr != null">#{posnr},</if>
            <if test="vkorgAuft != null">#{vkorgAuft},</if>
            <if test="zxszzms != null">#{zxszzms},</if>
            <if test="vtwegAuft != null">#{vtwegAuft},</if>
            <if test="zfxqdms != null">#{zfxqdms},</if>
            <if test="spara != null">#{spara},</if>
            <if test="zcpzms != null">#{zcpzms},</if>
            <if test="vkbur != null">#{vkbur},</if>
            <if test="zxsbgsms != null">#{zxsbgsms},</if>
            <if test="vkgrp != null">#{vkgrp},</if>
            <if test="zxszms != null">#{zxszms},</if>
            <if test="bzirkAuft != null">#{bzirkAuft},</if>
            <if test="zxsdqs != null">#{zxsdqs},</if>
            <if test="kunag != null">#{kunag},</if>
            <if test="zsdfmc != null">#{zsdfmc},</if>
            <if test="kunrg != null">#{kunrg},</if>
            <if test="zfkfms != null">#{zfkfms},</if>
            <if test="kunnr != null">#{kunnr},</if>
            <if test="zsdfms != null">#{zsdfms},</if>
            <if test="pstyv != null">#{pstyv},</if>
            <if test="werks != null">#{werks},</if>
            <if test="wadatIst != null">#{wadatIst},</if>
            <if test="podat != null">#{podat},</if>
            <if test="matnr != null">#{matnr},</if>
            <if test="arktx != null">#{arktx},</if>
            <if test="fklmg != null">#{fklmg},</if>
            <if test="mseh6 != null">#{mseh6},</if>
            <if test="zhsdj != null">#{zhsdj},</if>
            <if test="waerk != null">#{waerk},</if>
            <if test="netwr != null">#{netwr},</if>
            <if test="mwsbp != null">#{mwsbp},</if>
            <if test="zhsje != null">#{zhsje},</if>
            <if test="vtext1 != null">#{vtext1},</if>
            <if test="vgbel != null">#{vgbel},</if>
            <if test="vgpos != null">#{vgpos},</if>
            <if test="aubel != null">#{aubel},</if>
            <if test="aupos != null">#{aupos},</if>
            <if test="sfakn != null">#{sfakn},</if>
            <if test="zxsjg != null">#{zxsjg},</if>
            <if test="zxszk != null">#{zxszk},</if>
            <if test="zphjz != null">#{zphjz},</if>
            <if test="zphse != null">#{zphse},</if>
            <if test="zbcje != null">#{zbcje},</if>
            <if test="ernam1 != null">#{ernam1},</if>
            <if test="erdat != null">#{erdat},</if>
            <if test="lifnr1 != null">#{lifnr1},</if>
            <if test="name1 != null">#{name1},</if>
            <if test="lifnr2 != null">#{lifnr2},</if>
            <if test="name2 != null">#{name2},</if>
            <if test="lifnr3 != null">#{lifnr3},</if>
            <if test="name3 != null">#{name3},</if>
            <if test="zbclx != null">#{zbclx},</if>
            <if test="zoadh != null">#{zoadh},</if>
            <if test="znbph != null">#{znbph},</if>
            <if test="bstnk != null">#{bstnk},</if>
            <if test="charg != null">#{charg},</if>
            <if test="bwtar != null">#{bwtar},</if>
            <if test="vv003 != null">#{vv003},</if>
            <if test="htxt != null">#{htxt},</if>
            <if test="zzjsbh1 != null">#{zzjsbh1},</if>
            <if test="zzjsbh2 != null">#{zzjsbh2},</if>
            <if test="zzjsrq != null">#{zzjsrq},</if>
            <if test="zpdfAddr != null">#{zpdfAddr},</if>
            <if test="zrecEmail != null">#{zrecEmail},</if>
        </trim>
    </insert>

    <update id="updateSalesInvoiceReport" parameterType="SalesInvoiceReport">
        update SalesInvoiceReport
        <trim prefix="SET" suffixOverrides=",">
            <if test="vbeln != null">vbeln = #{vbeln},</if>
            <if test="fksto != null">fksto = #{fksto},</if>
            <if test="fkart != null">fkart = #{fkart},</if>
            <if test="rfbsk != null">rfbsk = #{rfbsk},</if>
            <if test="vtext != null">vtext = #{vtext},</if>
            <if test="fkdat != null">fkdat = #{fkdat},</if>
            <if test="posnr != null">posnr = #{posnr},</if>
            <if test="vkorgAuft != null">vkorg_auft = #{vkorgAuft},</if>
            <if test="zxszzms != null">zxszzms = #{zxszzms},</if>
            <if test="vtwegAuft != null">vtweg_auft = #{vtwegAuft},</if>
            <if test="zfxqdms != null">zfxqdms = #{zfxqdms},</if>
            <if test="spara != null">spara = #{spara},</if>
            <if test="zcpzms != null">zcpzms = #{zcpzms},</if>
            <if test="vkbur != null">vkbur = #{vkbur},</if>
            <if test="zxsbgsms != null">zxsbgsms = #{zxsbgsms},</if>
            <if test="vkgrp != null">vkgrp = #{vkgrp},</if>
            <if test="zxszms != null">zxszms = #{zxszms},</if>
            <if test="bzirkAuft != null">bzirk_auft = #{bzirkAuft},</if>
            <if test="zxsdqs != null">zxsdqs = #{zxsdqs},</if>
            <if test="kunag != null">kunag = #{kunag},</if>
            <if test="zsdfmc != null">zsdfmc = #{zsdfmc},</if>
            <if test="kunrg != null">kunrg = #{kunrg},</if>
            <if test="zfkfms != null">zfkfms = #{zfkfms},</if>
            <if test="kunnr != null">kunnr = #{kunnr},</if>
            <if test="zsdfms != null">zsdfms = #{zsdfms},</if>
            <if test="pstyv != null">pstyv = #{pstyv},</if>
            <if test="werks != null">werks = #{werks},</if>
            <if test="wadatIst != null">wadat_ist = #{wadatIst},</if>
            <if test="podat != null">podat = #{podat},</if>
            <if test="matnr != null">matnr = #{matnr},</if>
            <if test="arktx != null">arktx = #{arktx},</if>
            <if test="fklmg != null">fklmg = #{fklmg},</if>
            <if test="mseh6 != null">mseh6 = #{mseh6},</if>
            <if test="zhsdj != null">zhsdj = #{zhsdj},</if>
            <if test="waerk != null">waerk = #{waerk},</if>
            <if test="netwr != null">netwr = #{netwr},</if>
            <if test="mwsbp != null">mwsbp = #{mwsbp},</if>
            <if test="zhsje != null">zhsje = #{zhsje},</if>
            <if test="vtext1 != null">vtext1 = #{vtext1},</if>
            <if test="vgbel != null">vgbel = #{vgbel},</if>
            <if test="vgpos != null">vgpos = #{vgpos},</if>
            <if test="aubel != null">aubel = #{aubel},</if>
            <if test="aupos != null">aupos = #{aupos},</if>
            <if test="sfakn != null">sfakn = #{sfakn},</if>
            <if test="zxsjg != null">zxsjg = #{zxsjg},</if>
            <if test="zxszk != null">zxszk = #{zxszk},</if>
            <if test="zphjz != null">zphjz = #{zphjz},</if>
            <if test="zphse != null">zphse = #{zphse},</if>
            <if test="zbcje != null">zbcje = #{zbcje},</if>
            <if test="ernam1 != null">ernam1 = #{ernam1},</if>
            <if test="erdat != null">erdat = #{erdat},</if>
            <if test="lifnr1 != null">lifnr1 = #{lifnr1},</if>
            <if test="name1 != null">name1 = #{name1},</if>
            <if test="lifnr2 != null">lifnr2 = #{lifnr2},</if>
            <if test="name2 != null">name2 = #{name2},</if>
            <if test="lifnr3 != null">lifnr3 = #{lifnr3},</if>
            <if test="name3 != null">name3 = #{name3},</if>
            <if test="zbclx != null">zbclx = #{zbclx},</if>
            <if test="zoadh != null">zoadh = #{zoadh},</if>
            <if test="znbph != null">znbph = #{znbph},</if>
            <if test="bstnk != null">bstnk = #{bstnk},</if>
            <if test="charg != null">charg = #{charg},</if>
            <if test="bwtar != null">bwtar = #{bwtar},</if>
            <if test="vv003 != null">vv003 = #{vv003},</if>
            <if test="htxt != null">htxt = #{htxt},</if>
            <if test="zzjsbh1 != null">zzjsbh1 = #{zzjsbh1},</if>
            <if test="zzjsbh2 != null">zzjsbh2 = #{zzjsbh2},</if>
            <if test="zzjsrq != null">zzjsrq = #{zzjsrq},</if>
            <if test="zpdfAddr != null">zpdf_addr = #{zpdfAddr},</if>
            <if test="zrecEmail != null">zrec_email = #{zrecEmail},</if>
        </trim>
        where sid = #{sid}
    </update>

    <delete id="deleteSalesInvoiceReportBySid" parameterType="Long">
        delete
        from SalesInvoiceReport
        where sid = #{sid}
    </delete>

    <delete id="deleteSalesInvoiceReportBySids" parameterType="String">
        delete from SalesInvoiceReport where sid in
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>