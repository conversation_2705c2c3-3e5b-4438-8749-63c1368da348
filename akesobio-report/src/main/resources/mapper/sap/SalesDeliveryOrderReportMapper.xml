<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.SalesDeliveryOrderReportMapper">
    
    <resultMap type="SalesDeliveryOrderReport" id="SalesDeliveryOrderReportResult">
        <result property="sid"    column="sid"    />
        <result property="vstel"    column="vstel"    />
        <result property="vtext"    column="vtext"    />
        <result property="werks"    column="werks"    />
        <result property="name1"    column="name1"    />
        <result property="lgort"    column="lgort"    />
        <result property="lgobe"    column="lgobe"    />
        <result property="lfart"    column="lfart"    />
        <result property="zhlxms"    column="zhlxms"    />
        <result property="kunag"    column="kunag"    />
        <result property="sdfmc"    column="sdfmc"    />
        <result property="kunnr"    column="kunnr"    />
        <result property="zsdfmc"    column="zsdfmc"    />
        <result property="vbeln"    column="vbeln"    />
        <result property="posnr"    column="posnr"    />
        <result property="matnr"    column="matnr"    />
        <result property="maktx"    column="maktx"    />
        <result property="charg"    column="charg"    />
        <result property="zscpc"    column="zscpc"    />
        <result property="zscx"    column="zscx"    />
        <result property="hsdat"    column="hsdat"    />
        <result property="vfdat"    column="vfdat"    />
        <result property="lfimg"    column="lfimg"    />
        <result property="mseh6"    column="mseh6"    />
        <result property="wadatIst"    column="wadat_ist"    />
        <result property="wbstk"    column="wbstk"    />
        <result property="podat"    column="podat"    />
        <result property="pdstk"    column="pdstk"    />
        <result property="wadat"    column="wadat"    />
        <result property="bldat"    column="bldat"    />
        <result property="fkstk"    column="fkstk"    />
        <result property="bvbeln"    column="bvbeln"    />
        <result property="bposnr"    column="bposnr"    />
        <result property="rfbsk"    column="rfbsk"    />
        <result property="vgbel"    column="vgbel"    />
        <result property="bstnk"    column="bstnk"    />
        <result property="vgpos"    column="vgpos"    />
        <result property="kwmeng"    column="kwmeng"    />
        <result property="qtyUnite"    column="qty_unite"    />
        <result property="kbetrDj"    column="kbetr_dj"    />
        <result property="djPer"    column="dj_per"    />
        <result property="je"    column="je"    />
        <result property="jz"    column="jz"    />
        <result property="tax"    column="tax"    />
        <result property="erdat"    column="erdat"    />
        <result property="ernam1"    column="ernam1"    />
        <result property="znameCo"    column="zname_co"    />
        <result property="ztelNumber"    column="ztel_number"    />
        <result property="zstreet"    column="zstreet"    />
        <result property="vtweg"    column="vtweg"    />
    </resultMap>

    <sql id="selectSalesDeliveryOrderReportVo">
        select sid, vstel, vtext, werks, name1, lgort, lgobe, lfart, zhlxms, kunag, sdfmc, kunnr, zsdfmc, vbeln, posnr, matnr, maktx, charg, zscpc, zscx, hsdat, vfdat, lfimg, mseh6, wadat_ist, wbstk, podat, pdstk, wadat, bldat, fkstk, bvbeln, bposnr, rfbsk, vgbel, bstnk, vgpos, kwmeng, qty_unite, kbetr_dj, dj_per, je, jz, tax, erdat, ernam1, zname_co, ztel_number, zstreet, vtweg from sales_delivery_order_report
    </sql>

    <select id="selectSalesDeliveryOrderReportList" parameterType="SalesDeliveryOrderReport" resultMap="SalesDeliveryOrderReportResult">
        <include refid="selectSalesDeliveryOrderReportVo"/>
        <where>  
            <if test="vstel != null  and vstel != ''"> and vstel = #{vstel}</if>
            <if test="vtext != null  and vtext != ''"> and vtext = #{vtext}</if>
            <if test="werks != null  and werks != ''"> and werks = #{werks}</if>
            <if test="name1 != null  and name1 != ''"> and name1 = #{name1}</if>
            <if test="lgort != null  and lgort != ''"> and lgort = #{lgort}</if>
            <if test="lgobe != null  and lgobe != ''"> and lgobe = #{lgobe}</if>
            <if test="lfart != null  and lfart != ''"> and lfart = #{lfart}</if>
            <if test="zhlxms != null  and zhlxms != ''"> and zhlxms = #{zhlxms}</if>
            <if test="kunag != null  and kunag != ''"> and kunag = #{kunag}</if>
            <if test="sdfmc != null  and sdfmc != ''"> and sdfmc = #{sdfmc}</if>
            <if test="kunnr != null  and kunnr != ''"> and kunnr = #{kunnr}</if>
            <if test="zsdfmc != null  and zsdfmc != ''"> and zsdfmc = #{zsdfmc}</if>
            <if test="vbeln != null  and vbeln != ''"> and vbeln = #{vbeln}</if>
            <if test="posnr != null  and posnr != ''"> and posnr = #{posnr}</if>
            <if test="matnr != null  and matnr != ''"> and matnr = #{matnr}</if>
            <if test="maktx != null  and maktx != ''"> and maktx = #{maktx}</if>
            <if test="charg != null  and charg != ''"> and charg = #{charg}</if>
            <if test="zscpc != null  and zscpc != ''"> and zscpc = #{zscpc}</if>
            <if test="zscx != null  and zscx != ''"> and zscx = #{zscx}</if>
            <if test="hsdat != null  and hsdat != ''"> and hsdat = #{hsdat}</if>
            <if test="vfdat != null  and vfdat != ''"> and vfdat = #{vfdat}</if>
            <if test="lfimg != null  and lfimg != ''"> and lfimg = #{lfimg}</if>
            <if test="mseh6 != null  and mseh6 != ''"> and mseh6 = #{mseh6}</if>
            <if test="wadatIst != null  and wadatIst != ''"> and wadat_ist = #{wadatIst}</if>
            <if test="wbstk != null  and wbstk != ''"> and wbstk = #{wbstk}</if>
            <if test="podat != null  and podat != ''"> and podat = #{podat}</if>
            <if test="pdstk != null  and pdstk != ''"> and pdstk = #{pdstk}</if>
            <if test="wadat != null  and wadat != ''"> and wadat = #{wadat}</if>
            <if test="bldat != null  and bldat != ''"> and bldat = #{bldat}</if>
            <if test="fkstk != null  and fkstk != ''"> and fkstk = #{fkstk}</if>
            <if test="bvbeln != null  and bvbeln != ''"> and bvbeln = #{bvbeln}</if>
            <if test="bposnr != null  and bposnr != ''"> and bposnr = #{bposnr}</if>
            <if test="rfbsk != null  and rfbsk != ''"> and rfbsk = #{rfbsk}</if>
            <if test="vgbel != null  and vgbel != ''"> and vgbel = #{vgbel}</if>
            <if test="bstnk != null  and bstnk != ''"> and bstnk = #{bstnk}</if>
            <if test="vgpos != null  and vgpos != ''"> and vgpos = #{vgpos}</if>
            <if test="kwmeng != null  and kwmeng != ''"> and kwmeng = #{kwmeng}</if>
            <if test="qtyUnite != null  and qtyUnite != ''"> and qty_unite = #{qtyUnite}</if>
            <if test="kbetrDj != null  and kbetrDj != ''"> and kbetr_dj = #{kbetrDj}</if>
            <if test="djPer != null  and djPer != ''"> and dj_per = #{djPer}</if>
            <if test="je != null  and je != ''"> and je = #{je}</if>
            <if test="jz != null  and jz != ''"> and jz = #{jz}</if>
            <if test="tax != null  and tax != ''"> and tax = #{tax}</if>
            <if test="erdat != null  and erdat != ''"> and erdat = #{erdat}</if>
            <if test="ernam1 != null  and ernam1 != ''"> and ernam1 = #{ernam1}</if>
            <if test="znameCo != null  and znameCo != ''"> and zname_co = #{znameCo}</if>
            <if test="ztelNumber != null  and ztelNumber != ''"> and ztel_number = #{ztelNumber}</if>
            <if test="zstreet != null  and zstreet != ''"> and zstreet = #{zstreet}</if>
            <if test="vtweg != null  and vtweg != ''"> and vtweg = #{vtweg}</if>
        </where>
    </select>
    
    <select id="selectSalesDeliveryOrderReportBySid" parameterType="Long" resultMap="SalesDeliveryOrderReportResult">
        <include refid="selectSalesDeliveryOrderReportVo"/>
        where sid = #{sid}
    </select>
        
    <insert id="insertSalesDeliveryOrderReport" parameterType="SalesDeliveryOrderReport">
        insert into SalesDeliveryOrderReport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="vstel != null">vstel,</if>
            <if test="vtext != null">vtext,</if>
            <if test="werks != null">werks,</if>
            <if test="name1 != null">name1,</if>
            <if test="lgort != null">lgort,</if>
            <if test="lgobe != null">lgobe,</if>
            <if test="lfart != null">lfart,</if>
            <if test="zhlxms != null">zhlxms,</if>
            <if test="kunag != null">kunag,</if>
            <if test="sdfmc != null">sdfmc,</if>
            <if test="kunnr != null">kunnr,</if>
            <if test="zsdfmc != null">zsdfmc,</if>
            <if test="vbeln != null">vbeln,</if>
            <if test="posnr != null">posnr,</if>
            <if test="matnr != null">matnr,</if>
            <if test="maktx != null">maktx,</if>
            <if test="charg != null">charg,</if>
            <if test="zscpc != null">zscpc,</if>
            <if test="zscx != null">zscx,</if>
            <if test="hsdat != null">hsdat,</if>
            <if test="vfdat != null">vfdat,</if>
            <if test="lfimg != null">lfimg,</if>
            <if test="mseh6 != null">mseh6,</if>
            <if test="wadatIst != null">wadat_ist,</if>
            <if test="wbstk != null">wbstk,</if>
            <if test="podat != null">podat,</if>
            <if test="pdstk != null">pdstk,</if>
            <if test="wadat != null">wadat,</if>
            <if test="bldat != null">bldat,</if>
            <if test="fkstk != null">fkstk,</if>
            <if test="bvbeln != null">bvbeln,</if>
            <if test="bposnr != null">bposnr,</if>
            <if test="rfbsk != null">rfbsk,</if>
            <if test="vgbel != null">vgbel,</if>
            <if test="bstnk != null">bstnk,</if>
            <if test="vgpos != null">vgpos,</if>
            <if test="kwmeng != null">kwmeng,</if>
            <if test="qtyUnite != null">qty_unite,</if>
            <if test="kbetrDj != null">kbetr_dj,</if>
            <if test="djPer != null">dj_per,</if>
            <if test="je != null">je,</if>
            <if test="jz != null">jz,</if>
            <if test="tax != null">tax,</if>
            <if test="erdat != null">erdat,</if>
            <if test="ernam1 != null">ernam1,</if>
            <if test="znameCo != null">zname_co,</if>
            <if test="ztelNumber != null">ztel_number,</if>
            <if test="zstreet != null">zstreet,</if>
            <if test="vtweg != null">vtweg,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="vstel != null">#{vstel},</if>
            <if test="vtext != null">#{vtext},</if>
            <if test="werks != null">#{werks},</if>
            <if test="name1 != null">#{name1},</if>
            <if test="lgort != null">#{lgort},</if>
            <if test="lgobe != null">#{lgobe},</if>
            <if test="lfart != null">#{lfart},</if>
            <if test="zhlxms != null">#{zhlxms},</if>
            <if test="kunag != null">#{kunag},</if>
            <if test="sdfmc != null">#{sdfmc},</if>
            <if test="kunnr != null">#{kunnr},</if>
            <if test="zsdfmc != null">#{zsdfmc},</if>
            <if test="vbeln != null">#{vbeln},</if>
            <if test="posnr != null">#{posnr},</if>
            <if test="matnr != null">#{matnr},</if>
            <if test="maktx != null">#{maktx},</if>
            <if test="charg != null">#{charg},</if>
            <if test="zscpc != null">#{zscpc},</if>
            <if test="zscx != null">#{zscx},</if>
            <if test="hsdat != null">#{hsdat},</if>
            <if test="vfdat != null">#{vfdat},</if>
            <if test="lfimg != null">#{lfimg},</if>
            <if test="mseh6 != null">#{mseh6},</if>
            <if test="wadatIst != null">#{wadatIst},</if>
            <if test="wbstk != null">#{wbstk},</if>
            <if test="podat != null">#{podat},</if>
            <if test="pdstk != null">#{pdstk},</if>
            <if test="wadat != null">#{wadat},</if>
            <if test="bldat != null">#{bldat},</if>
            <if test="fkstk != null">#{fkstk},</if>
            <if test="bvbeln != null">#{bvbeln},</if>
            <if test="bposnr != null">#{bposnr},</if>
            <if test="rfbsk != null">#{rfbsk},</if>
            <if test="vgbel != null">#{vgbel},</if>
            <if test="bstnk != null">#{bstnk},</if>
            <if test="vgpos != null">#{vgpos},</if>
            <if test="kwmeng != null">#{kwmeng},</if>
            <if test="qtyUnite != null">#{qtyUnite},</if>
            <if test="kbetrDj != null">#{kbetrDj},</if>
            <if test="djPer != null">#{djPer},</if>
            <if test="je != null">#{je},</if>
            <if test="jz != null">#{jz},</if>
            <if test="tax != null">#{tax},</if>
            <if test="erdat != null">#{erdat},</if>
            <if test="ernam1 != null">#{ernam1},</if>
            <if test="znameCo != null">#{znameCo},</if>
            <if test="ztelNumber != null">#{ztelNumber},</if>
            <if test="zstreet != null">#{zstreet},</if>
            <if test="vtweg != null">#{vtweg},</if>
         </trim>
    </insert>

    <update id="updateSalesDeliveryOrderReport" parameterType="SalesDeliveryOrderReport">
        update SalesDeliveryOrderReport
        <trim prefix="SET" suffixOverrides=",">
            <if test="vstel != null">vstel = #{vstel},</if>
            <if test="vtext != null">vtext = #{vtext},</if>
            <if test="werks != null">werks = #{werks},</if>
            <if test="name1 != null">name1 = #{name1},</if>
            <if test="lgort != null">lgort = #{lgort},</if>
            <if test="lgobe != null">lgobe = #{lgobe},</if>
            <if test="lfart != null">lfart = #{lfart},</if>
            <if test="zhlxms != null">zhlxms = #{zhlxms},</if>
            <if test="kunag != null">kunag = #{kunag},</if>
            <if test="sdfmc != null">sdfmc = #{sdfmc},</if>
            <if test="kunnr != null">kunnr = #{kunnr},</if>
            <if test="zsdfmc != null">zsdfmc = #{zsdfmc},</if>
            <if test="vbeln != null">vbeln = #{vbeln},</if>
            <if test="posnr != null">posnr = #{posnr},</if>
            <if test="matnr != null">matnr = #{matnr},</if>
            <if test="maktx != null">maktx = #{maktx},</if>
            <if test="charg != null">charg = #{charg},</if>
            <if test="zscpc != null">zscpc = #{zscpc},</if>
            <if test="zscx != null">zscx = #{zscx},</if>
            <if test="hsdat != null">hsdat = #{hsdat},</if>
            <if test="vfdat != null">vfdat = #{vfdat},</if>
            <if test="lfimg != null">lfimg = #{lfimg},</if>
            <if test="mseh6 != null">mseh6 = #{mseh6},</if>
            <if test="wadatIst != null">wadat_ist = #{wadatIst},</if>
            <if test="wbstk != null">wbstk = #{wbstk},</if>
            <if test="podat != null">podat = #{podat},</if>
            <if test="pdstk != null">pdstk = #{pdstk},</if>
            <if test="wadat != null">wadat = #{wadat},</if>
            <if test="bldat != null">bldat = #{bldat},</if>
            <if test="fkstk != null">fkstk = #{fkstk},</if>
            <if test="bvbeln != null">bvbeln = #{bvbeln},</if>
            <if test="bposnr != null">bposnr = #{bposnr},</if>
            <if test="rfbsk != null">rfbsk = #{rfbsk},</if>
            <if test="vgbel != null">vgbel = #{vgbel},</if>
            <if test="bstnk != null">bstnk = #{bstnk},</if>
            <if test="vgpos != null">vgpos = #{vgpos},</if>
            <if test="kwmeng != null">kwmeng = #{kwmeng},</if>
            <if test="qtyUnite != null">qty_unite = #{qtyUnite},</if>
            <if test="kbetrDj != null">kbetr_dj = #{kbetrDj},</if>
            <if test="djPer != null">dj_per = #{djPer},</if>
            <if test="je != null">je = #{je},</if>
            <if test="jz != null">jz = #{jz},</if>
            <if test="tax != null">tax = #{tax},</if>
            <if test="erdat != null">erdat = #{erdat},</if>
            <if test="ernam1 != null">ernam1 = #{ernam1},</if>
            <if test="znameCo != null">zname_co = #{znameCo},</if>
            <if test="ztelNumber != null">ztel_number = #{ztelNumber},</if>
            <if test="zstreet != null">zstreet = #{zstreet},</if>
            <if test="vtweg != null">vtweg = #{vtweg},</if>
        </trim>
        where sid = #{sid}
    </update>

    <delete id="deleteSalesDeliveryOrderReportBySid" parameterType="Long">
        delete from SalesDeliveryOrderReport where sid = #{sid}
    </delete>

    <delete id="deleteSalesDeliveryOrderReportBySids" parameterType="String">
        delete from SalesDeliveryOrderReport where sid in 
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>