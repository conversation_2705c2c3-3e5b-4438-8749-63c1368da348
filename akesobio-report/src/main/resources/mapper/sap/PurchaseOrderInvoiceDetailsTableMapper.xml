<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.PurchaseOrderInvoiceDetailsTableMapper">

    <resultMap type="PurchaseOrderInvoiceDetailsTable" id="PurchaseOrderInvoiceDetailsTableResult">
        <result property="id"    column="id"    />
        <result property="werks"    column="werks"    />
        <result property="bsart"    column="bsart"    />
        <result property="batxt"    column="batxt"    />
        <result property="ebeln"    column="ebeln"    />
        <result property="ebelp"    column="ebelp"    />
        <result property="aedat"    column="aedat"    />
        <result property="zzoaid"    column="zzoaid"    />
        <result property="zzoaitem"    column="zzoaitem"    />
        <result property="matnr"    column="matnr"    />
        <result property="matkl"    column="matkl"    />
        <result property="wgbez"    column="wgbez"    />
        <result property="menge"    column="menge"    />
        <result property="meins"    column="meins"    />
        <result property="zzcms"    column="zzcms"    />
        <result property="lifnr"    column="lifnr"    />
        <result property="name1"    column="name1"    />
        <result property="groes"    column="groes"    />
        <result property="mblnr"    column="mblnr"    />
        <result property="mjahr"    column="mjahr"    />
        <result property="zeile"    column="zeile"    />
        <result property="shkzg"    column="shkzg"    />
        <result property="mengeWh"    column="menge_wh"    />
        <result property="budatMkpf"    column="budat_mkpf"    />
        <result property="licha"    column="licha"    />
        <result property="charg"    column="charg"    />
        <result property="belnrRseg"    column="belnr_rseg"    />
        <result property="gjahrRseg"    column="gjahr_rseg"    />
        <result property="buzeiRseg"    column="buzei_rseg"    />
        <result property="zfpdm"    column="zfpdm"    />
        <result property="zfphm"    column="zfphm"    />
        <result property="belnrBkpf"    column="belnr_bkpf"    />
        <result property="gjahrBkpf"    column="gjahr_bkpf"    />
        <result property="vbeln"    column="vbeln"    />
        <result property="posnr"    column="posnr"    />
        <result property="chargL"    column="charg_l"    />
        <result property="monat"    column="monat"    />
    </resultMap>

    <sql id="selectPurchaseOrderInvoiceDetailsTableVo">
        select id, werks, bsart, batxt, ebeln, ebelp, aedat, zzoaid, zzoaitem, matnr, matkl, wgbez, menge, meins, zzcms, lifnr, name1, groes, mblnr, mjahr, zeile, shkzg, menge_wh, budat_mkpf, licha, charg, belnr_rseg, gjahr_rseg, buzei_rseg, zfpdm, zfphm, belnr_bkpf, gjahr_bkpf, vbeln, posnr, charg_l, monat from purchase_order_Invoice_details_table
    </sql>

    <select id="selectPurchaseOrderInvoiceDetailsTableList" parameterType="PurchaseOrderInvoiceDetailsTable" resultMap="PurchaseOrderInvoiceDetailsTableResult">
        <include refid="selectPurchaseOrderInvoiceDetailsTableVo"/>
        <where>
            <if test="werks != null  and werks != ''"> and werks = #{werks}</if>
            <if test="bsart != null  and bsart != ''"> and bsart = #{bsart}</if>
            <if test="batxt != null  and batxt != ''"> and batxt = #{batxt}</if>
            <if test="ebeln != null  and ebeln != ''"> and ebeln = #{ebeln}</if>
            <if test="ebelp != null  and ebelp != ''"> and ebelp = #{ebelp}</if>
            <if test="aedat != null  and aedat != ''"> and aedat = #{aedat}</if>
            <if test="zzoaid != null  and zzoaid != ''"> and zzoaid = #{zzoaid}</if>
            <if test="zzoaitem != null  and zzoaitem != ''"> and zzoaitem = #{zzoaitem}</if>
            <if test="matnr != null  and matnr != ''"> and matnr = #{matnr}</if>
            <if test="matkl != null  and matkl != ''"> and matkl = #{matkl}</if>
            <if test="wgbez != null  and wgbez != ''"> and wgbez = #{wgbez}</if>
            <if test="menge != null  and menge != ''"> and menge = #{menge}</if>
            <if test="meins != null  and meins != ''"> and meins = #{meins}</if>
            <if test="zzcms != null  and zzcms != ''"> and zzcms = #{zzcms}</if>
            <if test="lifnr != null  and lifnr != ''"> and lifnr = #{lifnr}</if>
            <if test="name1 != null  and name1 != ''"> and name1 = #{name1}</if>
            <if test="groes != null  and groes != ''"> and groes = #{groes}</if>
            <if test="mblnr != null  and mblnr != ''"> and mblnr = #{mblnr}</if>
            <if test="mjahr != null  and mjahr != ''"> and mjahr = #{mjahr}</if>
            <if test="zeile != null  and zeile != ''"> and zeile = #{zeile}</if>
            <if test="shkzg != null  and shkzg != ''"> and shkzg = #{shkzg}</if>
            <if test="mengeWh != null  and mengeWh != ''"> and menge_wh = #{mengeWh}</if>
            <if test="budatMkpf != null  and budatMkpf != ''"> and budat_mkpf = #{budatMkpf}</if>
            <if test="licha != null  and licha != ''"> and licha = #{licha}</if>
            <if test="charg != null  and charg != ''"> and charg = #{charg}</if>
            <if test="belnrRseg != null  and belnrRseg != ''"> and belnr_rseg = #{belnrRseg}</if>
            <if test="gjahrRseg != null  and gjahrRseg != ''"> and gjahr_rseg = #{gjahrRseg}</if>
            <if test="buzeiRseg != null  and buzeiRseg != ''"> and buzei_rseg = #{buzeiRseg}</if>
            <if test="zfpdm != null  and zfpdm != ''"> and zfpdm = #{zfpdm}</if>
            <if test="zfphm != null  and zfphm != ''"> and zfphm = #{zfphm}</if>
            <if test="belnrBkpf != null  and belnrBkpf != ''"> and belnr_bkpf = #{belnrBkpf}</if>
            <if test="gjahrBkpf != null  and gjahrBkpf != ''"> and gjahr_bkpf = #{gjahrBkpf}</if>
            <if test="vbeln != null  and vbeln != ''"> and vbeln = #{vbeln}</if>
            <if test="posnr != null  and posnr != ''"> and posnr = #{posnr}</if>
            <if test="chargL != null  and chargL != ''"> and charg_l = #{chargL}</if>
            <if test="monat != null  and monat != ''"> and monat = #{monat}</if>
        </where>
    </select>

    <select id="selectPurchaseOrderInvoiceDetailsTableById" parameterType="Long" resultMap="PurchaseOrderInvoiceDetailsTableResult">
        <include refid="selectPurchaseOrderInvoiceDetailsTableVo"/>
        where id = #{id}
    </select>

    <insert id="insertPurchaseOrderInvoiceDetailsTable" parameterType="PurchaseOrderInvoiceDetailsTable">
        insert into purchase_order_Invoice_details_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="werks != null">werks,</if>
            <if test="bsart != null">bsart,</if>
            <if test="batxt != null">batxt,</if>
            <if test="ebeln != null">ebeln,</if>
            <if test="ebelp != null">ebelp,</if>
            <if test="aedat != null">aedat,</if>
            <if test="zzoaid != null">zzoaid,</if>
            <if test="zzoaitem != null">zzoaitem,</if>
            <if test="matnr != null">matnr,</if>
            <if test="matkl != null">matkl,</if>
            <if test="wgbez != null">wgbez,</if>
            <if test="menge != null">menge,</if>
            <if test="meins != null">meins,</if>
            <if test="zzcms != null">zzcms,</if>
            <if test="lifnr != null">lifnr,</if>
            <if test="name1 != null">name1,</if>
            <if test="groes != null">groes,</if>
            <if test="mblnr != null">mblnr,</if>
            <if test="mjahr != null">mjahr,</if>
            <if test="zeile != null">zeile,</if>
            <if test="shkzg != null">shkzg,</if>
            <if test="mengeWh != null">menge_wh,</if>
            <if test="budatMkpf != null">budat_mkpf,</if>
            <if test="licha != null">licha,</if>
            <if test="charg != null">charg,</if>
            <if test="belnrRseg != null">belnr_rseg,</if>
            <if test="gjahrRseg != null">gjahr_rseg,</if>
            <if test="buzeiRseg != null">buzei_rseg,</if>
            <if test="zfpdm != null">zfpdm,</if>
            <if test="zfphm != null">zfphm,</if>
            <if test="belnrBkpf != null">belnr_bkpf,</if>
            <if test="gjahrBkpf != null">gjahr_bkpf,</if>
            <if test="vbeln != null">vbeln,</if>
            <if test="posnr != null">posnr,</if>
            <if test="chargL != null">charg_l,</if>
            <if test="monat != null">monat,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="werks != null">#{werks},</if>
            <if test="bsart != null">#{bsart},</if>
            <if test="batxt != null">#{batxt},</if>
            <if test="ebeln != null">#{ebeln},</if>
            <if test="ebelp != null">#{ebelp},</if>
            <if test="aedat != null">#{aedat},</if>
            <if test="zzoaid != null">#{zzoaid},</if>
            <if test="zzoaitem != null">#{zzoaitem},</if>
            <if test="matnr != null">#{matnr},</if>
            <if test="matkl != null">#{matkl},</if>
            <if test="wgbez != null">#{wgbez},</if>
            <if test="menge != null">#{menge},</if>
            <if test="meins != null">#{meins},</if>
            <if test="zzcms != null">#{zzcms},</if>
            <if test="lifnr != null">#{lifnr},</if>
            <if test="name1 != null">#{name1},</if>
            <if test="groes != null">#{groes},</if>
            <if test="mblnr != null">#{mblnr},</if>
            <if test="mjahr != null">#{mjahr},</if>
            <if test="zeile != null">#{zeile},</if>
            <if test="shkzg != null">#{shkzg},</if>
            <if test="mengeWh != null">#{mengeWh},</if>
            <if test="budatMkpf != null">#{budatMkpf},</if>
            <if test="licha != null">#{licha},</if>
            <if test="charg != null">#{charg},</if>
            <if test="belnrRseg != null">#{belnrRseg},</if>
            <if test="gjahrRseg != null">#{gjahrRseg},</if>
            <if test="buzeiRseg != null">#{buzeiRseg},</if>
            <if test="zfpdm != null">#{zfpdm},</if>
            <if test="zfphm != null">#{zfphm},</if>
            <if test="belnrBkpf != null">#{belnrBkpf},</if>
            <if test="gjahrBkpf != null">#{gjahrBkpf},</if>
            <if test="vbeln != null">#{vbeln},</if>
            <if test="posnr != null">#{posnr},</if>
            <if test="chargL != null">#{chargL},</if>
            <if test="monat != null">#{monat},</if>
        </trim>
    </insert>

    <update id="updatePurchaseOrderInvoiceDetailsTable" parameterType="PurchaseOrderInvoiceDetailsTable">
        update purchase_order_Invoice_details_table
        <trim prefix="SET" suffixOverrides=",">
            <if test="werks != null">werks = #{werks},</if>
            <if test="bsart != null">bsart = #{bsart},</if>
            <if test="batxt != null">batxt = #{batxt},</if>
            <if test="ebeln != null">ebeln = #{ebeln},</if>
            <if test="ebelp != null">ebelp = #{ebelp},</if>
            <if test="aedat != null">aedat = #{aedat},</if>
            <if test="zzoaid != null">zzoaid = #{zzoaid},</if>
            <if test="zzoaitem != null">zzoaitem = #{zzoaitem},</if>
            <if test="matnr != null">matnr = #{matnr},</if>
            <if test="matkl != null">matkl = #{matkl},</if>
            <if test="wgbez != null">wgbez = #{wgbez},</if>
            <if test="menge != null">menge = #{menge},</if>
            <if test="meins != null">meins = #{meins},</if>
            <if test="zzcms != null">zzcms = #{zzcms},</if>
            <if test="lifnr != null">lifnr = #{lifnr},</if>
            <if test="name1 != null">name1 = #{name1},</if>
            <if test="groes != null">groes = #{groes},</if>
            <if test="mblnr != null">mblnr = #{mblnr},</if>
            <if test="mjahr != null">mjahr = #{mjahr},</if>
            <if test="zeile != null">zeile = #{zeile},</if>
            <if test="shkzg != null">shkzg = #{shkzg},</if>
            <if test="mengeWh != null">menge_wh = #{mengeWh},</if>
            <if test="budatMkpf != null">budat_mkpf = #{budatMkpf},</if>
            <if test="licha != null">licha = #{licha},</if>
            <if test="charg != null">charg = #{charg},</if>
            <if test="belnrRseg != null">belnr_rseg = #{belnrRseg},</if>
            <if test="gjahrRseg != null">gjahr_rseg = #{gjahrRseg},</if>
            <if test="buzeiRseg != null">buzei_rseg = #{buzeiRseg},</if>
            <if test="zfpdm != null">zfpdm = #{zfpdm},</if>
            <if test="zfphm != null">zfphm = #{zfphm},</if>
            <if test="belnrBkpf != null">belnr_bkpf = #{belnrBkpf},</if>
            <if test="gjahrBkpf != null">gjahr_bkpf = #{gjahrBkpf},</if>
            <if test="vbeln != null">vbeln = #{vbeln},</if>
            <if test="posnr != null">posnr = #{posnr},</if>
            <if test="chargL != null">charg_l = #{chargL},</if>
            <if test="monat != null">monat = #{monat},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePurchaseOrderInvoiceDetailsTableById" parameterType="Long">
        delete from purchase_order_Invoice_details_table where id = #{id}
    </delete>

    <delete id="deletePurchaseOrderInvoiceDetailsTableByIds" parameterType="String">
        delete from purchase_order_Invoice_details_table where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByYearAndMonth" parameterType="map">
        delete from purchase_order_Invoice_details_table where gjahr_bkpf = #{year} and monat = #{month}
    </delete>

    <insert id="insertList" parameterType="PurchaseOrderInvoiceDetailsTable">
        insert into purchase_order_Invoice_details_table (werks, bsart, batxt, ebeln, ebelp, aedat, zzoaid, zzoaitem, matnr, matkl,
        wgbez, menge, meins, zzcms, lifnr, name1, groes, mblnr, mjahr, zeile, shkzg, menge_wh, budat_mkpf, licha, charg, belnr_rseg,
        gjahr_rseg, buzei_rseg, zfpdm, zfphm, belnr_bkpf, gjahr_bkpf, vbeln, posnr, charg_l, monat)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.werks}, #{item.bsart}, #{item.batxt}, #{item.ebeln}, #{item.ebelp}, #{item.aedat}, #{item.zzoaid},
            #{item.zzoaitem}, #{item.matnr}, #{item.matkl},#{item.wgbez},#{item.menge}, #{item.meins}, #{item.zzcms},
            #{item.lifnr}, #{item.name1}, #{item.groes}, #{item.mblnr}, #{item.mjahr}, #{item.zeile}, #{item.shkzg},
            #{item.menge_wh}, #{item.budat_mkpf}, #{item.licha}, #{item.charg}, #{item.belnr_rseg}, #{item.gjahr_rseg},
            #{item.buzei_rseg},#{item.zfpdm}, #{item.zfphm}, #{item.belnr_bkpf}, #{item.gjahr_bkpf}, #{item.vbeln},
            #{item.posnr}, #{item.charg_l}, #{item.monat})
        </foreach>
    </insert>
</mapper>