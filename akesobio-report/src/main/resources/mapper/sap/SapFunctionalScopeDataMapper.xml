<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.SapFunctionalScopeDataMapper">

    <resultMap type="SapFunctionalScopeData" id="SapFunctionalScopeDataResult">
        <result property="id"    column="id"    />
        <result property="languageCode"    column="language_code"    />
        <result property="functionalScope"    column="functional_scope"    />
        <result property="functionalScopeName"    column="functional_scope_name"    />
    </resultMap>

    <sql id="selectSapFunctionalScopeDataVo">
        select id, language_code, functional_scope, functional_scope_name from sap_functional_scope_data
    </sql>

    <select id="selectSapFunctionalScopeDataList" parameterType="SapFunctionalScopeData" resultMap="SapFunctionalScopeDataResult">
        <include refid="selectSapFunctionalScopeDataVo"/>
        <where>
            <if test="languageCode != null  and languageCode != ''"> and language_code = #{languageCode}</if>
            <if test="functionalScope != null  and functionalScope != ''"> and functional_scope = #{functionalScope}</if>
            <if test="functionalScopeName != null  and functionalScopeName != ''"> and functional_scope_name like concat('%', #{functionalScopeName}, '%')</if>
        </where>
    </select>

    <select id="selectSapFunctionalScopeDataById" parameterType="Long" resultMap="SapFunctionalScopeDataResult">
        <include refid="selectSapFunctionalScopeDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertSapFunctionalScopeData" parameterType="SapFunctionalScopeData">
        insert into sap_functional_scope_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="languageCode != null">language_code,</if>
            <if test="functionalScope != null">functional_scope,</if>
            <if test="functionalScopeName != null">functional_scope_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="languageCode != null">#{languageCode},</if>
            <if test="functionalScope != null">#{functionalScope},</if>
            <if test="functionalScopeName != null">#{functionalScopeName},</if>
        </trim>
    </insert>

    <update id="updateSapFunctionalScopeData" parameterType="SapFunctionalScopeData">
        update sap_functional_scope_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="languageCode != null">language_code = #{languageCode},</if>
            <if test="functionalScope != null">functional_scope = #{functionalScope},</if>
            <if test="functionalScopeName != null">functional_scope_name = #{functionalScopeName},</if>
        </trim>
        where language_code = #{languageCode} and functional_scope = #{functionalScope} and functional_scope_name = #{functionalScopeName}
    </update>

    <delete id="deleteSapFunctionalScopeDataById" parameterType="Long">
        delete from sap_functional_scope_data where id = #{id}
    </delete>

    <delete id="deleteSapFunctionalScopeDataByIds" parameterType="String">
        delete from sap_functional_scope_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>