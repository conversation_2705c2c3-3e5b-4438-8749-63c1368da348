<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.FinancialExpenseSummaryTableMapper">

    <resultMap type="FinancialExpenseSummaryTable" id="FinancialExpenseSummaryTableResult">
        <result property="id"    column="id"    />
        <result property="butxt"    column="butxt"    />
        <result property="rbukrs"    column="rbukrs"    />
        <result property="gjahr"    column="gjahr"    />
        <result property="poper"    column="poper"    />
        <result property="racct"    column="racct"    />
        <result property="txt20"    column="txt20"    />
        <result property="rhcur"    column="rhcur"    />
        <result property="hsl"    column="hsl"    />
        <result property="rkcur"    column="rkcur"    />
        <result property="ksl"    column="ksl"    />
    </resultMap>

    <sql id="selectFinancialExpenseSummaryTableVo">
        select id, butxt, rbukrs, gjahr, poper, racct, txt20, rhcur, hsl, rkcur, ksl from financial_expense_summary_table
    </sql>

    <select id="selectFinancialExpenseSummaryTableList" parameterType="FinancialExpenseSummaryTable" resultMap="FinancialExpenseSummaryTableResult">
        <include refid="selectFinancialExpenseSummaryTableVo"/>
        <where>
            <if test="butxt != null  and butxt != ''"> and butxt = #{butxt}</if>
            <if test="rbukrs != null  and rbukrs != ''"> and rbukrs = #{rbukrs}</if>
            <if test="gjahr != null  and gjahr != ''"> and gjahr = #{gjahr}</if>
            <if test="poper != null  and poper != ''"> and poper = #{poper}</if>
            <if test="racct != null  and racct != ''"> and racct = #{racct}</if>
            <if test="txt20 != null  and txt20 != ''"> and txt20 = #{txt20}</if>
            <if test="rhcur != null  and rhcur != ''"> and rhcur = #{rhcur}</if>
            <if test="hsl != null  and hsl != ''"> and hsl = #{hsl}</if>
            <if test="rkcur != null  and rkcur != ''"> and rkcur = #{rkcur}</if>
            <if test="ksl != null  and ksl != ''"> and ksl = #{ksl}</if>
        </where>
    </select>

    <select id="selectFinancialExpenseSummaryTableById" parameterType="String" resultMap="FinancialExpenseSummaryTableResult">
        <include refid="selectFinancialExpenseSummaryTableVo"/>
        where id = #{id}
    </select>

    <insert id="insertFinancialExpenseSummaryTable" parameterType="FinancialExpenseSummaryTable">
        insert into financial_expense_summary_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="butxt != null">butxt,</if>
            <if test="rbukrs != null">rbukrs,</if>
            <if test="gjahr != null">gjahr,</if>
            <if test="poper != null">poper,</if>
            <if test="racct != null">racct,</if>
            <if test="txt20 != null">txt20,</if>
            <if test="rhcur != null">rhcur,</if>
            <if test="hsl != null">hsl,</if>
            <if test="rkcur != null">rkcur,</if>
            <if test="ksl != null">ksl,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="butxt != null">#{butxt},</if>
            <if test="rbukrs != null">#{rbukrs},</if>
            <if test="gjahr != null">#{gjahr},</if>
            <if test="poper != null">#{poper},</if>
            <if test="racct != null">#{racct},</if>
            <if test="txt20 != null">#{txt20},</if>
            <if test="rhcur != null">#{rhcur},</if>
            <if test="hsl != null">#{hsl},</if>
            <if test="rkcur != null">#{rkcur},</if>
            <if test="ksl != null">#{ksl},</if>
        </trim>
    </insert>

    <update id="updateFinancialExpenseSummaryTable" parameterType="FinancialExpenseSummaryTable">
        update financial_expense_summary_table
        <trim prefix="SET" suffixOverrides=",">
            <if test="butxt != null">butxt = #{butxt},</if>
            <if test="rbukrs != null">rbukrs = #{rbukrs},</if>
            <if test="gjahr != null">gjahr = #{gjahr},</if>
            <if test="poper != null">poper = #{poper},</if>
            <if test="racct != null">racct = #{racct},</if>
            <if test="txt20 != null">txt20 = #{txt20},</if>
            <if test="rhcur != null">rhcur = #{rhcur},</if>
            <if test="hsl != null">hsl = #{hsl},</if>
            <if test="rkcur != null">rkcur = #{rkcur},</if>
            <if test="ksl != null">ksl = #{ksl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinancialExpenseSummaryTableById" parameterType="String">
        delete from financial_expense_summary_table where id = #{id}
    </delete>

    <delete id="deleteFinancialExpenseSummaryTableByIds" parameterType="String">
        delete from financial_expense_summary_table where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByYearAndMonth" parameterType="map">
        delete from financial_expense_summary_table where gjahr = #{year} and monat = #{month}
    </delete>
</mapper>