<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.CashFlowReportMapper">
    
    <resultMap type="CashFlowReport" id="CashFlowReportResult">
        <result property="sid"    column="sid"    />
        <result property="f1"    column="f1"    />
        <result property="f2"    column="f2"    />
        <result property="f3"    column="f3"    />
        <result property="f4"    column="f4"    />
        <result property="bukrs"    column="bukrs"    />
        <result property="gjahr"    column="gjahr"    />
        <result property="monat"    column="monat"    />
    </resultMap>

    <sql id="selectCashFlowReportVo">
        select sid, f1, f2, f3, f4, bukrs, gjahr, monat from CashFlowReport
    </sql>

    <select id="selectCashFlowReportList" parameterType="CashFlowReport" resultMap="CashFlowReportResult">
        <include refid="selectCashFlowReportVo"/>
        <where>  
            <if test="f1 != null  and f1 != ''"> and f1 = #{f1}</if>
            <if test="f2 != null  and f2 != ''"> and f2 = #{f2}</if>
            <if test="f3 != null  and f3 != ''"> and f3 = #{f3}</if>
            <if test="f4 != null  and f4 != ''"> and f4 = #{f4}</if>
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="gjahr != null  and gjahr != ''"> and gjahr = #{gjahr}</if>
            <if test="monat != null  and monat != ''"> and monat = #{monat}</if>
        </where>
    </select>
    
    <select id="selectCashFlowReportBySid" parameterType="Long" resultMap="CashFlowReportResult">
        <include refid="selectCashFlowReportVo"/>
        where sid = #{sid}
    </select>
        
    <insert id="insertCashFlowReport" parameterType="CashFlowReport">
        insert into CashFlowReport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="f1 != null">f1,</if>
            <if test="f2 != null">f2,</if>
            <if test="f3 != null">f3,</if>
            <if test="f4 != null">f4,</if>
            <if test="bukrs != null">bukrs,</if>
            <if test="gjahr != null">gjahr,</if>
            <if test="monat != null">monat,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="f1 != null">#{f1},</if>
            <if test="f2 != null">#{f2},</if>
            <if test="f3 != null">#{f3},</if>
            <if test="f4 != null">#{f4},</if>
            <if test="bukrs != null">#{bukrs},</if>
            <if test="gjahr != null">#{gjahr},</if>
            <if test="monat != null">#{monat},</if>
         </trim>
    </insert>

    <update id="updateCashFlowReport" parameterType="CashFlowReport">
        update CashFlowReport
        <trim prefix="SET" suffixOverrides=",">
            <if test="f1 != null">f1 = #{f1},</if>
            <if test="f2 != null">f2 = #{f2},</if>
            <if test="f3 != null">f3 = #{f3},</if>
            <if test="f4 != null">f4 = #{f4},</if>
            <if test="bukrs != null">bukrs = #{bukrs},</if>
            <if test="gjahr != null">gjahr = #{gjahr},</if>
            <if test="monat != null">monat = #{monat},</if>
        </trim>
        where sid = #{sid}
    </update>

    <delete id="deleteCashFlowReportBySid" parameterType="Long">
        delete from CashFlowReport where sid = #{sid}
    </delete>

    <delete id="deleteCashFlowReportBySids" parameterType="String">
        delete from CashFlowReport where sid in 
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>