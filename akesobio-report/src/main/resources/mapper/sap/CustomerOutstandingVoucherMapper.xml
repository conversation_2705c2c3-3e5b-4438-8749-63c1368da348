<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.CustomerOutstandingVoucherMapper">
    
    <resultMap type="CustomerOutstandingVoucher" id="CustomerOutstandingVoucherResult">
        <result property="sid"    column="sid"    />
        <result property="bukrs"    column="bukrs"    />
        <result property="gjahr"    column="gjahr"    />
        <result property="kunnr"    column="kunnr"    />
        <result property="name1"    column="name1"    />
        <result property="waers"    column="waers"    />
        <result property="dmbtr"    column="dmbtr"    />
        <result property="budat"    column="budat"    />
        <result property="bldat"    column="bldat"    />
        <result property="blart"    column="blart"    />
        <result property="zuonr"    column="zuonr"    />
        <result property="xblnr"    column="xblnr"    />
        <result property="sgtxt"    column="sgtxt"    />
        <result property="zzjsbh1"    column="zzjsbh1"    />
        <result property="zzjsbh2"    column="zzjsbh2"    />
        <result property="zfbdt"    column="zfbdt"    />
        <result property="umskz"    column="umskz"    />
        <result property="belnr"    column="belnr"    />
        <result property="buzei"    column="buzei"    />
        <result property="zbd1t"    column="zbd1t"    />
        <result property="zdqdat"    column="zdqdat"    />
        <result property="zyqts"    column="zyqts"    />
        <result property="lifnr_z2"    column="lifnr_z2"    />
        <result property="uname"    column="uname"    />
    </resultMap>

    <sql id="selectCustomerOutstandingVoucherVo">
        SELECT
            sid,
            CASE
                bukrs
                WHEN '1000' THEN '中山康方生物医药有限公司'
                WHEN '1020' THEN '康方天成（广东）制药有限公司'
                WHEN '1030' THEN '康方赛诺医药有限公司'
                WHEN '1040' THEN '中山康方创新药物研究院有限公司'
                WHEN '1050' THEN '康方药业有限公司'
                WHEN '1060' THEN '康融东方（广东）医药有限公司'
                WHEN '1070' THEN '康融东方（广州）生物医药有限公司'
                WHEN '1080' THEN '康方隆跃（广东）科技有限公司'
                WHEN '1090' THEN '中山康方生物科技有限公司'
                WHEN '1100' THEN '中山康方生物医药有限公司北京分公司'
                WHEN '1200' THEN '康方添成科技（上海）有限公司'
                WHEN '1210' THEN '正大天晴康方（上海）生物医药科技有限公司'
                WHEN '1220' THEN '中山康方生物医药有限公司上海分公司'
                WHEN '1230' THEN '康方汇科（上海）生物有限公司'
                WHEN '1250' THEN '康方汇科（广州）生物有限公司'
                WHEN '1300' THEN '康方中国有限公司'
                WHEN '1610' THEN '泽昇医药 (广东)有限公司'
                ELSE '非康方公司'
                END AS bukrs,
            gjahr,kunnr,name1,waers,dmbtr,budat,bldat,blart,zuonr,xblnr,sgtxt,zzjsbh1,zzjsbh2,zfbdt,umskz,belnr,buzei,zbd1t,zdqdat,zyqts,lifnr_z2,uname
        FROM
            CustomerOutstandingVoucherReport
    </sql>

    <select id="selectCustomerOutstandingVoucherList" parameterType="CustomerOutstandingVoucher" resultMap="CustomerOutstandingVoucherResult">
        <include refid="selectCustomerOutstandingVoucherVo"/>
        <where>  
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="gjahr != null  and gjahr != ''"> and gjahr = #{gjahr}</if>
            <if test="kunnr != null  and kunnr != ''"> and kunnr LIKE CONCAT('%',#{kunnr},'%')</if>
            <if test="name1 != null  and name1 != ''"> and name1 = #{name1}</if>
            <if test="waers != null  and waers != ''"> and waers = #{waers}</if>
            <if test="dmbtr != null  and dmbtr != ''"> and dmbtr = #{dmbtr}</if>
            <if test="budat != null  and budat != ''"> and budat = #{budat}</if>
            <if test="bldat != null  and bldat != ''"> and bldat = #{bldat}</if>
            <if test="blart != null  and blart != ''"> and blart = #{blart}</if>
            <if test="zuonr != null  and zuonr != ''"> and zuonr = #{zuonr}</if>
            <if test="xblnr != null  and xblnr != ''"> and xblnr = #{xblnr}</if>
            <if test="sgtxt != null  and sgtxt != ''"> and sgtxt = #{sgtxt}</if>
            <if test="zzjsbh1 != null  and zzjsbh1 != ''"> and zzjsbh1 = #{zzjsbh1}</if>
            <if test="zzjsbh2 != null  and zzjsbh2 != ''"> and zzjsbh2 = #{zzjsbh2}</if>
            <if test="zfbdt != null  and zfbdt != ''"> and zfbdt = #{zfbdt}</if>
            <if test="umskz != null  and umskz != ''"> and umskz = #{umskz}</if>
            <if test="belnr != null  and belnr != ''"> and belnr = #{belnr}</if>
            <if test="buzei != null  and buzei != ''"> and buzei = #{buzei}</if>
            <if test="zbd1t != null  and zbd1t != ''"> and zbd1t = #{zbd1t}</if>
            <if test="zdqdat != null  and zdqdat != ''"> and zdqdat = #{zdqdat}</if>
            <if test="zyqts != null  and zyqts != ''"> and zyqts = #{zyqts}</if>
            <if test="lifnr_z2 != null  and lifnr_z2 != ''"> and lifnr_z2 = #{lifnr_z2}</if>
            <if test="uname != null  and uname != ''"> and uname = #{uname}</if>
        </where>
        order by gjahr desc
    </select>
    <select id="selectNotNullDateList" parameterType="CustomerOutstandingVoucher" resultMap="CustomerOutstandingVoucherResult">
        <include refid="selectCustomerOutstandingVoucherVo"/>
        <where>
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="gjahr != null  and gjahr != ''"> and gjahr = #{gjahr}</if>
            <if test="kunnr != null  and kunnr != ''"> and kunnr LIKE CONCAT('%',#{kunnr},'%')</if>
            <if test="name1 != null  and name1 != ''"> and name1 = #{name1}</if>
            <if test="waers != null  and waers != ''"> and waers = #{waers}</if>
            <if test="dmbtr != null  and dmbtr != ''"> and dmbtr = #{dmbtr}</if>
            <if test="budat != null  and budat != ''"> and budat = #{budat}</if>
            <if test="bldat != null  and bldat != ''"> and bldat = #{bldat}</if>
            <if test="blart != null  and blart != ''"> and blart = #{blart}</if>
            <if test="zuonr != null  and zuonr != ''"> and zuonr = #{zuonr}</if>
            <if test="xblnr != null  and xblnr != ''"> and xblnr = #{xblnr}</if>
            <if test="sgtxt != null  and sgtxt != ''"> and sgtxt = #{sgtxt}</if>
            <if test="zzjsbh1 != null  and zzjsbh1 != ''"> and zzjsbh1 = #{zzjsbh1}</if>
            <if test="zzjsbh2 != null  and zzjsbh2 != ''"> and zzjsbh2 = #{zzjsbh2}</if>
            <if test="zfbdt != null  and zfbdt != ''"> and zfbdt = #{zfbdt}</if>
            <if test="umskz != null  and umskz != ''"> and umskz = #{umskz}</if>
            <if test="belnr != null  and belnr != ''"> and belnr = #{belnr}</if>
            <if test="buzei != null  and buzei != ''"> and buzei = #{buzei}</if>
            <if test="zbd1t != null  and zbd1t != ''"> and zbd1t = #{zbd1t}</if>
            <if test="zdqdat != null  and zdqdat != ''"> and zdqdat = #{zdqdat}</if>
            <if test="zyqts != null  and zyqts != ''"> and zyqts = #{zyqts}</if>
            <if test="lifnr_z2 != null  and lifnr_z2 != ''"> and lifnr_z2 = #{lifnr_z2}</if>
            <if test="uname != null  and uname != ''"> and uname = #{uname}</if>
            and zfbdt IS NOT NULL
        </where>
        order by kunnr desc
    </select>
    
    <select id="selectCustomerOutstandingVoucherBySid" parameterType="Long" resultMap="CustomerOutstandingVoucherResult">
        <include refid="selectCustomerOutstandingVoucherVo"/>
        where sid = #{sid}
    </select>
        
    <insert id="insertCustomerOutstandingVoucher" parameterType="CustomerOutstandingVoucher">
        insert into CustomerOutstandingVoucherReport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="bukrs != null">bukrs,</if>
            <if test="gjahr != null">gjahr,</if>
            <if test="kunnr != null">kunnr,</if>
            <if test="name1 != null">name1,</if>
            <if test="waers != null">waers,</if>
            <if test="dmbtr != null">dmbtr,</if>
            <if test="budat != null">budat,</if>
            <if test="bldat != null">bldat,</if>
            <if test="blart != null">blart,</if>
            <if test="zuonr != null">zuonr,</if>
            <if test="xblnr != null">xblnr,</if>
            <if test="sgtxt != null">sgtxt,</if>
            <if test="zzjsbh1 != null">zzjsbh1,</if>
            <if test="zzjsbh2 != null">zzjsbh2,</if>
            <if test="zfbdt != null">zfbdt,</if>
            <if test="umskz != null">umskz,</if>
            <if test="belnr != null">belnr,</if>
            <if test="buzei != null">buzei,</if>
            <if test="zbd1t != null">zbd1t,</if>
            <if test="zdqdat != null">zdqdat,</if>
            <if test="zyqts != null">zyqts,</if>
            <if test="lifnr_z2 != null">lifnr_z2,</if>
            <if test="uname != null">uname,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="bukrs != null">#{bukrs},</if>
            <if test="gjahr != null">#{gjahr},</if>
            <if test="kunnr != null">#{kunnr},</if>
            <if test="name1 != null">#{name1},</if>
            <if test="waers != null">#{waers},</if>
            <if test="dmbtr != null">#{dmbtr},</if>
            <if test="budat != null">#{budat},</if>
            <if test="bldat != null">#{bldat},</if>
            <if test="blart != null">#{blart},</if>
            <if test="zuonr != null">#{zuonr},</if>
            <if test="xblnr != null">#{xblnr},</if>
            <if test="sgtxt != null">#{sgtxt},</if>
            <if test="zzjsbh1 != null">#{zzjsbh1},</if>
            <if test="zzjsbh2 != null">#{zzjsbh2},</if>
            <if test="zfbdt != null">#{zfbdt},</if>
            <if test="umskz != null">#{umskz},</if>
            <if test="belnr != null">#{belnr},</if>
            <if test="buzei != null">#{buzei},</if>
            <if test="zbd1t != null">#{zbd1t},</if>
            <if test="zdqdat != null">#{zdqdat},</if>
            <if test="zyqts != null">#{zyqts},</if>
            <if test="lifnr_z2 != null">#{lifnr_z2},</if>
            <if test="uname != null">#{uname},</if>
         </trim>
    </insert>

    <update id="updateCustomerOutstandingVoucher" parameterType="CustomerOutstandingVoucher">
        update CustomerOutstandingVoucherReport
        <trim prefix="SET" suffixOverrides=",">
            <if test="bukrs != null">bukrs = #{bukrs},</if>
            <if test="gjahr != null">gjahr = #{gjahr},</if>
            <if test="kunnr != null">kunnr = #{kunnr},</if>
            <if test="name1 != null">name1 = #{name1},</if>
            <if test="waers != null">waers = #{waers},</if>
            <if test="dmbtr != null">dmbtr = #{dmbtr},</if>
            <if test="budat != null">budat = #{budat},</if>
            <if test="bldat != null">bldat = #{bldat},</if>
            <if test="blart != null">blart = #{blart},</if>
            <if test="zuonr != null">zuonr = #{zuonr},</if>
            <if test="xblnr != null">xblnr = #{xblnr},</if>
            <if test="sgtxt != null">sgtxt = #{sgtxt},</if>
            <if test="zzjsbh1 != null">zzjsbh1 = #{zzjsbh1},</if>
            <if test="zzjsbh2 != null">zzjsbh2 = #{zzjsbh2},</if>
            <if test="zfbdt != null">zfbdt = #{zfbdt},</if>
            <if test="umskz != null">umskz = #{umskz},</if>
            <if test="belnr != null">belnr = #{belnr},</if>
            <if test="buzei != null">buzei = #{buzei},</if>
            <if test="zbd1t != null">zbd1t = #{zbd1t},</if>
            <if test="zdqdat != null">zdqdat = #{zdqdat},</if>
            <if test="zyqts != null">zyqts = #{zyqts},</if>
            <if test="lifnr_z2 != null">lifnr_z2 = #{lifnr_z2},</if>
            <if test="uname != null">uname = #{uname},</if>
        </trim>
        where bukrs = #{bukrs} and gjahr = #{gjahr} and kunnr = #{kunnr} and belnr = #{belnr} and buzei = #{buzei}
    </update>

    <delete id="deleteCustomerOutstandingVoucherBySid" parameterType="Long">
        delete from CustomerOutstandingVoucherReport where sid = #{sid}
    </delete>

    <delete id="deleteCustomerOutstandingVoucherBySids" parameterType="String">
        delete from CustomerOutstandingVoucherReport where sid in 
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>