<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.ManagementExpenseDetailsReportMapper">
    
    <resultMap type="ManagementExpenseDetailsReport" id="ManagementExpenseDetailsReportResult">
        <result property="sid"    column="sid"    />
        <result property="butxt"    column="butxt"    />
        <result property="rbukrs"    column="rbukrs"    />
        <result property="belnr"    column="belnr"    />
        <result property="docln"    column="docln"    />
        <result property="gjahr"    column="gjahr"    />
        <result property="poper"    column="poper"    />
        <result property="blart"    column="blart"    />
        <result property="racct"    column="racct"    />
        <result property="txt20"    column="txt20"    />
        <result property="rfarea"    column="rfarea"    />
        <result property="zhzkm"    column="zhzkm"    />
        <result property="zplkm"    column="zplkm"    />
        <result property="rcntr"    column="rcntr"    />
        <result property="ktext"    column="ktext"    />
        <result property="zbmmc"    column="zbmmc"    />
        <result property="zejbmFi"    column="zejbm_fi"    />
        <result property="sgtxt"    column="sgtxt"    />
        <result property="rhcur"    column="rhcur"    />
        <result property="hsl"    column="hsl"    />
        <result property="kursf"    column="kursf"    />
        <result property="rwcur"    column="rwcur"    />
        <result property="wsl"    column="wsl"    />
        <result property="rkcur"    column="rkcur"    />
        <result property="ksl"    column="ksl"    />
        <result property="monat"    column="monat"    />
    </resultMap>

    <sql id="selectManagementExpenseDetailsReportVo">
        select sid, butxt, rbukrs, belnr, docln, gjahr, poper, blart, racct, txt20, rfarea, zhzkm, zplkm, rcntr, ktext, zbmmc, zejbm_fi, sgtxt, rhcur, hsl, kursf, rwcur, wsl, rkcur, ksl, monat from management_expense_details_report
    </sql>

    <select id="selectManagementExpenseDetailsReportList" parameterType="ManagementExpenseDetailsReport" resultMap="ManagementExpenseDetailsReportResult">
        <include refid="selectManagementExpenseDetailsReportVo"/>
        <where>  
            <if test="butxt != null  and butxt != ''"> and butxt like concat('%', #{butxt}, '%')</if>
            <if test="rbukrs != null  and rbukrs != ''"> and rbukrs like concat('%', #{rbukrs}, '%')</if>
            <if test="belnr != null  and belnr != ''"> and belnr like concat('%', #{belnr}, '%')</if>
            <if test="docln != null  and docln != ''"> and docln like concat('%', #{docln}, '%')</if>
            <if test="gjahr != null  and gjahr != ''"> and gjahr like concat('%', #{gjahr}, '%')</if>
            <if test="poper != null  and poper != ''"> and poper like concat('%', #{poper}, '%')</if>
            <if test="blart != null  and blart != ''"> and blart like concat('%', #{blart}, '%')</if>
            <if test="racct != null  and racct != ''"> and racct like concat('%', #{racct}, '%')</if>
            <if test="txt20 != null  and txt20 != ''"> and txt20 like concat('%', #{txt20}, '%')</if>
            <if test="rfarea != null  and rfarea != ''"> and rfarea like concat('%', #{rfarea}, '%')</if>
            <if test="zhzkm != null  and zhzkm != ''"> and zhzkm like concat('%', #{zhzkm}, '%')</if>
            <if test="zplkm != null  and zplkm != ''"> and zplkm like concat('%', #{zplkm}, '%')</if>
            <if test="rcntr != null  and rcntr != ''"> and rcntr like concat('%', #{rcntr}, '%')</if>
            <if test="ktext != null  and ktext != ''"> and ktext like concat('%', #{ktext}, '%')</if>
            <if test="zbmmc != null  and zbmmc != ''"> and zbmmc like concat('%', #{zbmmc}, '%')</if>
            <if test="zejbmFi != null  and zejbmFi != ''"> and zejbm_fi like concat('%', #{zejbmFi}, '%')</if>
            <if test="sgtxt != null  and sgtxt != ''"> and sgtxt like concat('%', #{sgtxt}, '%')</if>
            <if test="rhcur != null  and rhcur != ''"> and rhcur like concat('%', #{rhcur}, '%')</if>
            <if test="hsl != null  and hsl != ''"> and hsl like concat('%', #{hsl}, '%')</if>
            <if test="kursf != null  and kursf != ''"> and kursf like concat('%', #{kursf}, '%')</if>
            <if test="rwcur != null  and rwcur != ''"> and rwcur like concat('%', #{rwcur}, '%')</if>
            <if test="wsl != null  and wsl != ''"> and wsl like concat('%', #{wsl}, '%')</if>
            <if test="rkcur != null  and rkcur != ''"> and rkcur like concat('%', #{rkcur}, '%')</if>
            <if test="ksl != null  and ksl != ''"> and ksl like concat('%', #{ksl}, '%')</if>
            <if test="monat != null  and monat != ''"> and monat like concat('%', #{monat}, '%')</if>
        </where>
    </select>
    
    <select id="selectManagementExpenseDetailsReportBySid" parameterType="Long" resultMap="ManagementExpenseDetailsReportResult">
        <include refid="selectManagementExpenseDetailsReportVo"/>
        where sid = #{sid}
    </select>
        
    <insert id="insertManagementExpenseDetailsReport" parameterType="ManagementExpenseDetailsReport">
        insert into management_expense_details_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="butxt != null">butxt,</if>
            <if test="rbukrs != null">rbukrs,</if>
            <if test="belnr != null">belnr,</if>
            <if test="docln != null">docln,</if>
            <if test="gjahr != null">gjahr,</if>
            <if test="poper != null">poper,</if>
            <if test="blart != null">blart,</if>
            <if test="racct != null">racct,</if>
            <if test="txt20 != null">txt20,</if>
            <if test="rfarea != null">rfarea,</if>
            <if test="zhzkm != null">zhzkm,</if>
            <if test="zplkm != null">zplkm,</if>
            <if test="rcntr != null">rcntr,</if>
            <if test="ktext != null">ktext,</if>
            <if test="zbmmc != null">zbmmc,</if>
            <if test="zejbmFi != null">zejbm_fi,</if>
            <if test="sgtxt != null">sgtxt,</if>
            <if test="rhcur != null">rhcur,</if>
            <if test="hsl != null">hsl,</if>
            <if test="kursf != null">kursf,</if>
            <if test="rwcur != null">rwcur,</if>
            <if test="wsl != null">wsl,</if>
            <if test="rkcur != null">rkcur,</if>
            <if test="ksl != null">ksl,</if>
            <if test="monat != null">monat,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="butxt != null">#{butxt},</if>
            <if test="rbukrs != null">#{rbukrs},</if>
            <if test="belnr != null">#{belnr},</if>
            <if test="docln != null">#{docln},</if>
            <if test="gjahr != null">#{gjahr},</if>
            <if test="poper != null">#{poper},</if>
            <if test="blart != null">#{blart},</if>
            <if test="racct != null">#{racct},</if>
            <if test="txt20 != null">#{txt20},</if>
            <if test="rfarea != null">#{rfarea},</if>
            <if test="zhzkm != null">#{zhzkm},</if>
            <if test="zplkm != null">#{zplkm},</if>
            <if test="rcntr != null">#{rcntr},</if>
            <if test="ktext != null">#{ktext},</if>
            <if test="zbmmc != null">#{zbmmc},</if>
            <if test="zejbmFi != null">#{zejbmFi},</if>
            <if test="sgtxt != null">#{sgtxt},</if>
            <if test="rhcur != null">#{rhcur},</if>
            <if test="hsl != null">#{hsl},</if>
            <if test="kursf != null">#{kursf},</if>
            <if test="rwcur != null">#{rwcur},</if>
            <if test="wsl != null">#{wsl},</if>
            <if test="rkcur != null">#{rkcur},</if>
            <if test="ksl != null">#{ksl},</if>
            <if test="monat != null">#{monat},</if>
         </trim>
    </insert>

    <insert id="insertList" parameterType="ManagementExpenseDetailsReport">
        insert into management_expense_details_report (butxt, rbukrs, belnr, docln, gjahr, poper, blart, racct, txt20, rfarea,
        zhzkm, zplkm, rcntr,ktext, zbmmc, zejbm_fi, sgtxt, rhcur, hsl, kursf,
        rwcur, wsl, rkcur, ksl, monat)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.butxt}, #{item.rbukrs}, #{item.belnr}, #{item.docln}, #{item.gjahr}, #{item.poper},#{item.blart},
            #{item.racct}, #{item.txt20}, #{item.rfarea}, #{item.zhzkm}, #{item.zplkm}, #{item.rcntr},#{item.ktext},
            #{item.zbmmc}, #{item.zejbmFi}, #{item.sgtxt}, #{item.rhcur} ,#{item.hsl}, #{item.kursf},#{item.rwcur},
            #{item.wsl}, #{item.rkcur}, #{item.ksl}, #{item.monat})
        </foreach>
    </insert>

    <update id="updateManagementExpenseDetailsReport" parameterType="ManagementExpenseDetailsReport">
        update management_expense_details_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="butxt != null">butxt = #{butxt},</if>
            <if test="rbukrs != null">rbukrs = #{rbukrs},</if>
            <if test="belnr != null">belnr = #{belnr},</if>
            <if test="docln != null">docln = #{docln},</if>
            <if test="gjahr != null">gjahr = #{gjahr},</if>
            <if test="poper != null">poper = #{poper},</if>
            <if test="blart != null">blart = #{blart},</if>
            <if test="racct != null">racct = #{racct},</if>
            <if test="txt20 != null">txt20 = #{txt20},</if>
            <if test="rfarea != null">rfarea = #{rfarea},</if>
            <if test="zhzkm != null">zhzkm = #{zhzkm},</if>
            <if test="zplkm != null">zplkm = #{zplkm},</if>
            <if test="rcntr != null">rcntr = #{rcntr},</if>
            <if test="ktext != null">ktext = #{ktext},</if>
            <if test="zbmmc != null">zbmmc = #{zbmmc},</if>
            <if test="zejbmFi != null">zejbm_fi = #{zejbmFi},</if>
            <if test="sgtxt != null">sgtxt = #{sgtxt},</if>
            <if test="rhcur != null">rhcur = #{rhcur},</if>
            <if test="hsl != null">hsl = #{hsl},</if>
            <if test="kursf != null">kursf = #{kursf},</if>
            <if test="rwcur != null">rwcur = #{rwcur},</if>
            <if test="wsl != null">wsl = #{wsl},</if>
            <if test="rkcur != null">rkcur = #{rkcur},</if>
            <if test="ksl != null">ksl = #{ksl},</if>
            <if test="monat != null">monat = #{monat},</if>
        </trim>
        where rbukrs = #{rbukrs} and belnr = #{belnr} and docln = #{docln} and gjahr = #{gjahr} and poper = #{poper}
    </update>

    <delete id="deleteManagementExpenseDetailsReportBySid" parameterType="Long">
        delete from management_expense_details_report where sid = #{sid}
    </delete>

    <delete id="deleteManagementExpenseDetailsReportBySids" parameterType="String">
        delete from management_expense_details_report where sid in 
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>