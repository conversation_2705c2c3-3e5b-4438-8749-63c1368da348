<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.FixedAssetsReportMapper">
    
    <resultMap type="FixedAssetsReport" id="FixedAssetsReportResult">
        <result property="sid"    column="sid"    />
        <result property="bukrs"    column="bukrs"    />
        <result property="gjahr"    column="gjahr"    />
        <result property="monat"    column="monat"    />
        <result property="butxt"    column="butxt"    />
        <result property="anlkl"    column="anlkl"    />
        <result property="txk20"    column="txk20"    />
        <result property="anln1"    column="anln1"    />
        <result property="anlhtxt"    column="anlhtxt"    />
        <result property="anln2"    column="anln2"    />
        <result property="txt50"    column="txt50"    />
        <result property="sernr"    column="sernr"    />
        <result property="invnr"    column="invnr"    />
        <result property="invzu"    column="invzu"    />
        <result property="txa50"    column="txa50"    />
        <result property="menge"    column="menge"    />
        <result property="meins"    column="meins"    />
        <result property="kostl"    column="kostl"    />
        <result property="ltext"    column="ltext"    />
        <result property="aktiv"    column="aktiv"    />
        <result property="ernam"    column="ernam"    />
        <result property="erdat"    column="erdat"    />
        <result property="afasl"    column="afasl"    />
        <result property="afbktx"    column="afbktx"    />
        <result property="ordtx2"    column="ordtx2"    />
        <result property="ordtx1"    column="ordtx1"    />
        <result property="afabg"    column="afabg"    />
        <result property="zndjar"    column="zndjar"    />
        <result property="zyzjqjs"    column="zyzjqjs"    />
        <result property="zsysyq"    column="zsysyq"    />
        <result property="zyzjl"    column="zyzjl"    />
        <result property="schrwProz"    column="schrw_proz"    />
        <result property="schrw"    column="schrw"    />
        <result property="zjz"    column="zjz"    />
        <result property="zncyz"    column="zncyz"    />
        <result property="zqmyz"    column="zqmyz"    />
        <result property="zbyljzj"    column="zbyljzj"    />
        <result property="zbnljzj"    column="zbnljzj"    />
        <result property="zncljzj"    column="zncljzj"    />
        <result property="zqmljzj"    column="zqmljzj"    />
        <result property="zqmjzzb"    column="zqmjzzb"    />
        <result property="xspeb"    column="xspeb"    />
        <result property="xloev"    column="xloev"    />
        <result property="deakt"    column="deakt"    />
        <result property="aenam"    column="aenam"    />
        <result property="zzjkm"    column="zzjkm"    />
        <result property="zzjkmms"    column="zzjkmms"    />
        <result property="xstil"    column="xstil"    />
        <result property="adatu"    column="adatu"    />
        <result property="zzckm"    column="zzckm"    />
        <result property="zzckmms"    column="zzckmms"    />
        <result property="zfykm"    column="zfykm"    />
        <result property="zfykmms"    column="zfykmms"    />
        <result property="lifnr"    column="lifnr"    />
        <result property="name1"    column="name1"    />
        <result property="herst"    column="herst"    />
    </resultMap>

    <resultMap id="fixedAssetsDetailsTable1" type="com.akesobio.report.sap.vo.FixedAssetsDetailsTableVo1">
        <result property="bukrs"    column="bukrs"    />
        <result property="txk20"    column="txk20"    />
        <result property="anln1"    column="anln1"    />
        <result property="txt50"    column="txt50"    />
        <result property="txa50"    column="txa50"    />
        <result property="aktiv"    column="aktiv"    />
        <result property="afabg"    column="afabg"    />
        <result property="zyzjqjs"    column="zyzjqjs"    />
        <result property="zqmyz"    column="zqmyz"    />
        <result property="zqmljzj"    column="zqmljzj"    />
        <result property="zjz"    column="zjz"    />
        <result property="netBookValueRatio"    column="netBookValueRatio"    />
    </resultMap>

    <resultMap id="fixedAssetsDetailsTable2" type="com.akesobio.report.sap.vo.FixedAssetsDetailsTableVo2">
        <result property="txk20"    column="txk20"    />
        <result property="anlkl"    column="anlkl"    />
        <result property="zqmyz"    column="zqmyz"    />
        <result property="zqmljzj"    column="zqmljzj"    />
        <result property="zjz"    column="zjz"    />
    </resultMap>

    <resultMap id="fixedAssetsDetailsTable3" type="com.akesobio.report.sap.vo.FixedAssetsDetailsTableVo3">
        <result property="bukrs"    column="bukrs"    />
        <result property="txk20"    column="txk20"    />
        <result property="anln1"    column="anln1"    />
        <result property="txt50"    column="txt50"    />
        <result property="zqmyz"    column="zqmyz"    />
        <result property="zqmljzj"    column="zqmljzj"    />
        <result property="zjz"    column="zjz"    />
        <result property="netBookValueRatio"    column="netBookValueRatio"    />
    </resultMap>

    <resultMap id="fixedAssetsDetailsTable4" type="com.akesobio.report.sap.vo.FixedAssetsDetailsTableVo4">
        <result property="txk20"    column="txk20"    />
        <result property="anln1"    column="anln1"    />
        <result property="txt50"    column="txt50"    />
        <result property="aktiv"    column="aktiv"    />
        <result property="zqmyz"    column="zqmyz"    />
        <result property="zqmljzj"    column="zqmljzj"    />
        <result property="zjz"    column="zjz"    />
        <result property="netBookValueRatio"    column="netBookValueRatio"    />
    </resultMap>

    <sql id="selectFixedAssetsReportVo">
        select sid, bukrs, gjahr, monat, butxt, anlkl, txk20, anln1, anlhtxt, anln2, txt50, sernr, invnr, invzu, txa50, menge, meins, kostl, ltext, aktiv, ernam, erdat, afasl, afbktx, ordtx2, ordtx1, afabg, zndjar, zyzjqjs, zsysyq, zyzjl, schrw_proz, schrw, zjz, zncyz, zqmyz, zbyljzj, zbnljzj, zncljzj, zqmljzj, zqmjzzb, xspeb, xloev, deakt, aenam, zzjkm, zzjkmms, xstil, adatu, zzckm, zzckmms, zfykm, zfykmms, lifnr, name1, herst from FixedAssetsReport
    </sql>

    <select id="selectFixedAssetsReportList" parameterType="FixedAssetsReport" resultMap="FixedAssetsReportResult">
        <include refid="selectFixedAssetsReportVo"/>
        <where>  
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="gjahr != null  and gjahr != ''"> and gjahr = #{gjahr}</if>
            <if test="monat != null  and monat != ''"> and monat like concat('%', #{monat}, '%')</if>
            <if test="butxt != null  and butxt != ''"> and butxt = #{butxt}</if>
            <if test="anlkl != null  and anlkl != ''"> and anlkl = #{anlkl}</if>
            <if test="txk20 != null  and txk20 != ''"> and txk20 = #{txk20}</if>
            <if test="anln1 != null  and anln1 != ''"> and anln1 = #{anln1}</if>
            <if test="anlhtxt != null  and anlhtxt != ''"> and anlhtxt = #{anlhtxt}</if>
            <if test="anln2 != null  and anln2 != ''"> and anln2 = #{anln2}</if>
            <if test="txt50 != null  and txt50 != ''"> and txt50 = #{txt50}</if>
            <if test="sernr != null  and sernr != ''"> and sernr = #{sernr}</if>
            <if test="invnr != null  and invnr != ''"> and invnr = #{invnr}</if>
            <if test="invzu != null  and invzu != ''"> and invzu = #{invzu}</if>
            <if test="txa50 != null  and txa50 != ''"> and txa50 = #{txa50}</if>
            <if test="menge != null "> and menge = #{menge}</if>
            <if test="meins != null  and meins != ''"> and meins = #{meins}</if>
            <if test="kostl != null  and kostl != ''"> and kostl = #{kostl}</if>
            <if test="ltext != null  and ltext != ''"> and ltext = #{ltext}</if>
            <if test="aktiv != null  and aktiv != ''"> and aktiv = #{aktiv}</if>
            <if test="ernam != null  and ernam != ''"> and ernam = #{ernam}</if>
            <if test="erdat != null  and erdat != ''"> and erdat = #{erdat}</if>
            <if test="afasl != null  and afasl != ''"> and afasl = #{afasl}</if>
            <if test="afbktx != null  and afbktx != ''"> and afbktx = #{afbktx}</if>
            <if test="ordtx2 != null  and ordtx2 != ''"> and ordtx2 = #{ordtx2}</if>
            <if test="ordtx1 != null  and ordtx1 != ''"> and ordtx1 = #{ordtx1}</if>
            <if test="afabg != null  and afabg != ''"> and afabg = #{afabg}</if>
            <if test="zndjar != null  and zndjar != ''"> and zndjar = #{zndjar}</if>
            <if test="zyzjqjs != null  and zyzjqjs != ''"> and zyzjqjs = #{zyzjqjs}</if>
            <if test="zsysyq != null  and zsysyq != ''"> and zsysyq = #{zsysyq}</if>
            <if test="zyzjl != null "> and zyzjl = #{zyzjl}</if>
            <if test="schrwProz != null "> and schrw_proz = #{schrwProz}</if>
            <if test="schrw != null "> and schrw = #{schrw}</if>
            <if test="zjz != null "> and zjz = #{zjz}</if>
            <if test="zncyz != null "> and zncyz = #{zncyz}</if>
            <if test="zqmyz != null "> and zqmyz = #{zqmyz}</if>
            <if test="zbyljzj != null "> and zbyljzj = #{zbyljzj}</if>
            <if test="zbnljzj != null "> and zbnljzj = #{zbnljzj}</if>
            <if test="zncljzj != null "> and zncljzj = #{zncljzj}</if>
            <if test="zqmljzj != null "> and zqmljzj = #{zqmljzj}</if>
            <if test="zqmjzzb != null "> and zqmjzzb = #{zqmjzzb}</if>
            <if test="xspeb != null  and xspeb != ''"> and xspeb = #{xspeb}</if>
            <if test="xloev != null  and xloev != ''"> and xloev = #{xloev}</if>
            <if test="deakt != null  and deakt != ''"> and deakt = #{deakt}</if>
            <if test="aenam != null  and aenam != ''"> and aenam = #{aenam}</if>
            <if test="zzjkm != null  and zzjkm != ''"> and zzjkm = #{zzjkm}</if>
            <if test="zzjkmms != null  and zzjkmms != ''"> and zzjkmms = #{zzjkmms}</if>
            <if test="xstil != null  and xstil != ''"> and xstil = #{xstil}</if>
            <if test="adatu != null  and adatu != ''"> and adatu = #{adatu}</if>
            <if test="zzckm != null  and zzckm != ''"> and zzckm = #{zzckm}</if>
            <if test="zzckmms != null  and zzckmms != ''"> and zzckmms = #{zzckmms}</if>
            <if test="zfykm != null  and zfykm != ''"> and zfykm = #{zfykm}</if>
            <if test="zfykmms != null  and zfykmms != ''"> and zfykmms = #{zfykmms}</if>
            <if test="lifnr != null  and lifnr != ''"> and lifnr = #{lifnr}</if>
            <if test="name1 != null  and name1 != ''"> and name1 = #{name1}</if>
            <if test="herst != null  and herst != ''"> and herst = #{herst}</if>
        </where>
    </select>

    <select id="selectFixedAssetsTable1List" parameterType="FixedAssetsReport" resultMap="fixedAssetsDetailsTable1">
        SELECT
        bukrs,
        txk20,
        anln1,
        txt50,
        txa50,
        aktiv,
        afabg,
        zyzjqjs,
        zjz,
        zqmyz,
        zqmljzj,
        CONCAT (
        CAST (
        ROUND( ( zjz / ( SELECT SUM ( zjz ) FROM FixedAssetsReport
        WHERE bukrs = COALESCE(#{bukrs}, bukrs) AND gjahr = #{gjahr} AND monat = #{monat} ) ) * 100, 6 ) AS DECIMAL ( 38, 6 )
        ),
        '%'
        ) AS netBookValueRatio
        FROM
        FixedAssetsReport
        <where>
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="txk20 != null  and txk20 != ''"> and txk20 like concat('%', #{txk20}, '%')</if>
            <if test="anln1 != null  and anln1 != ''"> and anln1 = #{anln1}</if>
            <if test="txt50 != null  and txt50 != ''"> and txt50 like concat('%', #{txt50}, '%')</if>
            <if test="txa50 != null  and txa50 != ''"> and txa50 = #{txa50}</if>
            <if test="aktiv != null  and aktiv != ''"> and aktiv = #{aktiv}</if>
            <if test="afabg != null  and afabg != ''"> and afabg = #{afabg}</if>
            <if test="zyzjqjs != null  and zyzjqjs != ''"> and zyzjqjs = #{zyzjqjs}</if>
            <if test="zqmyz != null "> and zqmyz = #{zqmyz}</if>
            <if test="zqmljzj != null "> and zqmljzj = #{zqmljzj}</if>
        </where>
        AND gjahr = #{gjahr}
        AND monat = #{monat}
    </select>


    <select id="selectFixedAssetsTable2List" parameterType="FixedAssetsReport" resultMap="fixedAssetsDetailsTable2">
        SELECT
        bukrs,
        txk20,
        anlkl,
        SUM(zqmyz) AS zqmyz,
        SUM(zqmljzj) AS zqmljzj,
        SUM(zjz) AS zjz
        FROM
        FixedAssetsReport
        <where>
            anlkl IN (
            'A230', 'A210','A160','A140','A110','A170','A120','A130','A140','A150','A180','A220','A320','A350',
            'A340','A310','A330','A610','A510','A410'
            )
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="txk20 != null  and txk20 != ''"> and txk20 like concat('%', #{txk20}, '%')</if>
            <if test="anlkl != null  and anlkl != ''"> and anlkl = #{anlkl}</if>
            <if test="zjz != null  and zjz != ''"> and zjz = #{zjz}</if>
            <if test="zqmyz != null "> and zqmyz = #{zqmyz}</if>
            <if test="zqmljzj != null "> and zqmljzj = #{zqmljzj}</if>
        </where>
        AND gjahr = #{gjahr}
        AND monat = #{monat}
        GROUP BY bukrs,txk20,anlkl
        order by anlkl ASC
    </select>


    <select id="selectFixedAssetsTable3List" parameterType="FixedAssetsReport" resultMap="fixedAssetsDetailsTable3">
        SELECT
        TOP 10
        bukrs,
        txk20,
        anln1,
        txt50,
        zjz,
        zqmyz,
        zqmljzj,
        CAST (
        ROUND( ( zjz / ( SELECT SUM ( zjz ) FROM FixedAssetsReport
        WHERE bukrs = COALESCE(#{bukrs}, bukrs) AND gjahr = #{gjahr} AND monat = #{monat} ) ) * 100, 6 ) AS DECIMAL ( 38, 6 )
        ) AS netBookValueRatio
        FROM
        FixedAssetsReport
        <where>
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="txk20 != null  and txk20 != ''"> and txk20 like concat('%', #{txk20}, '%')</if>
            <if test="anln1 != null  and anln1 != ''"> and anln1 = #{anln1}</if>
            <if test="txt50 != null  and txt50 != ''"> and txt50 like concat('%', #{txt50}, '%')</if>
            <if test="zqmyz != null "> and zqmyz = #{zqmyz}</if>
            <if test="zqmljzj != null "> and zqmljzj = #{zqmljzj}</if>
        </where>
        AND gjahr = #{gjahr}
        AND monat = #{monat}
        ORDER BY netBookValueRatio DESC
    </select>

    <select id="selectFixedAssetsTable4List" parameterType="FixedAssetsReport" resultMap="fixedAssetsDetailsTable4">
        SELECT
        TOP 10
        bukrs,
        txk20,
        anln1,
        txt50,
        aktiv,
        zjz,
        zqmyz,
        zqmljzj,
        CAST (
        ROUND( ( zjz / ( SELECT SUM ( zjz ) FROM FixedAssetsReport
        WHERE bukrs = COALESCE(#{bukrs}, bukrs) AND gjahr = #{gjahr} AND monat = #{monat} ) ) * 100, 6 ) AS DECIMAL ( 38, 6 )
        ) AS netBookValueRatio
        FROM
        FixedAssetsReport
        <where>
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="txk20 != null  and txk20 != ''"> and txk20 like concat('%', #{txk20}, '%')</if>
            <if test="anln1 != null  and anln1 != ''"> and anln1 = #{anln1}</if>
            <if test="txt50 != null  and txt50 != ''"> and txt50 like concat('%', #{txt50}, '%')</if>
            <if test="zqmyz != null "> and zqmyz = #{zqmyz}</if>
            <if test="zqmljzj != null "> and zqmljzj = #{zqmljzj}</if>
            and anlkl = 'A510'
            AND gjahr = #{gjahr}
            AND monat = #{monat}
        </where>
        ORDER BY netBookValueRatio DESC
    </select>

    <select id="selectFixedAssetsTable5List" parameterType="FixedAssetsReport" resultMap="fixedAssetsDetailsTable4">
        SELECT
        TOP 10
        bukrs,
        txk20,
        anln1,
        txt50,
        aktiv,
        zjz,
        zqmyz,
        zqmljzj,
        CAST (
        ROUND( ( zjz / ( SELECT SUM ( zjz ) FROM FixedAssetsReport
        WHERE bukrs = COALESCE(#{bukrs}, bukrs) AND gjahr = #{gjahr} AND monat = #{monat} ) ) * 100, 6 ) AS DECIMAL ( 38, 6 )
        ) AS netBookValueRatio
        FROM
        FixedAssetsReport
        <where>
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="txk20 != null  and txk20 != ''"> and txk20 like concat('%', #{txk20}, '%')</if>
            <if test="anln1 != null  and anln1 != ''"> and anln1 = #{anln1}</if>
            <if test="txt50 != null  and txt50 != ''"> and txt50 like concat('%', #{txt50}, '%')</if>
            <if test="zqmyz != null "> and zqmyz = #{zqmyz}</if>
            <if test="zqmljzj != null "> and zqmljzj = #{zqmljzj}</if>
            and anlkl = 'A410'
            AND gjahr = #{gjahr}
            AND monat = #{monat}
        </where>
        ORDER BY netBookValueRatio DESC
    </select>

    <select id="selectFixedAssetsTable6List" parameterType="FixedAssetsReport" resultMap="fixedAssetsDetailsTable4">
        SELECT
        TOP 10
        bukrs,
        txk20,
        anln1,
        txt50,
        aktiv,
        zjz,
        zqmyz,
        zqmljzj,
        CAST (
        ROUND( ( zjz / ( SELECT SUM ( zjz ) FROM FixedAssetsReport
        WHERE bukrs = COALESCE(#{bukrs}, bukrs) AND gjahr = #{gjahr} AND monat = #{monat} ) ) * 100, 6 ) AS DECIMAL ( 38, 6 )
        ) AS netBookValueRatio
        FROM
        FixedAssetsReport
        <where>
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="txk20 != null  and txk20 != ''"> and txk20 like concat('%', #{txk20}, '%')</if>
            <if test="anln1 != null  and anln1 != ''"> and anln1 = #{anln1}</if>
            <if test="txt50 != null  and txt50 != ''"> and txt50 like concat('%', #{txt50}, '%')</if>
            <if test="zqmyz != null "> and zqmyz = #{zqmyz}</if>
            <if test="zqmljzj != null "> and zqmljzj = #{zqmljzj}</if>
            AND anlkl IN ('A310', 'A320', 'A330', 'A340', 'A350')
            AND gjahr = #{gjahr}
            AND monat = #{monat}
        </where>
        ORDER BY netBookValueRatio DESC
    </select>

    <select id="selectFixedAssetsReportBySid" parameterType="Long" resultMap="FixedAssetsReportResult">
        <include refid="selectFixedAssetsReportVo"/>
        where sid = #{sid}
    </select>


    <insert id="insertFixedAssetsReport" parameterType="FixedAssetsReport">
        insert into FixedAssetsReport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="bukrs != null">bukrs,</if>
            <if test="gjahr != null">gjahr,</if>
            <if test="monat != null">monat,</if>
            <if test="butxt != null">butxt,</if>
            <if test="anlkl != null">anlkl,</if>
            <if test="txk20 != null">txk20,</if>
            <if test="anln1 != null">anln1,</if>
            <if test="anlhtxt != null">anlhtxt,</if>
            <if test="anln2 != null">anln2,</if>
            <if test="txt50 != null">txt50,</if>
            <if test="sernr != null">sernr,</if>
            <if test="invnr != null">invnr,</if>
            <if test="invzu != null">invzu,</if>
            <if test="txa50 != null">txa50,</if>
            <if test="menge != null">menge,</if>
            <if test="meins != null">meins,</if>
            <if test="kostl != null">kostl,</if>
            <if test="ltext != null">ltext,</if>
            <if test="aktiv != null">aktiv,</if>
            <if test="ernam != null">ernam,</if>
            <if test="erdat != null">erdat,</if>
            <if test="afasl != null">afasl,</if>
            <if test="afbktx != null">afbktx,</if>
            <if test="ordtx2 != null">ordtx2,</if>
            <if test="ordtx1 != null">ordtx1,</if>
            <if test="afabg != null">afabg,</if>
            <if test="zndjar != null">zndjar,</if>
            <if test="zyzjqjs != null">zyzjqjs,</if>
            <if test="zsysyq != null">zsysyq,</if>
            <if test="zyzjl != null">zyzjl,</if>
            <if test="schrwProz != null">schrw_proz,</if>
            <if test="schrw != null">schrw,</if>
            <if test="zjz != null">zjz,</if>
            <if test="zncyz != null">zncyz,</if>
            <if test="zqmyz != null">zqmyz,</if>
            <if test="zbyljzj != null">zbyljzj,</if>
            <if test="zbnljzj != null">zbnljzj,</if>
            <if test="zncljzj != null">zncljzj,</if>
            <if test="zqmljzj != null">zqmljzj,</if>
            <if test="zqmjzzb != null">zqmjzzb,</if>
            <if test="xspeb != null">xspeb,</if>
            <if test="xloev != null">xloev,</if>
            <if test="deakt != null">deakt,</if>
            <if test="aenam != null">aenam,</if>
            <if test="zzjkm != null">zzjkm,</if>
            <if test="zzjkmms != null">zzjkmms,</if>
            <if test="xstil != null">xstil,</if>
            <if test="adatu != null">adatu,</if>
            <if test="zzckm != null">zzckm,</if>
            <if test="zzckmms != null">zzckmms,</if>
            <if test="zfykm != null">zfykm,</if>
            <if test="zfykmms != null">zfykmms,</if>
            <if test="lifnr != null">lifnr,</if>
            <if test="name1 != null">name1,</if>
            <if test="herst != null">herst,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="bukrs != null">#{bukrs},</if>
            <if test="gjahr != null">#{gjahr},</if>
            <if test="monat != null">#{monat},</if>
            <if test="butxt != null">#{butxt},</if>
            <if test="anlkl != null">#{anlkl},</if>
            <if test="txk20 != null">#{txk20},</if>
            <if test="anln1 != null">#{anln1},</if>
            <if test="anlhtxt != null">#{anlhtxt},</if>
            <if test="anln2 != null">#{anln2},</if>
            <if test="txt50 != null">#{txt50},</if>
            <if test="sernr != null">#{sernr},</if>
            <if test="invnr != null">#{invnr},</if>
            <if test="invzu != null">#{invzu},</if>
            <if test="txa50 != null">#{txa50},</if>
            <if test="menge != null">#{menge},</if>
            <if test="meins != null">#{meins},</if>
            <if test="kostl != null">#{kostl},</if>
            <if test="ltext != null">#{ltext},</if>
            <if test="aktiv != null">#{aktiv},</if>
            <if test="ernam != null">#{ernam},</if>
            <if test="erdat != null">#{erdat},</if>
            <if test="afasl != null">#{afasl},</if>
            <if test="afbktx != null">#{afbktx},</if>
            <if test="ordtx2 != null">#{ordtx2},</if>
            <if test="ordtx1 != null">#{ordtx1},</if>
            <if test="afabg != null">#{afabg},</if>
            <if test="zndjar != null">#{zndjar},</if>
            <if test="zyzjqjs != null">#{zyzjqjs},</if>
            <if test="zsysyq != null">#{zsysyq},</if>
            <if test="zyzjl != null">#{zyzjl},</if>
            <if test="schrwProz != null">#{schrwProz},</if>
            <if test="schrw != null">#{schrw},</if>
            <if test="zjz != null">#{zjz},</if>
            <if test="zncyz != null">#{zncyz},</if>
            <if test="zqmyz != null">#{zqmyz},</if>
            <if test="zbyljzj != null">#{zbyljzj},</if>
            <if test="zbnljzj != null">#{zbnljzj},</if>
            <if test="zncljzj != null">#{zncljzj},</if>
            <if test="zqmljzj != null">#{zqmljzj},</if>
            <if test="zqmjzzb != null">#{zqmjzzb},</if>
            <if test="xspeb != null">#{xspeb},</if>
            <if test="xloev != null">#{xloev},</if>
            <if test="deakt != null">#{deakt},</if>
            <if test="aenam != null">#{aenam},</if>
            <if test="zzjkm != null">#{zzjkm},</if>
            <if test="zzjkmms != null">#{zzjkmms},</if>
            <if test="xstil != null">#{xstil},</if>
            <if test="adatu != null">#{adatu},</if>
            <if test="zzckm != null">#{zzckm},</if>
            <if test="zzckmms != null">#{zzckmms},</if>
            <if test="zfykm != null">#{zfykm},</if>
            <if test="zfykmms != null">#{zfykmms},</if>
            <if test="lifnr != null">#{lifnr},</if>
            <if test="name1 != null">#{name1},</if>
            <if test="herst != null">#{herst},</if>
         </trim>
    </insert>

    <update id="updateFixedAssetsReport" parameterType="FixedAssetsReport">
        update FixedAssetsReport
        <trim prefix="SET" suffixOverrides=",">
            <if test="bukrs != null">bukrs = #{bukrs},</if>
            <if test="gjahr != null">gjahr = #{gjahr},</if>
            <if test="monat != null">monat = #{monat},</if>
            <if test="butxt != null">butxt = #{butxt},</if>
            <if test="anlkl != null">anlkl = #{anlkl},</if>
            <if test="txk20 != null">txk20 = #{txk20},</if>
            <if test="anln1 != null">anln1 = #{anln1},</if>
            <if test="anlhtxt != null">anlhtxt = #{anlhtxt},</if>
            <if test="anln2 != null">anln2 = #{anln2},</if>
            <if test="txt50 != null">txt50 = #{txt50},</if>
            <if test="sernr != null">sernr = #{sernr},</if>
            <if test="invnr != null">invnr = #{invnr},</if>
            <if test="invzu != null">invzu = #{invzu},</if>
            <if test="txa50 != null">txa50 = #{txa50},</if>
            <if test="menge != null">menge = #{menge},</if>
            <if test="meins != null">meins = #{meins},</if>
            <if test="kostl != null">kostl = #{kostl},</if>
            <if test="ltext != null">ltext = #{ltext},</if>
            <if test="aktiv != null">aktiv = #{aktiv},</if>
            <if test="ernam != null">ernam = #{ernam},</if>
            <if test="erdat != null">erdat = #{erdat},</if>
            <if test="afasl != null">afasl = #{afasl},</if>
            <if test="afbktx != null">afbktx = #{afbktx},</if>
            <if test="ordtx2 != null">ordtx2 = #{ordtx2},</if>
            <if test="ordtx1 != null">ordtx1 = #{ordtx1},</if>
            <if test="afabg != null">afabg = #{afabg},</if>
            <if test="zndjar != null">zndjar = #{zndjar},</if>
            <if test="zyzjqjs != null">zyzjqjs = #{zyzjqjs},</if>
            <if test="zsysyq != null">zsysyq = #{zsysyq},</if>
            <if test="zyzjl != null">zyzjl = #{zyzjl},</if>
            <if test="schrwProz != null">schrw_proz = #{schrwProz},</if>
            <if test="schrw != null">schrw = #{schrw},</if>
            <if test="zjz != null">zjz = #{zjz},</if>
            <if test="zncyz != null">zncyz = #{zncyz},</if>
            <if test="zqmyz != null">zqmyz = #{zqmyz},</if>
            <if test="zbyljzj != null">zbyljzj = #{zbyljzj},</if>
            <if test="zbnljzj != null">zbnljzj = #{zbnljzj},</if>
            <if test="zncljzj != null">zncljzj = #{zncljzj},</if>
            <if test="zqmljzj != null">zqmljzj = #{zqmljzj},</if>
            <if test="zqmjzzb != null">zqmjzzb = #{zqmjzzb},</if>
            <if test="xspeb != null">xspeb = #{xspeb},</if>
            <if test="xloev != null">xloev = #{xloev},</if>
            <if test="deakt != null">deakt = #{deakt},</if>
            <if test="aenam != null">aenam = #{aenam},</if>
            <if test="zzjkm != null">zzjkm = #{zzjkm},</if>
            <if test="zzjkmms != null">zzjkmms = #{zzjkmms},</if>
            <if test="xstil != null">xstil = #{xstil},</if>
            <if test="adatu != null">adatu = #{adatu},</if>
            <if test="zzckm != null">zzckm = #{zzckm},</if>
            <if test="zzckmms != null">zzckmms = #{zzckmms},</if>
            <if test="zfykm != null">zfykm = #{zfykm},</if>
            <if test="zfykmms != null">zfykmms = #{zfykmms},</if>
            <if test="lifnr != null">lifnr = #{lifnr},</if>
            <if test="name1 != null">name1 = #{name1},</if>
            <if test="herst != null">herst = #{herst},</if>
        </trim>
        where sid = #{sid}
    </update>

    <delete id="deleteFixedAssetsReportBySid" parameterType="Long">
        delete from FixedAssetsReport where sid = #{sid}
    </delete>

    <delete id="deleteFixedAssetsReportBySids" parameterType="String">
        delete from FixedAssetsReport where sid in 
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>