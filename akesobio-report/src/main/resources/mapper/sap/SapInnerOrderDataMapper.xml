<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.SapInnerOrderDataMapper">

    <resultMap type="SapInnerOrderData" id="SapInnerOrderDataResult">
        <result property="id"    column="id"    />
        <result property="innerOrderNum"    column="inner_order_num"    />
        <result property="orderType"    column="order_type"    />
        <result property="orderText"    column="order_text"    />
        <result property="primaryClassification"    column="primary_classification"    />
        <result property="secondaryClassification"    column="secondary_classification"    />
        <result property="functionalScope"    column="functional_scope"    />
    </resultMap>

    <sql id="selectSapInnerOrderDataVo">
        select id, inner_order_num, order_type, order_text, primary_classification, secondary_classification, functional_scope from sap_inner_order_data
    </sql>

    <select id="selectSapInnerOrderDataList" parameterType="SapInnerOrderData" resultMap="SapInnerOrderDataResult">
        <include refid="selectSapInnerOrderDataVo"/>
        <where>
            <if test="innerOrderNum != null  and innerOrderNum != ''"> and inner_order_num = #{innerOrderNum}</if>
            <if test="orderType != null  and orderType != ''"> and order_type = #{orderType}</if>
            <if test="orderText != null  and orderText != ''"> and order_text = #{orderText}</if>
            <if test="primaryClassification != null  and primaryClassification != ''"> and primary_classification = #{primaryClassification}</if>
            <if test="secondaryClassification != null  and secondaryClassification != ''"> and secondary_classification = #{secondaryClassification}</if>
            <if test="functionalScope != null  and functionalScope != ''"> and functional_scope = #{functionalScope}</if>
        </where>
    </select>

    <select id="selectSapInnerOrderDataById" parameterType="Long" resultMap="SapInnerOrderDataResult">
        <include refid="selectSapInnerOrderDataVo"/>
        where id = #{id}
    </select>

    <select id="selectList" parameterType="java.util.List" resultMap="SapInnerOrderDataResult">
        <include refid="selectSapInnerOrderDataVo"/>
        <where>
            <if test="list != null and list.size() > 0">
                 and inner_order_num in
                <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                    #{item.innerOrderNum}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insertSapInnerOrderData" parameterType="SapInnerOrderData">
        insert into sap_inner_order_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="innerOrderNum != null">inner_order_num,</if>
            <if test="orderType != null">order_type,</if>
            <if test="orderText != null">order_text,</if>
            <if test="primaryClassification != null">primary_classification,</if>
            <if test="secondaryClassification != null">secondary_classification,</if>
            <if test="functionalScope != null">functional_scope,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="innerOrderNum != null">#{innerOrderNum},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="orderText != null">#{orderText},</if>
            <if test="primaryClassification != null">#{primaryClassification},</if>
            <if test="secondaryClassification != null">#{secondaryClassification},</if>
            <if test="functionalScope != null">#{functionalScope},</if>
        </trim>
    </insert>

    <update id="updateSapInnerOrderData" parameterType="SapInnerOrderData">
        update sap_inner_order_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="innerOrderNum != null">inner_order_num = #{innerOrderNum},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="orderText != null">order_text = #{orderText},</if>
            <if test="primaryClassification != null">primary_classification = #{primaryClassification},</if>
            <if test="secondaryClassification != null">secondary_classification = #{secondaryClassification},</if>
            <if test="functionalScope != null">functional_scope = #{functionalScope},</if>
        </trim>
        where inner_order_num = #{innerOrderNum} and order_type = #{orderType} and order_text = #{orderText}
            and primary_classification = #{primaryClassification} and secondary_classification = #{secondaryClassification}
    </update>

    <delete id="deleteSapInnerOrderDataById" parameterType="Long">
        delete from sap_inner_order_data where id = #{id}
    </delete>

    <delete id="deleteSapInnerOrderDataByIds" parameterType="String">
        delete from sap_inner_order_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertList" parameterType="SapInnerOrderData">
        insert into sap_inner_order_data (inner_order_num, order_type, order_text, primary_classification, secondary_classification, functional_scope)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.innerOrderNum}, #{item.orderType}, #{item.orderText}, #{item.primaryClassification},
             #{item.secondaryClassification}, #{item.functionalScope})
        </foreach>
    </insert>
</mapper>