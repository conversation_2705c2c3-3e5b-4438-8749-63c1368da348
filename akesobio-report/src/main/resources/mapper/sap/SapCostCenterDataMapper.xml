<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.SapCostCenterDataMapper">

    <resultMap type="SapCostCenterData" id="SapCostCenterDataResult">
        <result property="id"    column="id"    />
        <result property="bloc"    column="bloc"    />
        <result property="company"    column="company"    />
        <result property="costCenter"    column="cost_center"    />
        <result property="costCenterText"    column="cost_center_text"    />
        <result property="functionalScope"    column="functional_scope"    />
    </resultMap>

    <sql id="selectSapCostCenterDataVo">
        select id, bloc, company, cost_center, cost_center_text, functional_scope from sap_cost_center_data
    </sql>

    <select id="selectSapCostCenterDataList" parameterType="SapCostCenterData" resultMap="SapCostCenterDataResult">
        <include refid="selectSapCostCenterDataVo"/>
        <where>
            <if test="bloc != null  and bloc != ''"> and bloc = #{bloc}</if>
            <if test="company != null  and company != ''"> and company = #{company}</if>
            <if test="costCenter != null  and costCenter != ''"> and cost_center = #{costCenter}</if>
            <if test="costCenterText != null  and costCenterText != ''"> and cost_center_text = #{costCenterText}</if>
            <if test="functionalScope != null  and functionalScope != ''"> and functional_scope = #{functionalScope}</if>
        </where>
    </select>

    <select id="selectSapCostCenterDataById" parameterType="Long" resultMap="SapCostCenterDataResult">
        <include refid="selectSapCostCenterDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertSapCostCenterData" parameterType="SapCostCenterData">
        insert into sap_cost_center_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bloc != null">bloc,</if>
            <if test="company != null">company,</if>
            <if test="costCenter != null">cost_center,</if>
            <if test="costCenterText != null">cost_center_text,</if>
            <if test="functionalScope != null">functional_scope,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bloc != null">#{bloc},</if>
            <if test="company != null">#{company},</if>
            <if test="costCenter != null">#{costCenter},</if>
            <if test="costCenterText != null">#{costCenterText},</if>
            <if test="functionalScope != null">#{functionalScope},</if>
        </trim>
    </insert>

    <update id="updateSapCostCenterData" parameterType="SapCostCenterData">
        update sap_cost_center_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="bloc != null">bloc = #{bloc},</if>
            <if test="company != null">company = #{company},</if>
            <if test="costCenter != null">cost_center = #{costCenter},</if>
            <if test="costCenterText != null">cost_center_text = #{costCenterText},</if>
            <if test="functionalScope != null">functional_scope = #{functionalScope},</if>
        </trim>
        where bloc = #{bloc} and company = #{company} and cost_center = #{costCenter} and cost_center_text = #{costCenterText}
    </update>

    <delete id="deleteSapCostCenterDataById" parameterType="Long">
        delete from sap_cost_center_data where id = #{id}
    </delete>

    <delete id="deleteSapCostCenterDataByIds" parameterType="String">
        delete from sap_cost_center_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>