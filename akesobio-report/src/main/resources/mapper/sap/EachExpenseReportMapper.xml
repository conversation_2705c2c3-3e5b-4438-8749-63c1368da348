<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.EachExpenseReportMapper">
    
    <resultMap type="EachExpenseReport" id="EachExpenseReportResult">
        <result property="id"    column="id"    />
        <result property="butxt"    column="butxt"    />
        <result property="rbukrs"    column="rbukrs"    />
        <result property="gjahr"    column="gjahr"    />
        <result property="poper"    column="poper"    />
        <result property="racct"    column="racct"    />
        <result property="txt20"    column="txt20"    />
        <result property="rfarea"    column="rfarea"    />
        <result property="zhzkm"    column="zhzkm"    />
        <result property="zplkm"    column="zplkm"    />
        <result property="rcntr"    column="rcntr"    />
        <result property="ktext"    column="ktext"    />
        <result property="zbmmc"    column="zbmmc"    />
        <result property="zejbmFi"    column="zejbm_fi"    />
        <result property="rhcur"    column="rhcur"    />
        <result property="hsl"    column="hsl"    />
        <result property="rkcur"    column="rkcur"    />
        <result property="ksl"    column="ksl"    />
        <result property="monat"    column="monat"    />
    </resultMap>

    <sql id="selectEachExpenseReportVo">
        select id, butxt, rbukrs, gjahr, poper, racct, txt20, rfarea, zhzkm, zplkm, rcntr, ktext, zbmmc, zejbm_fi, rhcur, hsl, rkcur, ksl, monat from each_expense_report
    </sql>

    <select id="selectEachExpenseReportList" parameterType="EachExpenseReport" resultMap="EachExpenseReportResult">
        <include refid="selectEachExpenseReportVo"/>
        <where>  
            <if test="butxt != null  and butxt != ''"> and butxt = #{butxt}</if>
            <if test="rbukrs != null  and rbukrs != ''"> and rbukrs = #{rbukrs}</if>
            <if test="gjahr != null  and gjahr != ''"> and gjahr = #{gjahr}</if>
            <if test="poper != null  and poper != ''"> and poper = #{poper}</if>
            <if test="racct != null  and racct != ''"> and racct = #{racct}</if>
            <if test="txt20 != null  and txt20 != ''"> and txt20 = #{txt20}</if>
            <if test="rfarea != null  and rfarea != ''"> and rfarea = #{rfarea}</if>
            <if test="zhzkm != null  and zhzkm != ''"> and zhzkm = #{zhzkm}</if>
            <if test="zplkm != null  and zplkm != ''"> and zplkm = #{zplkm}</if>
            <if test="rcntr != null  and rcntr != ''"> and rcntr = #{rcntr}</if>
            <if test="ktext != null  and ktext != ''"> and ktext = #{ktext}</if>
            <if test="zbmmc != null  and zbmmc != ''"> and zbmmc = #{zbmmc}</if>
            <if test="zejbmFi != null  and zejbmFi != ''"> and zejbm_fi = #{zejbmFi}</if>
            <if test="rhcur != null  and rhcur != ''"> and rhcur = #{rhcur}</if>
            <if test="hsl != null  and hsl != ''"> and hsl = #{hsl}</if>
            <if test="rkcur != null  and rkcur != ''"> and rkcur = #{rkcur}</if>
            <if test="ksl != null  and ksl != ''"> and ksl = #{ksl}</if>
            <if test="monat != null  and monat != ''"> and monat = #{monat}</if>
        </where>
    </select>
    
    <select id="selectEachExpenseReportById" parameterType="String" resultMap="EachExpenseReportResult">
        <include refid="selectEachExpenseReportVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertEachExpenseReport" parameterType="EachExpenseReport">
        insert into each_expense_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="butxt != null">butxt,</if>
            <if test="rbukrs != null">rbukrs,</if>
            <if test="gjahr != null">gjahr,</if>
            <if test="poper != null">poper,</if>
            <if test="racct != null">racct,</if>
            <if test="txt20 != null">txt20,</if>
            <if test="rfarea != null">rfarea,</if>
            <if test="zhzkm != null">zhzkm,</if>
            <if test="zplkm != null">zplkm,</if>
            <if test="rcntr != null">rcntr,</if>
            <if test="ktext != null">ktext,</if>
            <if test="zbmmc != null">zbmmc,</if>
            <if test="zejbmFi != null">zejbm_fi,</if>
            <if test="rhcur != null">rhcur,</if>
            <if test="hsl != null">hsl,</if>
            <if test="rkcur != null">rkcur,</if>
            <if test="ksl != null">ksl,</if>
            <if test="monat != null">monat,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="butxt != null">#{butxt},</if>
            <if test="rbukrs != null">#{rbukrs},</if>
            <if test="gjahr != null">#{gjahr},</if>
            <if test="poper != null">#{poper},</if>
            <if test="racct != null">#{racct},</if>
            <if test="txt20 != null">#{txt20},</if>
            <if test="rfarea != null">#{rfarea},</if>
            <if test="zhzkm != null">#{zhzkm},</if>
            <if test="zplkm != null">#{zplkm},</if>
            <if test="rcntr != null">#{rcntr},</if>
            <if test="ktext != null">#{ktext},</if>
            <if test="zbmmc != null">#{zbmmc},</if>
            <if test="zejbmFi != null">#{zejbmFi},</if>
            <if test="rhcur != null">#{rhcur},</if>
            <if test="hsl != null">#{hsl},</if>
            <if test="rkcur != null">#{rkcur},</if>
            <if test="ksl != null">#{ksl},</if>
            <if test="monat != null">#{monat},</if>
         </trim>
    </insert>

    <insert id="insertList" parameterType="EachExpenseReport">
        insert into each_expense_report (butxt, rbukrs, gjahr, poper, racct, txt20, rfarea,
        zhzkm, zplkm, rcntr, ktext, zbmmc, zejbm_fi, rhcur, hsl, rkcur, ksl, monat)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.butxt}, #{item.rbukrs}, #{item.gjahr}, #{item.poper},#{item.racct}, #{item.txt20}, #{item.rfarea},
             #{item.zhzkm}, #{item.zplkm}, #{item.rcntr},#{item.ktext},#{item.zbmmc}, #{item.zejbmFi}, #{item.rhcur},
             #{item.hsl}, #{item.rkcur}, #{item.ksl}, #{item.monat})
        </foreach>
    </insert>

    <update id="updateEachExpenseReport" parameterType="EachExpenseReport">
        update each_expense_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="butxt != null">butxt = #{butxt},</if>
            <if test="rbukrs != null">rbukrs = #{rbukrs},</if>
            <if test="gjahr != null">gjahr = #{gjahr},</if>
            <if test="poper != null">poper = #{poper},</if>
            <if test="racct != null">racct = #{racct},</if>
            <if test="txt20 != null">txt20 = #{txt20},</if>
            <if test="rfarea != null">rfarea = #{rfarea},</if>
            <if test="zhzkm != null">zhzkm = #{zhzkm},</if>
            <if test="zplkm != null">zplkm = #{zplkm},</if>
            <if test="rcntr != null">rcntr = #{rcntr},</if>
            <if test="ktext != null">ktext = #{ktext},</if>
            <if test="zbmmc != null">zbmmc = #{zbmmc},</if>
            <if test="zejbmFi != null">zejbm_fi = #{zejbmFi},</if>
            <if test="rhcur != null">rhcur = #{rhcur},</if>
            <if test="hsl != null">hsl = #{hsl},</if>
            <if test="rkcur != null">rkcur = #{rkcur},</if>
            <if test="ksl != null">ksl = #{ksl},</if>
            <if test="monat != null">monat = #{monat},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEachExpenseReportById" parameterType="String">
        delete from each_expense_report where id = #{id}
    </delete>

    <delete id="deleteEachExpenseReportByIds" parameterType="String">
        delete from each_expense_report where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByYearAndMonth" parameterType="map">
        delete from each_expense_report where gjahr = #{year} and monat = #{month}
    </delete>
</mapper>