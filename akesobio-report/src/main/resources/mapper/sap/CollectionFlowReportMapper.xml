<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.CollectionFlowReportMapper">
    
    <resultMap type="CollectionFlowReport" id="CollectionFlowReportResult">
        <result property="sid"    column="sid"    />
        <result property="bukrs"    column="bukrs"    />
        <result property="hbkid"    column="hbkid"    />
        <result property="hktid"    column="hktid"    />
        <result property="zcinpay"    column="zcinpay"    />
        <result property="bankDate"    column="bank_date"    />
        <result property="partner"    column="partner"    />
        <result property="partBankAcctName"    column="part_bank_acct_name"    />
        <result property="partBankAcctNo"    column="part_bank_acct_no"    />
        <result property="direction"    column="direction"    />
        <result property="currency"    column="currency"    />
        <result property="otherAmount"    column="other_amount"    />
        <result property="zuonr"    column="zuonr"    />
        <result property="oriseq"    column="oriseq"    />
        <result property="ebrUse"    column="ebr_use"    />
        <result property="belnr"    column="belnr"    />
        <result property="ebrNo"    column="ebr_no"    />
        <result property="xreversed"    column="xreversed"    />
    </resultMap>

    <sql id="selectCollectionFlowReportVo">
        select sid, bukrs, hbkid, hktid, zcinpay, bank_date, partner, part_bank_acct_name, part_bank_acct_no, direction, currency, other_amount, zuonr, oriseq, ebr_use, belnr, ebr_no, xreversed from CollectionFlowReport
    </sql>

    <select id="selectCollectionFlowReportList" parameterType="CollectionFlowReport" resultMap="CollectionFlowReportResult">
        <include refid="selectCollectionFlowReportVo"/>
        <where>  
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="hbkid != null  and hbkid != ''"> and hbkid = #{hbkid}</if>
            <if test="hktid != null  and hktid != ''"> and hktid = #{hktid}</if>
            <if test="zcinpay != null  and zcinpay != ''"> and zcinpay = #{zcinpay}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and bank_date between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="partner != null  and partner != ''"> and partner = #{partner}</if>
            <if test="partBankAcctName != null  and partBankAcctName != ''"> and part_bank_acct_name like concat('%', #{partBankAcctName}, '%')</if>
            <if test="partBankAcctNo != null  and partBankAcctNo != ''"> and part_bank_acct_no = #{partBankAcctNo}</if>
            <if test="direction != null  and direction != ''"> and direction = #{direction}</if>
            <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
            <if test="otherAmount != null  and otherAmount != ''"> and other_amount = #{otherAmount}</if>
            <if test="zuonr != null  and zuonr != ''"> and zuonr = #{zuonr}</if>
            <if test="oriseq != null  and oriseq != ''"> and oriseq = #{oriseq}</if>
            <if test="ebrUse != null  and ebrUse != ''"> and ebr_use = #{ebrUse}</if>
            <if test="belnr != null  and belnr != ''"> and belnr = #{belnr}</if>
            <if test="ebrNo != null  and ebrNo != ''"> and ebr_no = #{ebrNo}</if>
            <if test="xreversed != null  and xreversed != ''"> and xreversed = #{xreversed}</if>
        </where>
    </select>
    
    <select id="selectCollectionFlowReportBySid" parameterType="Long" resultMap="CollectionFlowReportResult">
        <include refid="selectCollectionFlowReportVo"/>
        where sid = #{sid}
    </select>
        
    <insert id="insertCollectionFlowReport" parameterType="CollectionFlowReport">
        insert into CollectionFlowReport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="bukrs != null">bukrs,</if>
            <if test="hbkid != null">hbkid,</if>
            <if test="hktid != null">hktid,</if>
            <if test="zcinpay != null">zcinpay,</if>
            <if test="bankDate != null">bank_date,</if>
            <if test="partner != null">partner,</if>
            <if test="partBankAcctName != null">part_bank_acct_name,</if>
            <if test="partBankAcctNo != null">part_bank_acct_no,</if>
            <if test="direction != null">direction,</if>
            <if test="currency != null">currency,</if>
            <if test="otherAmount != null">other_amount,</if>
            <if test="zuonr != null">zuonr,</if>
            <if test="oriseq != null">oriseq,</if>
            <if test="ebrUse != null">ebr_use,</if>
            <if test="belnr != null">belnr,</if>
            <if test="ebrNo != null">ebr_no,</if>
            <if test="xreversed != null">xreversed,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="bukrs != null">#{bukrs},</if>
            <if test="hbkid != null">#{hbkid},</if>
            <if test="hktid != null">#{hktid},</if>
            <if test="zcinpay != null">#{zcinpay},</if>
            <if test="bankDate != null">#{bankDate},</if>
            <if test="partner != null">#{partner},</if>
            <if test="partBankAcctName != null">#{partBankAcctName},</if>
            <if test="partBankAcctNo != null">#{partBankAcctNo},</if>
            <if test="direction != null">#{direction},</if>
            <if test="currency != null">#{currency},</if>
            <if test="otherAmount != null">#{otherAmount},</if>
            <if test="zuonr != null">#{zuonr},</if>
            <if test="oriseq != null">#{oriseq},</if>
            <if test="ebrUse != null">#{ebrUse},</if>
            <if test="belnr != null">#{belnr},</if>
            <if test="ebrNo != null">#{ebrNo},</if>
            <if test="xreversed != null">#{xreversed},</if>
         </trim>
    </insert>

    <update id="updateCollectionFlowReport" parameterType="CollectionFlowReport">
        update CollectionFlowReport
        <trim prefix="SET" suffixOverrides=",">
            <if test="bukrs != null">bukrs = #{bukrs},</if>
            <if test="hbkid != null">hbkid = #{hbkid},</if>
            <if test="hktid != null">hktid = #{hktid},</if>
            <if test="zcinpay != null">zcinpay = #{zcinpay},</if>
            <if test="bankDate != null">bank_date = #{bankDate},</if>
            <if test="partner != null">partner = #{partner},</if>
            <if test="partBankAcctName != null">part_bank_acct_name = #{partBankAcctName},</if>
            <if test="partBankAcctNo != null">part_bank_acct_no = #{partBankAcctNo},</if>
            <if test="direction != null">direction = #{direction},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="otherAmount != null">other_amount = #{otherAmount},</if>
            <if test="zuonr != null">zuonr = #{zuonr},</if>
            <if test="oriseq != null">oriseq = #{oriseq},</if>
            <if test="ebrUse != null">ebr_use = #{ebrUse},</if>
            <if test="belnr != null">belnr = #{belnr},</if>
            <if test="ebrNo != null">ebr_no = #{ebrNo},</if>
            <if test="xreversed != null">xreversed = #{xreversed},</if>
        </trim>
        where sid = #{sid}
    </update>

    <delete id="deleteCollectionFlowReportBySid" parameterType="Long">
        delete from CollectionFlowReport where sid = #{sid}
    </delete>

    <delete id="deleteCollectionFlowReportBySids" parameterType="String">
        delete from CollectionFlowReport where sid in 
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>