<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.SapProductTaxRateTableMapper">

    <resultMap type="SapProductTaxRateTable" id="SapProductTaxRateTableResult">
        <result property="id"    column="id"    />
        <result property="materialNum"    column="material_num"    />
        <result property="countryCode"    column="country_code"    />
        <result property="saleOrganization"    column="sale_organization"    />
        <result property="taxClassification"    column="tax_classification"    />
        <result property="taxClassificationText"    column="tax_classification_text"    />
        <result property="taxCode"    column="tax_code"    />
        <result property="taxCodeText"    column="tax_code_text"    />
        <result property="taxRate"    column="tax_rate"    />
        <result property="taxRatePercentage"    column="tax_rate_percentage"    />
    </resultMap>

    <sql id="selectSapProductTaxRateTableVo">
        select id, material_num, country_code, sale_organization, tax_classification, tax_classification_text, tax_code, tax_code_text, tax_rate, tax_rate_percentage from sap_product_tax_rate_table
    </sql>

    <select id="selectSapProductTaxRateTableList" parameterType="SapProductTaxRateTable" resultMap="SapProductTaxRateTableResult">
        <include refid="selectSapProductTaxRateTableVo"/>
        <where>
            <if test="materialNum != null  and materialNum != ''"> and material_num = #{materialNum}</if>
            <if test="countryCode != null  and countryCode != ''"> and country_code = #{countryCode}</if>
            <if test="saleOrganization != null  and saleOrganization != ''"> and sale_organization = #{saleOrganization}</if>
            <if test="taxClassification != null  and taxClassification != ''"> and tax_classification = #{taxClassification}</if>
            <if test="taxClassificationText != null  and taxClassificationText != ''"> and tax_classification_text = #{taxClassificationText}</if>
            <if test="taxCode != null  and taxCode != ''"> and tax_code = #{taxCode}</if>
            <if test="taxCodeText != null  and taxCodeText != ''"> and tax_code_text = #{taxCodeText}</if>
            <if test="taxRate != null  and taxRate != ''"> and tax_rate = #{taxRate}</if>
            <if test="taxRatePercentage != null  and taxRatePercentage != ''"> and tax_rate_percentage = #{taxRatePercentage}</if>
        </where>
    </select>

    <select id="selectSapProductTaxRateTableById" parameterType="Long" resultMap="SapProductTaxRateTableResult">
        <include refid="selectSapProductTaxRateTableVo"/>
        where id = #{id}
    </select>

    <insert id="insertSapProductTaxRateTable" parameterType="SapProductTaxRateTable">
        insert into sap_product_tax_rate_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="materialNum != null">material_num,</if>
            <if test="countryCode != null">country_code,</if>
            <if test="saleOrganization != null">sale_organization,</if>
            <if test="taxClassification != null">tax_classification,</if>
            <if test="taxClassificationText != null">tax_classification_text,</if>
            <if test="taxCode != null">tax_code,</if>
            <if test="taxCodeText != null">tax_code_text,</if>
            <if test="taxRate != null">tax_rate,</if>
            <if test="taxRatePercentage != null">tax_rate_percentage,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="materialNum != null">#{materialNum},</if>
            <if test="countryCode != null">#{countryCode},</if>
            <if test="saleOrganization != null">#{saleOrganization},</if>
            <if test="taxClassification != null">#{taxClassification},</if>
            <if test="taxClassificationText != null">#{taxClassificationText},</if>
            <if test="taxCode != null">#{taxCode},</if>
            <if test="taxCodeText != null">#{taxCodeText},</if>
            <if test="taxRate != null">#{taxRate},</if>
            <if test="taxRatePercentage != null">#{taxRatePercentage},</if>
        </trim>
    </insert>

    <update id="updateSapProductTaxRateTable" parameterType="SapProductTaxRateTable">
        update sap_product_tax_rate_table
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialNum != null">material_num = #{materialNum},</if>
            <if test="countryCode != null">country_code = #{countryCode},</if>
            <if test="saleOrganization != null">sale_organization = #{saleOrganization},</if>
            <if test="taxClassification != null">tax_classification = #{taxClassification},</if>
            <if test="taxClassificationText != null">tax_classification_text = #{taxClassificationText},</if>
            <if test="taxCode != null">tax_code = #{taxCode},</if>
            <if test="taxCodeText != null">tax_code_text = #{taxCodeText},</if>
            <if test="taxRate != null">tax_rate = #{taxRate},</if>
            <if test="taxRatePercentage != null">tax_rate_percentage = #{taxRatePercentage},</if>
        </trim>
        where material_num = #{materialNum} and country_code = #{countryCode} and sale_organization = #{saleOrganization}
            and tax_classification = #{taxClassification} and tax_classification_text = #{taxClassificationText} and tax_code = #{taxCode}
            and tax_code_text = #{taxCodeText} and tax_rate = #{taxRate} and tax_rate_percentage = #{taxRatePercentage}
    </update>

    <delete id="deleteSapProductTaxRateTableById" parameterType="Long">
        delete from sap_product_tax_rate_table where id = #{id}
    </delete>

    <delete id="deleteSapProductTaxRateTableByIds" parameterType="String">
        delete from sap_product_tax_rate_table where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>