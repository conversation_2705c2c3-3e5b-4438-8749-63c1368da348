<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.CustomerBalanceReportMapper">
    
    <resultMap type="CustomerBalanceReport" id="CustomerBalanceReportResult">
        <result property="sid"    column="sid"    />
        <result property="gjahr"    column="gjahr"    />
        <result property="saknr"    column="saknr"    />
        <result property="txt50"    column="txt50"    />
        <result property="kunnr"    column="kunnr"    />
        <result property="bukrs"    column="bukrs"    />
        <result property="name1"    column="name1"    />
        <result property="umskz"    column="umskz"    />
        <result property="waers"    column="waers"    />
        <result property="opbal"    column="opbal"    />
        <result property="debbal"    column="debbal"    />
        <result property="credbal"    column="credbal"    />
        <result property="salds"    column="salds"    />
        <result property="saldh"    column="saldh"    />
        <result property="acytdBal"    column="acytd_bal"    />
    </resultMap>

    <sql id="selectCustomerBalanceReportVo">
        select sid, gjahr, saknr, txt50, kunnr, bukrs, name1, umskz, waers, opbal, debbal, credbal, salds, saldh, acytd_bal from CustomerBalanceReport
    </sql>

    <select id="selectCustomerBalanceReportList" parameterType="CustomerBalanceReport" resultMap="CustomerBalanceReportResult">
        <include refid="selectCustomerBalanceReportVo"/>
        <where>  
            <if test="gjahr != null  and gjahr != ''"> and gjahr = #{gjahr}</if>
            <if test="saknr != null  and saknr != ''"> and saknr = #{saknr}</if>
            <if test="txt50 != null  and txt50 != ''"> and txt50 = #{txt50}</if>
            <if test="kunnr != null  and kunnr != ''"> and kunnr LIKE CONCAT('%',#{kunnr},'%')</if>
            <if test="bukrs != null  and bukrs != ''"> and bukrs = #{bukrs}</if>
            <if test="name1 != null  and name1 != ''"> and name1 = #{name1}</if>
            <if test="umskz != null  and umskz != ''"> and umskz = #{umskz}</if>
            <if test="waers != null  and waers != ''"> and waers = #{waers}</if>
            <if test="opbal != null  and opbal != ''"> and opbal = #{opbal}</if>
            <if test="debbal != null  and debbal != ''"> and debbal = #{debbal}</if>
            <if test="credbal != null  and credbal != ''"> and credbal = #{credbal}</if>
            <if test="salds != null  and salds != ''"> and salds = #{salds}</if>
            <if test="saldh != null  and saldh != ''"> and saldh = #{saldh}</if>
            <if test="acytdBal != null  and acytdBal != ''"> and acytd_bal = #{acytdBal}</if>
        </where>
        order by gjahr desc
    </select>
    
    <select id="selectCustomerBalanceReportBySid" parameterType="Long" resultMap="CustomerBalanceReportResult">
        <include refid="selectCustomerBalanceReportVo"/>
        where sid = #{sid}
    </select>
        
    <insert id="insertCustomerBalanceReport" parameterType="CustomerBalanceReport">
        insert into CustomerBalanceReport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="gjahr != null">gjahr,</if>
            <if test="saknr != null">saknr,</if>
            <if test="txt50 != null">txt50,</if>
            <if test="kunnr != null">kunnr,</if>
            <if test="bukrs != null">bukrs,</if>
            <if test="name1 != null">name1,</if>
            <if test="umskz != null">umskz,</if>
            <if test="waers != null">waers,</if>
            <if test="opbal != null">opbal,</if>
            <if test="debbal != null">debbal,</if>
            <if test="credbal != null">credbal,</if>
            <if test="salds != null">salds,</if>
            <if test="saldh != null">saldh,</if>
            <if test="acytdBal != null">acytd_bal,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="gjahr != null">#{gjahr},</if>
            <if test="saknr != null">#{saknr},</if>
            <if test="txt50 != null">#{txt50},</if>
            <if test="kunnr != null">#{kunnr},</if>
            <if test="bukrs != null">#{bukrs},</if>
            <if test="name1 != null">#{name1},</if>
            <if test="umskz != null">#{umskz},</if>
            <if test="waers != null">#{waers},</if>
            <if test="opbal != null">#{opbal},</if>
            <if test="debbal != null">#{debbal},</if>
            <if test="credbal != null">#{credbal},</if>
            <if test="salds != null">#{salds},</if>
            <if test="saldh != null">#{saldh},</if>
            <if test="acytdBal != null">#{acytdBal},</if>
         </trim>
    </insert>

    <update id="updateCustomerBalanceReport" parameterType="CustomerBalanceReport">
        update CustomerBalanceReport
        <trim prefix="SET" suffixOverrides=",">
            <if test="gjahr != null">gjahr = #{gjahr},</if>
            <if test="saknr != null">saknr = #{saknr},</if>
            <if test="txt50 != null">txt50 = #{txt50},</if>
            <if test="kunnr != null">kunnr = #{kunnr},</if>
            <if test="bukrs != null">bukrs = #{bukrs},</if>
            <if test="name1 != null">name1 = #{name1},</if>
            <if test="umskz != null">umskz = #{umskz},</if>
            <if test="waers != null">waers = #{waers},</if>
            <if test="opbal != null">opbal = #{opbal},</if>
            <if test="debbal != null">debbal = #{debbal},</if>
            <if test="credbal != null">credbal = #{credbal},</if>
            <if test="salds != null">salds = #{salds},</if>
            <if test="saldh != null">saldh = #{saldh},</if>
            <if test="acytdBal != null">acytd_bal = #{acytdBal},</if>
        </trim>
        where sid = #{sid}
    </update>

    <delete id="deleteCustomerBalanceReportBySid" parameterType="Long">
        delete from CustomerBalanceReport where sid = #{sid}
    </delete>

    <delete id="deleteCustomerBalanceReportBySids" parameterType="String">
        delete from CustomerBalanceReport where sid in 
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>