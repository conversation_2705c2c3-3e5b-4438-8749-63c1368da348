<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.ZrpFi002Mapper">
    
    <resultMap type="ZrpFi002" id="ZrpFi002Result">
        <result property="sid"    column="sid"    />
        <result property="rbukrs"    column="rbukrs"    />
        <result property="gjahr"    column="gjahr"    />
        <result property="fiscyearper"    column="fiscyearper"    />
        <result property="blart"    column="blart"    />
        <result property="kdauf"    column="kdauf"    />
        <result property="kdpos"    column="kdpos"    />
        <result property="ebeln"    column="ebeln"    />
        <result property="ebelp"    column="ebelp"    />
        <result property="msl"    column="msl"    />
        <result property="runit"    column="runit"    />
        <result property="kunnr"    column="kunnr"    />
        <result property="matnr"    column="matnr"    />
        <result property="zzcms"    column="zzcms"    />
        <result property="bklas"    column="bklas"    />
        <result property="racct"    column="racct"    />
        <result property="vtweg"    column="vtweg"    />
        <result property="mtartPa"    column="mtart_pa"    />
        <result property="matklMm"    column="matkl_mm"    />
        <result property="xpaobjnrCoRel"    column="xpaobjnr_co_rel"    />
        <result property="auartPa"    column="auart_pa"    />
        <result property="vkburPa"    column="vkbur_pa"    />
        <result property="spart"    column="spart"    />
        <result property="waerk"    column="waerk"    />
        <result property="netpr"    column="netpr"    />
        <result property="kursk"    column="kursk"    />
        <result property="znetpr"    column="znetpr"    />
        <result property="zhslKursk"    column="zhsl_kursk"    />
        <result property="zgtpr"    column="zgtpr"    />
        <result property="hsl"    column="hsl"    />
        <result property="kzwi1"    column="kzwi1"    />
        <result property="vv003"    column="vv003"    />
        <result property="sj010"    column="sj010"    />
        <result property="sj020"    column="sj020"    />
        <result property="sj030"    column="sj030"    />
        <result property="sj040"    column="sj040"    />
        <result property="sj050"    column="sj050"    />
        <result property="sj060"    column="sj060"    />
        <result property="sj070"    column="sj070"    />
        <result property="zsjml"    column="zsjml"    />
        <result property="vv002"    column="vv002"    />
        <result property="bz010"    column="bz010"    />
        <result property="bz020"    column="bz020"    />
        <result property="bz030"    column="bz030"    />
        <result property="bz040"    column="bz040"    />
        <result property="bz050"    column="bz050"    />
        <result property="bz060"    column="bz060"    />
        <result property="bz070"    column="bz070"    />
        <result property="zbzml"    column="zbzml"    />
        <result property="zbzmll"    column="zbzmll"    />
        <result property="zsjmll"    column="zsjmll"    />
        <result property="kdgrpAna"    column="kdgrp_ana"    />
        <result property="perveAna"    column="perve_ana"    />
        <result property="bzirkAna"    column="bzirk_ana"    />
    </resultMap>

    <sql id="selectZrpFi002Vo">
        select sid, rbukrs, gjahr, fiscyearper, blart, kdauf, kdpos, ebeln, ebelp, msl, runit, kunnr, matnr, zzcms, bklas, racct, vtweg, mtart_pa, matkl_mm, xpaobjnr_co_rel, auart_pa, vkbur_pa, spart, waerk, netpr, kursk, znetpr, zhsl_kursk, zgtpr, hsl, kzwi1, vv003, sj010, sj020, sj030, sj040, sj050, sj060, sj070, zsjml, vv002, bz010, bz020, bz030, bz040, bz050, bz060, bz070, zbzml, zbzmll, zsjmll, kdgrp_ana, perve_ana, bzirk_ana from ZrpFi002
    </sql>

    <select id="selectZrpFi002List" parameterType="ZrpFi002" resultMap="ZrpFi002Result">
        <include refid="selectZrpFi002Vo"/>
        <where>  
            <if test="rbukrs != null  and rbukrs != ''"> and rbukrs = #{rbukrs}</if>
            <if test="gjahr != null  and gjahr != ''"> and gjahr = #{gjahr}</if>
            <if test="fiscyearper != null  and fiscyearper != ''"> and fiscyearper = #{fiscyearper}</if>
            <if test="blart != null  and blart != ''"> and blart = #{blart}</if>
            <if test="kdauf != null  and kdauf != ''"> and kdauf = #{kdauf}</if>
            <if test="kdpos != null  and kdpos != ''"> and kdpos = #{kdpos}</if>
            <if test="ebeln != null  and ebeln != ''"> and ebeln = #{ebeln}</if>
            <if test="ebelp != null  and ebelp != ''"> and ebelp = #{ebelp}</if>
            <if test="msl != null "> and msl = #{msl}</if>
            <if test="runit != null  and runit != ''"> and runit = #{runit}</if>
            <if test="kunnr != null  and kunnr != ''"> and kunnr = #{kunnr}</if>
            <if test="matnr != null  and matnr != ''"> and matnr = #{matnr}</if>
            <if test="zzcms != null  and zzcms != ''"> and zzcms = #{zzcms}</if>
            <if test="bklas != null  and bklas != ''"> and bklas = #{bklas}</if>
            <if test="racct != null  and racct != ''"> and racct = #{racct}</if>
            <if test="vtweg != null  and vtweg != ''"> and vtweg = #{vtweg}</if>
            <if test="mtartPa != null  and mtartPa != ''"> and mtart_pa = #{mtartPa}</if>
            <if test="matklMm != null  and matklMm != ''"> and matkl_mm = #{matklMm}</if>
            <if test="xpaobjnrCoRel != null  and xpaobjnrCoRel != ''"> and xpaobjnr_co_rel = #{xpaobjnrCoRel}</if>
            <if test="auartPa != null  and auartPa != ''"> and auart_pa = #{auartPa}</if>
            <if test="vkburPa != null  and vkburPa != ''"> and vkbur_pa = #{vkburPa}</if>
            <if test="spart != null  and spart != ''"> and spart = #{spart}</if>
            <if test="waerk != null  and waerk != ''"> and waerk = #{waerk}</if>
            <if test="netpr != null "> and netpr = #{netpr}</if>
            <if test="kursk != null "> and kursk = #{kursk}</if>
            <if test="znetpr != null  and znetpr != ''"> and znetpr = #{znetpr}</if>
            <if test="zhslKursk != null  and zhslKursk != ''"> and zhsl_kursk = #{zhslKursk}</if>
            <if test="zgtpr != null  and zgtpr != ''"> and zgtpr = #{zgtpr}</if>
            <if test="hsl != null  and hsl != ''"> and hsl = #{hsl}</if>
            <if test="kzwi1 != null  and kzwi1 != ''"> and kzwi1 = #{kzwi1}</if>
            <if test="vv003 != null  and vv003 != ''"> and vv003 = #{vv003}</if>
            <if test="sj010 != null  and sj010 != ''"> and sj010 = #{sj010}</if>
            <if test="sj020 != null  and sj020 != ''"> and sj020 = #{sj020}</if>
            <if test="sj030 != null  and sj030 != ''"> and sj030 = #{sj030}</if>
            <if test="sj040 != null  and sj040 != ''"> and sj040 = #{sj040}</if>
            <if test="sj050 != null  and sj050 != ''"> and sj050 = #{sj050}</if>
            <if test="sj060 != null  and sj060 != ''"> and sj060 = #{sj060}</if>
            <if test="sj070 != null  and sj070 != ''"> and sj070 = #{sj070}</if>
            <if test="zsjml != null  and zsjml != ''"> and zsjml = #{zsjml}</if>
            <if test="vv002 != null  and vv002 != ''"> and vv002 = #{vv002}</if>
            <if test="bz010 != null  and bz010 != ''"> and bz010 = #{bz010}</if>
            <if test="bz020 != null  and bz020 != ''"> and bz020 = #{bz020}</if>
            <if test="bz030 != null  and bz030 != ''"> and bz030 = #{bz030}</if>
            <if test="bz040 != null  and bz040 != ''"> and bz040 = #{bz040}</if>
            <if test="bz050 != null  and bz050 != ''"> and bz050 = #{bz050}</if>
            <if test="bz060 != null  and bz060 != ''"> and bz060 = #{bz060}</if>
            <if test="bz070 != null  and bz070 != ''"> and bz070 = #{bz070}</if>
            <if test="zbzml != null  and zbzml != ''"> and zbzml = #{zbzml}</if>
            <if test="zbzmll != null  and zbzmll != ''"> and zbzmll = #{zbzmll}</if>
            <if test="zsjmll != null  and zsjmll != ''"> and zsjmll = #{zsjmll}</if>
            <if test="kdgrpAna != null  and kdgrpAna != ''"> and kdgrp_ana = #{kdgrpAna}</if>
            <if test="perveAna != null  and perveAna != ''"> and perve_ana = #{perveAna}</if>
            <if test="bzirkAna != null  and bzirkAna != ''"> and bzirk_ana = #{bzirkAna}</if>
        </where>
    </select>
    
    <select id="selectZrpFi002BySid" parameterType="Long" resultMap="ZrpFi002Result">
        <include refid="selectZrpFi002Vo"/>
        where sid = #{sid}
    </select>
        
    <insert id="insertZrpFi002" parameterType="ZrpFi002">
        insert into ZrpFi002
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="rbukrs != null">rbukrs,</if>
            <if test="gjahr != null">gjahr,</if>
            <if test="fiscyearper != null">fiscyearper,</if>
            <if test="blart != null">blart,</if>
            <if test="kdauf != null">kdauf,</if>
            <if test="kdpos != null">kdpos,</if>
            <if test="ebeln != null">ebeln,</if>
            <if test="ebelp != null">ebelp,</if>
            <if test="msl != null">msl,</if>
            <if test="runit != null">runit,</if>
            <if test="kunnr != null">kunnr,</if>
            <if test="matnr != null">matnr,</if>
            <if test="zzcms != null">zzcms,</if>
            <if test="bklas != null">bklas,</if>
            <if test="racct != null">racct,</if>
            <if test="vtweg != null">vtweg,</if>
            <if test="mtartPa != null">mtart_pa,</if>
            <if test="matklMm != null">matkl_mm,</if>
            <if test="xpaobjnrCoRel != null">xpaobjnr_co_rel,</if>
            <if test="auartPa != null">auart_pa,</if>
            <if test="vkburPa != null">vkbur_pa,</if>
            <if test="spart != null">spart,</if>
            <if test="waerk != null">waerk,</if>
            <if test="netpr != null">netpr,</if>
            <if test="kursk != null">kursk,</if>
            <if test="znetpr != null">znetpr,</if>
            <if test="zhslKursk != null">zhsl_kursk,</if>
            <if test="zgtpr != null">zgtpr,</if>
            <if test="hsl != null">hsl,</if>
            <if test="kzwi1 != null">kzwi1,</if>
            <if test="vv003 != null">vv003,</if>
            <if test="sj010 != null">sj010,</if>
            <if test="sj020 != null">sj020,</if>
            <if test="sj030 != null">sj030,</if>
            <if test="sj040 != null">sj040,</if>
            <if test="sj050 != null">sj050,</if>
            <if test="sj060 != null">sj060,</if>
            <if test="sj070 != null">sj070,</if>
            <if test="zsjml != null">zsjml,</if>
            <if test="vv002 != null">vv002,</if>
            <if test="bz010 != null">bz010,</if>
            <if test="bz020 != null">bz020,</if>
            <if test="bz030 != null">bz030,</if>
            <if test="bz040 != null">bz040,</if>
            <if test="bz050 != null">bz050,</if>
            <if test="bz060 != null">bz060,</if>
            <if test="bz070 != null">bz070,</if>
            <if test="zbzml != null">zbzml,</if>
            <if test="zbzmll != null">zbzmll,</if>
            <if test="zsjmll != null">zsjmll,</if>
            <if test="kdgrpAna != null">kdgrp_ana,</if>
            <if test="perveAna != null">perve_ana,</if>
            <if test="bzirkAna != null">bzirk_ana,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="rbukrs != null">#{rbukrs},</if>
            <if test="gjahr != null">#{gjahr},</if>
            <if test="fiscyearper != null">#{fiscyearper},</if>
            <if test="blart != null">#{blart},</if>
            <if test="kdauf != null">#{kdauf},</if>
            <if test="kdpos != null">#{kdpos},</if>
            <if test="ebeln != null">#{ebeln},</if>
            <if test="ebelp != null">#{ebelp},</if>
            <if test="msl != null">#{msl},</if>
            <if test="runit != null">#{runit},</if>
            <if test="kunnr != null">#{kunnr},</if>
            <if test="matnr != null">#{matnr},</if>
            <if test="zzcms != null">#{zzcms},</if>
            <if test="bklas != null">#{bklas},</if>
            <if test="racct != null">#{racct},</if>
            <if test="vtweg != null">#{vtweg},</if>
            <if test="mtartPa != null">#{mtartPa},</if>
            <if test="matklMm != null">#{matklMm},</if>
            <if test="xpaobjnrCoRel != null">#{xpaobjnrCoRel},</if>
            <if test="auartPa != null">#{auartPa},</if>
            <if test="vkburPa != null">#{vkburPa},</if>
            <if test="spart != null">#{spart},</if>
            <if test="waerk != null">#{waerk},</if>
            <if test="netpr != null">#{netpr},</if>
            <if test="kursk != null">#{kursk},</if>
            <if test="znetpr != null">#{znetpr},</if>
            <if test="zhslKursk != null">#{zhslKursk},</if>
            <if test="zgtpr != null">#{zgtpr},</if>
            <if test="hsl != null">#{hsl},</if>
            <if test="kzwi1 != null">#{kzwi1},</if>
            <if test="vv003 != null">#{vv003},</if>
            <if test="sj010 != null">#{sj010},</if>
            <if test="sj020 != null">#{sj020},</if>
            <if test="sj030 != null">#{sj030},</if>
            <if test="sj040 != null">#{sj040},</if>
            <if test="sj050 != null">#{sj050},</if>
            <if test="sj060 != null">#{sj060},</if>
            <if test="sj070 != null">#{sj070},</if>
            <if test="zsjml != null">#{zsjml},</if>
            <if test="vv002 != null">#{vv002},</if>
            <if test="bz010 != null">#{bz010},</if>
            <if test="bz020 != null">#{bz020},</if>
            <if test="bz030 != null">#{bz030},</if>
            <if test="bz040 != null">#{bz040},</if>
            <if test="bz050 != null">#{bz050},</if>
            <if test="bz060 != null">#{bz060},</if>
            <if test="bz070 != null">#{bz070},</if>
            <if test="zbzml != null">#{zbzml},</if>
            <if test="zbzmll != null">#{zbzmll},</if>
            <if test="zsjmll != null">#{zsjmll},</if>
            <if test="kdgrpAna != null">#{kdgrpAna},</if>
            <if test="perveAna != null">#{perveAna},</if>
            <if test="bzirkAna != null">#{bzirkAna},</if>
         </trim>
    </insert>

    <update id="updateZrpFi002" parameterType="ZrpFi002">
        update ZrpFi002
        <trim prefix="SET" suffixOverrides=",">
            <if test="rbukrs != null">rbukrs = #{rbukrs},</if>
            <if test="gjahr != null">gjahr = #{gjahr},</if>
            <if test="fiscyearper != null">fiscyearper = #{fiscyearper},</if>
            <if test="blart != null">blart = #{blart},</if>
            <if test="kdauf != null">kdauf = #{kdauf},</if>
            <if test="kdpos != null">kdpos = #{kdpos},</if>
            <if test="ebeln != null">ebeln = #{ebeln},</if>
            <if test="ebelp != null">ebelp = #{ebelp},</if>
            <if test="msl != null">msl = #{msl},</if>
            <if test="runit != null">runit = #{runit},</if>
            <if test="kunnr != null">kunnr = #{kunnr},</if>
            <if test="matnr != null">matnr = #{matnr},</if>
            <if test="zzcms != null">zzcms = #{zzcms},</if>
            <if test="bklas != null">bklas = #{bklas},</if>
            <if test="racct != null">racct = #{racct},</if>
            <if test="vtweg != null">vtweg = #{vtweg},</if>
            <if test="mtartPa != null">mtart_pa = #{mtartPa},</if>
            <if test="matklMm != null">matkl_mm = #{matklMm},</if>
            <if test="xpaobjnrCoRel != null">xpaobjnr_co_rel = #{xpaobjnrCoRel},</if>
            <if test="auartPa != null">auart_pa = #{auartPa},</if>
            <if test="vkburPa != null">vkbur_pa = #{vkburPa},</if>
            <if test="spart != null">spart = #{spart},</if>
            <if test="waerk != null">waerk = #{waerk},</if>
            <if test="netpr != null">netpr = #{netpr},</if>
            <if test="kursk != null">kursk = #{kursk},</if>
            <if test="znetpr != null">znetpr = #{znetpr},</if>
            <if test="zhslKursk != null">zhsl_kursk = #{zhslKursk},</if>
            <if test="zgtpr != null">zgtpr = #{zgtpr},</if>
            <if test="hsl != null">hsl = #{hsl},</if>
            <if test="kzwi1 != null">kzwi1 = #{kzwi1},</if>
            <if test="vv003 != null">vv003 = #{vv003},</if>
            <if test="sj010 != null">sj010 = #{sj010},</if>
            <if test="sj020 != null">sj020 = #{sj020},</if>
            <if test="sj030 != null">sj030 = #{sj030},</if>
            <if test="sj040 != null">sj040 = #{sj040},</if>
            <if test="sj050 != null">sj050 = #{sj050},</if>
            <if test="sj060 != null">sj060 = #{sj060},</if>
            <if test="sj070 != null">sj070 = #{sj070},</if>
            <if test="zsjml != null">zsjml = #{zsjml},</if>
            <if test="vv002 != null">vv002 = #{vv002},</if>
            <if test="bz010 != null">bz010 = #{bz010},</if>
            <if test="bz020 != null">bz020 = #{bz020},</if>
            <if test="bz030 != null">bz030 = #{bz030},</if>
            <if test="bz040 != null">bz040 = #{bz040},</if>
            <if test="bz050 != null">bz050 = #{bz050},</if>
            <if test="bz060 != null">bz060 = #{bz060},</if>
            <if test="bz070 != null">bz070 = #{bz070},</if>
            <if test="zbzml != null">zbzml = #{zbzml},</if>
            <if test="zbzmll != null">zbzmll = #{zbzmll},</if>
            <if test="zsjmll != null">zsjmll = #{zsjmll},</if>
            <if test="kdgrpAna != null">kdgrp_ana = #{kdgrpAna},</if>
            <if test="perveAna != null">perve_ana = #{perveAna},</if>
            <if test="bzirkAna != null">bzirk_ana = #{bzirkAna},</if>
        </trim>
        where sid = #{sid}
    </update>

    <delete id="deleteZrpFi002BySid" parameterType="Long">
        delete from ZrpFi002 where sid = #{sid}
    </delete>

    <delete id="deleteZrpFi002BySids" parameterType="String">
        delete from ZrpFi002 where sid in 
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>