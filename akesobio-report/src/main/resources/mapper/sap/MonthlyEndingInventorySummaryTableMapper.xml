<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.MonthlyEndingInventorySummaryTableMapper">

    <resultMap type="MonthlyEndingInventorySummaryTable" id="MonthlyEndingInventorySummaryTableResult">
        <result property="id"    column="id"    />
        <result property="ryear"    column="ryear"    />
        <result property="racct"    column="racct"    />
        <result property="txt20"    column="txt20"    />
        <result property="rbukrs"    column="rbukrs"    />
        <result property="butxt"    column="butxt"    />
        <result property="hsl01"    column="hsl01"    />
        <result property="hsl02"    column="hsl02"    />
        <result property="hsl03"    column="hsl03"    />
        <result property="hsl04"    column="hsl04"    />
        <result property="hsl05"    column="hsl05"    />
        <result property="hsl06"    column="hsl06"    />
        <result property="hsl07"    column="hsl07"    />
        <result property="hsl08"    column="hsl08"    />
        <result property="hsl09"    column="hsl09"    />
        <result property="hsl10"    column="hsl10"    />
        <result property="hsl11"    column="hsl11"    />
        <result property="hsl12"    column="hsl12"    />
    </resultMap>

    <sql id="selectMonthlyEndingInventorySummaryTableVo">
        select id, ryear, racct, txt20, rbukrs, butxt, hsl01, hsl02, hsl03, hsl04, hsl05, hsl06, hsl07, hsl08, hsl09, hsl10, hsl11, hsl12 from monthly_ending_inventory_summary_table
    </sql>

    <select id="selectMonthlyEndingInventorySummaryTableList" parameterType="MonthlyEndingInventorySummaryTable" resultMap="MonthlyEndingInventorySummaryTableResult">
        <include refid="selectMonthlyEndingInventorySummaryTableVo"/>
        <where>
            <if test="ryear != null  and ryear != ''"> and ryear = #{ryear}</if>
            <if test="racct != null  and racct != ''"> and racct = #{racct}</if>
            <if test="rbukrs != null  and rbukrs != ''"> and rbukrs = #{rbukrs}</if>
        </where>
    </select>

    <select id="selectMonthlyEndingInventorySummaryTableById" parameterType="Long" resultMap="MonthlyEndingInventorySummaryTableResult">
        <include refid="selectMonthlyEndingInventorySummaryTableVo"/>
        where id = #{id}
    </select>

    <insert id="insertMonthlyEndingInventorySummaryTable" parameterType="MonthlyEndingInventorySummaryTable">
        insert into monthly_ending_inventory_summary_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ryear != null">ryear,</if>
            <if test="racct != null">racct,</if>
            <if test="txt20 != null">txt20,</if>
            <if test="rbukrs != null">rbukrs,</if>
            <if test="butxt != null">butxt,</if>
            <if test="hsl01 != null">hsl01,</if>
            <if test="hsl02 != null">hsl02,</if>
            <if test="hsl03 != null">hsl03,</if>
            <if test="hsl04 != null">hsl04,</if>
            <if test="hsl05 != null">hsl05,</if>
            <if test="hsl06 != null">hsl06,</if>
            <if test="hsl07 != null">hsl07,</if>
            <if test="hsl08 != null">hsl08,</if>
            <if test="hsl09 != null">hsl09,</if>
            <if test="hsl10 != null">hsl10,</if>
            <if test="hsl11 != null">hsl11,</if>
            <if test="hsl12 != null">hsl12,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ryear != null">#{ryear},</if>
            <if test="racct != null">#{racct},</if>
            <if test="txt20 != null">#{txt20},</if>
            <if test="rbukrs != null">#{rbukrs},</if>
            <if test="butxt != null">#{butxt},</if>
            <if test="hsl01 != null">#{hsl01},</if>
            <if test="hsl02 != null">#{hsl02},</if>
            <if test="hsl03 != null">#{hsl03},</if>
            <if test="hsl04 != null">#{hsl04},</if>
            <if test="hsl05 != null">#{hsl05},</if>
            <if test="hsl06 != null">#{hsl06},</if>
            <if test="hsl07 != null">#{hsl07},</if>
            <if test="hsl08 != null">#{hsl08},</if>
            <if test="hsl09 != null">#{hsl09},</if>
            <if test="hsl10 != null">#{hsl10},</if>
            <if test="hsl11 != null">#{hsl11},</if>
            <if test="hsl12 != null">#{hsl12},</if>
        </trim>
    </insert>

    <update id="updateMonthlyEndingInventorySummaryTable" parameterType="MonthlyEndingInventorySummaryTable">
        update monthly_ending_inventory_summary_table
        <trim prefix="SET" suffixOverrides=",">
            <if test="ryear != null">ryear = #{ryear},</if>
            <if test="racct != null">racct = #{racct},</if>
            <if test="txt20 != null">txt20 = #{txt20},</if>
            <if test="rbukrs != null">rbukrs = #{rbukrs},</if>
            <if test="butxt != null">butxt = #{butxt},</if>
            <if test="hsl01 != null">hsl01 = #{hsl01},</if>
            <if test="hsl02 != null">hsl02 = #{hsl02},</if>
            <if test="hsl03 != null">hsl03 = #{hsl03},</if>
            <if test="hsl04 != null">hsl04 = #{hsl04},</if>
            <if test="hsl05 != null">hsl05 = #{hsl05},</if>
            <if test="hsl06 != null">hsl06 = #{hsl06},</if>
            <if test="hsl07 != null">hsl07 = #{hsl07},</if>
            <if test="hsl08 != null">hsl08 = #{hsl08},</if>
            <if test="hsl09 != null">hsl09 = #{hsl09},</if>
            <if test="hsl10 != null">hsl10 = #{hsl10},</if>
            <if test="hsl11 != null">hsl11 = #{hsl11},</if>
            <if test="hsl12 != null">hsl12 = #{hsl12},</if>
        </trim>
        where ryear = #{ryear} and racct = #{racct} and rbukrs = #{rbukrs}
    </update>

    <delete id="deleteMonthlyEndingInventorySummaryTableById" parameterType="Long">
        delete from monthly_ending_inventory_summary_table where id = #{id}
    </delete>

    <delete id="deleteMonthlyEndingInventorySummaryTableByIds" parameterType="String">
        delete from monthly_ending_inventory_summary_table where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>