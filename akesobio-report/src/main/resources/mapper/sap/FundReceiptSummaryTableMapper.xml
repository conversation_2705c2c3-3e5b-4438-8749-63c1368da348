<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.FundReceiptSummaryTableMapper">

    <resultMap type="FundReceiptSummaryTable" id="FundReceiptSummaryTableResult">
        <result property="id"    column="id"    />
        <result property="year"    column="year"    />
        <result property="month"    column="month"    />
        <result property="bankDate"    column="bank_date"    />
        <result property="companyCode"    column="company_code"    />
        <result property="companyName"    column="company_name"    />
        <result property="currencyCode"    column="currency_code"    />
        <result property="salePaymentCollection"    column="sale_payment_collection"    />
        <result property="elseSalePaymentCollection"    column="else_sale_payment_collection"    />
        <result property="groupElsePaymentCollection"    column="group_else_payment_collection"    />
        <result property="groupSalePaymentCollection"    column="group_sale_payment_collection"    />
        <result property="financingIncome"    column="financing_income"    />
        <result property="supplierElsePaymentCollection"    column="supplier_else_payment_collection"    />
        <result property="clinicalRefund"    column="clinical_refund"    />
        <result property="other"    column="other"    />
        <result property="financialSpecialProject"    column="financial_special_project"    />
        <result property="amountSummary"    column="amount_summary"    />
    </resultMap>

    <sql id="selectFundReceiptSummaryTableVo">
        select id, year, month, bank_date, company_code, company_name, currency_code, sale_payment_collection, else_sale_payment_collection, group_else_payment_collection, group_sale_payment_collection, financing_income, supplier_else_payment_collection, clinical_refund, other, financial_special_project, amount_summary from fund_receipt_summary_table
    </sql>

    <select id="selectList" parameterType="java.util.List" resultMap="FundReceiptSummaryTableResult">
        <include refid="selectFundReceiptSummaryTableVo"/>
        <where>
            1=1 and
            <if test="list != null and list.size() > 0">
                CONCAT(year, month, bank_date, company_code) in
                <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                    #{item.year} + #{item.month} + #{item.bankDate} + #{item.companyCode}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insertList" parameterType="FundReceiptSummaryTable">
        insert into fund_receipt_summary_table (year, month, bank_date, company_code, company_name, currency_code, sale_payment_collection,
                                          else_sale_payment_collection, group_else_payment_collection, group_sale_payment_collection,
                                          financing_income, supplier_else_payment_collection, clinical_refund, other, financial_special_project, amount_summary)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.year}, #{item.month}, #{item.bankDate}, #{item.companyCode}, #{item.companyName}, #{item.currencyCode}, #{item.salePaymentCollection},
             #{item.elseSalePaymentCollection}, #{item.groupElsePaymentCollection}, #{item.groupSalePaymentCollection}, #{item.financingIncome},
             #{item.supplierElsePaymentCollection}, #{item.clinicalRefund}, #{item.other}, #{item.financialSpecialProject}, #{item.amountSummary})
        </foreach>
    </insert>

    <select id="selectFundReceiptSummaryTableList" parameterType="FundReceiptSummaryTable" resultMap="FundReceiptSummaryTableResult">
        <include refid="selectFundReceiptSummaryTableVo"/>
        <where>
            <if test="year != null  and year != ''"> and year = #{year}</if>
            <if test="month != null  and month != ''"> and month = #{month}</if>
            <if test="bankDate != null  and bankDate != ''"> and bank_date = #{bankDate}</if>
            <if test="companyCode != null  and companyCode != ''"> and company_code = #{companyCode}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="currencyCode != null  and currencyCode != ''"> and currency_code = #{currencyCode}</if>
            <if test="salePaymentCollection != null  and salePaymentCollection != ''"> and sale_payment_collection = #{salePaymentCollection}</if>
            <if test="elseSalePaymentCollection != null  and elseSalePaymentCollection != ''"> and else_sale_payment_collection = #{elseSalePaymentCollection}</if>
            <if test="groupElsePaymentCollection != null  and groupElsePaymentCollection != ''"> and group_else_payment_collection = #{groupElsePaymentCollection}</if>
            <if test="groupSalePaymentCollection != null  and groupSalePaymentCollection != ''"> and group_sale_payment_collection = #{groupSalePaymentCollection}</if>
            <if test="financingIncome != null  and financingIncome != ''"> and financing_income = #{financingIncome}</if>
            <if test="supplierElsePaymentCollection != null  and supplierElsePaymentCollection != ''"> and supplier_else_payment_collection = #{supplierElsePaymentCollection}</if>
            <if test="clinicalRefund != null  and clinicalRefund != ''"> and clinical_refund = #{clinicalRefund}</if>
            <if test="other != null  and other != ''"> and other = #{other}</if>
            <if test="financialSpecialProject != null  and financialSpecialProject != ''"> and financial_special_project = #{financialSpecialProject}</if>
            <if test="amountSummary != null  and amountSummary != ''"> and amount_summary = #{amountSummary}</if>
        </where>
    </select>

    <select id="selectFundReceiptSummaryTableById" parameterType="Long" resultMap="FundReceiptSummaryTableResult">
        <include refid="selectFundReceiptSummaryTableVo"/>
        where id = #{id}
    </select>

    <insert id="insertFundReceiptSummaryTable" parameterType="FundReceiptSummaryTable">
        insert into fund_receipt_summary_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="year != null">year,</if>
            <if test="month != null">month,</if>
            <if test="bankDate != null">bank_date,</if>
            <if test="companyCode != null">company_code,</if>
            <if test="companyName != null">company_name,</if>
            <if test="currencyCode != null">currency_code,</if>
            <if test="salePaymentCollection != null">sale_payment_collection,</if>
            <if test="elseSalePaymentCollection != null">else_sale_payment_collection,</if>
            <if test="groupElsePaymentCollection != null">group_else_payment_collection,</if>
            <if test="groupSalePaymentCollection != null">group_sale_payment_collection,</if>
            <if test="financingIncome != null">financing_income,</if>
            <if test="supplierElsePaymentCollection != null">supplier_else_payment_collection,</if>
            <if test="clinicalRefund != null">clinical_refund,</if>
            <if test="other != null">other,</if>
            <if test="financialSpecialProject != null">financial_special_project,</if>
            <if test="amountSummary != null">amount_summary,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="year != null">#{year},</if>
            <if test="month != null">#{month},</if>
            <if test="bankDate != null">#{bankDate},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="currencyCode != null">#{currencyCode},</if>
            <if test="salePaymentCollection != null">#{salePaymentCollection},</if>
            <if test="elseSalePaymentCollection != null">#{elseSalePaymentCollection},</if>
            <if test="groupElsePaymentCollection != null">#{groupElsePaymentCollection},</if>
            <if test="groupSalePaymentCollection != null">#{groupSalePaymentCollection},</if>
            <if test="financingIncome != null">#{financingIncome},</if>
            <if test="supplierElsePaymentCollection != null">#{supplierElsePaymentCollection},</if>
            <if test="clinicalRefund != null">#{clinicalRefund},</if>
            <if test="other != null">#{other},</if>
            <if test="financialSpecialProject != null">#{financialSpecialProject},</if>
            <if test="amountSummary != null">#{amountSummary},</if>
        </trim>
    </insert>

    <update id="updateFundReceiptSummaryTable" parameterType="FundReceiptSummaryTable">
        update fund_receipt_summary_table
        <trim prefix="SET" suffixOverrides=",">
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="bankDate != null">bank_date = #{bankDate},</if>
            <if test="companyCode != null">company_code = #{companyCode},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="currencyCode != null">currency_code = #{currencyCode},</if>
            <if test="salePaymentCollection != null">sale_payment_collection = #{salePaymentCollection},</if>
            <if test="elseSalePaymentCollection != null">else_sale_payment_collection = #{elseSalePaymentCollection},</if>
            <if test="groupElsePaymentCollection != null">group_else_payment_collection = #{groupElsePaymentCollection},</if>
            <if test="groupSalePaymentCollection != null">group_sale_payment_collection = #{groupSalePaymentCollection},</if>
            <if test="financingIncome != null">financing_income = #{financingIncome},</if>
            <if test="supplierElsePaymentCollection != null">supplier_else_payment_collection = #{supplierElsePaymentCollection},</if>
            <if test="clinicalRefund != null">clinical_refund = #{clinicalRefund},</if>
            <if test="other != null">other = #{other},</if>
            <if test="financialSpecialProject != null">financial_special_project = #{financialSpecialProject},</if>
            <if test="amountSummary != null">amount_summary = #{amountSummary},</if>
        </trim>
        where year = #{year} and month = #{month} and bank_date = #{bankDate} and company_code = #{companyCode}
    </update>

</mapper>