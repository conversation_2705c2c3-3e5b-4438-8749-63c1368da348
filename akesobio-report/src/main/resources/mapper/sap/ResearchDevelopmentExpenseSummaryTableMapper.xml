<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.ResearchDevelopmentExpenseSummaryTableMapper">

    <resultMap type="ResearchDevelopmentExpenseSummaryTable" id="ResearchDevelopmentExpenseSummaryTableResult">
        <result property="id"    column="id"    />
        <result property="butxt"    column="butxt"    />
        <result property="rbukrs"    column="rbukrs"    />
        <result property="gjahr"    column="gjahr"    />
        <result property="poper"    column="poper"    />
        <result property="aufnr"    column="aufnr"    />
        <result property="ktext"    column="ktext"    />
        <result property="racct"    column="racct"    />
        <result property="zzmkm"    column="zzmkm"    />
        <result property="zhzkm"    column="zhzkm"    />
        <result property="zplkm"    column="zplkm"    />
        <result property="rhcur"    column="rhcur"    />
        <result property="hsl"    column="hsl"    />
        <result property="rkcur"    column="rkcur"    />
        <result property="ksl"    column="ksl"    />
        <result property="scntr"    column="scntr"    />
        <result property="rcntr"    column="rcntr"    />
        <result property="zbmmc"    column="zbmmc"    />
        <result property="rfarea"    column="rfarea"    />
        <result property="zcbfl"    column="zcbfl"    />
        <result property="zsjlx"    column="zsjlx"    />
        <result property="zgxfl"    column="zgxfl"    />
        <result property="zypgx"    column="zypgx"    />
        <result property="zsyzmc"    column="zsyzmc"    />
        <result property="monat"    column="monat"    />
    </resultMap>

    <sql id="selectResearchDevelopmentExpenseSummaryTableVo">
        select id, butxt, rbukrs, gjahr, poper, aufnr, ktext, racct, zzmkm, zhzkm, zplkm, rhcur, hsl, rkcur, ksl, scntr, rcntr, zbmmc, rfarea, zcbfl, zsjlx, zgxfl, zypgx, zsyzmc, monat from research_development_expense_summary_table
    </sql>

    <select id="selectResearchDevelopmentExpenseSummaryTableList" parameterType="ResearchDevelopmentExpenseSummaryTable" resultMap="ResearchDevelopmentExpenseSummaryTableResult">
        <include refid="selectResearchDevelopmentExpenseSummaryTableVo"/>
        <where>
            <if test="butxt != null  and butxt != ''"> and butxt = #{butxt}</if>
            <if test="rbukrs != null  and rbukrs != ''"> and rbukrs = #{rbukrs}</if>
            <if test="gjahr != null  and gjahr != ''"> and gjahr = #{gjahr}</if>
            <if test="poper != null  and poper != ''"> and poper = #{poper}</if>
            <if test="aufnr != null  and aufnr != ''"> and aufnr = #{aufnr}</if>
            <if test="ktext != null  and ktext != ''"> and ktext = #{ktext}</if>
            <if test="racct != null  and racct != ''"> and racct = #{racct}</if>
            <if test="zzmkm != null  and zzmkm != ''"> and zzmkm = #{zzmkm}</if>
            <if test="zhzkm != null  and zhzkm != ''"> and zhzkm = #{zhzkm}</if>
            <if test="zplkm != null  and zplkm != ''"> and zplkm = #{zplkm}</if>
            <if test="rhcur != null  and rhcur != ''"> and rhcur = #{rhcur}</if>
            <if test="hsl != null  and hsl != ''"> and hsl = #{hsl}</if>
            <if test="rkcur != null  and rkcur != ''"> and rkcur = #{rkcur}</if>
            <if test="ksl != null  and ksl != ''"> and ksl = #{ksl}</if>
            <if test="scntr != null  and scntr != ''"> and scntr = #{scntr}</if>
            <if test="rcntr != null  and rcntr != ''"> and rcntr = #{rcntr}</if>
            <if test="zbmmc != null  and zbmmc != ''"> and zbmmc = #{zbmmc}</if>
            <if test="rfarea != null  and rfarea != ''"> and rfarea = #{rfarea}</if>
            <if test="zcbfl != null  and zcbfl != ''"> and zcbfl = #{zcbfl}</if>
            <if test="zsjlx != null  and zsjlx != ''"> and zsjlx = #{zsjlx}</if>
            <if test="zgxfl != null  and zgxfl != ''"> and zgxfl = #{zgxfl}</if>
            <if test="zypgx != null  and zypgx != ''"> and zypgx = #{zypgx}</if>
            <if test="zsyzmc != null  and zsyzmc != ''"> and zsyzmc = #{zsyzmc}</if>
            <if test="monat != null  and monat != ''"> and monat = #{monat}</if>
        </where>
    </select>

    <select id="selectResearchDevelopmentExpenseSummaryTableById" parameterType="Long" resultMap="ResearchDevelopmentExpenseSummaryTableResult">
        <include refid="selectResearchDevelopmentExpenseSummaryTableVo"/>
        where id = #{id}
    </select>

    <insert id="insertResearchDevelopmentExpenseSummaryTable" parameterType="ResearchDevelopmentExpenseSummaryTable">
        insert into research_development_expense_summary_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="butxt != null">butxt,</if>
            <if test="rbukrs != null">rbukrs,</if>
            <if test="gjahr != null">gjahr,</if>
            <if test="poper != null">poper,</if>
            <if test="aufnr != null">aufnr,</if>
            <if test="ktext != null">ktext,</if>
            <if test="racct != null">racct,</if>
            <if test="zzmkm != null">zzmkm,</if>
            <if test="zhzkm != null">zhzkm,</if>
            <if test="zplkm != null">zplkm,</if>
            <if test="rhcur != null">rhcur,</if>
            <if test="hsl != null">hsl,</if>
            <if test="rkcur != null">rkcur,</if>
            <if test="ksl != null">ksl,</if>
            <if test="scntr != null">scntr,</if>
            <if test="rcntr != null">rcntr,</if>
            <if test="zbmmc != null">zbmmc,</if>
            <if test="rfarea != null">rfarea,</if>
            <if test="zcbfl != null">zcbfl,</if>
            <if test="zsjlx != null">zsjlx,</if>
            <if test="zgxfl != null">zgxfl,</if>
            <if test="zypgx != null">zypgx,</if>
            <if test="zsyzmc != null">zsyzmc,</if>
            <if test="monat != null">monat,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="butxt != null">#{butxt},</if>
            <if test="rbukrs != null">#{rbukrs},</if>
            <if test="gjahr != null">#{gjahr},</if>
            <if test="poper != null">#{poper},</if>
            <if test="aufnr != null">#{aufnr},</if>
            <if test="ktext != null">#{ktext},</if>
            <if test="racct != null">#{racct},</if>
            <if test="zzmkm != null">#{zzmkm},</if>
            <if test="zhzkm != null">#{zhzkm},</if>
            <if test="zplkm != null">#{zplkm},</if>
            <if test="rhcur != null">#{rhcur},</if>
            <if test="hsl != null">#{hsl},</if>
            <if test="rkcur != null">#{rkcur},</if>
            <if test="ksl != null">#{ksl},</if>
            <if test="scntr != null">#{scntr},</if>
            <if test="rcntr != null">#{rcntr},</if>
            <if test="zbmmc != null">#{zbmmc},</if>
            <if test="rfarea != null">#{rfarea},</if>
            <if test="zcbfl != null">#{zcbfl},</if>
            <if test="zsjlx != null">#{zsjlx},</if>
            <if test="zgxfl != null">#{zgxfl},</if>
            <if test="zypgx != null">#{zypgx},</if>
            <if test="zsyzmc != null">#{zsyzmc},</if>
            <if test="monat != null">#{monat},</if>
        </trim>
    </insert>

    <update id="updateResearchDevelopmentExpenseSummaryTable" parameterType="ResearchDevelopmentExpenseSummaryTable">
        update research_development_expense_summary_table
        <trim prefix="SET" suffixOverrides=",">
            <if test="butxt != null">butxt = #{butxt},</if>
            <if test="rbukrs != null">rbukrs = #{rbukrs},</if>
            <if test="gjahr != null">gjahr = #{gjahr},</if>
            <if test="poper != null">poper = #{poper},</if>
            <if test="aufnr != null">aufnr = #{aufnr},</if>
            <if test="ktext != null">ktext = #{ktext},</if>
            <if test="racct != null">racct = #{racct},</if>
            <if test="zzmkm != null">zzmkm = #{zzmkm},</if>
            <if test="zhzkm != null">zhzkm = #{zhzkm},</if>
            <if test="zplkm != null">zplkm = #{zplkm},</if>
            <if test="rhcur != null">rhcur = #{rhcur},</if>
            <if test="hsl != null">hsl = #{hsl},</if>
            <if test="rkcur != null">rkcur = #{rkcur},</if>
            <if test="ksl != null">ksl = #{ksl},</if>
            <if test="scntr != null">scntr = #{scntr},</if>
            <if test="rcntr != null">rcntr = #{rcntr},</if>
            <if test="zbmmc != null">zbmmc = #{zbmmc},</if>
            <if test="rfarea != null">rfarea = #{rfarea},</if>
            <if test="zcbfl != null">zcbfl = #{zcbfl},</if>
            <if test="zsjlx != null">zsjlx = #{zsjlx},</if>
            <if test="zgxfl != null">zgxfl = #{zgxfl},</if>
            <if test="zypgx != null">zypgx = #{zypgx},</if>
            <if test="zsyzmc != null">zsyzmc = #{zsyzmc},</if>
            <if test="monat != null">monat = #{monat},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResearchDevelopmentExpenseSummaryTableById" parameterType="Long">
        delete from research_development_expense_summary_table where id = #{id}
    </delete>

    <delete id="deleteResearchDevelopmentExpenseSummaryTableByIds" parameterType="String">
        delete from research_development_expense_summary_table where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByYearAndMonth" parameterType="map">
        delete from research_development_expense_summary_table where gjahr = #{year} and monat = #{month}
    </delete>

    <insert id="insertList" parameterType="ResearchDevelopmentExpenseSummaryTable">
        insert into research_development_expense_summary_table (butxt, rbukrs, gjahr, poper, aufnr, ktext, racct, zzmkm, zhzkm, zplkm,
        rhcur, hsl, rkcur, ksl, scntr, rcntr, zbmmc, rfarea, zcbfl, zsjlx, zgxfl, zypgx, zsyzmc, monat)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.butxt}, #{item.rbukrs}, #{item.gjahr}, #{item.poper}, #{item.aufnr}, #{item.ktext}, #{item.racct},
            #{item.zzmkm}, #{item.zhzkm}, #{item.zplkm},#{item.rhcur},#{item.hsl}, #{item.rkcur}, #{item.ksl},
            #{item.scntr}, #{item.rcntr}, #{item.zbmmc}, #{item.rfarea}, #{item.zcbfl}, #{item.zsjlx}, #{item.zgxfl},
            #{item.zypgx}, #{item.zsyzmc}, #{item.monat})
        </foreach>
    </insert>
</mapper>