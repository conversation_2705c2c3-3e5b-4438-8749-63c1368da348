<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sap.mapper.SapSubjectDataMapper">

    <resultMap type="SapSubjectData" id="SapSubjectDataResult">
        <result property="id"    column="id"    />
        <result property="subjectTable"    column="subject_table"    />
        <result property="subject"    column="subject"    />
        <result property="subjectName"    column="subject_name"    />
    </resultMap>

    <sql id="selectSapSubjectDataVo">
        select id, subject_table, subject, subject_name from sap_subject_data
    </sql>

    <select id="selectSapSubjectDataList" parameterType="SapSubjectData" resultMap="SapSubjectDataResult">
        <include refid="selectSapSubjectDataVo"/>
        <where>
            <if test="subjectTable != null  and subjectTable != ''"> and subject_table = #{subjectTable}</if>
            <if test="subject != null  and subject != ''"> and subject = #{subject}</if>
            <if test="subjectName != null  and subjectName != ''"> and subject_name like concat('%', #{subjectName}, '%')</if>
        </where>
    </select>

    <select id="selectSapSubjectDataById" parameterType="Long" resultMap="SapSubjectDataResult">
        <include refid="selectSapSubjectDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertSapSubjectData" parameterType="SapSubjectData">
        insert into sap_subject_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="subjectTable != null">subject_table,</if>
            <if test="subject != null">subject,</if>
            <if test="subjectName != null">subject_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="subjectTable != null">#{subjectTable},</if>
            <if test="subject != null">#{subject},</if>
            <if test="subjectName != null">#{subjectName},</if>
        </trim>
    </insert>

    <update id="updateSapSubjectData" parameterType="SapSubjectData">
        update sap_subject_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="subjectTable != null">subject_table = #{subjectTable},</if>
            <if test="subject != null">subject = #{subject},</if>
            <if test="subjectName != null">subject_name = #{subjectName},</if>
        </trim>
        where subject_table = #{subjectTable} and subject = #{subject} and subject_name = #{subjectName}
    </update>

    <delete id="deleteSapSubjectDataById" parameterType="Long">
        delete from sap_subject_data where id = #{id}
    </delete>

    <delete id="deleteSapSubjectDataByIds" parameterType="String">
        delete from sap_subject_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>