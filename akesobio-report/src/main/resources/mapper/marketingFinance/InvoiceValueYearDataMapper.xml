<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.InvoiceValueYearDataMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.InvoiceValueYearData" id="InvoiceValueYearDataResult">
        <result property="year"    column="year" />
        <result property="month"   column="month"  />
        <result property="total"   column="total"  />
    </resultMap>

    <select id="selectInvoiceValueYearData"  resultMap="InvoiceValueYearDataResult">
        select Year(invoicingDate) year,Month(invoicingDate) month,SUM(CONVERT(DECIMAL(13,2),ISNULL(amount, 0))) total
        FROM invoice_value
        <where>
            <if test="year !=null and year != ''">and Year(invoicingDate) = #{year}</if>
            <if test="area !=null and area !=''">and area = #{area}</if>
        </where>
        Group by Year(invoicingDate),Month(invoicingDate)
        order by month ASC
    </select>

</mapper>
