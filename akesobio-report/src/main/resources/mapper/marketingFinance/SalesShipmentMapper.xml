<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.SalesShipmentMapper">

    <resultMap type="SalesShipment" id="SalesShipmentResult">
        <result property="applicationDate" column="applicationDate"/>
        <result property="singleNumber" column="singleNumber"/>
        <result property="businessPartnerCode" column="businessPartnerCode"/>
        <result property="businessPartnerName" column="businessPartnerName"/>
        <result property="businessPartnerLevel" column="businessPartnerLevel"/>
        <result property="salesGroup" column="salesGroup"/>
        <result property="salesRegion" column="salesRegion"/>
        <result property="businessManager" column="businessManager"/>
        <result property="productCode" column="productCode"/>
        <result property="productName" column="productName"/>
        <result property="orderQuantity" column="orderQuantity"/>
        <result property="salesUnitPrice" column="salesUnitPrice"/>
        <result property="totalPrice" column="totalPrice"/>
        <result property="contractAllowance" column="contractAllowance"/>
        <result property="balancePayment" column="balancePayment"/>
        <result property="receivables" column="receivables"/>
        <result property="batch" column="batch"/>
        <result property="sapSystemInvoiceNo" column="sapSystemInvoiceNo"/>
        <result property="invoiceNo" column="invoiceNo"/>
        <result property="invoicingTime" column="invoicingTime"/>
        <result property="number" column="number"/>
        <result property="dueReceivables" column="dueReceivables"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="salesContractCurrentSession" column="salesContractCurrentSession"/>
    </resultMap>

    <sql id="selectSalesShipmentVo">
        select applicationDate,
               singleNumber,
               businessPartnerCode,
               businessPartnerName,
               businessPartnerLevel,
               salesGroup,
               salesRegion,
               businessManager,
               productCode,
               productName,
               orderQuantity,
               salesUnitPrice,
               totalPrice,
               contractAllowance,
               balancePayment,
               receivables,
               batch,
               sapSystemInvoiceNo,
               invoiceNo,
               invoicingTime,
               number,
               dueReceivables,
               documentStatus,
               currentSession,
               salesContractCurrentSession
        from sales_shipment
    </sql>

    <select id="selectSalesShipmentList" parameterType="SalesShipment" resultMap="SalesShipmentResult">
        <include refid="selectSalesShipmentVo"/>
        <where>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''">
                and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}
            </if>
            <if test="singleNumber != null  and singleNumber != ''">and singleNumber like concat('%', #{singleNumber},
                '%')
            </if>
            <if test="businessPartnerCode != null  and businessPartnerCode != ''">and businessPartnerCode like
                concat('%', #{businessPartnerCode}, '%')
            </if>
            <if test="businessPartnerName != null  and businessPartnerName != ''">and businessPartnerName like
                concat('%', #{businessPartnerName}, '%')
            </if>
            <if test="businessPartnerLevel != null  and businessPartnerLevel != ''">and businessPartnerLevel like
                concat('%', #{businessPartnerLevel}, '%')
            </if>
            <if test="salesRegion != null  and salesRegion != ''">and salesRegion like concat('%', #{salesRegion},
                '%')
            </if>
            <if test="businessManager != null  and businessManager != ''">and businessManager = #{businessManager}</if>
            <if test="productCode != null  and productCode != ''">and productCode = #{productCode}</if>
            <if test="productName != null  and productName != ''">and productName like concat('%', #{productName},
                '%')
            </if>
            <if test="orderQuantity != null  and orderQuantity != ''">and orderQuantity = #{orderQuantity}</if>
            <if test="salesUnitPrice != null  and salesUnitPrice != ''">and salesUnitPrice = #{salesUnitPrice}</if>
            <if test="totalPrice != null  and totalPrice != ''">and totalPrice = #{totalPrice}</if>
            <if test="contractAllowance != null  and contractAllowance != ''">and contractAllowance =
                #{contractAllowance}
            </if>
            <if test="balancePayment != null  and balancePayment != ''">and balancePayment = #{balancePayment}</if>
            <if test="receivables != null  and receivables != ''">and receivables = #{receivables}</if>
            <if test="batch != null  and batch != ''">and batch = #{batch}</if>
            <if test="sapSystemInvoiceNo != null  and sapSystemInvoiceNo != ''">and sapSystemInvoiceNo like concat('%',
                #{sapSystemInvoiceNo}, '%')
            </if>
            <if test="invoiceNo != null  and invoiceNo != ''">and invoiceNo like concat('%', #{invoiceNo}, '%')</if>
            <if test="params.beginInvoicingTime != null and params.beginInvoicingTime != '' and params.endInvoicingTime != null and params.endInvoicingTime != ''">
                and invoicingTime between #{params.beginInvoicingTime} and #{params.endInvoicingTime}
            </if>
            <if test="number != null  and number != ''">and number = #{number}</if>
            <if test="params.beginDueReceivables != null and params.beginDueReceivables != '' and params.endDueReceivables != null and params.endDueReceivables != ''">
                and dueReceivables between #{params.beginDueReceivables} and #{params.endDueReceivables}
            </if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="salesContractCurrentSession != null  and salesContractCurrentSession != ''">and
                salesContractCurrentSession like concat('%', #{salesContractCurrentSession}, '%')
            </if>
            <if test="objectList !=null and objectList.size()>0 ">
                and salesGroup in
                <foreach collection="objectList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by applicationDate desc
    </select>
</mapper>