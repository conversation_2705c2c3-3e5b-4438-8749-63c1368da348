<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.TravelReimbursementMapper">
    
    <resultMap type="TravelReimbursement" id="TravelReimbursementResult">
        <result property="singleNumber"    column="singleNumber"    />
        <result property="reimbursementPerson"    column="reimbursementPerson"    />
        <result property="department"    column="department"    />
        <result property="applicationDate"    column="applicationDate"    />
        <result property="expensesAttribution"    column="expensesAttribution"    />
        <result property="expences"    column="expences"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="totalAmount"    column="totalAmount"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="documentStatus"    column="documentStatus"    />
    </resultMap>

    <sql id="selectTravelReimbursementVo">
        select singleNumber, reimbursementPerson, department, applicationDate, expensesAttribution, expences, projectNumber, totalAmount, currentSession, documentStatus from marketing_travel
    </sql>

    <select id="selectTravelReimbursementList" parameterType="TravelReimbursement" resultMap="TravelReimbursementResult">
        <include refid="selectTravelReimbursementVo"/>
        <where>  
            <if test="singleNumber != null  and singleNumber != ''"> and singleNumber like concat('%', #{singleNumber}, '%')</if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''"> and reimbursementPerson like concat('%', #{reimbursementPerson}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="expensesAttribution != null  and expensesAttribution != ''"> and expensesAttribution like concat('%', #{expensesAttribution}, '%')</if>
            <if test="expences != null  and expences != ''"> and expences like concat('%', #{expences}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="totalAmount != null  and totalAmount != ''"> and totalAmount = #{totalAmount}</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
        </where>
        order by applicationDate desc
    </select>
</mapper>