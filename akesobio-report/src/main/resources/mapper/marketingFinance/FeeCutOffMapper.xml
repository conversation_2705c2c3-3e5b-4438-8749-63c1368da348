<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.FeeCutOffMapper">
    
    <resultMap type="FeeCutOff" id="FeeCutOffResult">
        <result property="formName"    column="formName"    />
        <result property="oaOrderNumber"    column="oaOrderNumber"    />
        <result property="belongCompany"    column="belongCompany"    />
        <result property="documentNumber"    column="documentNumber"    />
        <result property="summarizeVoucherAmount"    column="summarizeVoucherAmount"    />
        <result property="meetingApplicationNumber"    column="meetingApplicationNumber"    />
        <result property="startDate"    column="startDate"    />
        <result property="endDate"    column="endDate"    />
        <result property="applicationDate"    column="applicationDate"    />
        <result property="approvalTime"    column="approvalTime"    />
    </resultMap>

    <sql id="selectFeeCutOffVo">
        select formName, oaOrderNumber, belongCompany, documentNumber, summarizeVoucherAmount, meetingApplicationNumber, startDate, endDate, applicationDate, approvalTime from fee_cut_off
    </sql>

    <select id="selectFeeCutOffList" parameterType="FeeCutOff" resultMap="FeeCutOffResult">
        <include refid="selectFeeCutOffVo"/>
        <where>  
            <if test="formName != null  and formName != ''"> and formName like concat('%', #{formName}, '%')</if>
            <if test="oaOrderNumber != null  and oaOrderNumber != ''"> and oaOrderNumber like concat('%', #{oaOrderNumber}, '%')</if>
            <if test="belongCompany != null  and belongCompany != ''"> and belongCompany like concat('%', #{belongCompany}, '%')</if>
            <if test="documentNumber != null  and documentNumber != ''"> and documentNumber = #{documentNumber}</if>
            <if test="summarizeVoucherAmount != null  and summarizeVoucherAmount != ''"> and summarizeVoucherAmount = #{summarizeVoucherAmount}</if>
            <if test="meetingApplicationNumber != null  and meetingApplicationNumber != ''"> and meetingApplicationNumber like concat('%', #{meetingApplicationNumber}, '%')</if>
            <if test="startDate != null "> and startDate = #{startDate}</if>
            <if test="endDate != null "> and endDate = #{endDate}</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''"> and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}</if>
        </where>
        order by applicationDate desc
    </select>
</mapper>