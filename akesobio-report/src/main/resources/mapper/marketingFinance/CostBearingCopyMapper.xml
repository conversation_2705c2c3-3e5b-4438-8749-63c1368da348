<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.CostBearingCopyMapper">

    <resultMap type="com.akesobio.report.marketingFinance.vo.CostBearingCopy" id="CostBearingCopyResult">
        <result property="tableName" column="tableName"/>
        <result property="approvalTime" column="approvalTime"/>
        <result property="meetingCategory" column="meetingCategory"/>
        <result property="departmentAllocationAmount" column="departmentAllocationAmount"/>
        <result property="meetingType" column="meetingType"/>

    </resultMap>
    <!-- 费用报销 客户费用 会议类别/费用类型/报销类型==外部费用 -->
    <select id="selectCostBearingCopyList1" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select
        (select fd_name from modeling_app_model where fd_table_name='ekp_fybxd') as 'tableName',
        (case when a.fd_3b4b7a5f10f01a is not null then CONVERT(char(100),a.fd_3b4b7a5f10f01a ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3a4a9bc91f7cee as 'meetingCategory',
        f.fd_3afc52440ab3f4 as 'departmentAllocationAmount',
        null as 'meetingType'
        from ekp_fybxd a
        left join ekp_ekp_fybxd_523006c794 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>
    </select>

    <!-- 费用调整单 客户费用 会议类别/费用类型/报销类型==费用 -->
    <select id="selectCostBearingCopyList2" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_fytz') as 'tableName',
        CONVERT(VARCHAR(100),a.fd_3b85a11cf8e9dc ,120) as 'approvalTime',
        a.fd_3b72d6d95a58b6 as 'meetingCategory',
        d.fd_3b73acf52feff0 as 'departmentAllocationAmount',
        null as 'meetingType'
        from ekp_fytz a
        left join ekp_ekp_fytz_d675c40ef4 d on d.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>

    </select>

    <!-- 费用报销 IIT 会议类别/费用类型/报销类型==外部费用-IIT相关 -->
    <select id="selectCostBearingCopyList3" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select
        (select fd_name from modeling_app_model where fd_table_name='ekp_fybxd') as 'tableName',
        (case when a.fd_3b4b7a5f10f01a is not null then CONVERT(char(100),a.fd_3b4b7a5f10f01a ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3a4a9bc91f7cee as 'meetingCategory',
        f.fd_3afc52440ab3f4 as 'departmentAllocationAmount',
        null as 'meetingType'
        from ekp_fybxd a
        left join ekp_ekp_fybxd_523006c794 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>
    </select>

    <!-- 对公付款 IIT 会议类型==*IIT* -->
    <select id="selectCostBearingCopyList4" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_dgfkx') as 'tableName',
        (case when a.fd_3a5f15c6a460ee is not null then CONVERT(char(100),a.fd_3a5f15c6a460ee ,120) else
        a.sap_year_month end) as 'approvalTime',
        (case when a.fd_3b697906dc6458 is not null then a.fd_3b697906dc6458 else a.fd_3b69858d5de53a end) as
        'meetingCategory',
        f.fd_3a19b3f86fa8fa as 'departmentAllocationAmount',
        a.fd_3b69793bb3102e as 'meetingType'
        from ekp_dgfkx a
        left join ekp_ekp_dgfkx_5061aaf228 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingType != null  and meetingType != ''">and meetingType like concat('%', #{meetingType},
                '%')
            </if>
        </where>

    </select>

    <!-- 会议费用报销 IIT 会议类型==*IIT* -->
    <select id="selectCostBearingCopyList5" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_hybx') as 'tableName',
        (case when a.fd_3a5f15909387e6 is not null then CONVERT(char(100),a.fd_3a5f15909387e6 ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3b4d36ac9dcb82 as 'meetingCategory',
        d.fd_39c7508c96bd16 as 'departmentAllocationAmount',
        a.fd_3b4d36b23f5d96 as 'meetingType'
        from ekp_hybx a
        left join ekp_ekp_hybx_5061aaf228 d on d.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingType != null  and meetingType != ''">and meetingType like concat('%', #{meetingType},
                '%')
            </if>
        </where>

    </select>

    <!-- 会议费用报销 会议费用 会议类别/费用类型/报销类型==外部 并且 会议类型==不包含*IIT*-->
    <select id="selectCostBearingCopyList6" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_hybx') as 'tableName',
        (case when a.fd_3a5f15909387e6 is not null then CONVERT(char(100),a.fd_3a5f15909387e6 ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3b4d36ac9dcb82 as 'meetingCategory',
        d.fd_39c7508c96bd16 as 'departmentAllocationAmount',
        a.fd_3b4d36b23f5d96 as 'meetingType'
        from ekp_hybx a
        left join ekp_ekp_hybx_5061aaf228 d on d.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
            <if test="meetingType != null  and meetingType != ''">and meetingType not like concat('%', #{meetingType},
                '%')
            </if>
        </where>
    </select>

    <!-- 会议费用报销 会议费用 会议类型==不包含*IIT*-->
    <select id="selectCostBearingCopyList7" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_hybx') as 'tableName',
        (case when a.fd_3a5f15909387e6 is not null then CONVERT(char(100),a.fd_3a5f15909387e6 ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3b4d36ac9dcb82 as 'meetingCategory',
        d.fd_39c7508c96bd16 as 'departmentAllocationAmount',
        a.fd_3b4d36b23f5d96 as 'meetingType'
        from ekp_hybx a
        left join ekp_ekp_hybx_5061aaf228 d on d.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingType != null  and meetingType != ''">and meetingType not like concat('%', #{meetingType},
                '%')
            </if>
        </where>
    </select>

    <!-- 对公付款 会议费用 会议类别/费用类型/报销类型==外部 会议类型==不包含*IIT*-->
    <select id="selectCostBearingCopyList8" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_dgfkx') as 'tableName',
        (case when a.fd_3a5f15c6a460ee is not null then CONVERT(char(100),a.fd_3a5f15c6a460ee ,120) else
        a.sap_year_month end) as 'approvalTime',
        (case when a.fd_3b697906dc6458 is not null then a.fd_3b697906dc6458 else a.fd_3b69858d5de53a end) as
        'meetingCategory',
        f.fd_3a19b3f86fa8fa as 'departmentAllocationAmount',
        a.fd_3b69793bb3102e as 'meetingType'
        from ekp_dgfkx a
        left join ekp_ekp_dgfkx_5061aaf228 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
            <if test="meetingType != null  and meetingType != ''">and meetingType not like concat('%', #{meetingType},
                '%')
            </if>
        </where>

    </select>

    <!-- 对公付款 会议费用 会议类型==不包含*IIT*-->
    <select id="selectCostBearingCopyList9" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_dgfkx') as 'tableName',
        (case when a.fd_3a5f15c6a460ee is not null then CONVERT(char(100),a.fd_3a5f15c6a460ee ,120) else
        a.sap_year_month end) as 'approvalTime',
        (case when a.fd_3b697906dc6458 is not null then a.fd_3b697906dc6458 else a.fd_3b69858d5de53a end) as
        'meetingCategory',
        f.fd_3a19b3f86fa8fa as 'departmentAllocationAmount',
        a.fd_3b69793bb3102e as 'meetingType'
        from ekp_dgfkx a
        left join ekp_ekp_dgfkx_5061aaf228 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingType != null  and meetingType != ''">and meetingType not like concat('%', #{meetingType},
                '%')
            </if>
        </where>

    </select>

    <!-- 费用调整 会议费用 会议类别/费用类型/报销类型包含会议-->
    <select id="selectCostBearingCopyList10" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_fytz') as 'tableName',
        CONVERT(VARCHAR(100),a.fd_3b85a11cf8e9dc ,120) as 'approvalTime',
        a.fd_3b72d6d95a58b6 as 'meetingCategory',
        d.fd_3b73acf52feff0 as 'departmentAllocationAmount',
        null as 'meetingType'
        from ekp_fytz a
        left join ekp_ekp_fytz_d675c40ef4 d on d.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory like concat('%', #{meetingCategory},'%')</if>
        </where>
    </select>

    <!-- 会议费用报销 内部费用 会议类别/费用类型/报销类型==内部-->
    <select id="selectCostBearingCopyList11" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_hybx') as 'tableName',
        (case when a.fd_3a5f15909387e6 is not null then CONVERT(char(100),a.fd_3a5f15909387e6 ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3b4d36ac9dcb82 as 'meetingCategory',
        d.fd_39c7508c96bd16 as 'departmentAllocationAmount',
        a.fd_3b4d36b23f5d96 as 'meetingType'
        from ekp_hybx a
        left join ekp_ekp_hybx_5061aaf228 d on d.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>

    </select>

    <!-- 费用报销单 内部费用 会议类别/费用类型/报销类型==职工福利-->
    <select id="selectCostBearingCopyList12" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select
        (select fd_name from modeling_app_model where fd_table_name='ekp_fybxd') as 'tableName',
        (case when a.fd_3b4b7a5f10f01a is not null then CONVERT(char(100),a.fd_3b4b7a5f10f01a ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3a4a9bc91f7cee as 'meetingCategory',
        f.fd_3afc52440ab3f4 as 'departmentAllocationAmount',
        null as 'meetingType'
        from ekp_fybxd a
        left join ekp_ekp_fybxd_523006c794 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>
    </select>

    <!-- 费用报销单 内部费用 会议类别/费用类型/报销类型==行政办公-->
    <select id="selectCostBearingCopyList13" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select
        (select fd_name from modeling_app_model where fd_table_name='ekp_fybxd') as 'tableName',
        (case when a.fd_3b4b7a5f10f01a is not null then CONVERT(char(100),a.fd_3b4b7a5f10f01a ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3a4a9bc91f7cee as 'meetingCategory',
        f.fd_3afc52440ab3f4 as 'departmentAllocationAmount',
        null as 'meetingType'
        from ekp_fybxd a
        left join ekp_ekp_fybxd_523006c794 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>

    </select>

    <!-- 费用报销单 内部费用 会议类别/费用类型/报销类型==行政办公及培训-->
    <select id="selectCostBearingCopyList14" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select
        (select fd_name from modeling_app_model where fd_table_name='ekp_fybxd') as 'tableName',
        (case when a.fd_3b4b7a5f10f01a is not null then CONVERT(char(100),a.fd_3b4b7a5f10f01a ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3a4a9bc91f7cee as 'meetingCategory',
        f.fd_3afc52440ab3f4 as 'departmentAllocationAmount',
        null as 'meetingType'
        from ekp_fybxd a
        left join ekp_ekp_fybxd_523006c794 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>

    </select>

    <!-- 对公付款 外部-宣传\捐赠 会议类别/费用类型/报销类型==外部费用-->
    <select id="selectCostBearingCopyList15" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_dgfkx') as 'tableName',
        (case when a.fd_3a5f15c6a460ee is not null then CONVERT(char(100),a.fd_3a5f15c6a460ee ,120) else
        a.sap_year_month end) as 'approvalTime',
        (case when a.fd_3b697906dc6458 is not null then a.fd_3b697906dc6458 else a.fd_3b69858d5de53a end) as
        'meetingCategory',
        f.fd_3a19b3f86fa8fa as 'departmentAllocationAmount',
        a.fd_3b69793bb3102e as 'meetingType'
        from ekp_dgfkx a
        left join ekp_ekp_dgfkx_5061aaf228 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>
    </select>

    <!-- 劳务对公合同(付款)审批-2023版 会议费用-->
    <select id="selectCostBearingCopyList16" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select
        (select fd_name from modeling_app_model where fd_table_name='ekp_LWDG') as 'tableName',
        (case when a.fd_3be2451a968976 is not null then CONVERT(char(100),a.fd_3be2451a968976 ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3a9ed26ba00dc0 as 'meetingType',
        a.fd_3a9ed101179cf8 as 'meetingCategory',
        d.fd_3a9ed3d6934042 as 'departmentAllocationAmount'
        from ekp_LWDG a
        left join ekp_ekp_LWDG_5061aaf228 d on d.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
        </where>
    </select>

    <!-- MDS报销 MDS费用-->
    <select id="selectCostBearingCopyList17" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_mdsbx') as 'tableName',
        (case when a.fd_3aeac477d7c25a is not null then CONVERT(char(100),a.fd_3aeac477d7c25a ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3aea097ea28ed6 as 'meetingCategory',
        f.fd_3b4eba1c5715e6 as 'departmentAllocationAmount',
        a.fd_3b6af35a0d845c as 'meetingType'
        from ekp_mdsbx a
        left join ekp_ekp_mdsbx_b9de146a92 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
        </where>
    </select>

    <!-- 开发进院报销单 开发进院-->
    <select id="selectCostBearingCopyList18" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_kfjybx') as 'tableName',
        (case when a.fd_3aeac477d7c25a is not null then CONVERT(char(100),a.fd_3aeac477d7c25a ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3aea097ea28ed6 as 'meetingCategory',
        f.fd_3b4eba1c5715e6 as 'departmentAllocationAmount',
        a.fd_3b6af35a0d845c as 'meetingType'
        from ekp_kfjybx a
        left join ekp_ekp_kfjybx_b9de146a92 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
        </where>

    </select>

    <!-- 差旅费报销单 内部费用-->
    <select id="selectCostBearingCopyList19" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_clfbxd') as 'tableName',
        (case when a.fd_3b487d273e8e90 is not null then CONVERT(char(100),a.fd_3b487d273e8e90 ,120) else
        a.sap_year_month end) as 'approvalTime',
        null as 'meetingCategory',
        d.fd_3b4951dd5f9d32 as 'departmentAllocationAmount',
        null as 'meetingType'
        from ekp_clfbxd a
        left join ekp_ekp_clfbxd_535cbc3fa8 d on d.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
        </where>
    </select>

    <!-- 对公付款  内部费用  会议类别/费用类型/报销类型包含内部-->
    <select id="selectCostBearingCopyList20" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_dgfkx') as 'tableName',
        (case when a.fd_3a5f15c6a460ee is not null then CONVERT(char(100),a.fd_3a5f15c6a460ee ,120) else
        a.sap_year_month end) as 'approvalTime',
        (case when a.fd_3b697906dc6458 is not null then a.fd_3b697906dc6458 else a.fd_3b69858d5de53a end) as
        'meetingCategory',
        f.fd_3a19b3f86fa8fa as 'departmentAllocationAmount',
        a.fd_3b69793bb3102e as 'meetingType'
        from ekp_dgfkx a
        left join ekp_ekp_dgfkx_5061aaf228 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory like
                concat('%',#{meetingCategory}, '%')
            </if>
        </where>
    </select>
    <!-- 对公付款 客户费用  会议类别/费用类型/报销类型==外部费用-->
    <select id="selectCostBearingCopyList21" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_dgfkx') as 'tableName',
        (case when a.fd_3a5f15c6a460ee is not null then CONVERT(char(100),a.fd_3a5f15c6a460ee ,120) else
        a.sap_year_month end) as 'approvalTime',
        (case when a.fd_3b697906dc6458 is not null then a.fd_3b697906dc6458 else a.fd_3b69858d5de53a end) as
        'meetingCategory',
        f.fd_3a19b3f86fa8fa as 'departmentAllocationAmount',
        a.fd_3b69793bb3102e as 'meetingType'
        from ekp_dgfkx a
        left join ekp_ekp_dgfkx_5061aaf228 f on f.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory=#{meetingCategory}</if>
        </where>
    </select>


    <!-- 费用调整 会议费用 会议类别/费用类型/报销类型==MDS-->
    <select id="selectCostBearingCopyList22" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_fytz') as 'tableName',
        CONVERT(VARCHAR(100),a.fd_3b85a11cf8e9dc ,120) as 'approvalTime',
        a.fd_3b72d6d95a58b6 as 'meetingCategory',
        d.fd_3b73acf52feff0 as 'departmentAllocationAmount',
        null as 'meetingType'
        from ekp_fytz a
        left join ekp_ekp_fytz_d675c40ef4 d on d.fd_parent_id=a.fd_id
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>
    </select>



    <!-- 会议费用报销 会议费用 会议类别/费用类型/报销类型==外部 并且 会议类型==不包含*IIT*
       查询前4月到前1月数据 根据申请日期，审批时间为空,单据是待审或者驳回-->
    <select id="selectCostBearing6" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType,a.applicationDate from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_hybx') as 'tableName',
        (case when a.fd_3a5f15909387e6 is not null then CONVERT(char(100),a.fd_3a5f15909387e6 ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3b4d36ac9dcb82 as 'meetingCategory',
        d.fd_39c7508c96bd16 as 'departmentAllocationAmount',
        a.fd_3b4d36b23f5d96 as 'meetingType',
        a.fd_39967bebe57d18    as 'applicationDate'
        from ekp_hybx a
        left join ekp_ekp_hybx_5061aaf228 d on d.fd_parent_id=a.fd_id
        LEFT JOIN [dbo].[modeling_model_main] q ON a.fd_id = q.fd_id
        where (q.doc_status ='11' or  q.doc_status ='20')  and a.fd_3a5f15909387e6 is null and a.fd_3a5f15909387e6 is null
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and applicationDate between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
            <if test="meetingType != null  and meetingType != ''">and meetingType not like concat('%', #{meetingType},
                '%')
            </if>
        </where>
    </select>

    <!-- 对公付款 会议费用 会议类别/费用类型/报销类型==外部 会议类型==不包含*IIT*
     * 查询前4月到前1月 数据
     * 根据申请日期，审批时间为空,单据是待审或者驳回-->
    <select id="selectCostBearing8" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType,a.applicationDate from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_dgfkx') as 'tableName',
        (case when a.fd_3a5f15c6a460ee is not null then CONVERT(char(100),a.fd_3a5f15c6a460ee ,120) else
        a.sap_year_month end) as 'approvalTime',
        (case when a.fd_3b697906dc6458 is not null then a.fd_3b697906dc6458 else a.fd_3b69858d5de53a end) as
        'meetingCategory',
        f.fd_3a19b3f86fa8fa as 'departmentAllocationAmount',
        a.fd_3b69793bb3102e as 'meetingType',
        a.fd_399755b9192748   as 'applicationDate'
        from ekp_dgfkx a
        left join ekp_ekp_dgfkx_5061aaf228 f on f.fd_parent_id=a.fd_id
        LEFT JOIN [dbo].[modeling_model_main] q ON a.fd_id = q.fd_id
        where (q.doc_status ='11' or  q.doc_status ='20')  and a.fd_3a5f15c6a460ee is null and a.sap_year_month is null
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and applicationDate between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
            <if test="meetingType != null  and meetingType != ''">and meetingType not like concat('%', #{meetingType},
                '%')
            </if>
        </where>

    </select>

    <!-- 费用调整 会议费用 会议类别/费用类型/报销类型包含会议
      * 查询前4月到前1月 数据
     * 根据申请日期，审批时间为空,单据是待审或者驳回-->
    <select id="selectCostBearing10" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType,a.applicationDate from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_fytz') as 'tableName',
        CONVERT(VARCHAR(100),a.fd_3b85a11cf8e9dc ,120) as 'approvalTime',
        a.fd_3b72d6d95a58b6 as 'meetingCategory',
        d.fd_3b73acf52feff0 as 'departmentAllocationAmount',
        null as 'meetingType',
        a.fd_3b72c3d7675c8e    as 'applicationDate'
        from ekp_fytz a
        left join ekp_ekp_fytz_d675c40ef4 d on d.fd_parent_id=a.fd_id
        LEFT JOIN [dbo].[modeling_model_main] q ON a.fd_id = q.fd_id
        where (q.doc_status ='11' or  q.doc_status ='20')  and a.fd_3b85a11cf8e9dc is null
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and applicationDate between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory like concat('%', #{meetingCategory}, '%')</if>
        </where>
    </select>

    <!-- 劳务对公合同(付款)审批-2023版 会议费用
     * 查询前4月到前1月 数据
     * 根据申请日期，审批时间为空,单据是待审或者驳回-->
    <select id="selectCostBearing16" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType,a.applicationDate from (
        select
        (select fd_name from modeling_app_model where fd_table_name='ekp_LWDG') as 'tableName',
        (case when a.fd_3be2451a968976 is not null then CONVERT(char(100),a.fd_3be2451a968976 ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3a9ed26ba00dc0 as 'meetingType',
        a.fd_3a9ed101179cf8 as 'meetingCategory',
        d.fd_3a9ed3d6934042 as 'departmentAllocationAmount',
        a.fd_39967bebe57d18    as 'applicationDate'
        from ekp_LWDG a
        left join ekp_ekp_LWDG_5061aaf228 d on d.fd_parent_id=a.fd_id
        LEFT JOIN [dbo].[modeling_model_main] q ON a.fd_id = q.fd_id
        where (q.doc_status ='11' or  q.doc_status ='20')  and a.fd_3be2451a968976 is null and a.sap_year_month is null
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and applicationDate between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
        </where>
    </select>

    <!-- 费用报销 客户费用 会议类别/费用类型/报销类型==外部费用
    * 查询前4月到前1月 数据
     * 根据申请日期，审批时间为空,单据是待审或者驳回-->
    <select id="selectCostBearing1" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType,a.applicationDate from (
        select
        (select fd_name from modeling_app_model where fd_table_name='ekp_fybxd') as 'tableName',
        (case when a.fd_3b4b7a5f10f01a is not null then CONVERT(char(100),a.fd_3b4b7a5f10f01a ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3a4a9bc91f7cee as 'meetingCategory',
        f.fd_3afc52440ab3f4 as 'departmentAllocationAmount',
        null as 'meetingType',
        a.fd_39967bebe57d18   as 'applicationDate'

        from ekp_fybxd a
        left join ekp_ekp_fybxd_523006c794 f on f.fd_parent_id=a.fd_id
        LEFT JOIN [dbo].[modeling_model_main] q ON a.fd_id = q.fd_id
        where (q.doc_status ='11' or  q.doc_status ='20')  and a.fd_3b4b7a5f10f01a is null and a.sap_year_month is null
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and applicationDate between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>
    </select>

    <!-- 费用调整单 客户费用 会议类别/费用类型/报销类型==费用
    * 查询前4月到前1月 数据
    * 根据申请日期，审批时间为空,单据是待审或者驳回-->
    <select id="selectCostBearing2" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType,a.applicationDate from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_fytz') as 'tableName',
        CONVERT(VARCHAR(100),a.fd_3b85a11cf8e9dc ,120) as 'approvalTime',
        a.fd_3b72d6d95a58b6 as 'meetingCategory',
        d.fd_3b73acf52feff0 as 'departmentAllocationAmount',
        null as 'meetingType',
        a.fd_3b72c3d7675c8e    as 'applicationDate'
        from ekp_fytz a
        left join ekp_ekp_fytz_d675c40ef4 d on d.fd_parent_id=a.fd_id
        LEFT JOIN [dbo].[modeling_model_main] q ON a.fd_id = q.fd_id
        where (q.doc_status ='11' or  q.doc_status ='20')  and a.fd_3b85a11cf8e9dc is null
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and applicationDate between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>

    </select>

    <!-- MDS报销 MDS费用  * 查询前4月到前1月 数据
                 * 根据申请日期，审批时间为空,单据是待审或者驳回-->
    <select id="selectCostBearing17" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType,a.applicationDate from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_mdsbx') as 'tableName',
        (case when a.fd_3aeac477d7c25a is not null then CONVERT(char(100),a.fd_3aeac477d7c25a ,120) else
        a.sap_year_month end) as 'approvalTime',
        a.fd_3aea097ea28ed6 as 'meetingCategory',
        f.fd_3b4eba1c5715e6 as 'departmentAllocationAmount',
        a.fd_3b6af35a0d845c as 'meetingType',
        a.fd_39967bebe57d18    as 'applicationDate'
        from ekp_mdsbx a
        left join ekp_ekp_mdsbx_b9de146a92 f on f.fd_parent_id=a.fd_id
        LEFT JOIN [dbo].[modeling_model_main] q ON a.fd_id = q.fd_id
        where (q.doc_status ='11' or  q.doc_status ='20')  and a.fd_3aeac477d7c25a is null and a.sap_year_month is null
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and applicationDate between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
        </where>
    </select>

    <!-- 费用调整 会议费用 会议类别/费用类型/报销类型==MDS
     * 查询前4月到前1月 数据
     * 根据申请日期，审批时间为空,单据是待审或者驳回-->
    <select id="selectCostBearing22" parameterType="com.akesobio.report.marketingFinance.vo.CostBearingCopy"
            resultMap="CostBearingCopyResult">
        select a.tableName,a.approvalTime,a.meetingCategory,a.departmentAllocationAmount,a.meetingType,a.applicationDate from (
        select (select fd_name from modeling_app_model where fd_table_name='ekp_fytz') as 'tableName',
        CONVERT(VARCHAR(100),a.fd_3b85a11cf8e9dc ,120) as 'approvalTime',
        a.fd_3b72d6d95a58b6 as 'meetingCategory',
        d.fd_3b73acf52feff0 as 'departmentAllocationAmount',
        null as 'meetingType',
        a.fd_3b72c3d7675c8e    as 'applicationDate'
        from ekp_fytz a
        left join ekp_ekp_fytz_d675c40ef4 d on d.fd_parent_id=a.fd_id
        LEFT JOIN [dbo].[modeling_model_main] q ON a.fd_id = q.fd_id
        where (q.doc_status ='11' or  q.doc_status ='20')  and a.fd_3b85a11cf8e9dc is null
        ) a
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and applicationDate between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
        </where>
    </select>


</mapper>