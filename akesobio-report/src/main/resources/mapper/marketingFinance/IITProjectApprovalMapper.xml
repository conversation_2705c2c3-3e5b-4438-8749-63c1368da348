<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.IITProjectApprovalMapper">

    <resultMap type="com.akesobio.report.marketingFinance.vo.IITProjectApprovalVo" id="IITProjectApprovalResult">
    </resultMap>

    <sql id="selectIITProjectApproval">
        SELECT id,
               dh,
               applicationDate,
               name,
               post,
               department,
               documentStatus,
               projectNumber,
               regionalManager,
               salesManager,
               projectName,
               researchCenter,
               sectionOffice,
               researcher,
               projectDate,
               projectAbstract,
               researchObjectives,
               fundingScheme,
               costBudget,
               num
        FROM (SELECT a.fd_id             AS id,
                     a.fd_3b06548c93c6f2 AS dh,
                     a.application_date  AS applicationDate,
                     p.fd_name           AS name,
                     a.job_title         AS post,
                     a.department,
                     (CASE
                         l.doc_status
                          WHEN '10' THEN
                              '草稿'
                          WHEN '20' THEN
                              '待审'
                          WHEN '11' THEN
                              '驳回'
                          WHEN '00' THEN
                              '废弃'
                          WHEN '30' THEN
                              '结束'
                          WHEN '31' THEN
                              '已反馈'
                          ELSE '不对劲'
                         END)            AS documentStatus,
                     a.fd_3b065b21e23696 AS projectNumber,
                     p1.fd_name          AS regionalManager,
                     p2.fd_name          AS salesManager,
                     a.fd_3b065b18f66cfc AS projectName,
                     a.fd_3b066155025996 AS researchCenter,
                     a.fd_3b06613dca9ff0 AS sectionOffice,
                     a.fd_3b0664d9c7bbaa AS researcher,
                     a.fd_3b0662f6a5a23a AS projectDate,
                     a.fd_3b0663a78ee706 AS projectAbstract,
                     a.fd_3b0663747bdc86 AS researchObjectives,
                     a.fd_3b0664466e3cd8 AS fundingScheme,
                     a.fd_3b06666375fe48 AS costBudget,
                     a.fd_3b06665f68f46c AS num
              FROM ekp_YX22 a
                       LEFT JOIN hr_staff_person_info p ON p.fd_id = a.applicant
                       LEFT JOIN hr_staff_person_info p1 ON p1.fd_id = a.fd_3b0a73d3c2c2c6
                       LEFT JOIN hr_staff_person_info p2 ON p2.fd_id = a.fd_3b0a73e0fa6d9a
                       LEFT JOIN lbpm_process l ON l.fd_id = a.fd_id
              WHERE l.doc_delete_flag != '1' AND l.doc_status IN ('20','30')
              UNION ALL
              SELECT
                  b.fd_id AS id, b.fd_3b06548c93c6f2 AS dh, b.application_date AS applicationDate, p.fd_name AS name, b.job_title AS post, b.department, (CASE
                  l2.doc_status
                  WHEN '10' THEN
                  '草稿'
                  WHEN '20' THEN
                  '待审'
                  WHEN '11' THEN
                  '驳回'
                  WHEN '00' THEN
                  '废弃'
                  WHEN '30' THEN
                  '结束'
                  WHEN '31' THEN
                  '已反馈' ELSE '不对劲'
                  END) AS documentStatus, b.fd_3b065b21e23696 AS projectNumber, p3.fd_name AS regionalManager, p4.fd_name AS salesManager, b.fd_3b065b18f66cfc AS projectName, b.fd_3b066155025996 AS researchCenter, b.fd_3b06613dca9ff0 AS sectionOffice, b.fd_3b0664d9c7bbaa AS researcher, b.fd_3b0662f6a5a23a AS projectDate, b.fd_3b0663a78ee706 AS projectAbstract, b.fd_3b0663747bdc86 AS researchObjectives, b.fd_3b0664466e3cd8 AS fundingScheme, b.fd_3b06666375fe48 AS costBudget, b.fd_3b06665f68f46c AS num
              FROM ekp_5b4b428e4019945dc9e5 b
                  LEFT JOIN hr_staff_person_info p
              ON p.fd_id = b.applicant
                  LEFT JOIN hr_staff_person_info p3 ON p3.fd_id = b.fd_3b0a73d3c2c2c6
                  LEFT JOIN hr_staff_person_info p4 ON p4.fd_id = b.fd_3b0a73e0fa6d9a
                  LEFT JOIN lbpm_process l2 ON l2.fd_id = b.fd_id
              WHERE l2.doc_delete_flag != '1' AND l2.doc_status IN ('20', '30')) tableData
    </sql>

    <select id="queryIITProjectApproval" parameterType="IITProjectApproval" resultMap="IITProjectApprovalResult">
        <include refid="selectIITProjectApproval"/>
        <where>
            <if test="dh != null  and dh != ''">and dh like concat('%', #{dh}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="salesManager != null  and salesManager != ''">and salesManager like concat('%', #{salesManager},
                '%')
            </if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>