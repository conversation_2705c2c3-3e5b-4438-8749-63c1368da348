<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.FundingPlanTotalMapper">
    <!-- 实体类  -->
    <resultMap type="FundingPlanTotal" id="FundingPlanTotalResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectFundingPlanTotalList">
        SELECT a.fd_3cbcec1f3b2b22                                                       AS area,
               a.fd_3cbcec1d3a2460                                                       AS region,
               a.fd_3cbcec221d6ffe                                                       AS months,
               a.fd_3cbcf19a3c2cdc                                                       AS rate,
               (CASE
                    WHEN a.fd_3cbcec1d3a2460 LIKE '%本部%' THEN 0
                    ELSE (CASE WHEN a.fd_3cbcfae505842c IS NULL THEN 0 ELSE a.fd_3cbcfae505842c END) +
                         (CASE WHEN a.fd_3cbcfb47b9e428 IS NULL THEN 0 ELSE a.fd_3cbcfb47b9e428 END)
                   END)                                                                  AS accumulateAmount,
               (CASE
                    WHEN a.fd_3cbcec1d3a2460 LIKE '%本部%' THEN 0
                    ELSE a.fd_3cbdb6f1854e46
                   END)                                                                  AS expectAmount,
               ((CASE
                     WHEN a.fd_3cbcfb09e04ab0 IS NULL THEN 0
                     ELSE a.fd_3cbcfb09e04ab0
                   END) +
                (CASE
                     WHEN a.fd_3cbdb8e0d6a9aa IS NULL THEN 0
                     ELSE a.fd_3cbdb8e0d6a9aa
                    END))                                                                AS payAmount,
               a.fd_3cbd8a0ca3c8a6                                                       AS payExpectAmount,
               a.fd_3cbdb9069808f0                                                       AS usableAmount,
               (select SUM(departmentAllocationAmount) / 10000
                from cost_bearing
                where businessApprovalTimes = a.fd_3cbcec221d6ffe
                  and (endLevelDepartment = a.fd_3cbcec1d3a2460 OR secondaryDepartment =
                                                                   a.fd_3cbcec1d3a2460)) AS totalAmount
        FROM ekp_0ab6ce93a4145d98c502 a
                 LEFT JOIN lbpm_process l ON l.fd_id = a.fd_id
    </sql>

    <!-- 方法 -->
    <select id="queryFundingPlanTotalList" parameterType="FundingPlanTotal" resultMap="FundingPlanTotalResult">
        <include refid="selectFundingPlanTotalList"/>
        <where>
            l.doc_status = '30'
            <if test="months != null and months != ''">and a.fd_3cbcec221d6ffe like concat('%', #{months}, '%')</if>
            <if test="region != null and region != ''">and a.fd_3cbcec1d3a2460 like concat('%', #{region}, '%')</if>
            <if test="area != null and area != ''">and a.fd_3cbcec1f3b2b22 like concat('%', #{area}, '%')</if>
        </where>
        ORDER BY a.fd_3cbcec1f3b2b22
    </select>
</mapper>