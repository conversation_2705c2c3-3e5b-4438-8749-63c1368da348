<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.ReportSummaryMapper">
    
    <resultMap type="ReportSummary" id="ReportSummaryResult">
        <result property="singleNumber"    column="singleNumber"    />
        <result property="reimbursementPerson"    column="reimbursementPerson"    />
        <result property="department"    column="department"    />
        <result property="expensesAttribution"    column="expensesAttribution"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="applicationDate"    column="applicationDate"    />
        <result property="totalAmount"    column="totalAmount"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
    </resultMap>

    <sql id="selectReportSummaryVo">
        select singleNumber, reimbursementPerson, department, expensesAttribution, projectNumber, applicationDate, totalAmount, documentStatus, currentSession from total_report
    </sql>

    <select id="selectReportSummaryList" parameterType="ReportSummary" resultMap="ReportSummaryResult">
        <include refid="selectReportSummaryVo"/>
        <where>  
            <if test="singleNumber != null  and singleNumber != ''"> and singleNumber like concat('%', #{singleNumber}, '%')</if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''"> and reimbursementPerson like concat('%', #{reimbursementPerson}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="expensesAttribution != null  and expensesAttribution != ''"> and expensesAttribution like concat('%', #{expensesAttribution}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="totalAmount != null  and totalAmount != ''"> and totalAmount = #{totalAmount}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
        </where>
        order by applicationDate desc
    </select>
</mapper>