<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.AccountsReceivableAgingMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.AccountsReceivableAging"
               id="AccountsReceivableAgingResult">
        <result property="documentDate" column="documentDate"/>
        <result property="amount" column="amount"/>
        <result property="salesArea" column="salesArea"/>
    </resultMap>
    <select id="selectAccountsReceivableAging1" resultMap="AccountsReceivableAgingResult">
        select c.salesArea, c.documentDate,c.amount from (
        select
        a.fd_3b358e037b4f4e as 'salesArea',
        b.bldat as 'documentDate',
        b.dmbtr as 'amount'
        from
        ekp_contrast a
        left join CustomerOutstandingVoucherReport b on b.kunnr=a.fd_3b358de59698e0) c
        <where>
            <if test="salesArea != null and salesArea !=''">and salesArea = #{salesArea} </if>
            <if test="startDate != null and startDate !='' and endDate != null and endDate !=''">
                and documentDate between #{startDate} and #{endDate}
            </if>
        </where>
    </select>
    <select id="selectAccountsReceivableAging2" resultMap="AccountsReceivableAgingResult">
        select c.salesArea, c.documentDate,c.amount from (
        select
        a.fd_3b358e037b4f4e as 'salesArea',
        b.bldat as 'documentDate',
        b.dmbtr as 'amount'
        from
        ekp_contrast a
        left join CustomerOutstandingVoucherReport b on b.kunnr=a.fd_3b358de59698e0) c
        <where>
            <if test="startDate != null and startDate !='' and endDate != null and endDate !=''">
                and documentDate between #{startDate} and #{endDate}
            </if>
            <if test="salesArea != null and salesArea !=''">and salesArea = #{salesArea} </if>
        </where>
    </select>
    <select id="selectAccountsReceivableAging3" resultMap="AccountsReceivableAgingResult">
        select c.salesArea, c.documentDate,c.amount from (
        select
        a.fd_3b358e037b4f4e as 'salesArea',
        b.bldat as 'documentDate',
        b.dmbtr as 'amount'
        from
        ekp_contrast a
        left join CustomerOutstandingVoucherReport b on b.kunnr=a.fd_3b358de59698e0) c
        <where>
            <if test="salesArea != null and salesArea !=''">and salesArea = #{salesArea} </if>
            <if test="endDate != null and endDate !=''">and  documentDate &lt;= #{endDate} </if>
        </where>
    </select>
</mapper>
