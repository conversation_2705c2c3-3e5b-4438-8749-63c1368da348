<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.OaSaleMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.OaSale" id="OaSaleResult">
        <result property="businessPartnerCode" column="businessPartnerCode"/>
        <result property="customerCode" column="customerCode"/>
        <result property="saleDate" column="saleDate"/>
        <result property="number" column="number"/>
        <result property="customerLevel" column="customerLevel"/>
    </resultMap>
    <select id="selectOaSale" resultMap="OaSaleResult">
        select c.businessPartnerCode,c.customerCode,c.saleDate,c.number,c.customerLevel from
        (select
        a.fd_3ac2ae6ab212ea as 'businessPartnerCode',
        a.fd_3c4aff1e3691ee as 'customerCode',
        a.fd_3c4eef73180c6c as 'saleDate',
        a.fd_3ae6fa080d9312 as 'number',
        a.fd_3c4aff28bce490 as 'customerLevel'
        from ekp_dealerSales1 a ) c
        <where>
            <if test="year != null and year !=''">and saleDate like concat('%',#{year},'%')</if>
        </where>
    </select>
    <select id="selectOaSale1" resultMap="OaSaleResult">
        select c.businessPartnerCode,c.customerCode,c.saleDate,c.number,c.customerLevel from
        (select
        a.fd_3ac2ae6ab212ea as 'businessPartnerCode',
        a.fd_3c4aff1e3691ee as 'customerCode',
        a.fd_3c4eef73180c6c as 'saleDate',
        a.fd_3ae6fa080d9312 as 'number',
        a.fd_3c4aff28bce490 as 'customerLevel'
        from ekp_dealerSales1 a where a.fd_3c4aff28bce490 is null) c
        <where>
            <if test="year != null and year !=''">and saleDate like concat('%',#{year},'%')</if>
        </where>
    </select>
</mapper>
