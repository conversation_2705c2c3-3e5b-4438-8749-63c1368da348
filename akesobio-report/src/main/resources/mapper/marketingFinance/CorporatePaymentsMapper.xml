<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.CorporatePaymentsMapper">
    
    <resultMap type="CorporatePayments" id="CorporatePaymentsResult">
        <result property="singleNumber"    column="singleNumber"    />
        <result property="reimbursementPerson"    column="reimbursementPerson"    />
        <result property="expences"    column="expences"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="department"    column="department"    />
        <result property="receivingUnit"    column="receivingUnit"    />
        <result property="applicationDate"    column="applicationDate"    />
        <result property="feeType"    column="feeType"    />
        <result property="costCategory"    column="costCategory"    />
        <result property="totalAmount"    column="totalAmount"    />
        <result property="amountPaid"    column="amountPaid"    />
        <result property="paymentAmount"    column="paymentAmount"    />
        <result property="uploadInvoice"    column="uploadInvoice"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
    </resultMap>

    <sql id="selectCorporatePaymentsVo">
        select singleNumber, reimbursementPerson, expences, projectNumber, department, receivingUnit, applicationDate, feeType, costCategory, totalAmount, amountPaid, paymentAmount, uploadInvoice, documentStatus, currentSession from public_payment
    </sql>

    <select id="selectCorporatePaymentsList" parameterType="CorporatePayments" resultMap="CorporatePaymentsResult">
        <include refid="selectCorporatePaymentsVo"/>
        <where>  
            <if test="singleNumber != null  and singleNumber != ''"> and singleNumber like concat('%', #{singleNumber}, '%')</if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''"> and reimbursementPerson like concat('%', #{reimbursementPerson}, '%')</if>
            <if test="expences != null  and expences != ''"> and expences like concat('%', #{expences}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="receivingUnit != null  and receivingUnit != ''"> and receivingUnit like concat('%', #{receivingUnit}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="feeType != null  and feeType != ''"> and feeType = #{feeType}</if>
            <if test="costCategory != null  and costCategory != ''"> and costCategory = #{costCategory}</if>
            <if test="totalAmount != null  and totalAmount != ''"> and totalAmount = #{totalAmount}</if>
            <if test="amountPaid != null  and amountPaid != ''"> and amountPaid = #{amountPaid}</if>
            <if test="paymentAmount != null  and paymentAmount != ''"> and paymentAmount = #{paymentAmount}</if>
            <if test="uploadInvoice != null  and uploadInvoice != ''"> and uploadInvoice = #{uploadInvoice}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
        </where>
        order by applicationDate desc
    </select>
</mapper>