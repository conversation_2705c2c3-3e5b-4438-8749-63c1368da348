<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.PureSalesDataMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.PureSalesData" id="PureSalesDataResult">
        <result property="yearMonth"    column="yearMonth" />
        <result property="number"   column="number"  />
        <result property="salesArea"   column="salesArea"  />
    </resultMap>
    <select id="selectPureSalesData"  resultMap="PureSalesDataResult">
        select  b.yearMonth, b.number,b.salesArea
        from (
        select
        a.fd_3c48b9ec9ca926 as 'salesArea',
        a.fd_3c48b9d9aaa832 as 'yearMonth',
        a.fd_3c48b9cfcaafea as 'number'
        from ekp_cxsj1 a
        ) b
        <where>
            <if test="yearMonth != null and yearMonth !=''">and yearMonth like concat('%', #{yearMonth}, '%')</if>
            <if test="salesArea != null and salesArea !=''">and salesArea =#{salesArea} </if>
        </where>

    </select>
    <select id="selectPureSalesData1"  resultMap="PureSalesDataResult">
        select  b.yearMonth, b.number,b.salesArea
        from (
        select
        a.fd_3c48b9ec9ca926 as 'salesArea',
        a.fd_3c48b9d9aaa832 as 'yearMonth',
        a.fd_3c48b9cfcaafea as 'number'
        from ekp_cxsj1 a
        ) b
        <where>
            <if test="yearMonth != null and yearMonth !=''">and yearMonth like concat('%', #{yearMonth}, '%')</if>
        </where>

    </select>
    <select id="selectPureSalesData2"  resultMap="PureSalesDataResult">
        select  b.yearMonth, b.number,b.salesArea
        from (
        select
        a.fd_3c48b9ec9ca926 as 'salesArea',
        a.fd_3c48b9d9aaa832 as 'yearMonth',
        a.fd_3c48b9cfcaafea as 'number'
        from ekp_cxsj1 a
        ) b
        <where>
            <if test="yearMonth != null and yearMonth !=''">and yearMonth like concat('%', #{yearMonth}, '%')</if>
        </where>

    </select>
</mapper>
