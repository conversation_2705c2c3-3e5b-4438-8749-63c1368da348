<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.MDSControlMapper">
    <!-- 实体类 -->
    <resultMap type="MDSControl" id="MDSControlResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectMDSControl">
        SELECT id,
               region + sales_period AS regionSalesPeriod,
               region,
               sales_period              AS salesPeriod,
               apply_for                 AS applyFor,
               reimburse,
               reimburse_use             AS reimburseUse,
               apply_for - reimburse_use AS residue,
               create_date               AS createDate
        FROM MDS_limit_control
    </sql>

    <!-- 方法 -->
    <select id="queryMDSControlList" parameterType="MDSControl" resultMap="MDSControlResult">
        <include refid="selectMDSControl"/>
        <where>
            <if test="region != null and region != ''">and region like concat('%', #{region}, '%')</if>
            <if test="salesPeriod != null and salesPeriod != ''">and sales_period like concat('%', #{salesPeriod},
                '%')
            </if>
            <!--            <if test="startDate != null and startDate != ''">and tableFieldName &gt;= #{startDate}</if>-->
            <!--            <if test="endDate != null and endDate != ''">and tableFieldName &lt;= #{endDate}</if>-->
        </where>
    </select>
</mapper>