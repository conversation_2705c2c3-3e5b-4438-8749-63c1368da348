<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.MeetingLedgerMapper">
    
    <resultMap type="MeetingLedger" id="MeetingLedgerResult">
        <result property="formName"    column="formName"    />
        <result property="belongCompany"    column="belongCompany"    />
        <result property="documentNumber"    column="documentNumber"    />
        <result property="summarizeVoucherAmount"    column="summarizeVoucherAmount"    />
        <result property="oaOrderNumber"    column="oaOrderNumber"    />
        <result property="meetingApplicationNumber"    column="meetingApplicationNumber"    />
        <result property="reimbursementDepartment"    column="reimbursementDepartment"    />
        <result property="allocatedAmount"    column="allocatedAmount"    />
        <result property="reimbursementPersonnel"    column="reimbursementPersonnel"    />
        <result property="paymentRecipient"    column="paymentRecipient"    />
        <result property="startDate"    column="startDate"    />
        <result property="endDate"    column="endDate"    />
        <result property="meetingName"    column="meetingName"    />
        <result property="meetingType"    column="meetingType"    />
        <result property="meetingCategory"    column="meetingCategory"    />
        <result property="meetingFormat"    column="meetingFormat"    />
        <result property="meetingApplicationDepartment"    column="meetingApplicationDepartment"    />
        <result property="city"    column="city"    />
        <result property="signNumber"    column="signNumber"    />
        <result property="expertsNumber"    column="expertsNumber"    />
        <result property="invitation"    column="invitation"    />
        <result property="schedule"    column="schedule"    />
        <result property="attendanceSheet"    column="attendanceSheet"    />
        <result property="photo"    column="photo"    />
        <result property="applicationDate"    column="applicationDate"    />
        <result property="approvalTime"    column="approvalTime"    />
        <result property="targetTerminal"    column="targetTerminal"    />
        <result property="expenseType" column="expenseType"/>
        <result property="serviceType" column="serviceType"/>
    </resultMap>

    <sql id="selectMeetingLedgerVo">
        select formName, belongCompany, documentNumber, summarizeVoucherAmount, oaOrderNumber, meetingApplicationNumber, reimbursementDepartment, allocatedAmount, reimbursementPersonnel, paymentRecipient, startDate, endDate, meetingName, meetingType, meetingCategory, meetingFormat, meetingApplicationDepartment, city, signNumber, expertsNumber, invitation, schedule, attendanceSheet, photo, applicationDate, approvalTime,targetTerminal,serviceType,expenseType from meeting_ledger
    </sql>

    <select id="selectMeetingLedgerList" parameterType="MeetingLedger" resultMap="MeetingLedgerResult">
        <include refid="selectMeetingLedgerVo"/>
        <where>  
            <if test="formName != null  and formName != ''"> and formName like concat('%', #{formName}, '%')</if>
            <if test="belongCompany != null  and belongCompany != ''"> and belongCompany like concat('%', #{belongCompany}, '%')</if>
            <if test="documentNumber != null  and documentNumber != ''"> and documentNumber like concat('%', #{documentNumber}, '%')</if>
            <if test="summarizeVoucherAmount != null  and summarizeVoucherAmount != ''"> and summarizeVoucherAmount = #{summarizeVoucherAmount}</if>
            <if test="oaOrderNumber != null  and oaOrderNumber != ''"> and oaOrderNumber like concat('%', #{oaOrderNumber}, '%')</if>
            <if test="meetingApplicationNumber != null  and meetingApplicationNumber != ''"> and meetingApplicationNumber like concat('%', #{meetingApplicationNumber}, '%')</if>
            <if test="reimbursementDepartment != null  and reimbursementDepartment != ''"> and reimbursementDepartment like concat('%', #{reimbursementDepartment}, '%')</if>
            <if test="allocatedAmount != null  and allocatedAmount != ''"> and allocatedAmount = #{allocatedAmount}</if>
            <if test="reimbursementPersonnel != null  and reimbursementPersonnel != ''"> and reimbursementPersonnel like concat('%', #{reimbursementPersonnel}, '%')</if>
            <if test="paymentRecipient != null  and paymentRecipient != ''"> and paymentRecipient like concat('%', #{paymentRecipient}, '%')</if>
            <if test="startDate != null "> and startDate = #{startDate}</if>
            <if test="endDate != null "> and endDate = #{endDate}</if>
            <if test="meetingName != null  and meetingName != ''"> and meetingName like concat('%', #{meetingName}, '%')</if>
            <if test="meetingType != null  and meetingType != ''"> and meetingType like concat('%', #{meetingType}, '%')</if>
            <if test="meetingCategory != null  and meetingCategory != ''"> and meetingCategory like concat('%', #{meetingCategory}, '%')</if>
            <if test="meetingFormat != null  and meetingFormat != ''"> and meetingFormat like concat('%', #{meetingFormat}, '%')</if>
            <if test="meetingApplicationDepartment != null  and meetingApplicationDepartment != ''"> and meetingApplicationDepartment like concat('%', #{meetingApplicationDepartment}, '%')</if>
            <if test="city != null  and city != ''"> and city like concat('%', #{city}, '%')</if>
            <if test="signNumber != null  and signNumber != ''"> and signNumber = #{signNumber}</if>
            <if test="expertsNumber != null  and expertsNumber != ''"> and expertsNumber = #{expertsNumber}</if>
            <if test="invitation != null  and invitation != ''"> and invitation = #{invitation}</if>
            <if test="schedule != null  and schedule != ''"> and schedule = #{schedule}</if>
            <if test="attendanceSheet != null  and attendanceSheet != ''"> and attendanceSheet = #{attendanceSheet}</if>
            <if test="photo != null  and photo != ''"> and photo = #{photo}</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''"> and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}</if>
        </where>
    </select>
</mapper>