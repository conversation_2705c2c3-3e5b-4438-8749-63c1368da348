<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.ShipmentAmountMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.ShipmentAmount" id="ShipmentAmountResult">
        <result property="salesArea" column="salesArea"/>
        <result property="amount" column="amount"/>
        <result property="invoicingDate" column="invoicingDate"/>
    </resultMap>
    <select id="selectShipmentAmount" resultMap="ShipmentAmountResult">
        select c.salesArea, c.amount,c.invoicingDate
        from
        (select
        a.fd_3b358e037b4f4e as 'salesArea',
        b.zhsje as 'amount',
        b.fkdat as 'invoicingDate'
        from
        ekp_contrast a
        left join SalesInvoiceReport b on b.kunag=a.fd_3b358de59698e0) c
        <where>
            <if test="salesArea != null and salesArea !=''">and salesArea =#{salesArea}</if>
            <if test="year != null and year !=''">and invoicingDate like concat('%', #{year}, '%')</if>
        </where>
    </select>
</mapper>
