<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.PatientDataMapper">
    <!-- 实体类  -->
    <resultMap type="PatientData" id="PatientDataResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectPatientDataList">
        SELECT
            a.id,
            a.patient_code,
            a.patient_name,
            a.sex,
            a.province,
            a.cancer_type,
            a.first_party_date,
            a.hospital_id,
            h.hospital_code,
            h.hospital_name,
            a.create_time,
            a.update_time
        FROM patient_data a
                 LEFT JOIN hospital_data h ON a.hospital_id = h.id
    </sql>
    
    <!-- 方法 -->
    <select id="queryPatientDataList" parameterType="PatientData" resultMap="PatientDataResult">
        <include refid="selectPatientDataList"/>
        <where>  
            <if test="patientCode != null and patientCode != ''"> and a.patient_code like concat('%', #{patientCode}, '%')</if>
            <if test="patientName != null and patientName != ''"> and a.patient_name like concat('%', #{patientName}, '%')</if>
            <if test="province != null and province != ''"> and a.province like concat('%', #{province}, '%')</if>
            <if test="cancerType != null and cancerType != ''"> and a.cancer_type like concat('%', #{cancerType}, '%')</if>
            <if test="hospitalId != null and hospitalId != ''"> and a.hospital_id like concat('%', #{hospitalId}, '%')</if>
            <if test="sex != null and sex != ''"> and a.sex like concat('%', #{sex}, '%')</if>

            <if test="startDate1 != null and startDate1 != ''">and a.first_party_date &gt;= #{startDate1}</if>
            <if test="endDate1 != null and endDate1 != ''">and a.first_party_date &lt;= #{endDate1}</if>

            <if test="startDate != null and startDate != ''">and a.create_time &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and a.create_time &lt;= #{endDate}</if>
            <if test="startDate2 != null and startDate2 != ''">and a.update_time &gt;= #{startDate2}</if>
            <if test="endDate2 != null and endDate2 != ''">and a.update_time &lt;= #{endDate2}</if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <!-- 根据id查询 -->
    <select id="queryPatientDataById" parameterType="String" resultType="PatientData">
        SELECT
            a.id,
            a.patient_code,
            a.patient_name,
            a.sex,
            a.province,
            a.cancer_type,
            a.first_party_date,
            a.hospital_id,
            h.hospital_code,
            h.hospital_name,
            a.create_time,
            a.update_time
        FROM patient_data a
                 LEFT JOIN hospital_data h ON a.hospital_id = h.id
        where a.id = #{id}
    </select>
    <!--根据编码查询-->
    <select id="checkPatientCodeUnique" parameterType="String" resultType="PatientData">
        SELECT
            a.id,
            a.patient_code,
            a.patient_name,
            a.sex,
            a.province,
            a.cancer_type,
            a.first_party_date,
            a.hospital_id,
            h.hospital_code,
            h.hospital_name,
            a.create_time,
            a.update_time
        FROM patient_data a
                 LEFT JOIN hospital_data h ON a.hospital_id = h.id
        where a.patient_code = #{patientCode}
    </select>
    <!-- 根据医院名称查询 -->
    <select id="checkPatientNameUnique" parameterType="String" resultType="PatientData">
        SELECT
            a.id,
            a.patient_code,
            a.patient_name,
            a.sex,
            a.province,
            a.cancer_type,
            a.first_party_date,
            a.hospital_id,
            h.hospital_code,
            h.hospital_name,
            a.create_time,
            a.update_time
        FROM patient_data a
                 LEFT JOIN hospital_data h ON a.hospital_id = h.id
        where a.patient_name = #{patientName}
    </select>
    <!-- 添加 -->
    <insert id="insertPatientData" useGeneratedKeys="true" parameterType="PatientData" keyProperty="id">
        INSERT INTO patient_data(
        <if test="patientCode != null and patientCode != ''">patient_code,</if>
        <if test="patientName != null and patientName != ''">patient_name,</if>
        <if test="province != null and province != ''">province,</if>
        <if test="cancerType != null and cancerType != ''">cancer_type,</if>
        <if test="firstPartyDate != null and firstPartyDate != ''">first_party_date,</if>
        <if test="sex != null and sex != ''">sex,</if>
        <if test="hospitalIds != null and hospitalIds.length > 0">hospital_id,</if>
        create_time,update_time
        )values

        <foreach collection="hospitalIds" item="e" separator="," >
            (
            <if test="patientCode != null and patientCode != ''">#{patientCode},</if>
            <if test="patientName != null and patientName != ''">#{patientName},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="cancerType != null and cancerType != ''">#{cancerType},</if>
            <if test="firstPartyDate != null and firstPartyDate != ''">#{firstPartyDate},</if>
            <if test="sex != null and sex != ''">#{sex},</if>
            <if test="hospitalIds != null and hospitalIds.length > 0">#{e},</if>
            getdate(),getdate()
            )
        </foreach>
<!--        (-->
<!--        <if test="patientCode != null and patientCode != ''">#{patientCode},</if>-->
<!--        <if test="patientName != null and patientName != ''">#{patientName},</if>-->
<!--        <if test="province != null and province != ''">#{province},</if>-->
<!--        <if test="cancerType != null and cancerType != ''">#{cancerType},</if>-->
<!--        <if test="firstPartyDate != null and firstPartyDate != ''">#{firstPartyDate},</if>-->
<!--        <if test="hospitalId != null and hospitalId != ''">#{hospitalId},</if>-->
<!--        getdate(),getdate()-->
<!--        )-->
    </insert>
    <!-- 根据id修改 -->
    <update id="updatePatientData" parameterType="PatientData">
        UPDATE patient_data
        <set>
            <if test="patientCode != null and patientCode != ''">patient_code = #{patientCode},</if>
            <if test="patientName != null and patientName != ''">patient_name = #{patientName},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="cancerType != null and cancerType != ''">cancer_type = #{cancerType},</if>
            <if test="firstPartyDate != null and firstPartyDate != ''">first_party_date = #{firstPartyDate},</if>
            <if test="hospitalId != null and hospitalId != ''">hospital_id = #{hospitalId},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            update_time = getdate()
        </set>
        where id = #{id}
    </update>
</mapper>