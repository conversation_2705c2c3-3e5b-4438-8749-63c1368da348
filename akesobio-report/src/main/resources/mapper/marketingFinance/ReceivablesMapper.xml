<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.ReceivablesMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.Receivables" id="ReceivablesResult">
        <result property="salesArea" column="salesArea"/>
        <result property="amount" column="amount"/>
        <result property="yearMonth" column="yearMonth"/>
    </resultMap>
    <select id="selectReceivables" resultMap="ReceivablesResult">
        select c.salesArea, c.amount,c.yearMonth
        from
        (select
        a.fd_3b358e037b4f4e as 'salesArea',
        b.gjahr as 'yearMonth',
        b.acytd_bal as 'amount'
        from
        ekp_contrast a
        left join CustomerBalanceReport b on b.kunnr = a.fd_3b358de59698e0) c
        <where>
            <if test="salesArea != null and salesArea !=''">and salesArea =#{salesArea}</if>
            <if test="year != null and year !=''">and yearMonth =#{year}</if>
        </where>
    </select>
</mapper>
