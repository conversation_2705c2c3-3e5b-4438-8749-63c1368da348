<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.TerminalSalesTrendMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.TerminalSalesTrend" id="TerminalSalesTrendResult">
        <result property="salesArea"   column="salesArea"  />
        <result property="yearMonth"   column="yearMonth"  />
        <result property="number"   column="number"  />
    </resultMap>
    <select id="selectTerminalSalesTrend"  resultMap="TerminalSalesTrendResult">
        select  b.yearMonth, b.number,b.salesArea
        from (
        select
        a.fd_3c48b9ec9ca926 as 'salesArea',
        a.fd_3c48b9d9aaa832 as 'yearMonth',
        a.fd_3c48b9cfcaafea as 'number'
        from ekp_cxsj1 a
        ) b
        /*select  b.yearMonth, b.number,b.salesArea
        from (
        select
        b.fd_3c48b9ec9ca926 as 'salesArea',
        b.fd_3c48b9d9aaa832 as 'yearMonth',
        b.fd_3c48b9cfcaafea as 'number'
        from ekp_cxsj a
        LEFT JOIN ekp_ekp_cxsj_b990df0a0c b on b.fd_parent_id=a.fd_id
        LEFT JOIN [dbo].[modeling_model_main] q ON a.fd_id = q.fd_id
        where
        q.doc_status !='10' or q.doc_status !='00'
        ) b*/

        <where>
            <if test="yearMonth != null  and yearMonth != ''">and yearMonth = #{yearMonth}</if>
            <if test="salesArea != null  and salesArea != ''">and salesArea = #{salesArea}</if>
        </where>
    </select>
</mapper>
