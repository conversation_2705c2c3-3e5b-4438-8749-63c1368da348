<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.CostBearingMapper">

    <resultMap type="CostBearing" id="CostBearingResult">
        <result property="tableName" column="tableName"/>
        <result property="OANumber" column="OANumber"/>
        <result property="applicationDate" column="applicationDate"/>
        <result property="applicant" column="applicant"/>
        <result property="applicantJobNumber" column="applicantJobNumber"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="currentSession" column="currentSession"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentProcessor" column="currentProcessor"/>
        <result property="approvalTime" column="approvalTime"/>
        <result property="expenses" column="expenses"/>
        <result property="department" column="department"/>
        <result property="meetingFormat" column="meetingFormat"/>
        <result property="meetingStartTime" column="meetingStartTime"/>
        <result property="meetingEndTime" column="meetingEndTime"/>
        <result property="conferenceCity" column="conferenceCity"/>
        <result property="meetingType" column="meetingType"/>
        <result property="meetingCategory" column="meetingCategory"/>
        <result property="totalReimbursementAmount" column="totalReimbursementAmount"/>
        <result property="actualSalesQuantity" column="actualSalesQuantity"/>
        <result property="period" column="period"/>
        <result property="thirdLevelDepartment" column="thirdLevelDepartment"/>
        <result property="secondaryDepartment" column="secondaryDepartment"/>
        <result property="endLevelDepartment" column="endLevelDepartment"/>
        <result property="departmentAllocationAmount" column="departmentAllocationAmount"/>
        <result property="isWhether" column="isWhether"/>
        <result property="loanNumber" column="loanNumber"/>
        <result property="loanAmount" column="loanAmount"/>
        <result property="conferenceApplicationNo" column="conferenceApplicationNo"/>
        <result property="meetingObjective" column="meetingObjective"/>
        <result property="applicationNumber" column="applicationNumber"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="indication" column="indication"/>
        <result property="pharmacyDealer" column="pharmacyDealer"/>
        <result property="costDescription" column="costDescription"/>
        <result property="costType2024" column="costType2024"/>
        <result property="businessType2024" column="businessType2024"/>
        <result property="contractApprovalForm" column="contractApprovalForm"/>
        <result property="corporateCooperationUnit" column="corporateCooperationUnit"/>
        <result property="prepaidVoucherNumber" column="prepaidVoucherNumber"/>
        <result property="businessApprovalTime" column="businessApprovalTime"/>
        <result property="availableCredit" column="availableCredit"/>
        <result property="projectNumber" column="projectNumber"/>
    </resultMap>

    <sql id="selectCostBearingVo">
        select tableName,
               OANumber,
               applicationDate,
               applicant,
               applicantJobNumber,
               reimbursementPerson,
               currentSession,
               documentStatus,
               currentProcessor,
               approvalTime,
               expenses,
               department,
               meetingFormat,
               meetingStartTime,
               meetingEndTime,
               conferenceCity,
               meetingType,
               meetingCategory,
               totalReimbursementAmount,
               actualSalesQuantity,
               period,
               thirdLevelDepartment,
               secondaryDepartment,
               endLevelDepartment,
               departmentAllocationAmount,
               isWhether,
               loanNumber,
               loanAmount,
               conferenceApplicationNo,
               meetingObjective,
               applicationNumber,
               sapPayNumber,
               indication,
               pharmacyDealer,
               costDescription,
               costType2024,
               businessType2024,
               contractApprovalForm,
               corporateCooperationUnit,
               prepaidVoucherNumber,
               businessApprovalTime,
               availableCredit,
               projectNumber
        from cost_bearing
    </sql>

    <select id="selectCostBearingList" parameterType="CostBearing" resultMap="CostBearingResult">
        <include refid="selectCostBearingVo"/>
        <where>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="OANumber != null  and OANumber != ''">and OANumber like concat('%', #{OANumber}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''">
                and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="applicantJobNumber != null  and applicantJobNumber != ''">and applicantJobNumber like concat('%',
                #{applicantJobNumber}, '%')
            </if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''">and reimbursementPerson like
                concat('%', #{reimbursementPerson}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentProcessor != null  and currentProcessor != ''">and currentProcessor = #{currentProcessor}
            </if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != '' and params.endApprovalTime != null and params.endApprovalTime != ''">
                and approvalTime between #{params.beginApprovalTime} and #{params.endApprovalTime}
            </if>
            <if test="expenses != null  and expenses != ''">and expenses like concat('%', #{expenses}, '%')</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="meetingFormat != null  and meetingFormat != ''">and meetingFormat like concat('%',
                #{meetingFormat}, '%')
            </if>
            <if test="meetingStartTime != null  and meetingStartTime != ''">and meetingStartTime = #{meetingStartTime}
            </if>
            <if test="meetingEndTime != null  and meetingEndTime != ''">and meetingEndTime = #{meetingEndTime}</if>
            <if test="conferenceCity != null  and conferenceCity != ''">and conferenceCity = #{conferenceCity}</if>
            <if test="meetingType != null  and meetingType != ''">and meetingType = #{meetingType}</if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory = #{meetingCategory}</if>
            <if test="totalReimbursementAmount != null  and totalReimbursementAmount != ''">and totalReimbursementAmount
                = #{totalReimbursementAmount}
            </if>
            <if test="actualSalesQuantity != null  and actualSalesQuantity != ''">and actualSalesQuantity =
                #{actualSalesQuantity}
            </if>
            <if test="period != null  and period != ''">and period = #{period}</if>
            <if test="thirdLevelDepartment != null  and thirdLevelDepartment != ''">and thirdLevelDepartment =
                #{thirdLevelDepartment}
            </if>
            <if test="secondaryDepartment != null  and secondaryDepartment != ''">and secondaryDepartment =
                #{secondaryDepartment}
            </if>
            <if test="endLevelDepartment != null  and endLevelDepartment != ''">and endLevelDepartment =
                #{endLevelDepartment}
            </if>
            <if test="departmentAllocationAmount != null  and departmentAllocationAmount != ''">and
                departmentAllocationAmount = #{departmentAllocationAmount}
            </if>
            <if test="isWhether != null  and isWhether != ''">and isWhether = #{isWhether}</if>
            <if test="loanNumber != null  and loanNumber != ''">and loanNumber like concat('%', #{loanNumber}, '%')</if>
            <if test="loanAmount != null  and loanAmount != ''">and loanAmount = #{loanAmount}</if>
            <if test="conferenceApplicationNo != null  and conferenceApplicationNo != ''">and conferenceApplicationNo
                like concat('%', #{conferenceApplicationNo}, '%')
            </if>
            <if test="meetingObjective != null  and meetingObjective != ''">and meetingObjective like concat('%',
                #{meetingObjective}, '%')
            </if>
            <if test="applicationNumber !=null and applicationNumber!=''">and applicationNumber like
                concat('%',#{applicationNumber},'%')
            </if>
            <if test="costType2024 != null  and costType2024 != ''">and costType2024 like concat('%', #{costType2024},
                '%')
            </if>
            <if test="businessType2024 != null  and businessType2024 != ''">and businessType2024 like concat('%',
                #{businessType2024}, '%')
            </if>
            <if test="contractApprovalForm != null  and contractApprovalForm != ''">and contractApprovalForm like
                concat('%', #{contractApprovalForm}, '%')
            </if>
            <if test="corporateCooperationUnit != null  and corporateCooperationUnit != ''">and corporateCooperationUnit
                like concat('%', #{corporateCooperationUnit}, '%')
            </if>
            <if test="params.beginBusinessApprovalTime != null and params.beginBusinessApprovalTime != '' and params.endBusinessApplicationDate != null and params.endBusinessApplicationDate != ''">
                and businessApprovalTime between #{params.beginBusinessApprovalTime} and
                #{params.endBusinessApplicationDate}
            </if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber
                like concat('%', #{projectNumber}, '%')
            </if>
        </where>
        order by OANumber desc
    </select>
</mapper>