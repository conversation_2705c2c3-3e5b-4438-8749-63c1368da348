<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.CommunicationExpensesMapper">
    
    <resultMap type="CommunicationExpenses" id="CommunicationExpensesResult">
        <result property="singleNumber"    column="singleNumber"    />
        <result property="reimbursementPerson"    column="reimbursementPerson"    />
        <result property="department"    column="department"    />
        <result property="applicationDate"    column="applicationDate"    />
        <result property="expences"    column="expences"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="totalAmount"    column="totalAmount"    />
        <result property="feeType"    column="feeType"    />
        <result property="feeDescription"    column="feeDescription"    />
        <result property="feeDepartment"    column="feeDepartment"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="targetTerminal"    column="targetTerminal"    />
    </resultMap>

    <sql id="selectCommunicationExpensesVo">
        select singleNumber, reimbursementPerson, department, applicationDate, expences, projectNumber, totalAmount, feeType, feeDescription, feeDepartment, documentStatus, currentSession, targetTerminal from communication_fee
    </sql>

    <select id="selectCommunicationExpensesList" parameterType="CommunicationExpenses" resultMap="CommunicationExpensesResult">
        <include refid="selectCommunicationExpensesVo"/>
        <where>  
            <if test="singleNumber != null  and singleNumber != ''"> and singleNumber like concat('%', #{singleNumber}, '%')</if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''"> and reimbursementPerson like concat('%', #{reimbursementPerson}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="expences != null  and expences != ''"> and expences like concat('%', #{expences}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber = #{projectNumber}</if>
            <if test="totalAmount != null  and totalAmount != ''"> and totalAmount = #{totalAmount}</if>
            <if test="feeType != null  and feeType != ''"> and feeType like concat('%', #{feeType}, '%')</if>
            <if test="feeDescription != null  and feeDescription != ''"> and feeDescription = #{feeDescription}</if>
            <if test="feeDepartment != null  and feeDepartment != ''"> and feeDepartment like concat('%', #{feeDepartment}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="targetTerminal != null  and targetTerminal != ''"> and targetTerminal like concat('%', #{targetTerminal}, '%')</if>
        </where>
        order by applicationDate desc
    </select>
</mapper>