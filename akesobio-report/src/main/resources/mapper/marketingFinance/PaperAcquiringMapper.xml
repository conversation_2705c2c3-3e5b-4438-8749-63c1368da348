<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.PaperAcquiringMapper">

    <resultMap type="com.akesobio.report.marketingFinance.domain.PaperAcquiring" id="PaperAcquiringResult">
        <result property="id"    column="id"    />
        <result property="reimbursementCategory"    column="reimbursementCategory"    />
        <result property="oaNum"    column="oaNum"    />
        <result property="userNum"    column="userNum"    />
        <result property="userName"    column="userName"    />
        <result property="department"    column="department"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="companyName"    column="companyName"    />
        <result property="companyCode"    column="companyCode"    />
        <result property="reimbursementAmount"    column="reimbursementAmount"    />
        <result property="expressNum"    column="expressNum"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectPaperAcquiringVo">
        select id, reimbursementCategory, oaNum, userNum, userName, department, documentStatus, currentSession, companyName, companyCode, reimbursementAmount, expressNum, type from paper_acquiring
    </sql>

        <select id="selectPaperAcquiringList" parameterType="com.akesobio.report.marketingFinance.domain.PaperAcquiring" resultMap="PaperAcquiringResult">
        select reimbursementCategory, oaNum, userNum, userName, department, documentStatus, currentSession, companyName, companyCode, reimbursementAmount, expressNum, type from conference_fees
        <where>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''"> and reimbursementCategory like concat('%', #{reimbursementCategory}, '%')</if>
            <if test="oaNum != null  and oaNum != ''"> and oaNum like concat('%', #{oaNum}, '%')</if>
            <if test="userNum != null  and userNum != ''"> and userNum like concat('%', #{userNum}, '%')</if>
            <if test="userName != null  and userName != ''"> and userName like concat('%', #{userName}, '%')</if>
            <if test="companyName != null  and companyName != ''"> and companyName like concat('%', #{companyName}, '%')</if>
            <if test="expressNum != null  and expressNum != ''"> and expressNum like concat('%', #{expressNum}, '%')</if>
        </where>
        UNION
        select reimbursementCategory, oaNum, userNum, userName, department, documentStatus, currentSession, companyName, companyCode, reimbursementAmount, expressNum, type from expenses_view
        <where>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''"> and reimbursementCategory like concat('%', #{reimbursementCategory}, '%')</if>
            <if test="oaNum != null  and oaNum != ''"> and oaNum like concat('%', #{oaNum}, '%')</if>
            <if test="userNum != null  and userNum != ''"> and userNum like concat('%', #{userNum}, '%')</if>
            <if test="userName != null  and userName != ''"> and userName like concat('%', #{userName}, '%')</if>
            <if test="companyName != null  and companyName != ''"> and companyName like concat('%', #{companyName}, '%')</if>
            <if test="expressNum != null  and expressNum != ''"> and expressNum like concat('%', #{expressNum}, '%')</if>
        </where>
        UNION
        select reimbursementCategory, oaNum, userNum, userName, department, documentStatus, currentSession, companyName, companyCode, reimbursementAmount, expressNum, type from travel_view
        <where>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''"> and reimbursementCategory like concat('%', #{reimbursementCategory}, '%')</if>
            <if test="oaNum != null  and oaNum != ''"> and oaNum like concat('%', #{oaNum}, '%')</if>
            <if test="userNum != null  and userNum != ''"> and userNum like concat('%', #{userNum}, '%')</if>
            <if test="userName != null  and userName != ''"> and userName like concat('%', #{userName}, '%')</if>
            <if test="companyName != null  and companyName != ''"> and companyName like concat('%', #{companyName}, '%')</if>
            <if test="expressNum != null  and expressNum != ''"> and expressNum like concat('%', #{expressNum}, '%')</if>
        </where>
        UNION
        select reimbursementCategory, oaNum, userNum, userName, department, documentStatus, currentSession, companyName, companyCode, reimbursementAmount, expressNum, type from corporate_payments_view
        <where>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''"> and reimbursementCategory like concat('%', #{reimbursementCategory}, '%')</if>
            <if test="oaNum != null  and oaNum != ''"> and oaNum like concat('%', #{oaNum}, '%')</if>
            <if test="userNum != null  and userNum != ''"> and userNum like concat('%', #{userNum}, '%')</if>
            <if test="userName != null  and userName != ''"> and userName like concat('%', #{userName}, '%')</if>
            <if test="companyName != null  and companyName != ''"> and companyName like concat('%', #{companyName}, '%')</if>
            <if test="expressNum != null  and expressNum != ''"> and expressNum like concat('%', #{expressNum}, '%')</if>
        </where>
        UNION
        select reimbursementCategory, oaNum, userNum, userName, department, documentStatus, currentSession, companyName, companyCode, reimbursementAmount, expressNum, type from loan_note_view
        <where>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''"> and reimbursementCategory like concat('%', #{reimbursementCategory}, '%')</if>
            <if test="oaNum != null  and oaNum != ''"> and oaNum like concat('%', #{oaNum}, '%')</if>
            <if test="userNum != null  and userNum != ''"> and userNum like concat('%', #{userNum}, '%')</if>
            <if test="userName != null  and userName != ''"> and userName like concat('%', #{userName}, '%')</if>
            <if test="companyName != null  and companyName != ''"> and companyName like concat('%', #{companyName}, '%')</if>
            <if test="expressNum != null  and expressNum != ''"> and expressNum like concat('%', #{expressNum}, '%')</if>
        </where>
        UNION
        select reimbursementCategory, oaNum, userNum, userName, department, documentStatus, currentSession, companyName, companyCode, reimbursementAmount, expressNum, type from meeting_loan_notes_view
        <where>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''"> and reimbursementCategory like concat('%', #{reimbursementCategory}, '%')</if>
            <if test="oaNum != null  and oaNum != ''"> and oaNum like concat('%', #{oaNum}, '%')</if>
            <if test="userNum != null  and userNum != ''"> and userNum like concat('%', #{userNum}, '%')</if>
            <if test="userName != null  and userName != ''"> and userName like concat('%', #{userName}, '%')</if>
            <if test="companyName != null  and companyName != ''"> and companyName like concat('%', #{companyName}, '%')</if>
            <if test="expressNum != null  and expressNum != ''"> and expressNum like concat('%', #{expressNum}, '%')</if>
        </where>
    </select>

</mapper>