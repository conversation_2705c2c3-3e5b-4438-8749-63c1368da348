<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.DeliveryReceiptMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.DeliveryReceipt" id="DeliveryReceiptResult">
        <result property="dealerCode"    column="dealerCode" />
        <result property="area"   column="area"  />
        <result property="invoicingDate"   column="invoicingDate"  />
        <result property="amount"   column="amount"  />
    </resultMap>
    <select id="selectDeliveryReceiptList"  resultMap="DeliveryReceiptResult">
        select
        a.fd_3b358de59698e0 as 'dealerCode',--经销商编码
        a.fd_3b358e037b4f4e as 'area', --区域
        b.fkdat as 'invoicingDate',--开票日期
        b.zxsjg as 'amount' --含税总金额
        from
        ekp_contrast a
        left join SalesInvoiceReport b on b.kunag=a.fd_3b358de59698e0
    </select>
</mapper>
