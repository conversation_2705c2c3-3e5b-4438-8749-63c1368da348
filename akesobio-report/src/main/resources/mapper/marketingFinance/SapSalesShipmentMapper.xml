<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.SapSalesShipmentMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.SapSalesShipment" id="SapSalesShipmentResult">
        <result property="inventoryDate" column="inventoryDate"/>
        <result property="salesArea" column="salesArea"/>
        <result property="number" column="number"/>
        <result property="amount" column="amount"/>
    </resultMap>
    <select id="selectSapSalesShipment" resultMap="SapSalesShipmentResult">
        select c.salesArea, c.amount,c.inventoryDate,c.number
        from
        (select
        a.fd_3b358e037b4f4e as 'salesArea',
        b.wadat_ist as 'inventoryDate',
        b.fklmg as 'number',
        b.zhsje as 'amount'
        from
        ekp_contrast a
        left join SalesInvoiceReport b on b.kunag=a.fd_3b358de59698e0
        where (b.vtext='发票' or b.vtext='退货贷记') and b.fksto='') c
        <where>
            <if test="year != null and year !=''">and inventoryDate like concat('%',#{year},'%')</if>
        </where>
    </select>
</mapper>
