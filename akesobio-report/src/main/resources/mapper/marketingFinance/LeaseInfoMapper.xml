<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.LeaseInfoMapper">
    
    <resultMap type="LeaseInfo" id="LeaseInfoResult">
        <result property="id"    column="id"    />
        <result property="month"    column="month"    />
        <result property="assetCode"    column="asset_code"    />
        <result property="assetName"    column="asset_name"    />
        <result property="orgName"    column="org_name"    />
        <result property="orgCode"    column="org_code"    />
        <result property="voucherCode"    column="voucher_code"    />
        <result property="monthlyDiscountRate"    column="monthly_discount_rate"    />
        <result property="periodCount"    column="period_count"    />
        <result property="usageRightAssert"    column="usage_right_assert"    />
        <result property="unacknowledgedFinancialCharges"    column="unacknowledged_financial_charges"    />
        <result property="leaseLiabilities"    column="lease_liabilities"    />
        <result property="balanceBegin"    column="balance_begin"    />
        <result property="interest"    column="interest"    />
        <result property="paymentAmount"    column="payment_amount"    />
        <result property="balanceEnd"    column="balance_end"    />
        <result property="paymentAmountMonthly"    column="payment_amount_monthly"    />
        <result property="processNumber"    column="process_number"    />
        <result property="applicant"    column="applicant"    />
    </resultMap>

    <sql id="selectLeaseInfoVo">
        select id, month, asset_code, asset_name, org_name, org_code, voucher_code, monthly_discount_rate, period_count, usage_right_assert, unacknowledged_financial_charges, lease_liabilities, balance_begin, interest, payment_amount, balance_end, payment_amount_monthly, process_number, applicant from LeaseInfoMD
    </sql>

    <select id="selectLeaseInfoList" parameterType="LeaseInfo" resultMap="LeaseInfoResult">
        <include refid="selectLeaseInfoVo"/>
        <where>  
            <if test="month != null  and month != ''"> and month like concat('%', #{month}, '%')</if>
            <if test="assetCode != null  and assetCode != ''"> and asset_code like concat('%', #{assetCode}, '%')</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="processNumber != null  and processNumber != ''"> and process_number like concat('%', #{processNumber}, '%')</if>
        </where>
    </select>
</mapper>