<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.YearMonthExpenditureMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.YearMonthExpenditure" id="YearMonthExpenditureResult">
        <result property="yearMonth" column="yearMonth"/>
        <result property="expenseCategory" column="expenseCategory"/>
        <result property="totalAmount" column="totalAmount"/>
        <result property="salesArea" column="salesArea"/>
        <result property="percentage" column="percentage"/>
    </resultMap>

    <select id="selectYearMonthExpenditure" resultMap="YearMonthExpenditureResult">
        select  b.year<PERSON><PERSON><PERSON>,  b.expenseCategory,  b.totalAmount,b.salesArea
        from (
        select
        a.fd_3c3e72c3556d92 as 'yearMonth',
        a.fd_3c3e7301b81e0a as 'expenseCategory',
        a.fd_3c3e72e32615f0 as 'totalAmount',
        a.fd_3c496325bf2490 as 'salesArea'
        from ekp_year_month_cost a
        where a.fd_3c3e734fc9a03e = '启用' and a.fd_3c56c7c676f628= '实际') b
        <where>
            <if test="yearMonth != null and yearMonth !=''">and yearMonth like concat('%', #{yearMonth}, '%')</if>
            <if test="expenseCategory != null and expenseCategory !=''">and expenseCategory =#{expenseCategory} </if>
        </where>
    </select>

    <select id="selectAreaYearExpenditure" resultMap="YearMonthExpenditureResult">
        select  b.yearMonth,  b.expenseCategory,  b.totalAmount,b.salesArea
        from (
        select
        a.fd_3c3e72c3556d92 as 'yearMonth',
        a.fd_3c3e7301b81e0a as 'expenseCategory',
        a.fd_3c3e72e32615f0 as 'totalAmount',
        a.fd_3c496325bf2490 as 'salesArea'
        from ekp_year_month_cost a
        where a.fd_3c3e734fc9a03e = '启用' and a.fd_3c56c7c676f628= '实际') b
        <where>
            <if test="yearMonth != null and yearMonth !=''">and yearMonth like concat('%', #{yearMonth}, '%')</if>
            <if test="salesArea != null and salesArea !=''">and salesArea =#{salesArea} </if>
        </where>
    </select>

    <select id="selectYearMonthExpenditure1" resultMap="YearMonthExpenditureResult">
        select  b.yearMonth,  b.expenseCategory,  b.totalAmount,b.salesArea,b.percentage
        from (
        select
        a.fd_3c3e72c3556d92 as 'yearMonth',
        a.fd_3c3e7301b81e0a as 'expenseCategory',
        a.fd_3c3e72e32615f0 as 'totalAmount',
        a.fd_3c496325bf2490 as 'salesArea',
        a.fd_3c56ca4247efc0 as 'percentage'
        from ekp_year_month_cost a
        where a.fd_3c3e734fc9a03e = '启用' and a.fd_3c56c7c676f628= '实际费率' and a.fd_3c56c786321bd6='2022') b
        <where>
            <if test="expenseCategory != null and expenseCategory !=''">and expenseCategory =#{expenseCategory} </if>
        </where>
    </select>

    <select id="selectYearMonthExpenditure2" resultMap="YearMonthExpenditureResult">
        select  b.yearMonth,  b.expenseCategory,  b.totalAmount,b.salesArea,b.percentage
        from (
        select
        a.fd_3c3e72c3556d92 as 'yearMonth',
        a.fd_3c3e7301b81e0a as 'expenseCategory',
        a.fd_3c3e72e32615f0 as 'totalAmount',
        a.fd_3c496325bf2490 as 'salesArea',
        a.fd_3c56ca4247efc0 as 'percentage'
        from ekp_year_month_cost a
        where a.fd_3c3e734fc9a03e = '启用' and a.fd_3c56c7c676f628= '目标费率' and a.fd_3c56c786321bd6='2023') b
        <where>
            <if test="expenseCategory != null and expenseCategory !=''">and expenseCategory =#{expenseCategory} </if>
        </where>
    </select>

</mapper>
