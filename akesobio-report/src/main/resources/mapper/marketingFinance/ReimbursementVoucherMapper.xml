<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.ReimbursementVoucherMapper">
    
    <resultMap type="ReimbursementVoucher" id="ReimbursementVoucherResult">
        <result property="applicationDate"    column="applicationDate"    />
        <result property="singleNumber"    column="singleNumber"    />
        <result property="department"    column="department"    />
        <result property="reimbursementPerson"    column="reimbursementPerson"    />
        <result property="expenses"    column="expenses"    />
        <result property="actualReimbursementAmount"    column="actualReimbursementAmount"    />
        <result property="whether"    column="whether"    />
        <result property="sapPayNumber"    column="sapPayNumber"    />
        <result property="sapCredentialPushTime"    column="sapCredentialPushTime"    />
        <result property="sapDocumentYear"    column="sapDocumentYear"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="tableName"    column="tableName"    />
        <result property="documentStatus"    column="documentStatus"    />
    </resultMap>

    <sql id="selectReimbursementVoucherVo">
        select applicationDate, singleNumber, department, reimbursementPerson, expenses, actualReimbursementAmount, whether, sapPayNumber, sapCredentialPushTime, sapDocumentYear, currentSession, tableName, documentStatus from reimbursement_voucher
    </sql>

    <select id="selectReimbursementVoucherList" parameterType="ReimbursementVoucher" resultMap="ReimbursementVoucherResult">
        <include refid="selectReimbursementVoucherVo"/>
        <where>  
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="singleNumber != null  and singleNumber != ''"> and singleNumber like concat('%', #{singleNumber}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''"> and reimbursementPerson like concat('%', #{reimbursementPerson}, '%')</if>
            <if test="expenses != null  and expenses != ''"> and expenses like concat('%', #{expenses}, '%')</if>
            <if test="actualReimbursementAmount != null  and actualReimbursementAmount != ''"> and actualReimbursementAmount = #{actualReimbursementAmount}</if>
            <if test="whether != null  and whether != ''"> and whether = #{whether}</if>
            <if test="sapPayNumber != null  and sapPayNumber != ''"> and sapPayNumber like concat('%', #{sapPayNumber}, '%')</if>
            <if test="params.beginSapCredentialPushTime != null and params.beginSapCredentialPushTime != '' and params.endSapCredentialPushTime != null and params.endSapCredentialPushTime != ''"> and sapCredentialPushTime between #{params.beginSapCredentialPushTime} and #{params.endSapCredentialPushTime}</if>
            <if test="sapDocumentYear != null  and sapDocumentYear != ''"> and sapDocumentYear like concat('%', #{sapDocumentYear}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="tableName != null  and tableName != ''"> and tableName like concat('%', #{tableName}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
        </where>
        order by applicationDate desc
    </select>
</mapper>