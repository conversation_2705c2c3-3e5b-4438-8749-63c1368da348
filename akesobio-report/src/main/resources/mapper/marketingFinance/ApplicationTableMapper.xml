<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.ApplicationTableMapper">
    <!-- 实体类  -->
    <resultMap type="ApplicationTable" id="ApplicationTableResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectApplicationTableList">
        SELECT id,
               formName,
               oddNumber,
               applicationDate,
               applicationName,
               jobNumber,
               documentStatus,
               currentSession,
               department,
--                thirdLevelDepartment,
--                secondaryDepartment,
--                endLevelDepartment,
               costBelong,
               companyCode,
               costType,
               businessType,
               meetingBegin,
               meetingEnd,
               meetingName,
               meetingCity,
               meetingNumber,
               expertNumber,
               costDepartment,
               shareAmounts,
               amounts,
               businessDate,
               reimbursement,
               targetTerminal
        FROM application_Table
    </sql>

    <!-- 方法 -->
    <select id="queryApplicationTableList" parameterType="ApplicationTable" resultMap="ApplicationTableResult">
        <include refid="selectApplicationTableList"/>
        <where>
            <if test="applicationName != null and applicationName != ''">and applicationName like concat('%',
                #{applicationName}, '%')
            </if>
            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
            <if test="startBusinessDate != null and startBusinessDate != ''">and businessDate &gt;=
                #{startBusinessDate}
            </if>
            <if test="endBusinessDate != null and endBusinessDate != ''">and businessDate &lt;= #{endBusinessDate}</if>
            <if test="formName != null and formName != ''">and formName like concat('%', #{formName}, '%')</if>
            <if test="oddNumber != null and oddNumber != ''">and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="currentSession != null and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="documentStatus != null and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="reimbursement != null and reimbursement != ''">and reimbursement like concat('%',
                #{reimbursement}, '%')
            </if>
        </where>
        ORDER BY applicationDate DESC
    </select>
</mapper>