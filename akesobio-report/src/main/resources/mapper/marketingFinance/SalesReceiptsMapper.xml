<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.SalesReceiptsMapper">

    <resultMap type="SalesReceipts" id="SalesReceiptsResult">
        <result property="businessPartnerCode" column="businessPartnerCode"/>
        <result property="businessPartnerName" column="businessPartnerName"/>
        <result property="businessPartnerLevel" column="businessPartnerLevel"/>
        <result property="salesGroup" column="salesGroup"/>
        <result property="salesRegion" column="salesRegion"/>
        <result property="businessManager" column="businessManager"/>
        <result property="transactionDate" column="transactionDate"/>
        <result property="transactionAmount" column="transactionAmount"/>
        <result property="area" column="area"/>
    </resultMap>

    <sql id="selectSalesReceiptsVo">
        select businessPartnerCode,
               businessPartnerName,
               businessPartnerLevel,
               salesGroup,
               salesRegion,
               businessManager,
               transactionDate,
               transactionAmount,
               area
        from sales_collection_query
    </sql>

    <select id="selectSalesReceiptsList" parameterType="SalesReceipts" resultMap="SalesReceiptsResult">
        <include refid="selectSalesReceiptsVo"/>
        <where>
            <if test="businessPartnerCode != null  and businessPartnerCode != ''">and businessPartnerCode like
                concat('%', #{businessPartnerCode}, '%')
            </if>
            <if test="businessPartnerName != null  and businessPartnerName != ''">and businessPartnerName like
                concat('%', #{businessPartnerName}, '%')
            </if>
            <if test="businessPartnerLevel != null  and businessPartnerLevel != ''">and businessPartnerLevel like
                concat('%', #{businessPartnerLevel}, '%')
            </if>
            <if test="salesRegion != null  and salesRegion != ''">and salesRegion like concat('%', #{salesRegion},
                '%')
            </if>
            <if test="businessManager != null  and businessManager != ''">and businessManager = #{businessManager}</if>
            <if test="params.beginTransactionDate != null and params.beginTransactionDate != '' and params.endTransactionDate != null and params.endTransactionDate != ''">
                and transactionDate between #{params.beginTransactionDate} and #{params.endTransactionDate}
            </if>
            <if test="transactionAmount != null  and transactionAmount != ''">and transactionAmount =
                #{transactionAmount}
            </if>
            <if test="area != null  and area != ''">and area like concat('%', #{area}, '%')</if>
            <if test="objectList !=null and objectList.size()>0 ">
                and salesGroup in
                <foreach collection="objectList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by transactionDate desc
    </select>
</mapper>