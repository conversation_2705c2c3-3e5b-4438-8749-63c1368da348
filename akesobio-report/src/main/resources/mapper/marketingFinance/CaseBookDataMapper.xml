<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.CaseBookDataMapper">
    <!-- 实体类  -->
    <resultMap type="CaseBookData" id="CaseBookDataResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectCaseBookDataList">
        SELECT
            a.id,
            a.terminal,
            a.product,
            a.hospital_id,
            a.doctor_id,
            a.patient_id,
            a.treatmentNumber,
            a.area,
            a.scheme,
            a.drugName,
            a.medicationDosage,
            a.medicationCycle,
            a.outcome,
            a.isPD,
            a.isNP,
            a.buyingMedicineNumber,
            a.buyingMedicineWay,
            a.notes1,
            a.notes2,
            h.hospital_name,
            d.doctor_name,
            d.position,
            d.department,
            p.patient_name,
            p.sex,
            p.cancer_type,
            p.cancer_subspecies,
            a.create_time,
            a.update_time
        FROM caseBook_data a
                 LEFT JOIN hospital_data h ON a.hospital_id = h.id
                 LEFT JOIN doctor_data d ON a.doctor_id = d.id
                 LEFT JOIN patient_data p ON a.patient_id = p.id
    </sql>
    
    <!-- 方法 -->
    <select id="queryDataList" parameterType="CaseBookData" resultMap="CaseBookDataResult">
        <include refid="selectCaseBookDataList"/>
        <where>  
            <if test="terminal != null and terminal != ''"> and a.terminal like concat('%', #{terminal}, '%')</if>
            <if test="product != null and product != ''"> and a.product like concat('%', #{product}, '%')</if>
            <if test="treatmentNumber != null and treatmentNumber != ''"> and a.treatmentNumber like concat('%', #{treatmentNumber}, '%')</if>
            <if test="area != null and area != ''"> and a.area like concat('%', #{area}, '%')</if>
            <if test="scheme != null and scheme != ''"> and a.scheme like concat('%', #{scheme}, '%')</if>
            <if test="drugName != null and drugName != ''"> and a.drugName like concat('%', #{drugName}, '%')</if>
            <if test="medicationDosage != null and medicationDosage != ''"> and a.medicationDosage like concat('%', #{medicationDosage}, '%')</if>
            <if test="medicationCycle != null and medicationCycle != ''"> and a.medicationCycle like concat('%', #{medicationCycle}, '%')</if>
            <if test="outcome != null and outcome != ''"> and a.outcome like concat('%', #{outcome}, '%')</if>
            <if test="isPD != null and isPD != ''"> and a.isPD like concat('%', #{isPD}, '%')</if>
            <if test="isNP != null and isNP != ''"> and a.isNP like concat('%', #{isNP}, '%')</if>
            <if test="buyingMedicineNumber != null and buyingMedicineNumber != ''"> and a.buyingMedicineNumber like concat('%', #{buyingMedicineNumber}, '%')</if>
            <if test="buyingMedicineWay != null and buyingMedicineWay != ''"> and a.buyingMedicineWay like concat('%', #{buyingMedicineWay}, '%')</if>
            <if test="notes1 != null and notes1 != ''"> and a.notes1 like concat('%', #{notes1}, '%')</if>
            <if test="notes2 != null and notes2 != ''"> and a.notes2 like concat('%', #{notes2}, '%')</if>
            <if test="hospitalId != null and hospitalId != ''"> and a.hospital_id like concat('%', #{hospitalId}, '%')</if>
            <if test="doctorId != null and doctorId != ''"> and a.doctor_id like concat('%', #{doctorId}, '%')</if>
            <if test="patientId != null and patientId != ''"> and a.patient_id like concat('%', #{patientId}, '%')</if>

            <if test="startDate != null and startDate != ''">and a.create_time &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and a.create_time &lt;= #{endDate}</if>
            <if test="startDate1 != null and startDate1 != ''">and a.update_time &gt;= #{startDate1}</if>
            <if test="endDate1 != null and endDate1 != ''">and a.update_time &lt;= #{endDate1}</if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <!-- 根据id查询 -->
    <select id="queryDataById" parameterType="String" resultType="CaseBookData">
        SELECT
            a.id,
            a.terminal,
            a.product,
            a.hospital_id,
            a.doctor_id,
            a.patient_id,
            a.treatmentNumber,
            a.area,
            a.scheme,
            a.drugName,
            a.medicationDosage,
            a.medicationCycle,
            a.outcome,
            a.isPD,
            a.isNP,
            a.buyingMedicineNumber,
            a.buyingMedicineWay,
            a.notes1,
            a.notes2,
            h.hospital_name,
            d.doctor_name,
            d.position,
            d.department,
            p.patient_name,
            p.sex,
            p.cancer_type,
            p.cancer_subspecies,
            a.create_time,
            a.update_time
        FROM caseBook_data a
                 LEFT JOIN hospital_data h ON a.hospital_id = h.id
                 LEFT JOIN doctor_data d ON a.doctor_id = d.id
                 LEFT JOIN patient_data p ON a.patient_id = p.id
        where a.id = #{id}
    </select>
    <!-- 添加 -->
    <insert id="insertData" useGeneratedKeys="true" parameterType="DoctorData" keyProperty="id">
        INSERT INTO caseBook_data(
        <if test="terminal != null and terminal != ''">terminal,</if>
        <if test="product != null and product != ''">product,</if>
        <if test="hospitalId != null and hospitalId != ''">hospital_id,</if>
        <if test="doctorId != null and doctorId != ''">doctor_id,</if>
        <if test="patientId != null and patientId != ''">patient_id,</if>
        <if test="treatmentNumber != null and treatmentNumber != ''">treatmentNumber,</if>
        <if test="area != null and area != ''">area,</if>
        <if test="scheme != null and scheme != ''">scheme,</if>
        <if test="drugName != null and drugName != ''">drugName,</if>
        <if test="medicationDosage != null and medicationDosage != ''">medicationDosage,</if>
        <if test="medicationCycle != null and medicationCycle != ''">medicationCycle,</if>
        <if test="outcome != null and outcome != ''">outcome,</if>
        <if test="isPD != null and isPD != ''">isPD,</if>
        <if test="isNP != null and isNP != ''">isNP,</if>
        <if test="buyingMedicineNumber != null and buyingMedicineNumber != ''">buyingMedicineNumber,</if>
        <if test="buyingMedicineWay != null and buyingMedicineWay != ''">buyingMedicineWay,</if>
        <if test="notes1 != null and notes1 != ''">notes1,</if>
        <if test="notes2 != null and notes2 != ''">notes2,</if>
        create_time,update_time
        )values
                (
                <if test="terminal != null and terminal != ''">#{terminal},</if>
                <if test="product != null and product != ''">#{product},</if>
                <if test="hospitalId != null and hospitalId != ''">#{hospitalId},</if>
                <if test="doctorId != null and doctorId != ''">#{doctorId},</if>
                <if test="patientId != null and patientId != ''">#{patientId},</if>
                <if test="treatmentNumber != null and treatmentNumber != ''">#{treatmentNumber},</if>
                <if test="area != null and area != ''">#{area},</if>
                <if test="scheme != null and scheme != ''">#{scheme},</if>
                <if test="drugName != null and drugName != ''">#{drugName},</if>
                <if test="medicationDosage != null and medicationDosage != ''">#{medicationDosage},</if>
                <if test="medicationCycle != null and medicationCycle != ''">#{medicationCycle},</if>
                <if test="outcome != null and outcome != ''">#{outcome},</if>
                <if test="isPD != null and isPD != ''">#{isPD},</if>
                <if test="isNP != null and isNP != ''">#{isNP},</if>
                <if test="buyingMedicineNumber != null and buyingMedicineNumber != ''">#{buyingMedicineNumber},</if>
                <if test="buyingMedicineWay != null and buyingMedicineWay != ''">#{buyingMedicineWay},</if>
                <if test="notes1 != null and notes1 != ''">#{notes1},</if>
                <if test="notes2 != null and notes2 != ''">#{notes2},</if>
                getdate(),getdate()
                )
    </insert>
    <!-- 根据id修改 -->
    <update id="updateData" parameterType="DoctorData">
        UPDATE caseBook_data
        <set>
            <if test="terminal != null and terminal != ''">terminal = #{terminal},</if>
            <if test="product != null and product != ''">product = #{product},</if>
            <if test="hospitalId != null and hospitalId != ''">hospital_id = #{hospitalId},</if>
            <if test="doctorId != null and doctorId != ''">doctor_id = #{doctorId},</if>
            <if test="patientId != null and patientId != ''">patient_id = #{patientId},</if>
            <if test="treatmentNumber != null and treatmentNumber != ''">treatmentNumber = #{treatmentNumber},</if>
            <if test="area != null and area != ''">area = #{area},</if>
            <if test="scheme != null and scheme != ''">scheme = #{scheme},</if>
            <if test="drugName != null and drugName != ''">drugName = #{drugName},</if>
            <if test="medicationDosage != null and medicationDosage != ''">medicationDosage = #{medicationDosage},</if>
            <if test="medicationCycle != null and medicationCycle != ''">medicationCycle = #{medicationCycle},</if>
            <if test="outcome != null and outcome != ''">outcome = #{outcome},</if>
            <if test="isPD != null and isPD != ''">isPD = #{isPD},</if>
            <if test="isNP != null and isNP != ''">isNP = #{isNP},</if>
            <if test="buyingMedicineNumber != null and buyingMedicineNumber != ''">buyingMedicineNumber = #{buyingMedicineNumber},</if>
            <if test="buyingMedicineWay != null and buyingMedicineWay != ''">buyingMedicineWay = #{buyingMedicineWay},</if>
            <if test="notes1 != null and notes1 != ''">notes1 = #{notes1},</if>
            <if test="notes2 != null and notes2 != ''">notes2 = #{notes2},</if>
            update_time = getdate()
        </set>
        where id = #{id}
    </update>
</mapper>