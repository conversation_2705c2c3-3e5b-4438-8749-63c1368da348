<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.ThirdPartySuppliersMapper">

    <resultMap type="ThirdPartySuppliers" id="ThirdPartySuppliersResult">
        <result property="singleNumber" column="singleNumber"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="applicationDate" column="applicationDate"/>
        <result property="department" column="department"/>
        <result property="cooperativeUnit" column="cooperativeUnit"/>
        <result property="feeAmount" column="feeAmount"/>
        <result property="taxAmount" column="taxAmount"/>
        <result property="voucherNo" column="voucherNo"/>
        <result property="approvalTimeYear" column="approvalTimeYear"/>
        <result property="approvalTimeMonth" column="approvalTimeMonth"/>
        <result property="approvalTime" column="approvalTime"/>
        <result property="ledgerAccount" column="ledgerAccount"/>
        <result property="meetingName" column="meetingName"/>
        <result property="meetingDepartment" column="meetingDepartment"/>
        <result property="meetingNumber" column="meetingNumber"/>
        <result property="meetingNumbers" column="meetingNumbers"/>
        <result property="conferenceCity" column="conferenceCity"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="tableName" column="tableName"/>
    </resultMap>

    <sql id="selectTps">
        select singleNumber, reimbursementPerson, applicationDate, department,
        cooperativeUnit, feeAmount, taxAmount,voucherNo,approvalTimeYear,
        approvalTimeMonth, approvalTime, ledgerAccount, meetingName, meetingDepartment,
        meetingNumber, meetingNumbers, conferenceCity, documentStatus,
        currentSession, tableName from thirdPartySuppliers_view
    </sql>

        <select id="queryThirdPartySuppliers" parameterType="ThirdPartySuppliersQuery" resultMap="ThirdPartySuppliersResult">
        <include refid="selectTps"/>
        <where>
            <if test="applyForStartDate != null  and applyForStartDate != ''"> and applicationDate &gt;= #{applyForStartDate}</if>
            <if test="applyForEndDate != null  and applyForEndDate != ''"> and applicationDate &lt;= #{applyForEndDate}</if>
            <if test="carefulStartDate != null  and carefulStartDate != ''"> and approvalTime &gt;= #{carefulStartDate}</if>
            <if test="carefulEndDate != null  and carefulEndDate != ''"> and approvalTime &lt;= #{carefulEndDate}</if>
            <if test="providerName != null  and providerName != ''"> and cooperativeUnit like concat('%', #{providerName}, '%')</if>
            <if test="oaNumber != null  and oaNumber != ''"> and singleNumber like concat('%', #{oaNumber}, '%')</if>
        </where>
        order by singleNumber desc
    </select>
</mapper>