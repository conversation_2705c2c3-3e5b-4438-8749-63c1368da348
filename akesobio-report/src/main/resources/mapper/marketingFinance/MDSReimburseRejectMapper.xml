<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.MDSReimburseRejectMapper">
    <!-- 实体类 -->
    <resultMap type="MDSReimburseReject" id="MDSReimburseRejectResult">
    </resultMap>

    <!-- sql语句 -->
    <sql id="selectMDSReimburseRejectList">
        SELECT region,
               sales_period     AS salesPeriod,
               reimburse_number AS reimburseNumber
        FROM MDS_reimburse_reject
    </sql>

    <!-- 方法 -->
    <select id="queryMDSReimburseRejectMapperList" parameterType="MDSReimburseReject"
            resultMap="MDSReimburseRejectResult">
        <include refid="selectMDSReimburseRejectList"/>
        <where>
            <if test="region != null  and region != ''">and region like concat('%', #{region}, '%')</if>
            <if test="salesPeriod != null  and salesPeriod != ''">and sales_period like concat('%', #{salesPeriod},
                '%')
            </if>
        </where>
    </select>
</mapper>