<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.FundingPlanMapper">
    
    <resultMap type="FundingPlan" id="FundingPlanResult">
        <result property="affiliationForm"    column="affiliationForm"    />
        <result property="applicationDate"    column="applicationDate"    />
        <result property="applicant"    column="applicant"    />
        <result property="department"    column="department"    />
        <result property="month"    column="month"    />
        <result property="cardunili"    column="cardunili"    />
        <result property="privateFees"    column="privateFees"    />
        <result property="iit"    column="iit"    />
        <result property="other"    column="other"    />
        <result property="travelExpenses"    column="travelExpenses"    />
        <result property="administrativeOffice"    column="administrativeOffice"    />
        <result property="communicationExpenses"    column="communicationExpenses"    />
        <result property="publicFees"    column="publicFees"    />
        <result property="mds"    column="mds"    />
        <result property="developmentAdmission"    column="developmentAdmission"    />
        <result property="fixedCreditLoan"    column="fixedCreditLoan"    />
        <result property="imprest"    column="imprest"    />
    </resultMap>

    <sql id="selectFundingPlanVo">
        select affiliationForm, applicationDate, applicant, department, month, cardunili, privateFees, iit, other, travelExpenses, administrativeOffice, communicationExpenses, publicFees, mds, developmentAdmission, fixedCreditLoan, imprest from funding_plan
    </sql>

    <select id="selectFundingPlanList" parameterType="FundingPlan" resultMap="FundingPlanResult">
        <include refid="selectFundingPlanVo"/>
        <where>  
            <if test="affiliationForm != null  and affiliationForm != ''"> and affiliationForm like concat('%', #{affiliationForm}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="month != null  and month != ''"> and month like concat('%', #{month}, '%')</if>
            <if test="cardunili != null  and cardunili != ''"> and cardunili = #{cardunili}</if>
            <if test="privateFees != null  and privateFees != ''"> and privateFees = #{privateFees}</if>
            <if test="iit != null  and iit != ''"> and iit = #{iit}</if>
            <if test="other != null  and other != ''"> and other = #{other}</if>
            <if test="travelExpenses != null  and travelExpenses != ''"> and travelExpenses = #{travelExpenses}</if>
            <if test="administrativeOffice != null  and administrativeOffice != ''"> and administrativeOffice = #{administrativeOffice}</if>
            <if test="communicationExpenses != null  and communicationExpenses != ''"> and communicationExpenses = #{communicationExpenses}</if>
            <if test="publicFees != null  and publicFees != ''"> and publicFees = #{publicFees}</if>
            <if test="mds != null  and mds != ''"> and mds = #{mds}</if>
            <if test="developmentAdmission != null  and developmentAdmission != ''"> and developmentAdmission = #{developmentAdmission}</if>
            <if test="fixedCreditLoan != null  and fixedCreditLoan != ''"> and fixedCreditLoan = #{fixedCreditLoan}</if>
            <if test="imprest != null  and imprest != ''"> and imprest = #{imprest}</if>
        </where>
    </select>
</mapper>