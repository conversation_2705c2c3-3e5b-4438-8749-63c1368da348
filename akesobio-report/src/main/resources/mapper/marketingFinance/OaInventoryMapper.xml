<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.OaInventoryMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.OaInventory" id="OaInventoryResult">
        <result property="businessPartnerCode" column="businessPartnerCode"/>
        <result property="inventoryDate" column="inventoryDate"/>
        <result property="number" column="number"/>
    </resultMap>
    <select id="selectOaInventory" resultMap="OaInventoryResult">
        select c.businessPartnerCode,c.inventoryDate,c.number from
        (select
        a.fd_3ac2ae6ab212ea as 'businessPartnerCode',
        a.fd_3c4eef73180c6c as 'inventoryDate',
        a.fd_3ae6fa080d9312 as 'number'
        from ekp_dealerInventory1 a) c
        <where>
            <if test="year != null and year !=''">and inventoryDate like concat('%',#{year},'%')</if>
        </where>
    </select>
</mapper>
