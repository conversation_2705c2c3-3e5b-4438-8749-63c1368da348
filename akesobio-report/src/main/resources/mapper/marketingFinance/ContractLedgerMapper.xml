<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.ContractLedgerMapper">
    
    <resultMap type="ContractLedger" id="ContractLedgerResult">
        <result property="singleNumber"    column="singleNumber"    />
        <result property="reimbursementPerson"    column="reimbursementPerson"    />
        <result property="department"    column="department"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="expenses"    column="expenses"    />
        <result property="contractType"    column="contractType"    />
        <result property="contractNumber"    column="contractNumber"    />
        <result property="co"    column="co"    />
        <result property="tradeAmount"    column="tradeAmount"    />
        <result property="paymentMilestones"    column="paymentMilestones"    />
        <result property="amount"    column="amount"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="billPaymentNumber"    column="billPaymentNumber"    />
        <result property="thisPaymentAmount"    column="thisPaymentAmount"    />
        <result property="invoice"    column="invoice"    />
        <result property="invoiceNumber"    column="invoiceNumber"    />
    </resultMap>

    <sql id="selectContractLedgerVo">
        select singleNumber, reimbursementPerson, department, projectNumber, expenses, contractType, contractNumber, co, tradeAmount, paymentMilestones, amount, documentStatus, currentSession, billPaymentNumber, thisPaymentAmount, invoice, invoiceNumber from contract_ledger
    </sql>

    <select id="selectContractLedgerList" parameterType="ContractLedger" resultMap="ContractLedgerResult">
        <include refid="selectContractLedgerVo"/>
        <where>  
            <if test="singleNumber != null  and singleNumber != ''"> and singleNumber like concat('%', #{singleNumber}, '%')</if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''"> and reimbursementPerson like concat('%', #{reimbursementPerson}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="expenses != null  and expenses != ''"> and expenses like concat('%', #{expenses}, '%')</if>
            <if test="contractType != null  and contractType != ''"> and contractType like concat('%', #{contractType}, '%')</if>
            <if test="contractNumber != null  and contractNumber != ''"> and contractNumber like concat('%', #{contractNumber}, '%')</if>
            <if test="co != null  and co != ''"> and co like concat('%', #{co}, '%')</if>
            <if test="tradeAmount != null  and tradeAmount != ''"> and tradeAmount = #{tradeAmount}</if>
            <if test="paymentMilestones != null  and paymentMilestones != ''"> and paymentMilestones = #{paymentMilestones}</if>
            <if test="amount != null  and amount != ''"> and amount = #{amount}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="billPaymentNumber != null  and billPaymentNumber != ''"> and billPaymentNumber like concat('%', #{billPaymentNumber}, '%')</if>
            <if test="thisPaymentAmount != null  and thisPaymentAmount != ''"> and thisPaymentAmount = #{thisPaymentAmount}</if>
            <if test="invoice != null  and invoice != ''"> and invoice = #{invoice}</if>
            <if test="invoiceNumber != null  and invoiceNumber != ''"> and invoiceNumber like concat('%', #{invoiceNumber}, '%')</if>
        </where>
        order by singleNumber desc
    </select>
</mapper>