<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.LoanStatementMapper">
    
    <resultMap type="LoanStatement" id="LoanStatementResult">
        <result property="singleNumber"    column="singleNumber"    />
        <result property="reimbursementPerson"    column="reimbursementPerson"    />
        <result property="applicationDate"    column="applicationDate"    />
        <result property="expenses"    column="expenses"    />
        <result property="reimbursement"    column="reimbursement"    />
        <result property="whether"    column="whether"    />
        <result property="loanAmount"    column="loanAmount"    />
        <result property="totalActualReimbursement"    column="totalActualReimbursement"    />
        <result property="useTo"    column="useTo"    />
        <result property="estimatedRepaymentDate"    column="estimatedRepaymentDate"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="tableName"    column="tableName"    />
    </resultMap>

    <sql id="selectLoanStatementVo">
        select singleNumber, reimbursementPerson, applicationDate, expenses, reimbursement, whether, loanAmount, totalActualReimbursement, useTo, estimatedRepaymentDate, documentStatus, currentSession, tableName from loanStatement
    </sql>

    <select id="selectLoanStatementList" parameterType="LoanStatement" resultMap="LoanStatementResult">
        <include refid="selectLoanStatementVo"/>
        <where>  
            <if test="singleNumber != null  and singleNumber != ''"> and singleNumber like concat('%', #{singleNumber}, '%')</if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''"> and reimbursementPerson like concat('%', #{reimbursementPerson}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="expenses != null  and expenses != ''"> and expenses like concat('%', #{expenses}, '%')</if>
            <if test="reimbursement != null  and reimbursement != ''"> and reimbursement = #{reimbursement}</if>
            <if test="whether != null  and whether != ''"> and whether = #{whether}</if>
            <if test="loanAmount != null  and loanAmount != ''"> and loanAmount = #{loanAmount}</if>
            <if test="totalActualReimbursement != null  and totalActualReimbursement != ''"> and totalActualReimbursement = #{totalActualReimbursement}</if>
            <if test="useTo != null  and useTo != ''"> and useTo like concat('%', #{useTo}, '%')</if>
            <if test="params.beginEstimatedRepaymentDate != null and params.beginEstimatedRepaymentDate != '' and params.endEstimatedRepaymentDate != null and params.endEstimatedRepaymentDate != ''"> and estimatedRepaymentDate between #{params.beginEstimatedRepaymentDate} and #{params.endEstimatedRepaymentDate}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="tableName != null  and tableName != ''"> and tableName like concat('%', #{tableName}, '%')</if>
        </where>
        order by applicationDate desc
    </select>
</mapper>