<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.AddedTaxInvoiceMapper">
    
    <resultMap type="AddedTaxInvoice" id="AddedTaxInvoiceResult">
        <result property="invoiceNo"    column="invoiceNo"    />
        <result property="fileAddress"    column="fileAddress"    />
        <result property="totalTax"    column="totalTax"    />
        <result property="amountTax"    column="amountTax"    />
        <result property="invoiceDate"    column="invoiceDate"    />
    </resultMap>

    <resultMap type="com.akesobio.report.marketingFinance.vo.AddedTaxInvoiceVo" id="AddedTaxInvoiceResultVo">
        <result property="invoiceNo"    column="invoice_no"    />
        <result property="feeType"    column="fee_type"    />
        <result property="oaNum"    column="oa_num"    />
        <result property="createDate"    column="create_date"    />
        <result property="fdId"    column="fd_id"    />
    </resultMap>

    <resultMap type="com.akesobio.report.marketingFinance.vo.AddedTaxInvoiceVo" id="DownloadInvoiceResult">
        <result property="invoiceNo"    column="invoiceNo"    />
        <result property="fileAddress"    column="fileAddress"    />
        <result property="totalTax"    column="totalTax"    />
        <result property="amountTax"    column="amountTax"    />
        <result property="invoiceDate"    column="invoiceDate"    />
        <result property="invoiceContent"    column="invoiceContent"    />
        <result property="saleName" column="saleName"/>
    </resultMap>

    <resultMap type="com.akesobio.report.marketingFinance.vo.AddedTaxInvoiceVo" id="InvoiceResult">
        <result property="invoiceNo"    column="invoice_no"    />
        <result property="fileAddress"    column="file_address"    />
        <result property="totalTax"    column="total_tax"    />
        <result property="amountTax"    column="amount_tax"    />
        <result property="invoiceDate"    column="invoice_date"    />
        <result property="invoiceContent"    column="commodity_name"    />
    </resultMap>

    <sql id="selectAddedTaxInvoiceVo">
        select invoiceNo, fileAddress, totalTax, amountTax, invoiceDate from invoice_batch_out
    </sql>

    <sql id="queryInvoiceVo">
        select invoice_no, file_address, total_tax, amount_tax, invoice_date, commodity_name from marketing_invoice
    </sql>

    <sql id="queryInvoiceFeeVo">
       select  invoice_no,invoice_id,fee_type,file_address,oa_num,create_date,fd_id from queryInvoice_fee_vo
    </sql>

    <select id="selectAddedTaxInvoiceList" parameterType="AddedTaxInvoice" resultMap="AddedTaxInvoiceResult">
        <include refid="selectAddedTaxInvoiceVo"/>
        <where>  
            <if test="invoiceNo != null  and invoiceNo != ''"> and invoiceNo like concat('%', #{invoiceNo}, '%')</if>
            <if test="fileAddress != null  and fileAddress != ''"> and fileAddress = #{fileAddress}</if>
            <if test="totalTax != null  and totalTax != ''"> and totalTax = #{totalTax}</if>
            <if test="amountTax != null  and amountTax != ''"> and amountTax = #{amountTax}</if>
            <if test="params.beginInvoiceDate != null and params.beginInvoiceDate != '' and params.endInvoiceDate != null and params.endInvoiceDate != ''"> and invoiceDate between #{params.beginInvoiceDate} and #{params.endInvoiceDate}</if>
        </where>
        order by invoiceDate desc
    </select>

    <select id="queryInvoiceFeeList" parameterType="com.akesobio.report.marketingFinance.vo.AddedTaxInvoiceVo" resultMap="AddedTaxInvoiceResultVo">
        <include refid="queryInvoiceFeeVo"/>
        <where>
            <if test="invoiceNo != null  and invoiceNo != ''"> and invoice_no like concat('%', #{invoiceNo}, '%')</if>
            <if test="feeType != null  and feeType != ''"> and fee_type like concat('%', #{feeType}, '%')</if>
            <if test="oaNum != null  and oaNum != ''"> and oa_num like concat('%', #{oaNum}, '%')</if>
                <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_date between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="invoiceList != null and invoiceList.size > 0" >
                and invoice_id in
                <foreach item="item" collection="invoiceList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and invoice_no IS NOT NULL AND invoice_no &lt;&gt; '定额及其他'
        </where>
        order by create_date desc
    </select>

    <select id="queryMarketingInvoice" parameterType="com.akesobio.report.marketingFinance.vo.AddedTaxInvoiceVo" resultMap="AddedTaxInvoiceResultVo">

    </select>
<!--NO1-->
    <select id="selectList2" parameterType="java.util.List"  resultMap="DownloadInvoiceResult">
        SELECT
        DISTINCT invoice.file_address AS fileAddress,
        invoice.invoice_no as invoiceNo,
        invoice.id as invoiceId,
        invoice.invoice_date as invoiceDate,
        invoice.total_tax as totalTax,
        invoice.amount_tax as amountTax,
        idl.commodity_name as invoiceContent,
        invoice.sale_name as saleName
        FROM
        invoice
        LEFT JOIN invoice_detail_list AS idl ON invoice.id = idl.invoice_id
        WHERE
        (invoice.inspection_status = 0 OR invoice.inspection_status = 1)
        AND invoice.invoice_state = 0
        AND invoice.verify_status IN (0, 1)
        AND (invoice.is_compliance_collect = '1' OR invoice.is_compliance_collect = '0')
        AND invoice.is_del = 0
        AND (idl.row_no = '1' OR idl.row_no IS null)
        <if test="invoiceList != null and invoiceList.size > 0 and invoiceList.size &lt;= 2000">
            AND CONCAT(invoice.invoice_no, invoice.id) IN
            <foreach item="item" collection="invoiceList" open="(" separator="," close=")">
                #{item.invoiceNo} + #{item.invoiceId}
            </foreach>
        </if>
    </select>

    <select id="selectList" parameterType="java.util.List" resultMap="DownloadInvoiceResult">
        SELECT
        invoice.file_address AS fileAddress,
        invoice.invoice_no AS invoiceNo,
        invoice.id AS invoiceId,
        invoice.invoice_date AS invoiceDate,
        invoice.total_tax AS totalTax,
        invoice.amount_tax AS amountTax,
        idl.commodity_name AS invoiceContent,
        invoice.sale_name AS saleName
        FROM
        invoice
        LEFT JOIN invoice_detail_list AS idl ON invoice.id = idl.invoice_id
        WHERE
        (invoice.inspection_status = 0 OR invoice.inspection_status = 1)
        AND invoice.invoice_state = 0
        AND invoice.verify_status IN (0, 1)
        AND (invoice.is_compliance_collect = '1' OR invoice.is_compliance_collect = '0')
        AND invoice.is_del = 0
        AND (idl.row_no = '1' OR idl.row_no IS NULL)

        <if test="invoiceList != null and invoiceList.size > 0 and invoiceList.size &lt;= 2000">
            AND CONCAT(invoice.invoice_no, invoice.id) IN
            <foreach item="item" collection="invoiceList" open="(" separator="," close=")">
                CONCAT(#{item.invoiceNo}, #{item.invoiceId})
            </foreach>
        </if>
    </select>
    <select id="selectAddedTaxInvoice"  parameterType="java.util.List" resultMap="DownloadInvoiceResult">
        SELECT
            DISTINCT invoice.file_address AS fileAddress,
                     invoice.invoice_no as invoiceNo,
                     invoice.id as invoiceId,
                     invoice.invoice_date as invoiceDate,
                     invoice.total_tax as totalTax,
                     invoice.amount_tax as amountTax,
                     idl.commodity_name as invoiceContent,
                     invoice.sale_name as saleName
        FROM
            invoice
                LEFT JOIN invoice_detail_list AS idl ON invoice.id = idl.invoice_id
        WHERE
            (invoice.inspection_status = 0 OR invoice.inspection_status = 1)
          AND invoice.invoice_state = 0
          AND invoice.verify_status IN (0, 1)
          AND (invoice.is_compliance_collect = '1' OR invoice.is_compliance_collect = '0')
          AND invoice.is_del = 0
          and invoice.reimburse_status = 2
          AND (idl.row_no = '1' OR idl.row_no IS null)
          and invoice.sale_name like concat('%',#{saleName},'%')
    </select>


</mapper>