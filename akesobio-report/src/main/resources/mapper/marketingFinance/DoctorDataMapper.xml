<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.DoctorDataMapper">
    <!-- 实体类  -->
    <resultMap type="DoctorData" id="DoctorDataResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectDoctorDataList">
        SELECT
            a.id,
            a.doctor_code,
            a.doctor_name,
            a.province,
            a.city,
            a.department,
            a.position,
            a.hospital_id,
            h.hospital_code,
            h.hospital_name,
            a.create_time,
            a.update_time
        FROM doctor_data a
                 LEFT JOIN hospital_data h ON a.hospital_id = h.id
    </sql>
    
    <!-- 方法 -->
    <select id="queryDoctorDataList" parameterType="DoctorData" resultMap="DoctorDataResult">
        <include refid="selectDoctorDataList"/>
        <where>  
            <if test="doctorCode != null and doctorCode != ''"> and a.doctor_code like concat('%', #{doctorCode}, '%')</if>
            <if test="doctorName != null and doctorName != ''"> and a.doctor_name like concat('%', #{doctorName}, '%')</if>
            <if test="province != null and province != ''"> and a.province like concat('%', #{province}, '%')</if>
            <if test="city != null and city != ''"> and a.city like concat('%', #{city}, '%')</if>
            <if test="department != null and department != ''"> and a.department like concat('%', #{department}, '%')</if>
            <if test="position != null and position != ''"> and a.position like concat('%', #{position}, '%')</if>
            <if test="hospitalId != null and hospitalId != ''"> and a.hospital_id like concat('%', #{hospitalId}, '%')</if>

            <if test="startDate != null and startDate != ''">and a.create_time &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and a.create_time &lt;= #{endDate}</if>
            <if test="startDate1 != null and startDate1 != ''">and a.update_time &gt;= #{startDate1}</if>
            <if test="endDate1 != null and endDate1 != ''">and a.update_time &lt;= #{endDate1}</if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <!-- 根据id查询 -->
    <select id="queryDoctorDataById" parameterType="String" resultType="DoctorData">
        SELECT
            a.id,
            a.doctor_code,
            a.doctor_name,
            a.province,
            a.city,
            a.department,
            a.position,
            a.hospital_id,
            h.hospital_code,
            h.hospital_name,
            a.create_time,
            a.update_time
        FROM doctor_data a
                 LEFT JOIN hospital_data h ON a.hospital_id = h.id
        where a.id = #{id}
    </select>
    <!--根据编码查询-->
    <select id="checkDoctorCodeUnique" parameterType="String" resultType="DoctorData">
        SELECT
            a.id,
            a.doctor_code,
            a.doctor_name,
            a.province,
            a.city,
            a.department,
            a.position,
            a.hospital_id,
            h.hospital_code,
            h.hospital_name,
            a.create_time,
            a.update_time
        FROM doctor_data a
                 LEFT JOIN hospital_data h ON a.hospital_id = h.id
        where a.doctor_code = #{doctorCode}
    </select>
    <!-- 根据医院名称查询 -->
    <select id="checkDoctorNameUnique" parameterType="String" resultType="DoctorData">
        SELECT
            a.id,
            a.doctor_code,
            a.doctor_name,
            a.province,
            a.city,
            a.department,
            a.position,
            a.hospital_id,
            h.hospital_code,
            h.hospital_name,
            a.create_time,
            a.update_time
        FROM doctor_data a
                 LEFT JOIN hospital_data h ON a.hospital_id = h.id
        where a.doctor_name = #{doctorName}
    </select>
    <!-- 添加 -->
    <insert id="insertDoctorData" useGeneratedKeys="true" parameterType="DoctorData" keyProperty="id">
        INSERT INTO doctor_data(
        <if test="doctorCode != null and doctorCode != ''">doctor_code,</if>
        <if test="doctorName != null and doctorName != ''">doctor_name,</if>
        <if test="province != null and province != ''">province,</if>
        <if test="city != null and city != ''">city,</if>
        <if test="department != null and department != ''">department,</if>
        <if test="position != null and position != ''">position,</if>
        <if test="hospitalIds != null and hospitalIds.length > 0">hospital_id,</if>
        create_time,update_time
        )values

        <foreach collection="hospitalIds" item="e" separator="," >
            (
            <if test="doctorCode != null and doctorCode != ''">#{doctorCode},</if>
            <if test="doctorName != null and doctorName != ''">#{doctorName},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="department != null and department != ''">#{department},</if>
            <if test="position != null and position != ''">#{position},</if>
            <if test="hospitalIds != null and hospitalIds.length > 0">#{e},</if>
            getdate(),getdate()
            )
        </foreach>
<!--        (-->
<!--        <if test="doctorCode != null and doctorCode != 0">#{doctorCode},</if>-->
<!--        <if test="doctorName != null and doctorName != ''">#{doctorName},</if>-->
<!--        <if test="province != null and province != ''">#{province},</if>-->
<!--        <if test="city != null and city != ''">#{city},</if>-->
<!--        <if test="department != null and department != ''">#{department},</if>-->
<!--        <if test="position != null and position != ''">#{position},</if>-->
<!--        <if test="hospitalId != null and hospitalId != ''">#{hospitalId},</if>-->
<!--        getdate(),getdate()-->
<!--        )-->
    </insert>
    <!-- 根据id修改 -->
    <update id="updateDoctorData" parameterType="DoctorData">
        UPDATE doctor_data
        <set>
            <if test="doctorCode != null and doctorCode != ''">doctor_code = #{doctorCode},</if>
            <if test="doctorName != null and doctorName != ''">doctor_name = #{doctorName},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="department != null and department != ''">department = #{department},</if>
            <if test="position != null and position != ''">position = #{position},</if>
            <if test="hospitalId != null and hospitalId != ''">hospital_id = #{hospitalId},</if>
            update_time = getdate()
        </set>
        where id = #{id}
    </update>
</mapper>