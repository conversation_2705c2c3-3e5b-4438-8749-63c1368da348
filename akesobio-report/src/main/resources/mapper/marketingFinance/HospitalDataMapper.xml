<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.HospitalDataMapper">
    <!-- 实体类  -->
    <resultMap type="HospitalData" id="HospitalDataResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectHospitalDataList">
        SELECT
            id,
            hospital_code,
            hospital_name,
            director_region,
            province,
            city,
            region,
            area,
            create_time,
            update_time
        FROM hospital_data
    </sql>
    
    <!-- 方法 -->
    <select id="queryHospitalDataList" parameterType="HospitalData" resultMap="HospitalDataResult">
        <include refid="selectHospitalDataList"/>
        <where>  
            <if test="hospitalCode != null and hospitalCode != ''"> and hospital_code like concat('%', #{hospitalCode}, '%')</if>
            <if test="hospitalName != null and hospitalName != ''"> and hospital_name like concat('%', #{hospitalName}, '%')</if>
            <if test="directorRegion != null and directorRegion != ''"> and director_region like concat('%', #{directorRegion}, '%')</if>
            <if test="province != null and province != ''"> and province like concat('%', #{province}, '%')</if>
            <if test="city != null and city != ''"> and city like concat('%', #{city}, '%')</if>
            <if test="region != null and region != ''"> and region like concat('%', #{region}, '%')</if>
            <if test="area != null and area != ''"> and area like concat('%', #{area}, '%')</if>

            <if test="startDate != null and startDate != ''">and create_time &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and create_time &lt;= #{endDate}</if>
            <if test="startDate1 != null and startDate1 != ''">and update_time &gt;= #{startDate1}</if>
            <if test="endDate1 != null and endDate1 != ''">and update_time &lt;= #{endDate1}</if>
        </where>
        ORDER BY create_time DESC
    </select>
    <!-- 根据id查询 -->
    <select id="queryHospitalDataById" parameterType="String" resultType="HospitalData">
        SELECT
            id,
            hospital_code,
            hospital_name,
            director_region,
            province,
            city,
            region,
            area,
            create_time,
            update_time
        FROM hospital_data
        where id = #{id}
    </select>
    <!--根据医院编码查询-->
    <select id="checkHospitalCodeUnique" parameterType="String" resultType="HospitalData">
        SELECT
            id,
            hospital_code,
            hospital_name,
            director_region,
            province,
            city,
            region,
            area,
            create_time,
            update_time
        FROM hospital_data
        where hospital_code = #{hospitalCode}
    </select>
    <!-- 根据医院名称查询 -->
    <select id="checkHospitalNameUnique" parameterType="String" resultType="HospitalData">
        SELECT
            id,
            hospital_code,
            hospital_name,
            director_region,
            province,
            city,
            region,
            area,
            create_time,
            update_time
        FROM hospital_data
        where hospital_name = #{hoapitalName}
    </select>
    <!-- 添加 -->
    <insert id="insertHospitalData" useGeneratedKeys="true" parameterType="HospitalData" keyProperty="id">
        INSERT INTO hospital_data(
        <if test="hospitalCode != null and hospitalCode != ''">hospital_code,</if>
        <if test="hospitalName != null and hospitalName != ''">hospital_name,</if>
        <if test="directorRegion != null and directorRegion != ''">director_region,</if>
        <if test="province != null and province != ''">province,</if>
        <if test="city != null and city != ''">city,</if>
        <if test="region != null and region != ''">region,</if>
        <if test="area != null and area != ''">area,</if>
                                  create_time,update_time
        )values (
        <if test="hospitalCode != null and hospitalCode != ''">#{hospitalCode},</if>
        <if test="hospitalName != null and hospitalName != ''">#{hospitalName},</if>
        <if test="directorRegion != null and directorRegion != ''">#{directorRegion},</if>
        <if test="province != null and province != ''">#{province},</if>
        <if test="city != null and city != ''">#{city},</if>
        <if test="region != null and region != ''">#{region},</if>
        <if test="area != null and area != ''">#{area},</if>
                 getdate(),getdate()
        )
    </insert>
    <!-- 根据id修改 -->
    <update id="updateHospitalData" parameterType="HospitalData">
        UPDATE hospital_data
        <set>
            <if test="hospitalCode != null and hospitalCode != ''">hospital_code = #{hospitalCode},</if>
            <if test="hospitalName != null and hospitalName != ''">hospital_name = #{hospitalName},</if>
            <if test="directorRegion != null and directorRegion != ''">director_region = #{directorRegion},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="region != null and region != ''">region = #{region},</if>
            <if test="area != null and area != ''">area = #{area},</if>
            update_time = getdate()
        </set>
        where id = #{id}
    </update>
</mapper>