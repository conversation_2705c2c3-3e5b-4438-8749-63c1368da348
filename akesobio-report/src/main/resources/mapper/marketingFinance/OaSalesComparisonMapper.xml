<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.marketingFinance.mapper.OaSalesComparisonMapper">
    <resultMap type="com.akesobio.report.marketingFinance.vo.OaSalesComparison" id="OaSalesComparisonResult">
        <result property="code" column="code"/>
        <result property="area" column="area"/>
    </resultMap>
    <select id="selectOaSalesComparison" resultMap="OaSalesComparisonResult">
        select b.code,b.area from
        (select
        a.fd_3b358de59698e0 as 'code',
        a.fd_3b358e037b4f4e as 'area'
        from ekp_contrast a) b
        <where>
            <if test="code != null and code !=''">and code = #{code}</if>
        </where>
    </select>
</mapper>
