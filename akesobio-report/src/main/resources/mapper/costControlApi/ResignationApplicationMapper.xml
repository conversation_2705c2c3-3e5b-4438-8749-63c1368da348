<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControlApi.mapper.ResignationApplicationMapper">

    <resultMap type="com.akesobio.report.costControlApi.entity.ResignationApplicationInfo" id="ResignationApplicationInfoResult">
        <result property="jobNumber" column="jobNumber"/>
        <result property="oaNumber" column="oaNumber"/>
        <result property="statusValue" column="statusValue"/>
        <result property="approvalStatus" column="approvalStatus"/>
    </resultMap>

    <sql id="selectResignationApplicationVo">
        select 
            a.fd_gongHao as 'jobNumber',
            a.fd_danHao as 'oaNumber',
            b.doc_status as 'statusValue',
            (CASE b.doc_status
                WHEN '00' THEN '离职申请单已废弃'
                WHEN '10' THEN '离职申请单未提交，处于草稿状态'
                WHEN '11' THEN '离职申请单被驳回，随时可再发起'
                WHEN '20' THEN '离职申请单在审批中'
                WHEN '30' THEN '离职申请已经结束'
                ELSE '无'
            END) as 'approvalStatus'
        from ekp_resignation_application a
        left join hr_ratify_main b on a.fd_danHao=b.doc_number
        where a.fd_danHao is not null 
            and b.doc_status != '00' 
            and b.doc_status != '10' 
            and a.fd_danHao !='HR010-20220521177'
    </sql>

    <select id="selectResignationApplicationList" parameterType="com.akesobio.report.costControlApi.entity.ResignationApplicationInfo" resultMap="ResignationApplicationInfoResult">
        <include refid="selectResignationApplicationVo"/>
        <where>
            <if test="jobNumber != null and jobNumber != ''">
                and a.fd_gongHao like concat('%', #{jobNumber}, '%')
            </if>
            <if test="oaNumber != null and oaNumber != ''">
                and a.fd_danHao like concat('%', #{oaNumber}, '%')
            </if>
            <if test="statusValue != null and statusValue != ''">
                and b.doc_status = #{statusValue}
            </if>
            <if test="approvalStatus != null and approvalStatus != ''">
                and (CASE b.doc_status
                    WHEN '00' THEN '离职申请单已废弃'
                    WHEN '10' THEN '离职申请单未提交，处于草稿状态'
                    WHEN '11' THEN '离职申请单被驳回，随时可再发起'
                    WHEN '20' THEN '离职申请单在审批中'
                    WHEN '30' THEN '离职申请已经结束'
                    ELSE '无'
                END) like concat('%', #{approvalStatus}, '%')
            </if>
        </where>
        order by a.fd_danHao desc
    </select>

    <select id="queryResignationApplicationByPage" parameterType="com.akesobio.report.costControlApi.entity.ResignationApplicationInfo" resultMap="ResignationApplicationInfoResult">
        <include refid="selectResignationApplicationVo"/>
        <where>
            <if test="jobNumber != null and jobNumber != ''">
                and a.fd_gongHao like concat('%', #{jobNumber}, '%')
            </if>
            <if test="oaNumber != null and oaNumber != ''">
                and a.fd_danHao like concat('%', #{oaNumber}, '%')
            </if>
            <if test="statusValue != null and statusValue != ''">
                and b.doc_status = #{statusValue}
            </if>
            <if test="approvalStatus != null and approvalStatus != ''">
                and (CASE b.doc_status
                    WHEN '00' THEN '离职申请单已废弃'
                    WHEN '10' THEN '离职申请单未提交，处于草稿状态'
                    WHEN '11' THEN '离职申请单被驳回，随时可再发起'
                    WHEN '20' THEN '离职申请单在审批中'
                    WHEN '30' THEN '离职申请已经结束'
                    ELSE '无'
                END) like concat('%', #{approvalStatus}, '%')
            </if>
        </where>
        order by a.fd_danHao desc
    </select>

</mapper>
