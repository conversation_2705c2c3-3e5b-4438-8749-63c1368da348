<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControlApi.mapper.AuthorizedJobNumberMapper">

    <resultMap type="com.akesobio.report.costControlApi.entity.AuthorizedJobNumber" id="AuthorizedJobNumberResult">
        <result property="id" column="id"/>
        <result property="jobNumber" column="job_number"/>
        <result property="authType" column="auth_type"/>
        <result property="status" column="status"/>
        <result property="description" column="description"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectAuthorizedJobNumberVo">
        select id, job_number, auth_type, status, description, create_time, update_time, create_by, update_by 
        from authorized_job_numbers
    </sql>

    <select id="selectAuthorizedJobNumberList" parameterType="com.akesobio.report.costControlApi.entity.AuthorizedJobNumber" resultMap="AuthorizedJobNumberResult">
        <include refid="selectAuthorizedJobNumberVo"/>
        <where>
            <if test="jobNumber != null  and jobNumber != ''">and job_number = #{jobNumber}</if>
            <if test="authType != null  and authType != ''">and auth_type = #{authType}</if>
            <if test="status != null">and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectAuthorizedJobNumberById" parameterType="Long" resultMap="AuthorizedJobNumberResult">
        <include refid="selectAuthorizedJobNumberVo"/>
        where id = #{id}
    </select>

    <select id="selectActiveJobNumbers"  resultType="String">
        select job_number 
        from authorized_job_numbers
        order by job_number
    </select>

    <select id="checkJobNumberAuthorization" resultType="int">
        select count(1) 
        from authorized_job_numbers 
        where job_number = #{jobNumber} and auth_type = #{authType} and status = 1
    </select>

    <insert id="insertAuthorizedJobNumber" parameterType="com.akesobio.report.costControlApi.entity.AuthorizedJobNumber" useGeneratedKeys="true" keyProperty="id">
        insert into authorized_job_numbers
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobNumber != null and jobNumber != ''">job_number,</if>
            <if test="authType != null and authType != ''">auth_type,</if>
            <if test="status != null">status,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobNumber != null and jobNumber != ''">#{jobNumber},</if>
            <if test="authType != null and authType != ''">#{authType},</if>
            <if test="status != null">#{status},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            now()
        </trim>
    </insert>

    <update id="updateAuthorizedJobNumber" parameterType="com.akesobio.report.costControlApi.entity.AuthorizedJobNumber">
        update authorized_job_numbers
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobNumber != null and jobNumber != ''">job_number = #{jobNumber},</if>
            <if test="authType != null and authType != ''">auth_type = #{authType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAuthorizedJobNumberById" parameterType="Long">
        delete from authorized_job_numbers where id = #{id}
    </delete>

    <delete id="deleteAuthorizedJobNumberByIds" parameterType="Long">
        delete from authorized_job_numbers where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByJobNumberAndAuthType">
        delete from authorized_job_numbers 
        where job_number = #{jobNumber} and auth_type = #{authType}
    </delete>

    <insert id="batchInsertAuthorizedJobNumbers">
        insert into authorized_job_numbers (job_number, auth_type, status, create_by, create_time)
        values
        <foreach collection="jobNumbers" item="jobNumber" separator=",">
            (#{jobNumber}, #{authType}, 1, #{createBy}, now())
        </foreach>
    </insert>

</mapper>