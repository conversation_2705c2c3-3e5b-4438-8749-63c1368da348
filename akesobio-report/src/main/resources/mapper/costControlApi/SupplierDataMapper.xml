<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControlApi.mapper.SupplierDataMapper">

    <resultMap type="com.akesobio.report.costControlApi.entity.Supplier" id="SupplierResult">
        <result property="businessPartnerCode" column="businessPartnerCode"/>
        <result property="businessPartnerName" column="businessPartnerName"/>
        <result property="deleteFlag" column="deleteFlag"/>
        <result property="updateTime" column="updateTime"/>
        <result property="bankAccountNumber" column="bankAccountNumber"/>
        <result property="bankName" column="bankName"/>
        <result property="bankAccountName" column="bankAccountName"/>
        <result property="bankDetailIdentification" column="bankDetailIdentification"/>
        <result property="interbankNumber" column="interbankNumber"/>
    </resultMap>

    <select id="selectSupplierList" parameterType="com.akesobio.report.costControlApi.entity.Supplier" resultMap="SupplierResult">
        SELECT
        businessPartnerCode,
        businessPartnerName,
        deleteFlag,
        updateTime,
        bankAccountNumber,
        bankName,
        bankAccountName,
        bankDetailIdentification,
        interbankNumber
        FROM
        (
        SELECT
        a.partner 'businessPartnerCode',
        a.name_org1 'businessPartnerName',
        a.efficient 'deleteFlag',
        a.updatetime 'updateTime',
        a.bankn 'bankAccountNumber',
        a.banka 'bankName',
        a.accname 'bankAccountName',
        a.bkvid 'bankDetailIdentification',
        a.bankl 'interbankNumber'
        FROM
        SupplierMD a
        WHERE
        a.efficient= '1'
        AND ( a.zsfyxgys != '' OR a.bu_group IN ( 'Z001', 'Z002', 'Z003', 'Z006' ) )
        ) b
        <where>
            <if test="updateTime != null  and updateTime != ''">and updateTime &lt; #{updateTime}</if>
        </where>
    </select>
</mapper>