<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControlApi.mapper.CostCenterInfoMapper">

    <resultMap type="com.akesobio.report.costControlApi.entity.CostCenterInfo" id="CostCenterInfoResult">
        <result property="costCenterName" column="costCenterName"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="companyName" column="companyName"/>
        <result property="enableFlag" column="enableFlag"/>
    </resultMap>

    <select id="selectCostCenterInfoList" resultMap="CostCenterInfoResult">
        SELECT
        costCenterName,
        costCenterCode,
        companyName,
        enableFlag
        FROM
        ( SELECT a.ltext+'('+ a.kostl+')' 'costCenterName', a.kostl 'costCenterCode', a.name2 'companyName', a.efficient
        'enableFlag'
        FROM AccounSubMainMD a ) b
        <where>
            <if test="costCenterName != null ">and costCenterName = #{costCenterName}</if>
            <if test="companyName != null  and companyName != ''">and companyName = #{companyName}</if>
        </where>
    </select>
</mapper>