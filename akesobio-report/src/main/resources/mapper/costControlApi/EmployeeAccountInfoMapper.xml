<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControlApi.mapper.EmployeeAccountInfoMapper">

    <resultMap type="com.akesobio.report.costControlApi.entity.EmployeeAccountInfo" id="EmployeeAccountInfoResult">
        <result property="employeeNumber" column="employeeNumber"/>
        <result property="bankAccount" column="bankAccount"/>
        <result property="bankName" column="bankName"/>
        <result property="bankCode" column="bankCode"/>
        <result property="bankAccountName" column="bankAccountName"/>
        <result property="bankBranch" column="bankBranch"/>
        <result property="defaultBank" column="defaultBank"/>
        <result property="enableFlag" column="enableFlag"/>
        <result property="currencyInformation" column="currencyInformation"/>
    </resultMap>

    <select id="selectEmployeeAccountInfoList" resultMap="EmployeeAccountInfoResult">
        SELECT employeeNumber,
        bankAccount,
        bankName,
        bankCode,
        bankAccountName,
        bankBranch,
        defaultBank,
        enableFlag,
        currencyInformation
        FROM (
        SELECT a.partner AS 'employeeNumber', a.bankn AS 'bankAccount', a.accname AS 'bankName', a.bankl AS 'bankCode',
        a.name_org1 AS 'bankAccountName', a.accname AS 'bankBranch', a.bkvid  AS 'defaultBank', ( CASE a.efficient WHEN '1'
        THEN 'Y' ELSE 'N' END ) 'enableFlag', NULL AS 'currencyInformation'
        FROM SupplierMD a
        ) b
        <where>
            <if test="employeeNumber != null  and employeeNumber != ''">and employeeNumber = #{employeeNumber}</if>
        </where>
    </select>
</mapper>