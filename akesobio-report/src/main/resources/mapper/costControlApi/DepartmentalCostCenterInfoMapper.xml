<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControlApi.mapper.DepartmentalCostCenterInfoMapper">

    <resultMap type="com.akesobio.report.costControlApi.entity.DepartmentalCostCenterInfo"
               id="DepartmentalCostCenterInfoResult">
        <result property="area" column="area"/>
        <result property="finalDepartment" column="finalDepartment"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="enableFlag" column="enableFlag"/>
        <result property="areaCode" column="areaCode"/>
    </resultMap>

    <select id="selectDepartmentalCostCenterInfoList" resultMap="DepartmentalCostCenterInfoResult">
        SELECT
        area,
        finalDepartment,
        costCenterCode,
        enableFlag,
        areaCode
        FROM
        (
        SELECT
        a.fd_3d6cf941698e32 'area',
        a.fd_3d6cf9489aeb7c 'finalDepartment',
        a.fd_3d6cf947a76d3a 'costCenterCode',
        a.fd_3d6cf949ec0306 'enableFlag',
        a.fd_3d75996eb4915c 'areaCode'
        FROM
        ekp_bmcbzx a
        WHERE
        a.fd_3d6cf949ec0306= '是'
        ) b
        <where>
            <if test="department != null and department != '' ">and area = #{department}
            </if>
            <if test="department != null and department != '' ">or finalDepartment = #{department}
            </if>
        </where>
    </select>
</mapper>