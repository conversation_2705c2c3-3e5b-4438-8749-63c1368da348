<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControlApi.mapper.ResignationInfoMapper">

    <resultMap type="com.akesobio.report.costControlApi.entity.ResignationInfo" id="ResignationInfoResult">
        <result property="employeeName" column="employeeName"/>
        <result property="employeeID" column="employeeID"/>
        <result property="enableFlag" column="enableFlag"/>
    </resultMap>

    <select id="selectResignationInfoList" parameterType="Supplier" resultMap="ResignationInfoResult">
        SELECT a.name 'employeeName', a.job_number 'employeeID', (
            CASE
                a.resignation_status
                WHEN '10' THEN
                    'Y'
                WHEN '20' THEN
                    'N'
                WHEN '11' THEN
                    'N'
                WHEN '00' THEN
                    'Y'
                WHEN '30' THEN
                    'N'
                WHEN '0' THEN
                    'Y'
                ELSE 'Y'
                END
            ) 'enableFlag'
        FROM hr_employee_info a
        WHERE a.employee_status!= ''
	AND a.dept_name_all LIKE '%商业运营部%'
    </select>
</mapper>