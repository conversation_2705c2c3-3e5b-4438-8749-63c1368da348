<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControlApi.mapper.OaResignInfoMapper">

    <resultMap type="com.akesobio.report.costControlApi.entity.OaResignInfo" id="OaResignInfoResult">
        <result property="oddNumbers" column="oddNumbers"/>
        <result property="applicationDate" column="applicationDate"/>
        <result property="jobNumber" column="jobNumber"/>
        <result property="approvalStatus" column="approvalStatus"/>
        <result property="resignationPersonnel" column="resignationPersonnel"/>
        <result property="terminationDate" column="terminationDate"/>
    </resultMap>

    <select id="selectOaResignInfoList" resultMap="OaResignInfoResult">
        SELECT b.fd_login_name 'jobNumber', e.fd_danHao 'oddNumbers', e.fd_shenQingRiQi 'applicationDate', e.fd_zuiHouGongZuoRi 'terminationDate', (
            CASE
                f.doc_status
                WHEN '00' THEN
                    '离职申请单已废弃'
                WHEN '10' THEN
                    '离职申请单未提交，处于草稿状态'
                WHEN '11' THEN
                    '离职申请单被驳回，随时可再发起'
                WHEN '20' THEN
                    '离职申请单在审批中'
                WHEN '30' THEN
                    '离职申请已经结束'
                ELSE '无'
                END
            ) AS 'approvalStatus' --审批状态

        FROM hr_ratify_leave a
                 LEFT JOIN sys_org_person b ON b.fd_id = a.fd_leave_staff_id
                 LEFT JOIN sys_org_element c ON c.fd_id = b.fd_id
                 LEFT JOIN hr_ratify_main d ON d.fd_id = a.fd_id
                 LEFT JOIN ekp_resignation_application e ON e.fd_danHao = d.doc_number
                 LEFT JOIN hr_ratify_main f ON e.fd_id = f.fd_id
        WHERE e.fd_shenQingRiQi > '2025-01-01'
          AND f.doc_status IN ('30')
        /*SELECT a.fd_danHao 'oddNumbers', a.fd_shenQingRiQi 'applicationDate', a.fd_gongHao 'jobNumber', a.fd_zuiHouGongZuoRi 'terminationDate', (
            CASE
                f.doc_status
                WHEN '00' THEN
                    '离职申请单已废弃'
                WHEN '10' THEN
                    '离职申请单未提交，处于草稿状态'
                WHEN '11' THEN
                    '离职申请单被驳回，随时可再发起'
                WHEN '20' THEN
                    '离职申请单在审批中'
                WHEN '30' THEN
                    '离职申请已经结束'
                ELSE '无'
                END
            ) AS 'approvalStatus' --审批状态

        FROM ekp_resignation_application a
                 LEFT JOIN hr_ratify_main f ON a.fd_id = f.fd_id
        WHERE a.fd_zuiHouGongZuoRi > '2024-12-14'
          AND f.doc_status = '30'*/
    </select>

    <select id="selectOaResignInfoListOA" resultMap="OaResignInfoResult">
        SELECT
        oddNumbers,
        applicationDate,
        jobNumber,
        terminationDate,
        resignationPersonnel,
        approvalStatus
        FROM
        (
        SELECT
        a.fd_danHao 'oddNumbers',
        a.fd_shenQingRiQi 'applicationDate',
        a.fd_gongHao 'jobNumber',
        CONVERT ( CHAR ( 10 ), a.fd_zuiHouGongZuoRi , 120 ) 'terminationDate',
        s.fd_name 'resignationPersonnel',
        (
        CASE
        f.doc_status
        WHEN '00' THEN
        '离职申请单已废弃'
        WHEN '10' THEN
        '离职申请单未提交，处于草稿状态'
        WHEN '11' THEN
        '离职申请单被驳回，随时可再发起'
        WHEN '20' THEN
        '离职申请单在审批中'
        WHEN '30' THEN
        '离职申请已经结束' ELSE '无'
        END
        ) AS 'approvalStatus' --审批状态

        FROM
        ekp_resignation_application a
        LEFT JOIN hr_ratify_main f ON a.fd_id = f.fd_id
        LEFT JOIN sys_org_element s ON s.fd_no= a.fd_gongHao
        ) b
        <where>
            <if test="terminationDate != null  and terminationDate != ''">and terminationDate like concat('%',
                #{terminationDate}, '%')
            </if>
        </where>
    </select>
</mapper>