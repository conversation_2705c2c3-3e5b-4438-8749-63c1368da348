<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControlApi.mapper.LedgerAccountInfoMapper">

    <resultMap type="com.akesobio.report.costControlApi.entity.LedgerAccountInfo" id="LedgerAccountInfoResult">
        <result property="accountingCode" column="accountingCode"/>
        <result property="accountingSubjectStructureName" column="accountingSubjectStructureName"/>
        <result property="accountingSubjectName" column="accountingSubjectName"/>
        <result property="category" column="category"/>
        <result property="enableFlag" column="enableFlag"/>
    </resultMap>

    <select id="selectLedgerAccountInfoList" resultMap="LedgerAccountInfoResult">
        SELECT--费控会计科目
              a.fd_3d733c45a81122 'accountingCode', a.fd_3d733c4106a1ea 'accountingSubjectName', '康方会计科目' 'accountingSubjectStructureName',
              a.fd_3d733c5769d7d0 'category', ( CASE a.fd_3d733c4bc79058 WHEN '是' THEN 'Y' ELSE 'N' END ) 'enableFlag'
        FROM ekp_fkkjkm a
        /*SELECT--差旅报销
              a.fd_3a5425e42b5732 'accountingCode', a.fd_3a5425f18f1308 'accountingSubjectName', '康方会计科目' 'accountingSubjectStructureName',
              a.fd_3a5425d420c8a4 'category', ( CASE a.fd_3a54260884c110 WHEN '启用' THEN 'Y' ELSE 'N' END ) 'enableFlag'
        FROM ekp_ClbxKmcwb a
        UNION ALL
        SELECT--费用报销
              a.fd_3a5425e42b5732 'accountingCode', a.fd_3a5425f18f1308 'accountingSubjectName', '康方会计科目' 'accountingSubjectStructureName',
              a.fd_3ca236fcbdabec 'category', ( CASE a.fd_3a54260884c110 WHEN '启用' THEN 'Y' ELSE 'N' END ) 'enableFlag'
        FROM ekp_FybxKmcwb a
        UNION ALL
        SELECT--会议费用报销
              a.fd_3a5425e42b5732 'accountingCode', a.fd_3a5425f18f1308 'accountingSubjectName', '康方会计科目' 'accountingSubjectStructureName',
              a.fd_3a5425d420c8a4 'category', ( CASE a.fd_3a54260884c110 WHEN '启用' THEN 'Y' ELSE 'N' END ) 'enableFlag'
        FROM ekp_HyfybxKmcwb a
        UNION ALL
        SELECT--市场准入（开发进院费用类别）
              a.fd_3a5425e42b5732 'accountingCode', a.fd_3a5425f18f1308 'accountingSubjectName', '康方会计科目' 'accountingSubjectStructureName',
              a.fd_3a5425d420c8a4 'category', ( CASE a.fd_3a54260884c110 WHEN '启用' THEN 'Y' ELSE 'N' END ) 'enableFlag'
        FROM ekp_HyfybxKmcwb a*/
    </select>
</mapper>