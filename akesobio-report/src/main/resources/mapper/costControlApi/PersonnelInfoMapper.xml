<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControlApi.mapper.PersonnelInfoMapper">

    <resultMap type="com.akesobio.report.costControlApi.entity.PersonnelInfo" id="PersonnelInfoResult">
        <result property="userEmployeeID" column="userEmployeeID"/>
        <result property="emailAddress" column="emailAddress"/>
        <result property="fullName" column="fullName"/>
        <result property="departmentCode" column="departmentCode"/>
        <result property="defaultAccountingCompany" column="defaultAccountingCompany"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="jobCode" column="jobCode"/>
        <result property="userLevel" column="userLevel"/>
        <result property="phoneNumber" column="phoneNumber"/>
        <result property="userName" column="userName"/>
        <result property="enableFlag" column="enableFlag"/>
        <result property="joinedDate" column="joinedDate"/>
        <result property="terminationDate" column="terminationDate"/>
        <result property="disableDate" column="disableDate"/>
        <result property="superiorEmployeeNumber" column="superiorEmployeeNumber"/>
        <result property="sapPartner" column="sapPartner"/>
        <result property="baseCity" column="baseCity"/>
        <result property="column1" column="column1"/>
    </resultMap>

    <select id="selectAllPersonnelInfoList" resultMap="PersonnelInfoResult">
        SELECT userEmployeeID,
        emailAddress,
        fullName,
        departmentCode,
        defaultAccountingCompany,
        costCenterCode,
        jobCode,
        userLevel,
        phoneNumber,
        userName,
        enableFlag,
        joinedDate,
        terminationDate,
        disableDate,
        superiorEmployeeNumber,
        sapPartner,
        baseCity,
        column1
        FROM (
        SELECT a.job_number 'userEmployeeID', a.email 'emailAddress', a.name 'fullName', a.dept_id 'departmentCode', a.org_id 'defaultAccountingCompany', a.cost_center_code 'costCenterCode', NULL 'jobCode', a.business_trip_type 'userLevel', a.mobile 'phoneNumber', a.job_number+ '@akesobio.com' AS 'userName', a.employee_status 'enableFlag', a.entry_date 'joinedDate', a.leave_date 'terminationDate', a.leave_date 'disableDate', b.job_number 'superiorEmployeeNumber', a.sap_partner 'sapPartner', ( CASE a.work_address WHEN '中山火炬' THEN '中山' ELSE a.work_address END ) 'baseCity', a.sap_partner 'column1'
        FROM hr_employee_info a
        LEFT JOIN hr_employee_info b ON a.leader_id = b.id
        WHERE a.employee_status!= ''
        ) b
    </select>

    <select id="selectPersonnelInfoList" resultMap="PersonnelInfoResult">
        SELECT userEmployeeID,
        emailAddress,
        fullName,
        departmentCode,
        defaultAccountingCompany,
        costCenterCode,
        jobCode,
        userLevel,
        phoneNumber,
        userName,
        enableFlag,
        joinedDate,
        terminationDate,
        disableDate,
        superiorEmployeeNumber,
        sapPartner,
        baseCity,
        column1
        FROM (
        SELECT a.job_number 'userEmployeeID', a.email 'emailAddress', a.name 'fullName', a.dept_id 'departmentCode',
        a.org_id 'defaultAccountingCompany', a.cost_center_code 'costCenterCode', NULL 'jobCode', a.business_trip_type
        'userLevel', a.mobile 'phoneNumber', a.job_number+ '@akesobio.com' AS 'userName', a.employee_status
        'enableFlag', a.entry_date 'joinedDate', a.leave_date 'terminationDate', a.leave_date 'disableDate',
        b.job_number 'superiorEmployeeNumber', a.sap_partner 'sapPartner', ( CASE a.work_address WHEN '中山火炬' THEN '中山'
        ELSE a.work_address END ) 'baseCity', a.sap_partner 'column1'
        FROM hr_employee_info a
        LEFT JOIN hr_employee_info b ON a.leader_id = b.id
        WHERE a.employee_status!= ''
        AND ( a.dept_name_all LIKE '%商业运营部%' OR a.dept_name_all LIKE '%财务%' OR a.dept_name_all LIKE '%生产与质量部门_产品供应部%' OR
        a.dept_name_all LIKE '%总裁办%' )
        <if test="authorizedJobNumbers != null and authorizedJobNumbers.size() > 0">
            OR a.job_number IN
            <foreach item="jobNumber" index="index" collection="authorizedJobNumbers" open="(" separator="," close=")">
                #{jobNumber}
            </foreach>
        </if>
        ) b
        <where>
            <if test="personnelInfo.userEmployeeID != null  and personnelInfo.userEmployeeID != ''">and userEmployeeID = #{personnelInfo.userEmployeeID}</if>
        </where>
    </select>

    <select id="selectPersonnelInfoListNew" resultMap="PersonnelInfoResult">
        SELECT userEmployeeID,
        emailAddress,
        fullName,
        departmentCode,
        defaultAccountingCompany,
        costCenterCode,
        jobCode,
        userLevel,
        phoneNumber,
        userName,
        enableFlag,
        joinedDate,
        terminationDate,
        disableDate,
        superiorEmployeeNumber,
        sapPartner,
        baseCity,
        column1
        FROM (
        SELECT a.job_number 'userEmployeeID', a.email 'emailAddress', a.name 'fullName', a.dept_id 'departmentCode', a.org_id 'defaultAccountingCompany', a.cost_center_code 'costCenterCode', NULL 'jobCode', a.business_trip_type 'userLevel', a.mobile 'phoneNumber', a.job_number+ '@akesobio.com' AS 'userName', a.employee_status 'enableFlag', a.entry_date 'joinedDate', a.leave_date 'terminationDate', a.leave_date 'disableDate', b.job_number 'superiorEmployeeNumber', a.sap_partner 'sapPartner', ( CASE a.work_address WHEN '中山火炬' THEN '中山' ELSE a.work_address END ) 'baseCity', a.sap_partner 'column1'
        FROM hr_employee_info a
        LEFT JOIN hr_employee_info b ON a.leader_id = b.id
        WHERE a.employee_status = '在职'
        AND (a.dept_name_all LIKE '%商业运营部%' OR a.dept_name_all LIKE '%财务%' OR
        a.dept_name_all LIKE '%生产与质量部门_产品供应部%' OR a.dept_name_all LIKE '%总裁办%')
        <if test="authorizedJobNumbers != null and authorizedJobNumbers.size() > 0">
            OR a.job_number IN
            <foreach item="jobNumber" index="index" collection="authorizedJobNumbers" open="(" separator="," close=")">
                #{jobNumber}
            </foreach>
        </if>
        ) b
    </select>
</mapper>