<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControlApi.mapper.DepartmentInfoMapper">

    <resultMap type="com.akesobio.report.costControlApi.entity.DepartmentInfo" id="DepartmentInfoResult">
        <result property="departmentId" column="departmentId"/>
        <result property="departmentName" column="departmentName"/>
        <result property="superiorDepartmentCode" column="superiorDepartmentCode"/>
        <result property="organizationName" column="organizationName"/>
        <result property="departmentJobNumber" column="departmentJobNumber"/>
        <result property="type" column="type"/>
        <result property="serialNumber" column="serialNumber"/>
        <result property="costCenterLogo" column="costCenterLogo"/>
        <result property="enableFlag" column="enableFlag"/>
        <result property="updateTime" column="updateTime"/>
    </resultMap>

    <select id="selectDepartmentInfoList" resultMap="DepartmentInfoResult">
        SELECT departmentId,
               departmentName,
               superiorDepartmentCode,
               organizationName,
               departmentJobNumber,
               type,
               serialNumber,
               costCenterLogo,
               enableFlag,
               updateTime
        FROM (
                 SELECT a.fd_id 'departmentId', a.fd_name 'departmentName', b.fd_id 'superiorDepartmentCode', c.fd_name 'organizationName', d.fd_no 'departmentJobNumber', 'D' 'type',
                        NULL 'serialNumber', 'N' 'costCenterLogo',
                        a.fd_is_available 'enableFlag', a.fd_alter_time 'updateTime'
                 FROM sys_org_element a
                          LEFT JOIN sys_org_element b ON b.fd_id = a.fd_parentid
                          LEFT JOIN sys_org_element c ON c.fd_id = a.fd_parentorgid
                          LEFT JOIN sys_org_element d ON d.fd_id = a.fd_this_leaderid
                 WHERE a.fd_org_type = '2'
                   AND a.fd_is_available = '1'
                   AND c.fd_name IN ('中山康方', '康方药业', '康方赛诺', '康融广东', '泽昇医药', '北京分公司', '上海汇科', 'BVI')
             ) b
        ORDER BY updateTime desc
    </select>

    <select id="selectDepartmentInfoOldList" resultMap="DepartmentInfoResult">
        SELECT departmentId,
               departmentName,
               superiorDepartmentCode,
               organizationName,
               departmentJobNumber,
               type,
               serialNumber,
               costCenterLogo,
               enableFlag,
               updateTime
        FROM (
                 SELECT a.fd_id 'departmentId', a.fd_name 'departmentName', b.fd_id 'superiorDepartmentCode', c.fd_name 'organizationName', d.fd_no 'departmentJobNumber', 'D' 'type',
                        NULL 'serialNumber', 'N' 'costCenterLogo',
                        a.fd_is_available 'enableFlag', a.fd_alter_time 'updateTime'
                 FROM sys_org_element a
                          LEFT JOIN sys_org_element b ON b.fd_id = a.fd_parentid
                          LEFT JOIN sys_org_element c ON c.fd_id = a.fd_parentorgid
                          LEFT JOIN sys_org_element d ON d.fd_id = a.fd_this_leaderid
                 WHERE a.fd_org_type = '2'
                   AND a.fd_is_available = '0'
                   AND a.fd_alter_time > '2024-12-20'
             ) b
    </select>


    <select id="selectDepartmentInfoList1" resultMap="DepartmentInfoResult">
        SELECT departmentId,
               departmentName,
               superiorDepartmentCode,
               organizationName,
               departmentJobNumber,
               type,
               serialNumber,
               costCenterLogo,
               enableFlag,
               updateTime
        FROM (
                 SELECT a.fd_id 'departmentId', a.fd_name 'departmentName', b.fd_id 'superiorDepartmentCode', c.fd_name 'organizationName', d.fd_no 'departmentJobNumber', 'C' 'type',
                        NULL 'serialNumber', 'Y' 'costCenterLogo',
                        a.fd_is_available 'enableFlag', a.fd_alter_time 'updateTime'
                 FROM sys_org_element a
                          LEFT JOIN sys_org_element b ON b.fd_id = a.fd_parentid
                          LEFT JOIN sys_org_element c ON c.fd_id = a.fd_parentorgid
                          LEFT JOIN sys_org_element d ON d.fd_id = a.fd_this_leaderid
                 WHERE a.fd_org_type = '2'
                   AND a.fd_is_available = '1'
--                    AND c.fd_name IN ('康方生物', '康方药业', '康方赛诺', '康融广东', '泽昇药业', '北京分公司', '康方汇科（上海）')
             ) b
    </select>

</mapper>