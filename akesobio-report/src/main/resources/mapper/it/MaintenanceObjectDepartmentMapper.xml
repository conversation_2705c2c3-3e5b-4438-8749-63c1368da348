<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.it.mapper.MaintenanceObjectDepartmentMapper">

    <resultMap type="MaintenanceObjectDepartment" id="MaintenanceObjectDepartmentResult">
        <result property="name" column="name"/>
        <result property="jobNumber" column="jobNumber"/>
        <result property="groupFinance" column="groupFinance"/>
        <result property="marketingFinance" column="marketingFinance"/>
        <result property="commercialOperations" column="commercialOperations"/>
        <result property="legalDepartment" column="legalDepartment"/>
        <result property="equipment" column="equipment"/>
        <result property="productionDepartment" column="productionDepartment"/>
        <result property="qualityControl" column="qualityControl"/>
        <result property="qualityAssurance" column="qualityAssurance"/>
        <result property="humanResourcesDepartment" column="humanResourcesDepartment"/>
        <result property="materialManagementDepartment" column="materialManagementDepartment"/>
        <result property="productionAutomation" column="productionAutomation"/>
        <result property="pharmaceuticalAffairsDepartmen" column="pharmaceuticalAffairsDepartmen"/>
        <result property="externalAffairsDepartment" column="externalAffairsDepartment"/>
        <result property="clinicalScienceDepartment" column="clinicalScienceDepartment"/>
        <result property="clinicalDepartment" column="clinicalDepartment"/>
        <result property="operationDepartment" column="operationDepartment"/>
        <result property="biostatisticsDepartment" column="biostatisticsDepartment"/>
        <result property="pharmacovigilanceDepartment" column="pharmacovigilanceDepartment"/>
        <result property="productionScienceTechnology" column="productionScienceTechnology"/>
        <result property="softwareMaintenance" column="softwareMaintenance"/>
        <result property="networkMaintenance" column="networkMaintenance"/>
        <result property="hardwareMaintenance" column="hardwareMaintenance"/>
        <result property="weakCurrentEngineering" column="weakCurrentEngineering"/>
        <result property="otherMaintenance" column="otherMaintenance"/>
        <result property="reinstallTheSystem" column="reinstallTheSystem"/>
        <result property="applicationSoftwareInstallatio" column="applicationSoftwareInstallatio"/>
        <result property="softwareTechnicalSupport" column="softwareTechnicalSupport"/>
        <result property="softwareUpdates" column="softwareUpdates"/>
        <result property="softwareFaultRepair" column="softwareFaultRepair"/>
        <result property="exchangeBoard" column="exchangeBoard"/>
        <result property="informationSecurityEquipment" column="informationSecurityEquipment"/>
        <result property="lineFaultMaintenance" column="lineFaultMaintenance"/>
        <result property="networkingConfiguration" column="networkingConfiguration"/>
        <result property="wiFiMaintenance" column="wiFiMaintenance"/>
        <result property="externalNetwork" column="externalNetwork"/>
        <result property="server" column="server"/>
        <result property="monitor" column="monitor"/>
        <result property="miniPrinter" column="miniPrinter"/>
        <result property="copy" column="copy"/>
        <result property="terminalBox" column="terminalBox"/>
        <result property="sound" column="sound"/>
        <result property="laptop" column="laptop"/>
        <result property="camera" column="camera"/>
        <result property="terminalAccessControl" column="terminalAccessControl"/>
        <result property="telephone" column="telephone"/>
        <result property="burner" column="burner"/>
        <result property="projector" column="projector"/>
        <result property="projectionScreen" column="projectionScreen"/>
        <result property="maxhub" column="maxhub"/>
        <result property="weakCurrentPlanning" column="weakCurrentPlanning"/>
        <result property="weakCurrentConstruction" column="weakCurrentConstruction"/>
        <result property="weakCurrentConference" column="weakCurrentConference"/>
        <result property="basicData" column="basicData"/>
        <result property="accountPermissions" column="accountPermissions"/>
        <result property="maintenanceAssistance" column="maintenanceAssistance"/>
        <result property="problem" column="problem"/>
        <result property="machineRoomInspection" column="machineRoomInspection"/>
    </resultMap>


    <select id="selectMaintenanceObjectDepartmentList" parameterType="MaintenanceObjectDepartment"
            resultMap="MaintenanceObjectDepartmentResult">
        select * from (
        select
        b.fd_name as 'name',
        a.fd_jobno as 'jobNumber',
        SUM(CASE c.fd_targetdep WHEN '集团财务' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'groupFinance',
        SUM(CASE c.fd_targetdep WHEN '营销财务' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'marketingFinance',
        SUM(CASE c.fd_targetdep WHEN '商业运营' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'commercialOperations',
        SUM(CASE c.fd_targetdep WHEN '法务部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'legalDepartment',
        SUM(CASE c.fd_targetdep WHEN '设备部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'equipment',
        SUM(CASE c.fd_targetdep WHEN '生产部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'productionDepartment',
        SUM(CASE c.fd_targetdep WHEN '质量控制部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'qualityControl',
        SUM(CASE c.fd_targetdep WHEN '质量保证部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'qualityAssurance',
        SUM(CASE c.fd_targetdep WHEN '人力资源部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'humanResourcesDepartment',
        SUM(CASE c.fd_targetdep WHEN '物料管理部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'materialManagementDepartment',
        SUM(CASE c.fd_targetdep WHEN '生产自动化' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'productionAutomation',
        SUM(CASE c.fd_targetdep WHEN '药政事务部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'pharmaceuticalAffairsDepartmen',
        SUM(CASE c.fd_targetdep WHEN '外联事务部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'externalAffairsDepartment',
        SUM(CASE c.fd_targetdep WHEN '临床科学部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'clinicalScienceDepartment',
        SUM(CASE c.fd_targetdep WHEN '临床部门' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'clinicalDepartment',
        SUM(CASE c.fd_targetdep WHEN '运营部门' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'operationDepartment',
        SUM(CASE c.fd_targetdep WHEN '生物统计部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'biostatisticsDepartment',
        SUM(CASE c.fd_targetdep WHEN 'PV药物警戒部' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'pharmacovigilanceDepartment',
        SUM(CASE c.fd_targetdep WHEN 'MST生产科学与技术' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'productionScienceTechnology',
        SUM(CASE c.fd_worktype2_text WHEN '软件运维' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'softwareMaintenance',
        SUM(CASE c.fd_worktype2_text WHEN '网络运维' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'networkMaintenance',
        SUM(CASE c.fd_worktype2_text WHEN '硬件运维' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'hardwareMaintenance',
        SUM(CASE c.fd_worktype2_text WHEN '弱电工程' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'weakCurrentEngineering',
        SUM(CASE c.fd_worktype2_text WHEN '其他运维' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'otherMaintenance',
        SUM(CASE c.fd_worktype4_text WHEN '重装系统' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'reinstallTheSystem',
        SUM(CASE c.fd_worktype4_text WHEN '应用软件安装' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'applicationSoftwareInstallation',
        SUM(CASE c.fd_worktype4_text WHEN '软件技术支持' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'softwareTechnicalSupport',
        SUM(CASE c.fd_worktype4_text WHEN '软件更新' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'softwareUpdates',
        SUM(CASE c.fd_worktype4_text WHEN '软件故障维修' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'softwareFaultRepair',
        SUM(CASE c.fd_worktype4_text WHEN '交换机' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'exchangeBoard',
        SUM(CASE c.fd_worktype4_text WHEN '信息安全设备' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'informationSecurityEquipment',
        SUM(CASE c.fd_worktype4_text WHEN '线路故障维修' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'lineFaultMaintenance',
        SUM(CASE c.fd_worktype4_text WHEN '组网配置' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'networkingConfiguration',
        SUM(CASE c.fd_worktype4_text WHEN 'WiFi维护' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'wiFiMaintenance',
        SUM(CASE c.fd_worktype4_text WHEN '外部网络' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'externalNetwork',
        SUM(CASE c.fd_worktype4_text WHEN '服务器' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'server',
        SUM(CASE c.fd_worktype4_text WHEN '显示器' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'monitor',
        SUM(CASE c.fd_worktype4_text WHEN '小型打印机' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'miniPrinter',
        SUM(CASE c.fd_worktype4_text WHEN '复印件' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'copy',
        SUM(CASE c.fd_worktype4_text WHEN '终端盒' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'terminalBox',
        SUM(CASE c.fd_worktype4_text WHEN '音响' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'sound',
        SUM(CASE c.fd_worktype4_text WHEN '笔记本' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'laptop',
        SUM(CASE c.fd_worktype4_text WHEN '摄像机' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'camera',
        SUM(CASE c.fd_worktype4_text WHEN '终端门禁' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'terminalAccessControl',
        SUM(CASE c.fd_worktype4_text WHEN '电话机' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'telephone',
        SUM(CASE c.fd_worktype4_text WHEN '刻录机' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'burner',
        SUM(CASE c.fd_worktype4_text WHEN '投影仪' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'projector',
        SUM(CASE c.fd_worktype4_text WHEN '投影幕布' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'projectionScreen',
        SUM(CASE c.fd_worktype4_text WHEN 'MAXHUB' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'maxhub',
        SUM(CASE c.fd_worktype4_text WHEN '弱电规划' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'weakCurrentPlanning',
        SUM(CASE c.fd_worktype4_text WHEN '弱电施工' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'weakCurrentConstruction',
        SUM(CASE c.fd_worktype4_text WHEN '弱电会议' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'weakCurrentConference',
        SUM(CASE c.fd_worktype4_text WHEN '基础数据' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'basicData',
        SUM(CASE c.fd_worktype4_text WHEN '账号权限' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'accountPermissions',
        SUM(CASE c.fd_worktype4_text WHEN '运维协助' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'maintenanceAssistance',
        SUM(CASE c.fd_worktype4_text WHEN '问题答疑' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'problem',
        SUM(CASE c.fd_worktype4_text WHEN '机房检查' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'machineRoomInspection'
        from ekp_gzit_workplan a
        left join hr_org_element b on b.fd_id=a.fd_applicant
        left join ekp_t_workplan_list c on c.fd_parent_id=a.fd_id
        <where>
            <if test="params.beginDateCreated != null  and params.beginDateCreated != '' and params.endDateCreated != null  and params.endDateCreated != '' ">
                and c.fd_completiontime between #{params.beginDateCreated} and #{params.endDateCreated}
            </if>
            <if test="name != null  and name != ''">and b.fd_name like concat('%', #{name}, '%')</if>
            <if test="jobNumber != null  and jobNumber != ''">and a.fd_jobno like concat('%', #{jobNumber}, '%')</if>
            and
            c.fd_worktype1_text='桌面运维'
            and
            c.fd_state ='2'
            and
            (b.fd_name='陈灿桥'
            or
            b.fd_name='邓怀业')
        </where>
        GROUP BY
        a.fd_jobno,b.fd_name
        )d
    </select>

</mapper>