<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.operationLog.mapper.OperationLogMapper">
    <!-- 实体类  -->
    <resultMap type="OperationLog" id="OperationLogResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectOperationLogList">
        SELECT
            id,
            table_name,
            operation_type,
            operator,
            create_time,
            original_data,
            new_data
        FROM operation_log
    </sql>
    
    <!-- 方法 -->
    <select id="queryOperationLogList" parameterType="OperationLog" resultMap="OperationLogResult">
        <include refid="selectOperationLogList"/>
        <where>  
            <if test="tableName != null and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
            <if test="operationType != null and operationType != ''"> and operation_type like concat('%', #{operationType}, '%')</if>
            <if test="operator != null and operator != ''"> and operator like concat('%', #{operator}, '%')</if>

            <if test="startDate != null and startDate != ''">and create_time &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and create_time &lt;= #{endDate}</if>
        </where>
        ORDER BY create_time DESC
    </select>
    <!-- 添加 -->
    <insert id="insertOperationLog" parameterType="OperationLog">
        INSERT INTO operation_log(
        <if test="tableName != null and tableName != ''">table_name,</if>
        <if test="operationType != null and operationType != ''">operation_type,</if>
        <if test="operator != null and operator != ''">operator,</if>
        <if test="originalData != null and originalData != ''">original_data,</if>
        <if test="newData != null and newData != ''">new_data,</if>
        create_time
        )values (
        <if test="tableName != null and tableName != ''">#{tableName},</if>
        <if test="operationType != null and operationType != ''">#{operationType},</if>
        <if test="operator != null and operator != ''">#{operator},</if>
        <if test="originalData != null and originalData != ''">#{originalData},</if>
        <if test="newData != null and newData != ''">#{newData},</if>
        getdate()
        )
    </insert>
</mapper>