<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.FinancialBriefingMapper">

    <resultMap type="FinancialBriefing" id="FinancialBriefingResult">
        <result property="id" column="id"/>
        <result property="fileName" column="fileName"/>
        <result property="filePath" column="filePath"/>
        <result property="jobNumber" column="jobNumber"/>
    </resultMap>

    <sql id="selectFinancialBriefingVo">
        select id, fileName, filePath, jobNumber
        from financial_briefing
    </sql>

    <select id="selectFinancialBriefingList" parameterType="FinancialBriefing" resultMap="FinancialBriefingResult">
        <include refid="selectFinancialBriefingVo"/>
        <where>
            <if test="fileName != null  and fileName != ''">and fileName like concat('%', #{fileName}, '%')</if>
            <if test="filePath != null  and filePath != ''">and filePath = #{filePath}</if>
            <if test="jobNumber != null  and jobNumber != ''">and jobNumber = #{jobNumber}</if>
        </where>
    </select>

    <select id="selectFinancialBriefingById" parameterType="Integer" resultMap="FinancialBriefingResult">
        <include refid="selectFinancialBriefingVo"/>
        where id = #{id}
    </select>

    <insert id="insertFinancialBriefing" parameterType="FinancialBriefing">
        insert into financial_briefing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fileName != null">fileName,</if>
            <if test="filePath != null">filePath,</if>
            <if test="jobNumber != null">jobNumber,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="jobNumber != null">#{jobNumber},</if>
        </trim>
    </insert>

    <update id="updateFinancialBriefing" parameterType="FinancialBriefing">
        update financial_briefing
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">fileName = #{fileName},</if>
            <if test="filePath != null">filePath = #{filePath},</if>
            <if test="jobNumber != null">jobNumber = #{jobNumber},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinancialBriefingById" parameterType="Integer">
        delete
        from financial_briefing
        where id = #{id}
    </delete>

    <delete id="deleteFinancialBriefingByIds" parameterType="String">
        delete from financial_briefing where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>