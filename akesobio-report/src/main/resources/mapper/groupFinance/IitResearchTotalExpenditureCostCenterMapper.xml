<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.IitResearchTotalExpenditureCostCenterMapper">
    <resultMap type="com.akesobio.report.groupFinance.vo.IitResearchTotalExpenditureCostCenterVo" id="IitResearchTotalExpenditureCostCenterVoResult">
        <result property="year"    column="year" />
        <result property="month"   column="month"  />
        <result property="total"   column="total"  />
    </resultMap>
    <select id="costCenterList"  resultMap="IitResearchTotalExpenditureCostCenterVoResult">
        select Year(paymentApplicationDate) year,Month(paymentApplicationDate) month,SUM(CONVERT(DECIMAL(13,2),ISNULL(amount, 0))) total
        FROM IIT_research_business
        <where>
        <if test="costCenter !=null and costCenter != ''">and costCenterCode=#{costCenter}</if>
        <if test="date !=null and date !=''">and paymentApplicationDate >=#{date}</if>
        </where>
        Group by Year(paymentApplicationDate),Month(paymentApplicationDate)
        order by month ASC
    </select>

</mapper>
