<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.FixedAssetsMapper">
    <resultMap type="com.akesobio.report.groupFinance.domain.FixedAssets" id="FixedAssetsResult">
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="assetClassificationName" column="assetClassificationName"/>
        <result property="money1" column="money1"/>
        <result property="money2" column="money2"/>
        <result property="money3" column="money3"/>
    </resultMap>

    <select id="selectFixedAssets" resultMap="FixedAssetsResult">
        select companyName,assetClassificationName,money1,money2,money3,companyCode
        from
        (select
        a.butxt as 'companyName',
        a.bukrs as 'companyCode',
        a.txk20 as 'assetClassificationName',
        a.zqmyz as 'money1',
        a.zqmljzj as 'money2',
        a.zjz as 'money3'
        from FixedAssetsReport a) b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="assetClassificationName !=null and assetClassificationName !=''">and assetClassificationName =
                #{assetClassificationName}
            </if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
        </where>
    </select>

    <select id="selectFixedAssetsA1A2" resultMap="FixedAssetsResult">
        select companyName,assetClassificationName,money1,money2,money3,companyCode
        from
        (select
        a.butxt as 'companyName',
        a.bukrs as 'companyCode',
        a.txk20 as 'assetClassificationName',
        a.zqmyz as 'money1',
        a.zqmljzj as 'money2',
        a.zjz as 'money3'
        from FixedAssetsReport a where (a.anlkl='A230' or a.anlkl='A210' or a.anlkl='A160' or a.anlkl='A140' or a.anlkl='A110' or a.anlkl='A170' or a.anlkl='A120')) b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="assetClassificationName !=null and assetClassificationName !=''">and assetClassificationName =
                #{assetClassificationName}
            </if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
        </where>
    </select>

    <select id="selectFixedAssetsA3" resultMap="FixedAssetsResult">
        select companyName,assetClassificationName,money1,money2,money3,companyCode
        from
        (select
        a.butxt as 'companyName',
        a.bukrs as 'companyCode',
        a.txk20 as 'assetClassificationName',
        a.zqmyz as 'money1',
        a.zqmljzj as 'money2',
        a.zjz as 'money3'
        from FixedAssetsReport a where (a.anlkl='A310' or a.anlkl='A320' or a.anlkl='A330' or a.anlkl='A340' or a.anlkl='A350')) b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="assetClassificationName !=null and assetClassificationName !=''">and assetClassificationName =
                #{assetClassificationName}
            </if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
        </where>
    </select>

    <select id="selectTop10FixedAssets" resultMap="FixedAssetsResult">
        select top 10 b.companyName,b.assetClassificationName,b.money1,b.money2,b.money3,b.companyCode,b.assetDescription
        from
        (select
        a.butxt as 'companyName',
        a.bukrs as 'companyCode',
        a.txk20 as 'assetClassificationName',
        a.zqmyz as 'money1',
        a.zqmljzj as 'money2',
        a.zjz as 'money3',
        a.txt50 as 'assetDescription'
        from FixedAssetsReport a ) b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
        </where>
        order by money3 desc
    </select>

    <select id="selectTop10ConstructionInProgress" resultMap="FixedAssetsResult">
        select top 10 b.companyName,b.assetClassificationName,b.money1,b.money2,b.money3,b.companyCode,b.assetDescription
        from
        (select
        a.butxt as 'companyName',
        a.bukrs as 'companyCode',
        a.txk20 as 'assetClassificationName',
        a.zqmyz as 'money1',
        a.zqmljzj as 'money2',
        a.zjz as 'money3',
        a.txt50 as 'assetDescription'
        from FixedAssetsReport a where a.txk20='在建工程') b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
        </where>
        order by money3 desc
    </select>

    <select id="selectTop10DeferredExpenses" resultMap="FixedAssetsResult">
        select top 10 b.companyName,b.assetClassificationName,b.money1,b.money2,b.money3,b.companyCode,b.assetDescription
        from
        (select
        a.butxt as 'companyName',
        a.bukrs as 'companyCode',
        a.txk20 as 'assetClassificationName',
        a.zqmyz as 'money1',
        a.zqmljzj as 'money2',
        a.zjz as 'money3',
        a.txt50 as 'assetDescription'
        from FixedAssetsReport a where a.txk20='长期待摊费用') b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
        </where>
        order by money3 desc
    </select>

    <select id="selectIntangibleAssets" resultMap="FixedAssetsResult">
        select companyName,assetClassificationName,money1,money2,money3,companyCode
        from
        (select
        a.butxt as 'companyName',
        a.bukrs as 'companyCode',
        a.txk20 as 'assetClassificationName',
        a.zqmyz as 'money1',
        a.zqmljzj as 'money2',
        a.zjz as 'money3'
        from FixedAssetsReport a where (a.anlkl='A310' or a.anlkl='A320' or a.anlkl='A330' or a.anlkl='A340' or a.anlkl='A350')) b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
        </where>
    </select>

    <select id="selectTop10IntangibleAssets" resultMap="FixedAssetsResult">
        select top 10 b.companyName,b.assetClassificationName,b.money1,b.money2,b.money3,b.companyCode,b.assetDescription
        from
        (select
        a.butxt as 'companyName',
        a.bukrs as 'companyCode',
        a.txk20 as 'assetClassificationName',
        a.zqmyz as 'money1',
        a.zqmljzj as 'money2',
        a.zjz as 'money3',
        a.txt50 as 'assetDescription'
        from FixedAssetsReport a where (a.anlkl='A310' or a.anlkl='A320' or a.anlkl='A330' or a.anlkl='A340' or a.anlkl='A350')) b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
        </where>
    </select>

    <select id="selectFixedAssetCategory" resultMap="FixedAssetsResult">
        select companyName,assetClassificationName,money1,money2,money3,companyCode,assetDescription
        from
        (select
        a.butxt as 'companyName',
        a.bukrs as 'companyCode',
        a.txk20 as 'assetClassificationName',
        a.zqmyz as 'money1',
        a.zqmljzj as 'money2',
        a.zjz as 'money3',
        a.txt50 as 'assetDescription'
        from FixedAssetsReport a where (a.anlkl='A110' or a.anlkl='A120' or a.anlkl='A130' or a.anlkl='A140' or a.anlkl='A150'
            or a.anlkl='A160' or a.anlkl='A170' or a.anlkl='A180' or a.anlkl='A210' or a.anlkl='A220' or a.anlkl='A230')) b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
        </where>
    </select>

    <select id="selectIntangibleAssetsCategory" resultMap="FixedAssetsResult">
        select companyName,assetClassificationName,money1,money2,money3,companyCode,assetDescription
        from
        (select
        a.butxt as 'companyName',
        a.bukrs as 'companyCode',
        a.txk20 as 'assetClassificationName',
        a.zqmyz as 'money1',
        a.zqmljzj as 'money2',
        a.zjz as 'money3',
        a.txt50 as 'assetDescription'
        from FixedAssetsReport a where (a.anlkl='A310' or a.anlkl='A320' or a.anlkl='A330' or a.anlkl='A340' or a.anlkl='A350')) b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
        </where>
    </select>




</mapper>