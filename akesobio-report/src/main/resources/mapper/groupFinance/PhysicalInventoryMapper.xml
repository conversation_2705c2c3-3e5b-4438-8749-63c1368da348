<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.PhysicalInventoryMapper">
    <resultMap type="com.akesobio.report.groupFinance.domain.PhysicalInventory" id="PhysicalInventoryResult">
        <result property="factory" column="factory"/>
        <result property="materialNo" column="materialNo"/>
        <result property="materialDescription" column="materialDescription"/>
        <result property="inventoryQuantity" column="inventoryQuantity"/>
        <result property="inventoryAmount" column="inventoryAmount"/>
        <result property="transitQuantity" column="transitQuantity"/>
        <result property="transitAmount" column="transitAmount"/>
        <result property="inventoryDate" column="inventoryDate"/>
    </resultMap>
    <select id="selectPhysicalInventoryList" resultMap="PhysicalInventoryResult">
        select factory,materialNo,materialDescription,inventoryQuantity,inventoryAmount,transitQuantity,transitAmount,inventoryDate
        from (
        select
        a.werks 'factory',
        a.matnr 'materialNo',
        a.maktx 'materialDescription',
        a.stock_qty 'inventoryQuantity',
        a.stock_amount 'inventoryAmount',
        a.act_qty 'transitQuantity',
        a.act_amount 'transitAmount',
        a.zdate 'inventoryDate'
        from ActualInventoryReport a) b
        <where>
            <if test="factory != null and factory !=''">and factory = #{factory}</if>
            <if test="materialNo != null and materialNo !=''">and materialNo like concat('%',#{materialNo},'%')</if>
            <if test="inventoryDate != null and inventoryDate !=''">and inventoryDate = #{inventoryDate}</if>

        </where>
    </select>
</mapper>