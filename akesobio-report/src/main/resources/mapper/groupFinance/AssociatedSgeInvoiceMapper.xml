<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.AssociatedSgeInvoiceMapper">
    
    <resultMap type="AssociatedSgeInvoice" id="AssociatedSgeInvoiceResult">
        <result property="userName"    column="user_name"    />
        <result property="userAccount"    column="user_account"    />
        <result property="createTime"    column="create_time"    />
        <result property="invoiceNo"    column="invoice_no"    />
        <result property="invoiceCode"    column="invoice_code"    />
        <result property="invoiceDate"    column="invoice_date"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="deductTax"    column="deduct_tax"    />
        <result property="amountTax"    column="amount_tax"    />
        <result property="purchaserName"    column="purchaser_name"    />
        <result property="purchaserTaxNo"    column="purchaser_tax_no"    />
        <result property="purchaserAddressPhone"    column="purchaser_address_phone"    />
        <result property="purchaserBank"    column="purchaser_bank"    />
        <result property="saleName"    column="sale_name"    />
        <result property="saleTaxNo"    column="saleTax_no"    />
        <result property="saleAddressPhone"    column="sale_address_phone"    />
        <result property="saleBank"    column="sale_bank"    />
        <result property="machineCode"    column="machine_code"    />
        <result property="checkCode"    column="check_code"    />
        <result property="remark"    column="remark"    />
        <result property="commodityName"    column="commodity_name"    />
        <result property="unit"    column="unit"    />
        <result property="quantity"    column="quantity"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="amount"    column="amount"    />
        <result property="taxRate"    column="tax_rate"    />
        <result property="tax"    column="tax"    />
        <result property="isDeduct"    column="is_deduct"    />
        <result property="updateTime"    column="update_time"    />
        <result property="orgId"    column="org_id"    />
        <result property="orgName"    column="org_name"    />
        <result property="signStatus"    column="sign_status"    />
        <result property="fileAddress"    column="file_address"    />
        <result property="accounttingNo"    column="accountting_no"    />
    </resultMap>

    <sql id="selectAssociatedSgeInvoiceVo">
        select i.user_name, i.user_account, i.create_time, i.invoice_no, i.invoice_code, i.invoice_date, i.invoice_type, i.total_amount, i.deduct_tax, i.amount_tax, i.purchaser_name, i.purchaser_tax_no, i.purchaser_address_phone, i.purchaser_bank, i.sale_name, i.sale_tax_no, i.sale_address_phone, i.sale_bank, i.machine_code, i.check_code, i.remark, idl.commodity_name, idl.unit, idl.quantity, idl.unit_price, idl.amount, idl.tax_rate, idl.tax, i.is_deduct, i.update_time, i.org_id, i.org_name, i.sign_status, i.file_address, qrml.accountting_no
        from
            invoice AS i
            LEFT JOIN query_reimbursement_main_list AS qrml ON i.invoice_no = qrml.inv_num
            LEFT JOIN invoice_detail_list AS idl ON i.id = idl.invoice_id
        WHERE
            i.reimburse_status = 2
          AND i.invoice_type NOT IN ('1002', '1003', '1004', '1005', '1010', '14', '1007', '1008')
    </sql>

    <select id="selectAssociatedSgeInvoiceList" parameterType="AssociatedSgeInvoice" resultMap="AssociatedSgeInvoiceResult">
        <include refid="selectAssociatedSgeInvoiceVo"/>
        <if test="userName != null  and userName != ''"> and i.user_name like concat('%', #{userName}, '%')</if>
        <if test="userAccount != null  and userAccount != ''"> and i.user_account = #{userAccount}</if>
        <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and i.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        <if test="invoiceNo != null  and invoiceNo != ''"> and i.invoice_no = #{invoiceNo}</if>
        <if test="invoiceCode != null  and invoiceCode != ''"> and i.invoice_code = #{invoiceCode}</if>
        <if test="invoiceDate != null  and invoiceDate != ''"> and i.invoice_date = #{invoiceDate}</if>
        <if test="invoiceType != null  and invoiceType != ''"> and i.invoice_type = #{invoiceType}</if>
        <if test="totalAmount != null "> and i.total_amount = #{totalAmount}</if>
        <if test="deductTax != null "> and i.deduct_tax = #{deductTax}</if>
        <if test="amountTax != null "> and i.amount_tax = #{amountTax}</if>
        <if test="purchaserName != null  and purchaserName != ''"> and i.purchaser_name like concat('%', #{purchaserName}, '%')</if>
        <if test="purchaserTaxNo != null  and purchaserTaxNo != ''"> and i.purchaser_tax_no = #{purchaserTaxNo}</if>
        <if test="purchaserAddressPhone != null  and purchaserAddressPhone != ''"> and i.purchaser_address_phone = #{purchaserAddressPhone}</if>
        <if test="purchaserBank != null  and purchaserBank != ''"> and i.purchaser_bank = #{purchaserBank}</if>
        <if test="saleName != null  and saleName != ''"> and i.sale_name like concat('%', #{saleName}, '%')</if>
        <if test="saleTaxNo != null  and saleTaxNo != ''"> and i.sale_tax_no = #{saleTaxNo}</if>
        <if test="saleAddressPhone != null  and saleAddressPhone != ''"> and i.sale_address_phone = #{saleAddressPhone}</if>
        <if test="saleBank != null  and saleBank != ''"> and i.sale_bank = #{saleBank}</if>
        <if test="machineCode != null  and machineCode != ''"> and i.machine_code = #{machineCode}</if>
        <if test="checkCode != null  and checkCode != ''"> and i.check_code = #{checkCode}</if>
        <if test="commodityName != null  and commodityName != ''"> and idl.commodity_name like concat('%', #{commodityName}, '%')</if>
        <if test="unit != null  and unit != ''"> and idl.unit = #{unit}</if>
        <if test="quantity != null "> and idl.quantity = #{quantity}</if>
        <if test="unitPrice != null "> and idl.unit_price = #{unitPrice}</if>
        <if test="amount != null "> and idl.amount = #{amount}</if>
        <if test="taxRate != null "> and idl.tax_rate = #{taxRate}</if>
        <if test="tax != null "> and idl.tax = #{tax}</if>
        <if test="isDeduct != null "> and i.is_deduct = #{isDeduct}</if>
        <if test="updateTime != null  and updateTime != ''"> and i.update_time = #{updateTime}</if>
        <if test="orgId != null  and orgId != ''"> and i.org_id = #{orgId}</if>
        <if test="orgName != null  and orgName != ''"> and i.org_name like concat('%', #{orgName}, '%')</if>
        <if test="signStatus != null "> and i.sign_status = #{signStatus}</if>
        <if test="fileAddress != null  and fileAddress != ''"> and i.file_address = #{fileAddress}</if>
        <if test="accounttingNo != null  and accounttingNo != ''"> and qrml.accountting_no = #{accounttingNo}</if>
        order by i.create_time desc
    </select>
    
    <select id="selectAssociatedSgeInvoiceByUserName" parameterType="String" resultMap="AssociatedSgeInvoiceResult">
        <include refid="selectAssociatedSgeInvoiceVo"/>
        where userName = #{userName}
    </select>
        
    <insert id="insertAssociatedSgeInvoice" parameterType="AssociatedSgeInvoice">
        insert into associated_sge_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">userName,</if>
            <if test="userAccount != null">userAccount,</if>
            <if test="createTime != null">createTime,</if>
            <if test="invoiceNo != null">invoiceNo,</if>
            <if test="invoiceCode != null">invoiceCode,</if>
            <if test="invoiceDate != null">invoiceDate,</if>
            <if test="invoiceType != null">invoiceType,</if>
            <if test="totalAmount != null">totalAmount,</if>
            <if test="deductTax != null">deductTax,</if>
            <if test="amountTax != null">amountTax,</if>
            <if test="purchaserName != null">purchaserName,</if>
            <if test="purchaserTaxNo != null">purchaserTaxNo,</if>
            <if test="purchaserAddressPhone != null">purchaserAddressPhone,</if>
            <if test="purchaserBank != null">purchaserBank,</if>
            <if test="saleName != null">saleName,</if>
            <if test="saleTaxNo != null">saleTaxNo,</if>
            <if test="saleAddressPhone != null">saleAddressPhone,</if>
            <if test="saleBank != null">saleBank,</if>
            <if test="machineCode != null">machineCode,</if>
            <if test="checkCode != null">checkCode,</if>
            <if test="remark != null">remark,</if>
            <if test="commodityName != null">commodityName,</if>
            <if test="unit != null">unit,</if>
            <if test="quantity != null">quantity,</if>
            <if test="unitPrice != null">unitPrice,</if>
            <if test="amount != null">amount,</if>
            <if test="taxRate != null">taxRate,</if>
            <if test="tax != null">tax,</if>
            <if test="isDeduct != null">isDeduct,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="orgId != null">orgId,</if>
            <if test="orgName != null">orgName,</if>
            <if test="signStatus != null">signStatus,</if>
            <if test="fileAddress != null">fileAddress,</if>
            <if test="accounttingNo != null">accounttingNo,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="userAccount != null">#{userAccount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="invoiceNo != null">#{invoiceNo},</if>
            <if test="invoiceCode != null">#{invoiceCode},</if>
            <if test="invoiceDate != null">#{invoiceDate},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="deductTax != null">#{deductTax},</if>
            <if test="amountTax != null">#{amountTax},</if>
            <if test="purchaserName != null">#{purchaserName},</if>
            <if test="purchaserTaxNo != null">#{purchaserTaxNo},</if>
            <if test="purchaserAddressPhone != null">#{purchaserAddressPhone},</if>
            <if test="purchaserBank != null">#{purchaserBank},</if>
            <if test="saleName != null">#{saleName},</if>
            <if test="saleTaxNo != null">#{saleTaxNo},</if>
            <if test="saleAddressPhone != null">#{saleAddressPhone},</if>
            <if test="saleBank != null">#{saleBank},</if>
            <if test="machineCode != null">#{machineCode},</if>
            <if test="checkCode != null">#{checkCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="commodityName != null">#{commodityName},</if>
            <if test="unit != null">#{unit},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="amount != null">#{amount},</if>
            <if test="taxRate != null">#{taxRate},</if>
            <if test="tax != null">#{tax},</if>
            <if test="isDeduct != null">#{isDeduct},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="signStatus != null">#{signStatus},</if>
            <if test="fileAddress != null">#{fileAddress},</if>
            <if test="accounttingNo != null">#{accounttingNo},</if>
         </trim>
    </insert>

    <update id="updateAssociatedSgeInvoice" parameterType="AssociatedSgeInvoice">
        update associated_sge_invoice
        <trim prefix="SET" suffixOverrides=",">
            <if test="userAccount != null">userAccount = #{userAccount},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="invoiceNo != null">invoiceNo = #{invoiceNo},</if>
            <if test="invoiceCode != null">invoiceCode = #{invoiceCode},</if>
            <if test="invoiceDate != null">invoiceDate = #{invoiceDate},</if>
            <if test="invoiceType != null">invoiceType = #{invoiceType},</if>
            <if test="totalAmount != null">totalAmount = #{totalAmount},</if>
            <if test="deductTax != null">deductTax = #{deductTax},</if>
            <if test="amountTax != null">amountTax = #{amountTax},</if>
            <if test="purchaserName != null">purchaserName = #{purchaserName},</if>
            <if test="purchaserTaxNo != null">purchaserTaxNo = #{purchaserTaxNo},</if>
            <if test="purchaserAddressPhone != null">purchaserAddressPhone = #{purchaserAddressPhone},</if>
            <if test="purchaserBank != null">purchaserBank = #{purchaserBank},</if>
            <if test="saleName != null">saleName = #{saleName},</if>
            <if test="saleTaxNo != null">saleTaxNo = #{saleTaxNo},</if>
            <if test="saleAddressPhone != null">saleAddressPhone = #{saleAddressPhone},</if>
            <if test="saleBank != null">saleBank = #{saleBank},</if>
            <if test="machineCode != null">machineCode = #{machineCode},</if>
            <if test="checkCode != null">checkCode = #{checkCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="commodityName != null">commodityName = #{commodityName},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="unitPrice != null">unitPrice = #{unitPrice},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="taxRate != null">taxRate = #{taxRate},</if>
            <if test="tax != null">tax = #{tax},</if>
            <if test="isDeduct != null">isDeduct = #{isDeduct},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="orgId != null">orgId = #{orgId},</if>
            <if test="orgName != null">orgName = #{orgName},</if>
            <if test="signStatus != null">signStatus = #{signStatus},</if>
            <if test="fileAddress != null">fileAddress = #{fileAddress},</if>
            <if test="accounttingNo != null">accounttingNo = #{accounttingNo},</if>
        </trim>
        where userName = #{userName}
    </update>

    <delete id="deleteAssociatedSgeInvoiceByUserName" parameterType="String">
        delete from associated_sge_invoice where userName = #{userName}
    </delete>

    <delete id="deleteAssociatedSgeInvoiceByUserNames" parameterType="String">
        delete from associated_sge_invoice where userName in 
        <foreach item="userName" collection="array" open="(" separator="," close=")">
            #{userName}
        </foreach>
    </delete>
</mapper>