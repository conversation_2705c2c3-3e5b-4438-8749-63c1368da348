<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ExpenseReimbursementMapper">

    <resultMap type="ExpenseReimbursement" id="ExpenseReimbursementResult">
        <result property="OANumber" column="OANumber"/>
        <result property="createdDate" column="createdDate"/>
        <result property="reimbursementCategory" column="reimbursementCategory"/>
        <result property="companyCode" column="companyCode"/>
        <result property="companyName" column="companyName"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="reimbursementPersonNumber" column="reimbursementPersonNumber"/>
        <result property="jobTitle" column="jobTitle"/>
        <result property="department" column="department"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="expenseAccountName" column="expenseAccountName"/>
        <result property="specialInvoiceTax" column="specialInvoiceTax"/>
        <result property="currency" column="currency"/>
        <result property="reimbursementAmount" column="reimbursementAmount"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="businessType" column="businessType"/>
        <result property="businessDescription" column="businessDescription"/>
        <result property="centerName" column="centerName"/>
        <result property="presidentBusiness" column="presidentBusiness"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="accountCode" column="accountCode"/>
        <result property="invoiceNo" column="invoiceNo"/>
        <result property="amountWithoutTax" column="amountWithoutTax"/>
        <result property="currentSession" column="currentSession"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="sapDocumentStatus" column="sapDocumentStatus"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="sapCredentialPushDate" column="sapCredentialPushDate"/>
        <result property="businessOccurrenceDate" column="businessOccurrenceDate"/>
        <result property="sapVoucherNumber" column="sapVoucherNumber"/>
    </resultMap>

    <sql id="selectExpenseReimbursementVo">
        select OANumber,
               createdDate,
               reimbursementCategory,
               companyCode,
               companyName,
               reimbursementPerson,
               reimbursementPersonNumber,
               jobTitle,
               department,
               costCenterName,
               expenseAccountName,
               specialInvoiceTax,
               currency,
               reimbursementAmount,
               projectNumber,
               businessType,
               businessDescription,
               centerName,
               presidentBusiness,
               costCenterCode,
               accountCode,
               invoiceNo,
               amountWithoutTax,
               currentSession,
               documentStatus,
               sapDocumentStatus,
               applicationSubject,
               sapCredentialPushDate,
               businessOccurrenceDate,
               sapVoucherNumber
        from reimbursement
    </sql>

    <select id="selectExpenseReimbursementList" parameterType="ExpenseReimbursement"
            resultMap="ExpenseReimbursementResult">
        <include refid="selectExpenseReimbursementVo"/>
        <where>
            <if test="OANumber != null  and OANumber != ''">and OANumber like concat('%', #{OANumber}, '%')</if>
            <if test="params.beginCreatedDate != null and params.beginCreatedDate != '' and params.endCreatedDate != null and params.endCreatedDate != ''">
                and createdDate between #{params.beginCreatedDate} and #{params.endCreatedDate}
            </if>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''">and reimbursementCategory like
                concat('%', #{reimbursementCategory}, '%')
            </if>
            <if test="companyCode != null  and companyCode != ''">and companyCode = #{companyCode}</if>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''">and reimbursementPerson like
                concat('%', #{reimbursementPerson}, '%')
            </if>
            <if test="reimbursementPersonNumber != null  and reimbursementPersonNumber != ''">and
                reimbursementPersonNumber = #{reimbursementPersonNumber}
            </if>
            <if test="jobTitle != null  and jobTitle != ''">and jobTitle = #{jobTitle}</if>
            <if test="department != null  and department != ''">and department = #{department}</if>
            <if test="costCenterName != null  and costCenterName != ''">and costCenterName like concat('%',
                #{costCenterName}, '%')
            </if>
            <if test="expenseAccountName != null  and expenseAccountName != ''">and expenseAccountName like concat('%',
                #{expenseAccountName}, '%')
            </if>
            <if test="specialInvoiceTax != null  and specialInvoiceTax != ''">and specialInvoiceTax =
                #{specialInvoiceTax}
            </if>
            <if test="currency != null  and currency != ''">and currency = #{currency}</if>
            <if test="reimbursementAmount != null  and reimbursementAmount != ''">and reimbursementAmount =
                #{reimbursementAmount}
            </if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="businessType != null  and businessType != ''">and businessType = #{businessType}</if>
            <if test="businessDescription != null  and businessDescription != ''">and businessDescription =
                #{businessDescription}
            </if>
            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="presidentBusiness != null  and presidentBusiness != ''">and presidentBusiness =
                #{presidentBusiness}
            </if>
            <if test="costCenterCode != null  and costCenterCode != ''">and costCenterCode = #{costCenterCode}</if>
            <if test="accountCode != null  and accountCode != ''">and accountCode = #{accountCode}</if>
            <if test="invoiceNo != null  and invoiceNo != ''">and invoiceNo = #{invoiceNo}</if>
            <if test="amountWithoutTax != null  and amountWithoutTax != ''">and amountWithoutTax = #{amountWithoutTax}
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession = #{currentSession}</if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus = #{documentStatus}</if>
            <if test="sapDocumentStatus != null  and sapDocumentStatus != ''">and sapDocumentStatus =
                #{sapDocumentStatus}
            </if>
            <if test="applicationSubject != null  and applicationSubject != ''">and applicationSubject =
                #{applicationSubject}
            </if>
            <if test="sapCredentialPushDate != null  and sapCredentialPushDate != ''">and sapCredentialPushDate =
                #{sapCredentialPushDate}
            </if>
        </where>
        order by createdDate desc
    </select>
</mapper>