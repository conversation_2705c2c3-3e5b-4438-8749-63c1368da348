<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.IitResearchTotalExpenditureTemporaryMapper">
    <resultMap type="IitResearchTotalExpenditure" id="IitResearchTotalExpenditureResult">
        <result property="paymentApplicationDate" column="paymentApplicationDate"/>
        <result property="singleNumber" column="singleNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="department" column="department"/>
        <result property="contractNo" column="contractNo"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="costBearingCompany" column="costBearingCompany"/>
        <result property="receivingUnit" column="receivingUnit"/>
        <result property="centerNo" column="centerNo"/>
        <result property="centerName" column="centerName"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="projectType" column="projectType"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="contractType" column="contractType"/>
        <result property="outsourcingSubdivision" column="outsourcingSubdivision"/>
        <result property="paymentDescription" column="paymentDescription"/>
        <result property="accountCode" column="accountCode"/>
        <result property="suject" column="suject"/>
        <result property="settlementPeriod" column="settlementPeriod"/>
        <result property="invoiceType" column="invoiceType"/>
        <result property="invoiceNo" column="invoiceNo"/>
        <result property="taxRate" column="taxRate"/>
        <result property="amountWithoutTax" column="amountWithoutTax"/>
        <result property="specialTax" column="specialTax"/>
        <result property="amount" column="amount"/>
        <result property="ascriptionDepartment" column="ascriptionDepartment"/>
        <result property="amountIncludingTax" column="amountIncludingTax"/>
        <result property="regionalManager" column="regionalManager"/>
        <result property="salesAreaDirector" column="salesAreaDirector"/>
        <result property="remark" column="remark"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="invoice" column="invoice"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="sapInvoiceNumber" column="sapInvoiceNumber"/>
        <result property="sapTaxAmount" column="sapTaxAmount"/>
        <result property="tableName" column="tableName"/>
        <result property="applicantJobNumber" column="applicantJobNumber"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="meetingCategory" column="meetingCategory"/>
        <result property="businessType" column="businessType"/>
        <result property="thirdLevelDepartment" column="thirdLevelDepartment"/>
        <result property="secondaryDepartment" column="secondaryDepartment"/>
        <result property="endLevelDepartment" column="endLevelDepartment"/>
        <result property="costDescription" column="costDescription"/>
        <result property="remarks" column="remarks"/>
        <result property="systemProjectNumber" column="systemProjectNumber"/>
        <result property="collectionProjectNumber" column="collectionProjectNumber"/>
        <result property="departmentAllocationAmount" column="departmentAllocationAmount"/>
    </resultMap>

    <sql id="selectIitResearchTotalExpenditureVo">
        select paymentApplicationDate,
               singleNumber,
               applicant,
               department,
               contractNo,
               applicationSubject,
               costBearingCompany,
               receivingUnit,
               centerNo,
               centerName,
               costCenterCode,
               costCenterName,
               projectType,
               projectNumber,
               contractType,
               outsourcingSubdivision,
               paymentDescription,
               accountCode,
               suject,
               settlementPeriod,
               invoiceType,
               invoiceNo,
               taxRate,
               amountWithoutTax,
               specialTax,
               amount,
               ascriptionDepartment,
               amountIncludingTax,
               regionalManager,
               salesAreaDirector,
               remark,
               documentStatus,
               currentSession,
               invoice,
               sapPayNumber,
               sapInvoiceNumber,
               sapTaxAmount,
               tableName,
               applicantJobNumber,
               reimbursementPerson,
               meetingCategory,
               businessType,
               thirdLevelDepartment,
               secondaryDepartment,
               endLevelDepartment,
               costDescription,
               remarks,
               systemProjectNumber,
               collectionProjectNumber,
               departmentAllocationAmount
        from iit_research_total_expenditure
    </sql>

    <select id="selectIitResearchTotalExpenditureList" parameterType="IitResearchTotalExpenditure"
            resultMap="IitResearchTotalExpenditureResult">
        <include refid="selectIitResearchTotalExpenditureVo"/>
        <where>
            <if test="paymentApplicationDate != null ">and paymentApplicationDate = #{paymentApplicationDate}</if>
            <if test="singleNumber != null  and singleNumber != ''">and singleNumber = #{singleNumber}</if>
            <if test="applicant != null  and applicant != ''">and applicant = #{applicant}</if>
            <if test="department != null  and department != ''">and department = #{department}</if>
            <if test="contractNo != null  and contractNo != ''">and contractNo = #{contractNo}</if>
            <if test="applicationSubject != null  and applicationSubject != ''">and applicationSubject =
                #{applicationSubject}
            </if>
            <if test="costBearingCompany != null  and costBearingCompany != ''">and costBearingCompany =
                #{costBearingCompany}
            </if>
            <if test="receivingUnit != null  and receivingUnit != ''">and receivingUnit = #{receivingUnit}</if>
            <if test="centerNo != null  and centerNo != ''">and centerNo = #{centerNo}</if>
            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="costCenterCode != null  and costCenterCode != ''">and costCenterCode = #{costCenterCode}</if>
            <if test="costCenterName != null  and costCenterName != ''">and costCenterName like concat('%',
                #{costCenterName}, '%')
            </if>
            <if test="projectType != null  and projectType != ''">and projectType = #{projectType}</if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber = #{projectNumber}</if>
            <if test="contractType != null  and contractType != ''">and contractType = #{contractType}</if>
            <if test="outsourcingSubdivision != null  and outsourcingSubdivision != ''">and outsourcingSubdivision =
                #{outsourcingSubdivision}
            </if>
            <if test="paymentDescription != null  and paymentDescription != ''">and paymentDescription =
                #{paymentDescription}
            </if>
            <if test="accountCode != null  and accountCode != ''">and accountCode = #{accountCode}</if>
            <if test="suject != null  and suject != ''">and suject = #{suject}</if>
            <if test="settlementPeriod != null  and settlementPeriod != ''">and settlementPeriod = #{settlementPeriod}
            </if>
            <if test="invoiceType != null  and invoiceType != ''">and invoiceType = #{invoiceType}</if>
            <if test="invoiceNo != null  and invoiceNo != ''">and invoiceNo = #{invoiceNo}</if>
            <if test="taxRate != null  and taxRate != ''">and taxRate = #{taxRate}</if>
            <if test="amountWithoutTax != null  and amountWithoutTax != ''">and amountWithoutTax = #{amountWithoutTax}
            </if>
            <if test="specialTax != null  and specialTax != ''">and specialTax = #{specialTax}</if>
            <if test="amount != null  and amount != ''">and amount = #{amount}</if>
            <if test="ascriptionDepartment != null  and ascriptionDepartment != ''">and ascriptionDepartment =
                #{ascriptionDepartment}
            </if>
            <if test="amountIncludingTax != null  and amountIncludingTax != ''">and amountIncludingTax =
                #{amountIncludingTax}
            </if>
            <if test="regionalManager != null  and regionalManager != ''">and regionalManager = #{regionalManager}</if>
            <if test="salesAreaDirector != null  and salesAreaDirector != ''">and salesAreaDirector =
                #{salesAreaDirector}
            </if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus = #{documentStatus}</if>
            <if test="currentSession != null  and currentSession != ''">and currentSession = #{currentSession}</if>
            <if test="invoice != null  and invoice != ''">and invoice = #{invoice}</if>
            <if test="sapPayNumber != null  and sapPayNumber != ''">and sapPayNumber = #{sapPayNumber}</if>
            <if test="sapInvoiceNumber != null  and sapInvoiceNumber != ''">and sapInvoiceNumber = #{sapInvoiceNumber}
            </if>
            <if test="sapTaxAmount != null  and sapTaxAmount != ''">and sapTaxAmount = #{sapTaxAmount}</if>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="applicantJobNumber != null  and applicantJobNumber != ''">and applicantJobNumber like concat('%',
                #{applicantJobNumber}, '%')
            </if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''">and reimbursementPerson like
                concat('%', #{reimbursementPerson}, '%')
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory like concat('%',
                #{meetingCategory}, '%')
            </if>
            <if test="businessType != null  and businessType != ''">and businessType like concat('%', #{businessType},
                '%')
            </if>
            <if test="thirdLevelDepartment != null  and thirdLevelDepartment != ''">and thirdLevelDepartment like
                concat('%', #{thirdLevelDepartment}, '%')
            </if>
            <if test="secondaryDepartment != null  and secondaryDepartment != ''">and secondaryDepartment like
                concat('%', #{secondaryDepartment}, '%')
            </if>
            <if test="endLevelDepartment != null  and endLevelDepartment != ''">and endLevelDepartment like concat('%',
                #{endLevelDepartment}, '%')
            </if>
            <if test="costDescription != null  and costDescription != ''">and costDescription like concat('%',
                #{costDescription}, '%')
            </if>
            <if test="remarks != null  and remarks != ''">and remarks like concat('%', #{remarks}, '%')</if>
            <if test="systemProjectNumber != null  and systemProjectNumber != ''">and systemProjectNumber like
                concat('%', #{systemProjectNumber}, '%')
            </if>
            <if test="collectionProjectNumber != null  and collectionProjectNumber != ''">and collectionProjectNumber
                like concat('%', #{collectionProjectNumber}, '%')
            </if>
            <if test="departmentAllocationAmount != null ">and departmentAllocationAmount =
                #{departmentAllocationAmount}
            </if>
        </where>
    </select>

    <select id="selectIitResearchTotalExpenditureByPaymentApplicationDate" parameterType="Date"
            resultMap="IitResearchTotalExpenditureResult">
        <include refid="selectIitResearchTotalExpenditureVo"/>
        where paymentApplicationDate = #{paymentApplicationDate}
    </select>

    <insert id="insertIitResearchTotalExpenditure" parameterType="IitResearchTotalExpenditure">
        insert into iit_research_total_expenditure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="paymentApplicationDate != null">paymentApplicationDate,</if>
            <if test="singleNumber != null">singleNumber,</if>
            <if test="applicant != null">applicant,</if>
            <if test="department != null">department,</if>
            <if test="contractNo != null">contractNo,</if>
            <if test="applicationSubject != null">applicationSubject,</if>
            <if test="costBearingCompany != null">costBearingCompany,</if>
            <if test="receivingUnit != null">receivingUnit,</if>
            <if test="centerNo != null">centerNo,</if>
            <if test="centerName != null">centerName,</if>
            <if test="costCenterCode != null">costCenterCode,</if>
            <if test="costCenterName != null">costCenterName,</if>
            <if test="projectType != null">projectType,</if>
            <if test="projectNumber != null">projectNumber,</if>
            <if test="contractType != null">contractType,</if>
            <if test="outsourcingSubdivision != null">outsourcingSubdivision,</if>
            <if test="paymentDescription != null">paymentDescription,</if>
            <if test="accountCode != null">accountCode,</if>
            <if test="suject != null">suject,</if>
            <if test="settlementPeriod != null">settlementPeriod,</if>
            <if test="invoiceType != null">invoiceType,</if>
            <if test="invoiceNo != null">invoiceNo,</if>
            <if test="taxRate != null">taxRate,</if>
            <if test="amountWithoutTax != null">amountWithoutTax,</if>
            <if test="specialTax != null">specialTax,</if>
            <if test="amount != null">amount,</if>
            <if test="ascriptionDepartment != null">ascriptionDepartment,</if>
            <if test="amountIncludingTax != null">amountIncludingTax,</if>
            <if test="regionalManager != null">regionalManager,</if>
            <if test="salesAreaDirector != null">salesAreaDirector,</if>
            <if test="remark != null">remark,</if>
            <if test="documentStatus != null">documentStatus,</if>
            <if test="currentSession != null">currentSession,</if>
            <if test="invoice != null">invoice,</if>
            <if test="sapPayNumber != null">sapPayNumber,</if>
            <if test="sapInvoiceNumber != null">sapInvoiceNumber,</if>
            <if test="sapTaxAmount != null">sapTaxAmount,</if>
            <if test="tableName != null">tableName,</if>
            <if test="applicantJobNumber != null">applicantJobNumber,</if>
            <if test="reimbursementPerson != null">reimbursementPerson,</if>
            <if test="meetingCategory != null">meetingCategory,</if>
            <if test="businessType != null">businessType,</if>
            <if test="thirdLevelDepartment != null">thirdLevelDepartment,</if>
            <if test="secondaryDepartment != null">secondaryDepartment,</if>
            <if test="endLevelDepartment != null">endLevelDepartment,</if>
            <if test="costDescription != null">costDescription,</if>
            <if test="remarks != null">remarks,</if>
            <if test="systemProjectNumber != null">systemProjectNumber,</if>
            <if test="collectionProjectNumber != null">collectionProjectNumber,</if>
            <if test="departmentAllocationAmount != null">departmentAllocationAmount,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="paymentApplicationDate != null">#{paymentApplicationDate},</if>
            <if test="singleNumber != null">#{singleNumber},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="department != null">#{department},</if>
            <if test="contractNo != null">#{contractNo},</if>
            <if test="applicationSubject != null">#{applicationSubject},</if>
            <if test="costBearingCompany != null">#{costBearingCompany},</if>
            <if test="receivingUnit != null">#{receivingUnit},</if>
            <if test="centerNo != null">#{centerNo},</if>
            <if test="centerName != null">#{centerName},</if>
            <if test="costCenterCode != null">#{costCenterCode},</if>
            <if test="costCenterName != null">#{costCenterName},</if>
            <if test="projectType != null">#{projectType},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="contractType != null">#{contractType},</if>
            <if test="outsourcingSubdivision != null">#{outsourcingSubdivision},</if>
            <if test="paymentDescription != null">#{paymentDescription},</if>
            <if test="accountCode != null">#{accountCode},</if>
            <if test="suject != null">#{suject},</if>
            <if test="settlementPeriod != null">#{settlementPeriod},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="invoiceNo != null">#{invoiceNo},</if>
            <if test="taxRate != null">#{taxRate},</if>
            <if test="amountWithoutTax != null">#{amountWithoutTax},</if>
            <if test="specialTax != null">#{specialTax},</if>
            <if test="amount != null">#{amount},</if>
            <if test="ascriptionDepartment != null">#{ascriptionDepartment},</if>
            <if test="amountIncludingTax != null">#{amountIncludingTax},</if>
            <if test="regionalManager != null">#{regionalManager},</if>
            <if test="salesAreaDirector != null">#{salesAreaDirector},</if>
            <if test="remark != null">#{remark},</if>
            <if test="documentStatus != null">#{documentStatus},</if>
            <if test="currentSession != null">#{currentSession},</if>
            <if test="invoice != null">#{invoice},</if>
            <if test="sapPayNumber != null">#{sapPayNumber},</if>
            <if test="sapInvoiceNumber != null">#{sapInvoiceNumber},</if>
            <if test="sapTaxAmount != null">#{sapTaxAmount},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="applicantJobNumber != null">#{applicantJobNumber},</if>
            <if test="reimbursementPerson != null">#{reimbursementPerson},</if>
            <if test="meetingCategory != null">#{meetingCategory},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="thirdLevelDepartment != null">#{thirdLevelDepartment},</if>
            <if test="secondaryDepartment != null">#{secondaryDepartment},</if>
            <if test="endLevelDepartment != null">#{endLevelDepartment},</if>
            <if test="costDescription != null">#{costDescription},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="systemProjectNumber != null">#{systemProjectNumber},</if>
            <if test="collectionProjectNumber != null">#{collectionProjectNumber},</if>
            <if test="departmentAllocationAmount != null">#{departmentAllocationAmount},</if>
        </trim>
    </insert>
</mapper>