<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ExpenseReimbursementCostCenterMapper">
    <resultMap type="com.akesobio.report.groupFinance.vo.ExpenseReimbursementCostCenterVo" id="ExpenseReimbursementCostCenterVoResult">
        <result property="year"    column="year" />
        <result property="month"   column="month"  />
        <result property="total"   column="total"  />
    </resultMap>
    <select id="costCenterList"  resultMap="ExpenseReimbursementCostCenterVoResult">
        select Year(createdDate) year,Month(createdDate) month,SUM(reimbursementAmount) total
        FROM reimbursement
        <where>
            <if test="costCenter !=null and costCenter != ''">and costCenterCode=#{costCenter}</if>
            <if test="startDate !=null and startDate !=''">and createdDate >=#{startDate}</if>
<!--            <if test="endDate !=null and endDate !=''">and createdDate <=#{endDate}</if>-->
        </where>
        Group by Year(createdDate),Month(createdDate)
    </select>
</mapper>