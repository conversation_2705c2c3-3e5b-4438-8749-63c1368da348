<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ResearchDevelopmentExpenseCostMapper">

    <resultMap type="ResearchDevelopmentExpenseCost" id="ResearchDevelopmentExpenseCostResult">
        <result property="id" column="id"/>
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="yearMonth" column="yearMonth"/>
        <result property="amount" column="amount"/>
        <result property="costType" column="costType"/>
        <result property="pipelineType" column="pipelineType"/>
        <result property="drugPipeline" column="drugPipeline"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="deleteStatus" column="deleteStatus"/>
    </resultMap>

    <sql id="selectResearchDevelopmentExpenseCostVo">
        select id,
               companyName,
               companyCode, year, month, yearMonth, amount, costType, pipelineType, drugPipeline, projectNumber, deleteStatus
        from research_development_expense_cost
    </sql>

    <select id="selectResearchDevelopmentExpenseCostList" parameterType="ResearchDevelopmentExpenseCost"
            resultMap="ResearchDevelopmentExpenseCostResult">
        <include refid="selectResearchDevelopmentExpenseCostVo"/>
        <where>
            <if test="companyName != null  and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode != null  and companyCode != ''">and companyCode = #{companyCode}</if>
            <if test="year != null ">and year = #{year}</if>
            <if test="month != null  and month != ''">and month = #{month}</if>
            <if test="yearMonth != null  and yearMonth != ''">and yearMonth = #{yearMonth}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="costType != null  and costType != ''">and costType = #{costType}</if>
            <if test="pipelineType != null  and pipelineType != ''">and pipelineType = #{pipelineType}</if>
            <if test="drugPipeline != null  and drugPipeline != ''">and drugPipeline = #{drugPipeline}</if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber = #{projectNumber}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="monthList !=null and monthList.size>0">
                and month in
                <foreach collection="monthList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="companyList !=null and companyList.size>0">
                and companyName in
                <foreach collection="companyList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and deleteStatus='0'
        </where>
    </select>

    <select id="selectResearchDevelopmentExpenseCostById" parameterType="Integer"
            resultMap="ResearchDevelopmentExpenseCostResult">
        <include refid="selectResearchDevelopmentExpenseCostVo"/>
        where id = #{id}
    </select>

    <insert id="insertResearchDevelopmentExpenseCost" parameterType="ResearchDevelopmentExpenseCost">
        insert into research_development_expense_cost
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="year != null">year,</if>
            <if test="month != null">month,</if>
            <if test="yearMonth != null">yearMonth,</if>
            <if test="amount != null">amount,</if>
            <if test="costType != null">costType,</if>
            <if test="pipelineType != null">pipelineType,</if>
            <if test="drugPipeline != null">drugPipeline,</if>
            <if test="projectNumber != null">projectNumber,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="year != null">#{year},</if>
            <if test="month != null">#{month},</if>
            <if test="yearMonth != null">#{yearMonth},</if>
            <if test="amount != null">#{amount},</if>
            <if test="costType != null">#{costType},</if>
            <if test="pipelineType != null">#{pipelineType},</if>
            <if test="drugPipeline != null">#{drugPipeline},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
        </trim>
    </insert>

    <update id="updateResearchDevelopmentExpenseCost" parameterType="ResearchDevelopmentExpenseCost">
        update research_development_expense_cost
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="yearMonth != null">yearMonth = #{yearMonth},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="costType != null">costType = #{costType},</if>
            <if test="pipelineType != null">pipelineType = #{pipelineType},</if>
            <if test="drugPipeline != null">drugPipeline = #{drugPipeline},</if>
            <if test="projectNumber != null">projectNumber = #{projectNumber},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResearchDevelopmentExpenseCostById" parameterType="Integer">
        delete
        from research_development_expense_cost
        where id = #{id}
    </delete>

    <delete id="deleteResearchDevelopmentExpenseCostByIds" parameterType="String">
        delete from research_development_expense_cost where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAllResearchDevelopmenteExpenseCost">
        delete
        from research_development_expense_cost
    </delete>
</mapper>