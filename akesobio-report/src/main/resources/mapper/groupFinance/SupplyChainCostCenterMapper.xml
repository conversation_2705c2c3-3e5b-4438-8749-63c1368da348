<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.SupplyChainCostCenterMapper">
    <resultMap type="com.akesobio.report.groupFinance.vo.SupplyChainCostCenterVo" id="SupplyChainCostCenterVoResult">
        <result property="year"    column="year" />
        <result property="month"   column="month"  />
        <result property="total"   column="total"  />
    </resultMap>
    <select id="costCenterList"  resultMap="SupplyChainCostCenterVoResult">
        select Year(paymentApplicationDate) year,Month(paymentApplicationDate) month,SUM(CONVERT(DECIMAL(13,2),ISNULL(amount, 0))) total
        FROM supply_spending
        <where>
            <if test="costCenter !=null and costCenter != ''">and costCenterCode=#{costCenter}</if>
            <if test="startDate !=null and startDate !='' and endDate !=null and endDate!=''">and paymentApplicationDate between #{startDate} and #{endDate} </if>
        </where>
        Group by Year(paymentApplicationDate),Month(paymentApplicationDate)
        order by month ASC
    </select>
    <select id="selectSupplyPieChart" resultMap="SupplyChainCostCenterVoResult">
        select  sum(amount) as total ,costBearingCompany
        from supply_spending
        WHERE Year(paymentApplicationDate) = 2022 and costBearingCompany is not null
        Group by costBearingCompany
    </select>
</mapper>
