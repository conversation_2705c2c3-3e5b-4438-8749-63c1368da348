<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ClinicalTotalExpensesTemporaryMapper">
    <resultMap type="ClinicalTotalExpenses" id="ClinicalTotalExpensesResult">
        <result property="OANumber" column="OANumber"/>
        <result property="contractNo" column="contractNo"/>
        <result property="paymentApplicationDate" column="paymentApplicationDate"/>
        <result property="applicant" column="applicant"/>
        <result property="department" column="department"/>
        <result property="costBearingCompany" column="costBearingCompany"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="feeType" column="feeType"/>
        <result property="paymentDescription" column="paymentDescription"/>
        <result property="suject" column="suject"/>
        <result property="settlementPeriod" column="settlementPeriod"/>
        <result property="casesNumber" column="casesNumber"/>
        <result property="amount" column="amount"/>
        <result property="currency" column="currency"/>
        <result property="exchangeRate" column="exchangeRate"/>
        <result property="receivingUnit" column="receivingUnit"/>
        <result property="contractType" column="contractType"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenter" column="costCenter"/>
        <result property="accountCode" column="accountCode"/>
        <result property="amountWithoutTax" column="amountWithoutTax"/>
        <result property="specialTax" column="specialTax"/>
        <result property="currentSession" column="currentSession"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="sapDocumentStatus" column="sapDocumentStatus"/>
        <result property="sapCredentialPushDate" column="sapCredentialPushDate"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="invoiceType" column="invoiceType"/>
        <result property="invoiceNumber" column="invoiceNumber"/>
        <result property="invoice" column="invoice"/>
        <result property="sapInvoiceNumber" column="sapInvoiceNumber"/>
        <result property="sapTaxAmount" column="sapTaxAmount"/>
        <result property="currentProcessor" column="currentProcessor"/>
        <result property="centerNumber" column="centerNumber"/>
        <result property="invoiceIssuanceDate" column="invoiceIssuanceDate"/>
        <result property="supplierDate" column="supplierDate"/>
        <result property="paymentDate" column="paymentDate"/>
        <result property="recyclingDate" column="recyclingDate"/>
        <result property="confirmTime" column="confirmTime"/>
        <result property="paymentTime" column="paymentTime"/>
        <result property="invoiceCollectionTime" column="invoiceCollectionTime"/>
    </resultMap>

    <sql id="selectClinicalTotalExpensesVo">
        select OANumber,
               contractNo,
               paymentApplicationDate,
               applicant,
               department,
               costBearingCompany,
               projectNumber,
               feeType,
               paymentDescription,
               suject,
               settlementPeriod,
               casesNumber,
               amount,
               currency,
               exchangeRate,
               receivingUnit,
               contractType,
               costCenterCode,
               costCenter,
               accountCode,
               amountWithoutTax,
               specialTax,
               currentSession,
               documentStatus,
               sapDocumentStatus,
               sapCredentialPushDate,
               applicationSubject,
               sapPayNumber,
               invoiceType,
               invoiceNumber,
               invoice,
               sapInvoiceNumber,
               sapTaxAmount,
               currentProcessor,
               centerNumber,
               invoiceIssuanceDate,
               supplierDate,
               paymentDate,
               recyclingDate,
               confirmTime,
               paymentTime,
               invoiceCollectionTime
        from clinical_total_expenses
    </sql>

    <select id="selectClinicalTotalExpensesList" parameterType="ClinicalTotalExpenses"
            resultMap="ClinicalTotalExpensesResult">
        <include refid="selectClinicalTotalExpensesVo"/>
        <where>
            <if test="OANumber != null  and OANumber != ''">and OANumber like concat('%', #{OANumber}, '%')</if>
            <if test="contractNo != null  and contractNo != ''">and contractNo like concat('%', #{contractNo}, '%')</if>
            <if test="params.beginPaymentApplicationDate != null and params.beginPaymentApplicationDate != '' and params.endPaymentApplicationDate != null and params.endPaymentApplicationDate != ''">
                and paymentApplicationDate between #{params.beginPaymentApplicationDate} and
                #{params.endPaymentApplicationDate}
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="costBearingCompany != null  and costBearingCompany != ''">and costBearingCompany like concat('%',
                #{costBearingCompany}, '%')
            </if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="feeType != null  and feeType != ''">and feeType like concat('%', #{feeType}, '%')</if>
            <if test="paymentDescription != null  and paymentDescription != ''">and paymentDescription like concat('%',
                #{paymentDescription}, '%')
            </if>
            <if test="suject != null  and suject != ''">and suject like concat('%', #{suject}, '%')</if>
            <if test="settlementPeriod != null  and settlementPeriod != ''">and settlementPeriod like concat('%',
                #{settlementPeriod}, '%')
            </if>
            <if test="casesNumber != null  and casesNumber != ''">and casesNumber = #{casesNumber}</if>
            <if test="amount != null  and amount != ''">and amount = #{amount}</if>
            <if test="currency != null  and currency != ''">and currency like concat('%', #{currency}, '%')</if>
            <if test="exchangeRate != null  and exchangeRate != ''">and exchangeRate = #{exchangeRate}</if>
            <if test="receivingUnit != null  and receivingUnit != ''">and receivingUnit like concat('%',
                #{receivingUnit}, '%')
            </if>
            <if test="contractType != null  and contractType != ''">and contractType like concat('%', #{contractType},
                '%')
            </if>
            <if test="costCenterCode != null  and costCenterCode != ''">and costCenterCode like concat('%',
                #{costCenterCode}, '%')
            </if>
            <if test="costCenter != null  and costCenter != ''">and costCenter like concat('%', #{costCenter}, '%')</if>
            <if test="accountCode != null  and accountCode != ''">and accountCode like concat('%', #{accountCode},
                '%')
            </if>
            <if test="amountWithoutTax != null  and amountWithoutTax != ''">and amountWithoutTax = #{amountWithoutTax}
            </if>
            <if test="specialTax != null  and specialTax != ''">and specialTax = #{specialTax}</if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="sapDocumentStatus != null  and sapDocumentStatus != ''">and sapDocumentStatus like concat('%',
                #{sapDocumentStatus}, '%')
            </if>
            <if test="sapCredentialPushDate != null  and sapCredentialPushDate != ''">and sapCredentialPushDate like
                concat('%', #{sapCredentialPushDate}, '%')
            </if>
            <if test="applicationSubject != null  and applicationSubject != ''">and applicationSubject like concat('%',
                #{applicationSubject}, '%')
            </if>
            <if test="sapPayNumber != null  and sapPayNumber != ''">and sapPayNumber like concat('%', #{sapPayNumber},
                '%')
            </if>
            <if test="currentProcessor != null  and currentProcessor != ''">and currentProcessor like concat('%',
                #{currentProcessor}, '%')
            </if>
            <if test="centerNumber != null  and centerNumber != ''">and centerNumber like concat('%', #{centerNumber},
                '%')
            </if>
            <if test="params.beginInvoiceIssuanceDate != null and params.beginInvoiceIssuanceDate != '' and params.endInvoiceIssuanceDate != null and params.endInvoiceIssuanceDate != ''">
                and invoiceIssuanceDate between #{params.beginInvoiceIssuanceDate} and #{params.endInvoiceIssuanceDate}
            </if>
            <if test="params.beginSupplierDate != null and params.beginSupplierDate != '' and params.endSupplierDate != null and params.endSupplierDate != ''">
                and supplierDate between #{params.beginSupplierDate} and #{params.endSupplierDate}
            </if>
            <if test="params.beginPaymentDate != null and params.beginPaymentDate != '' and params.endPaymentDate != null and params.endPaymentDate != ''">
                and paymentDate between #{params.beginPaymentDate} and #{params.endPaymentDate}
            </if>
            <if test="params.beginRecyclingDate != null and params.beginRecyclingDate != '' and params.endRecyclingDate != null and params.endRecyclingDate != ''">
                and recyclingDate between #{params.beginRecyclingDate} and #{params.endRecyclingDate}
            </if>
            <if test="confirmTime != null  and confirmTime != ''">and confirmTime = #{confirmTime}</if>
            <if test="paymentTime != null  and paymentTime != ''">and paymentTime = #{paymentTime}</if>
            <if test="invoiceCollectionTime != null  and invoiceCollectionTime != ''">and invoiceCollectionTime =
                #{invoiceCollectionTime}
            </if>
        </where>
        order by paymentApplicationDate desc
    </select>

    <select id="selectClinicalTotalExpensesByOANumber" parameterType="String" resultMap="ClinicalTotalExpensesResult">
        <include refid="selectClinicalTotalExpensesVo"/>
        where OANumber = #{OANumber}
    </select>

    <insert id="insertClinicalTotalExpenses" parameterType="ClinicalTotalExpenses">
        insert into clinical_total_expenses
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="OANumber != null">OANumber,</if>
            <if test="contractNo != null">contractNo,</if>
            <if test="paymentApplicationDate != null">paymentApplicationDate,</if>
            <if test="applicant != null">applicant,</if>
            <if test="department != null">department,</if>
            <if test="costBearingCompany != null">costBearingCompany,</if>
            <if test="projectNumber != null">projectNumber,</if>
            <if test="feeType != null">feeType,</if>
            <if test="paymentDescription != null">paymentDescription,</if>
            <if test="suject != null">suject,</if>
            <if test="settlementPeriod != null">settlementPeriod,</if>
            <if test="casesNumber != null">casesNumber,</if>
            <if test="amount != null">amount,</if>
            <if test="currency != null">currency,</if>
            <if test="exchangeRate != null">exchangeRate,</if>
            <if test="receivingUnit != null">receivingUnit,</if>
            <if test="contractType != null">contractType,</if>
            <if test="costCenterCode != null">costCenterCode,</if>
            <if test="costCenter != null">costCenter,</if>
            <if test="accountCode != null">accountCode,</if>
            <if test="amountWithoutTax != null">amountWithoutTax,</if>
            <if test="specialTax != null">specialTax,</if>
            <if test="currentSession != null">currentSession,</if>
            <if test="documentStatus != null">documentStatus,</if>
            <if test="sapDocumentStatus != null">sapDocumentStatus,</if>
            <if test="sapCredentialPushDate != null">sapCredentialPushDate,</if>
            <if test="applicationSubject != null">applicationSubject,</if>
            <if test="sapPayNumber != null">sapPayNumber,</if>
            <if test="invoiceType != null">invoiceType,</if>
            <if test="invoiceNumber != null">invoiceNumber,</if>
            <if test="invoice != null">invoice,</if>
            <if test="sapInvoiceNumber != null">sapInvoiceNumber,</if>
            <if test="sapTaxAmount != null">sapTaxAmount,</if>
            <if test="currentProcessor != null">currentProcessor,</if>
            <if test="centerNumber != null">centerNumber,</if>
            <if test="invoiceIssuanceDate != null">invoiceIssuanceDate,</if>
            <if test="supplierDate != null">supplierDate,</if>
            <if test="paymentDate != null">paymentDate,</if>
            <if test="recyclingDate != null">recyclingDate,</if>
            <if test="confirmTime != null">confirmTime,</if>
            <if test="paymentTime != null">paymentTime,</if>
            <if test="invoiceCollectionTime != null">invoiceCollectionTime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="OANumber != null">#{OANumber},</if>
            <if test="contractNo != null">#{contractNo},</if>
            <if test="paymentApplicationDate != null">#{paymentApplicationDate},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="department != null">#{department},</if>
            <if test="costBearingCompany != null">#{costBearingCompany},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="feeType != null">#{feeType},</if>
            <if test="paymentDescription != null">#{paymentDescription},</if>
            <if test="suject != null">#{suject},</if>
            <if test="settlementPeriod != null">#{settlementPeriod},</if>
            <if test="casesNumber != null">#{casesNumber},</if>
            <if test="amount != null">#{amount},</if>
            <if test="currency != null">#{currency},</if>
            <if test="exchangeRate != null">#{exchangeRate},</if>
            <if test="receivingUnit != null">#{receivingUnit},</if>
            <if test="contractType != null">#{contractType},</if>
            <if test="costCenterCode != null">#{costCenterCode},</if>
            <if test="costCenter != null">#{costCenter},</if>
            <if test="accountCode != null">#{accountCode},</if>
            <if test="amountWithoutTax != null">#{amountWithoutTax},</if>
            <if test="specialTax != null">#{specialTax},</if>
            <if test="currentSession != null">#{currentSession},</if>
            <if test="documentStatus != null">#{documentStatus},</if>
            <if test="sapDocumentStatus != null">#{sapDocumentStatus},</if>
            <if test="sapCredentialPushDate != null">#{sapCredentialPushDate},</if>
            <if test="applicationSubject != null">#{applicationSubject},</if>
            <if test="sapPayNumber != null">#{sapPayNumber},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="invoiceNumber != null">#{invoiceNumber},</if>
            <if test="invoice != null">#{invoice},</if>
            <if test="sapInvoiceNumber != null">#{sapInvoiceNumber},</if>
            <if test="sapTaxAmount != null">#{sapTaxAmount},</if>
            <if test="currentProcessor != null">#{currentProcessor},</if>
            <if test="centerNumber != null">#{centerNumber},</if>
            <if test="invoiceIssuanceDate != null">#{invoiceIssuanceDate},</if>
            <if test="supplierDate != null">#{supplierDate},</if>
            <if test="paymentDate != null">#{paymentDate},</if>
            <if test="recyclingDate != null">#{recyclingDate},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="invoiceCollectionTime != null">#{invoiceCollectionTime},</if>
        </trim>
    </insert>
</mapper>