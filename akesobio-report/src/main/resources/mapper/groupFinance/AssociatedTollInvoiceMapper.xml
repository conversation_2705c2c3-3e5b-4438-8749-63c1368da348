<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.AssociatedTollInvoiceMapper">
    
    <resultMap type="AssociatedTollInvoice" id="AssociatedTollInvoiceResult">
        <result property="userName"    column="user_name"    />
        <result property="userAccount"    column="user_account"    />
        <result property="createTime"    column="create_time"    />
        <result property="invoiceNo"    column="invoice_no"    />
        <result property="invoiceCode"    column="invoice_code"    />
        <result property="invoiceDate"    column="invoice_date"    />
        <result property="amountTax"    column="amount_tax"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="isDeduct"    column="is_deduct"    />
        <result property="deductTax"    column="deduct_tax"    />
        <result property="updateTime"    column="update_time"    />
        <result property="orgId"    column="org_id"    />
        <result property="orgName"    column="org_name"    />
        <result property="signStatus"    column="sign_status"    />
        <result property="fileAddress"    column="file_address"    />
        <result property="accounttingNo"    column="accountting_no"    />
    </resultMap>

    <sql id="selectAssociatedTollInvoiceVo">
        select i.user_name, i.user_account, i.create_time, i.invoice_no, i.invoice_code, i.invoice_date, i.amount_tax, i.invoice_type, i.is_deduct, i.deduct_tax, i.update_time, i.org_id, i.org_name, i.sign_status, i.file_address, qrml.accountting_no
        from
             invoice AS i
                 LEFT JOIN query_reimbursement_main_list AS qrml ON i.invoice_no = qrml.inv_num
        WHERE i.reimburse_status = 2 AND (i.invoice_type = '1008' OR i.invoice_type = '14')
    </sql>

    <select id="selectAssociatedTollInvoiceList" parameterType="AssociatedTollInvoice" resultMap="AssociatedTollInvoiceResult">
        <include refid="selectAssociatedTollInvoiceVo"/>
        <if test="userName != null  and userName != ''"> and i.user_name like concat('%', #{userName}, '%')</if>
        <if test="userAccount != null  and userAccount != ''"> and i.user_account = #{userAccount}</if>
        <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and i.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        <if test="invoiceNo != null  and invoiceNo != ''"> and i.invoice_no = #{invoiceNo}</if>
        <if test="invoiceCode != null  and invoiceCode != ''"> and i.invoice_code = #{invoiceCode}</if>
        <if test="invoiceDate != null  and invoiceDate != ''"> and i.invoice_date = #{invoiceDate}</if>
        <if test="amountTax != null "> and i.amount_tax = #{amountTax}</if>
        <if test="invoiceType != null  and invoiceType != ''"> and i.invoice_type = #{invoiceType}</if>
        <if test="isDeduct != null "> and i.is_deduct = #{isDeduct}</if>
        <if test="deductTax != null "> and i.deduct_tax = #{deductTax}</if>
        <if test="updateTime != null  and updateTime != ''"> and i.update_time = #{updateTime}</if>
        <if test="orgId != null  and orgId != ''"> and i.org_id = #{orgId}</if>
        <if test="orgName != null  and orgName != ''"> and i.org_name like concat('%', #{orgName}, '%')</if>
        <if test="signStatus != null "> and i.sign_status = #{signStatus}</if>
        <if test="fileAddress != null  and fileAddress != ''"> and i.file_address = #{fileAddress}</if>
        <if test="accounttingNo != null  and accounttingNo != ''"> and qrml.accountting_no = #{accounttingNo}</if>
        order by i.create_time desc
    </select>
    
    <select id="selectAssociatedTollInvoiceByUserName" parameterType="String" resultMap="AssociatedTollInvoiceResult">
        <include refid="selectAssociatedTollInvoiceVo"/>
        where userName = #{userName}
    </select>
        
    <insert id="insertAssociatedTollInvoice" parameterType="AssociatedTollInvoice">
        insert into associated_toll_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">userName,</if>
            <if test="userAccount != null">userAccount,</if>
            <if test="createTime != null">createTime,</if>
            <if test="invoiceNo != null">invoiceNo,</if>
            <if test="invoiceCode != null">invoiceCode,</if>
            <if test="invoiceDate != null">invoiceDate,</if>
            <if test="amountTax != null">amountTax,</if>
            <if test="invoiceType != null">invoiceType,</if>
            <if test="isDeduct != null">isDeduct,</if>
            <if test="deductTax != null">deductTax,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="orgId != null">orgId,</if>
            <if test="orgName != null">orgName,</if>
            <if test="signStatus != null">signStatus,</if>
            <if test="fileAddress != null">fileAddress,</if>
            <if test="accounttingNo != null">accounttingNo,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="userAccount != null">#{userAccount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="invoiceNo != null">#{invoiceNo},</if>
            <if test="invoiceCode != null">#{invoiceCode},</if>
            <if test="invoiceDate != null">#{invoiceDate},</if>
            <if test="amountTax != null">#{amountTax},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="isDeduct != null">#{isDeduct},</if>
            <if test="deductTax != null">#{deductTax},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="signStatus != null">#{signStatus},</if>
            <if test="fileAddress != null">#{fileAddress},</if>
            <if test="accounttingNo != null">#{accounttingNo},</if>
         </trim>
    </insert>

    <update id="updateAssociatedTollInvoice" parameterType="AssociatedTollInvoice">
        update associated_toll_invoice
        <trim prefix="SET" suffixOverrides=",">
            <if test="userAccount != null">userAccount = #{userAccount},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="invoiceNo != null">invoiceNo = #{invoiceNo},</if>
            <if test="invoiceCode != null">invoiceCode = #{invoiceCode},</if>
            <if test="invoiceDate != null">invoiceDate = #{invoiceDate},</if>
            <if test="amountTax != null">amountTax = #{amountTax},</if>
            <if test="invoiceType != null">invoiceType = #{invoiceType},</if>
            <if test="isDeduct != null">isDeduct = #{isDeduct},</if>
            <if test="deductTax != null">deductTax = #{deductTax},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="orgId != null">orgId = #{orgId},</if>
            <if test="orgName != null">orgName = #{orgName},</if>
            <if test="signStatus != null">signStatus = #{signStatus},</if>
            <if test="fileAddress != null">fileAddress = #{fileAddress},</if>
            <if test="accounttingNo != null">accounttingNo = #{accounttingNo},</if>
        </trim>
        where userName = #{userName}
    </update>

    <delete id="deleteAssociatedTollInvoiceByUserName" parameterType="String">
        delete from associated_toll_invoice where userName = #{userName}
    </delete>

    <delete id="deleteAssociatedTollInvoiceByUserNames" parameterType="String">
        delete from associated_toll_invoice where userName in 
        <foreach item="userName" collection="array" open="(" separator="," close=")">
            #{userName}
        </foreach>
    </delete>
</mapper>