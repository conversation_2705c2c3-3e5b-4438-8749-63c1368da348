<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.LogisticsCostDetailsMapper">

    <resultMap type="LogisticsCostDetails" id="LogisticsCostDetailsResult">
        <result property="id" column="id"/>
        <result property="companyEntity" column="companyEntity"/>
        <result property="factory" column="factory"/>
        <result property="orderNumber" column="orderNumber"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentProcessor" column="currentProcessor"/>
        <result property="currentSession" column="currentSession"/>
        <result property="purchaseOrderNumber" column="purchaseOrderNumber"/>
        <result property="applicationDate" column="applicationDate"/>
        <result property="expenseYear" column="expenseYear"/>
        <result property="expenseMonth" column="expenseMonth"/>
        <result property="applicant" column="applicant"/>
        <result property="supplierCode" column="supplierCode"/>
        <result property="supplierName" column="supplierName"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="internalOrderNumber" column="internalOrderNumber"/>
        <result property="firstLevelClassification" column="firstLevelClassification"/>
        <result property="secondaryClassification" column="secondaryClassification"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="serviceName" column="serviceName"/>
        <result property="accountingSubjectCode" column="accountingSubjectCode"/>
        <result property="accountingSubjectName" column="accountingSubjectName"/>
        <result property="functionalScope" column="functionalScope"/>
        <result property="functionalScopeName" column="functionalScopeName"/>
        <result property="quantity" column="quantity"/>
        <result property="unitPrice" column="unitPrice"/>
        <result property="money" column="money"/>
        <result property="taxRate" column="taxRate"/>
        <result property="remarks" column="remarks"/>
        <result property="deleteStatus" column="deleteStatus"/>
    </resultMap>

    <sql id="selectLogisticsCostDetailsVo">
        select id,
               companyEntity,
               factory,
               orderNumber,
               documentStatus,
               currentProcessor,
               currentSession,
               purchaseOrderNumber,
               applicationDate,
               expenseYear,
               expenseMonth,
               applicant,
               supplierCode,
               supplierName,
               costCenterCode,
               costCenterName,
               projectNumber,
               internalOrderNumber,
               firstLevelClassification,
               secondaryClassification,
               serviceCode,
               serviceName,
               accountingSubjectCode,
               accountingSubjectName,
               functionalScope,
               functionalScopeName,
               quantity,
               unitPrice,
               money,
               taxRate,
               remarks,
               deleteStatus
        from logistics_cost_details
    </sql>

    <select id="selectLogisticsCostDetailsList" parameterType="LogisticsCostDetails"
            resultMap="LogisticsCostDetailsResult">
        <include refid="selectLogisticsCostDetailsVo"/>
        <where>
            <if test="companyEntity != null  and companyEntity != ''">and companyEntity like concat('%',
                #{companyEntity}, '%')
            </if>
            <if test="factory != null  and factory != ''">and factory like concat('%', #{factory}, '%')</if>
            <if test="orderNumber != null  and orderNumber != ''">and orderNumber like concat('%', #{orderNumber},
                '%')
            </if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentProcessor != null  and currentProcessor != ''">and currentProcessor like concat('%',
                #{currentProcessor}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="purchaseOrderNumber != null  and purchaseOrderNumber != ''">and purchaseOrderNumber like
                concat('%', #{purchaseOrderNumber}, '%')
            </if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''">
                and applicationDate between #{params.beginApplicationDate}+' 00:00:00' and #{params.endApplicationDate}+' 23:59:59'
            </if>
            <if test="expenseYear != null  and expenseYear != ''">and expenseYear like concat('%', #{expenseYear},
                '%')
            </if>
            <if test="expenseMonth != null  and expenseMonth != ''">and expenseMonth = #{expenseMonth}
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="supplierCode != null  and supplierCode != ''">and supplierCode like concat('%', #{supplierCode},
                '%')
            </if>
            <if test="supplierName != null  and supplierName != ''">and supplierName like concat('%', #{supplierName},
                '%')
            </if>
            <if test="costCenterCode != null  and costCenterCode != ''">and costCenterCode like concat('%',
                #{costCenterCode}, '%')
            </if>
            <if test="costCenterName != null  and costCenterName != ''">and costCenterName like concat('%',
                #{costCenterName}, '%')
            </if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="internalOrderNumber != null  and internalOrderNumber != ''">and internalOrderNumber like
                concat('%', #{internalOrderNumber}, '%')
            </if>
            <if test="firstLevelClassification != null  and firstLevelClassification != ''">and firstLevelClassification
                like concat('%', #{firstLevelClassification}, '%')
            </if>
            <if test="secondaryClassification != null  and secondaryClassification != ''">and secondaryClassification
                like concat('%', #{secondaryClassification}, '%')
            </if>
            <if test="serviceCode != null  and serviceCode != ''">and serviceCode like concat('%', #{serviceCode},
                '%')
            </if>
            <if test="serviceName != null  and serviceName != ''">and serviceName like concat('%', #{serviceName},
                '%')
            </if>
            <if test="accountingSubjectCode != null  and accountingSubjectCode != ''">and accountingSubjectCode like
                concat('%', #{accountingSubjectCode}, '%')
            </if>
            <if test="accountingSubjectName != null  and accountingSubjectName != ''">and accountingSubjectName like
                concat('%', #{accountingSubjectName}, '%')
            </if>
            <if test="functionalScope != null  and functionalScope != ''">and functionalScope like concat('%',
                #{functionalScope}, '%')
            </if>
            <if test="functionalScopeName != null  and functionalScopeName != ''">and functionalScopeName like
                concat('%', #{functionalScopeName}, '%')
            </if>
            <if test="quantity != null ">and quantity = #{quantity}</if>
            <if test="unitPrice != null ">and unitPrice = #{unitPrice}</if>
            <if test="money != null ">and money = #{money}</if>
            <if test="taxRate != null  and taxRate != ''">and taxRate = #{taxRate}</if>
            <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="documentStatusList !=null and documentStatusList.size>0">
                and documentStatus in
                <foreach collection="documentStatusList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectLogisticsCostDetailsById" parameterType="Integer" resultMap="LogisticsCostDetailsResult">
        <include refid="selectLogisticsCostDetailsVo"/>
        where id = #{id}
    </select>

    <insert id="insertLogisticsCostDetails" parameterType="LogisticsCostDetails">
        insert into logistics_cost_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyEntity != null">companyEntity,</if>
            <if test="factory != null">factory,</if>
            <if test="orderNumber != null">orderNumber,</if>
            <if test="documentStatus != null">documentStatus,</if>
            <if test="currentProcessor != null">currentProcessor,</if>
            <if test="currentSession != null">currentSession,</if>
            <if test="purchaseOrderNumber != null">purchaseOrderNumber,</if>
            <if test="applicationDate != null">applicationDate,</if>
            <if test="expenseYear != null">expenseYear,</if>
            <if test="expenseMonth != null">expenseMonth,</if>
            <if test="applicant != null">applicant,</if>
            <if test="supplierCode != null">supplierCode,</if>
            <if test="supplierName != null">supplierName,</if>
            <if test="costCenterCode != null">costCenterCode,</if>
            <if test="costCenterName != null">costCenterName,</if>
            <if test="projectNumber != null">projectNumber,</if>
            <if test="internalOrderNumber != null">internalOrderNumber,</if>
            <if test="firstLevelClassification != null">firstLevelClassification,</if>
            <if test="secondaryClassification != null">secondaryClassification,</if>
            <if test="serviceCode != null">serviceCode,</if>
            <if test="serviceName != null">serviceName,</if>
            <if test="accountingSubjectCode != null">accountingSubjectCode,</if>
            <if test="accountingSubjectName != null">accountingSubjectName,</if>
            <if test="functionalScope != null">functionalScope,</if>
            <if test="functionalScopeName != null">functionalScopeName,</if>
            <if test="quantity != null">quantity,</if>
            <if test="unitPrice != null">unitPrice,</if>
            <if test="money != null">money,</if>
            <if test="taxRate != null">taxRate,</if>
            <if test="remarks != null">remarks,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyEntity != null">#{companyEntity},</if>
            <if test="factory != null">#{factory},</if>
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="documentStatus != null">#{documentStatus},</if>
            <if test="currentProcessor != null">#{currentProcessor},</if>
            <if test="currentSession != null">#{currentSession},</if>
            <if test="purchaseOrderNumber != null">#{purchaseOrderNumber},</if>
            <if test="applicationDate != null">#{applicationDate},</if>
            <if test="expenseYear != null">#{expenseYear},</if>
            <if test="expenseMonth != null">#{expenseMonth},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="supplierCode != null">#{supplierCode},</if>
            <if test="supplierName != null">#{supplierName},</if>
            <if test="costCenterCode != null">#{costCenterCode},</if>
            <if test="costCenterName != null">#{costCenterName},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="internalOrderNumber != null">#{internalOrderNumber},</if>
            <if test="firstLevelClassification != null">#{firstLevelClassification},</if>
            <if test="secondaryClassification != null">#{secondaryClassification},</if>
            <if test="serviceCode != null">#{serviceCode},</if>
            <if test="serviceName != null">#{serviceName},</if>
            <if test="accountingSubjectCode != null">#{accountingSubjectCode},</if>
            <if test="accountingSubjectName != null">#{accountingSubjectName},</if>
            <if test="functionalScope != null">#{functionalScope},</if>
            <if test="functionalScopeName != null">#{functionalScopeName},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="money != null">#{money},</if>
            <if test="taxRate != null">#{taxRate},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
        </trim>
    </insert>

    <update id="updateLogisticsCostDetails" parameterType="LogisticsCostDetails">
        update logistics_cost_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyEntity != null">companyEntity = #{companyEntity},</if>
            <if test="factory != null">factory = #{factory},</if>
            <if test="orderNumber != null">orderNumber = #{orderNumber},</if>
            <if test="documentStatus != null">documentStatus = #{documentStatus},</if>
            <if test="currentProcessor != null">currentProcessor = #{currentProcessor},</if>
            <if test="currentSession != null">currentSession = #{currentSession},</if>
            <if test="purchaseOrderNumber != null">purchaseOrderNumber = #{purchaseOrderNumber},</if>
            <if test="applicationDate != null">applicationDate = #{applicationDate},</if>
            <if test="expenseYear != null">expenseYear = #{expenseYear},</if>
            <if test="expenseMonth != null">expenseMonth = #{expenseMonth},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="supplierCode != null">supplierCode = #{supplierCode},</if>
            <if test="supplierName != null">supplierName = #{supplierName},</if>
            <if test="costCenterCode != null">costCenterCode = #{costCenterCode},</if>
            <if test="costCenterName != null">costCenterName = #{costCenterName},</if>
            <if test="projectNumber != null">projectNumber = #{projectNumber},</if>
            <if test="internalOrderNumber != null">internalOrderNumber = #{internalOrderNumber},</if>
            <if test="firstLevelClassification != null">firstLevelClassification = #{firstLevelClassification},</if>
            <if test="secondaryClassification != null">secondaryClassification = #{secondaryClassification},</if>
            <if test="serviceCode != null">serviceCode = #{serviceCode},</if>
            <if test="serviceName != null">serviceName = #{serviceName},</if>
            <if test="accountingSubjectCode != null">accountingSubjectCode = #{accountingSubjectCode},</if>
            <if test="accountingSubjectName != null">accountingSubjectName = #{accountingSubjectName},</if>
            <if test="functionalScope != null">functionalScope = #{functionalScope},</if>
            <if test="functionalScopeName != null">functionalScopeName = #{functionalScopeName},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="unitPrice != null">unitPrice = #{unitPrice},</if>
            <if test="money != null">money = #{money},</if>
            <if test="taxRate != null">taxRate = #{taxRate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLogisticsCostDetailsById" parameterType="Integer">
        delete
        from logistics_cost_details
        where id = #{id}
    </delete>

    <delete id="deleteLogisticsCostDetailsByIds" parameterType="String">
        delete from logistics_cost_details where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAllLogisticsCostDetails">
        delete
        from logistics_cost_details
    </delete>

</mapper>