<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.FinancialIndexMapper">

    <resultMap type="FinancialIndex" id="FinancialIndexResult">
        <result property="id" column="id"/>
        <result property="fileName" column="fileName"/>
        <result property="filePath" column="filePath"/>
        <result property="jobNumber" column="jobNumber"/>
    </resultMap>

    <sql id="selectFinancialIndexVo">
        select id, fileName, filePath, jobNumber
        from financial_index
    </sql>

    <select id="selectFinancialIndexList" parameterType="FinancialIndex" resultMap="FinancialIndexResult">
        <include refid="selectFinancialIndexVo"/>
        <where>
            <if test="fileName != null  and fileName != ''">and fileName like concat('%', #{fileName}, '%')</if>
            <if test="filePath != null  and filePath != ''">and filePath = #{filePath}</if>
            <if test="jobNumber != null  and jobNumber != ''">and jobNumber = #{jobNumber}</if>
        </where>
    </select>

    <select id="selectFinancialIndexById" parameterType="Integer" resultMap="FinancialIndexResult">
        <include refid="selectFinancialIndexVo"/>
        where id = #{id}
    </select>

    <insert id="insertFinancialIndex" parameterType="FinancialIndex">
        insert into financial_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fileName != null">fileName,</if>
            <if test="filePath != null">filePath,</if>
            <if test="jobNumber != null">jobNumber,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="jobNumber != null">#{jobNumber},</if>
        </trim>
    </insert>

    <update id="updateFinancialIndex" parameterType="FinancialIndex">
        update financial_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">fileName = #{fileName},</if>
            <if test="filePath != null">filePath = #{filePath},</if>
            <if test="jobNumber != null">jobNumber = #{jobNumber},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinancialIndexById" parameterType="Integer">
        delete
        from financial_index
        where id = #{id}
    </delete>

    <delete id="deleteFinancialIndexByIds" parameterType="String">
        delete from financial_index where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>