<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ResearchDevelopmentExpenseSummaryMapper">
    <resultMap type="com.akesobio.report.groupFinance.domain.ResearchDevelopmentExpenseSummary"
               id="ResearchDevelopmentExpenseSummaryResult">
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="amount" column="amount"/>
        <result property="costType" column="costType"/>
        <result property="pipelineType" column="pipelineType"/>
        <result property="drugPipeline" column="drugPipeline"/>
        <result property="projectNumber" column="projectNumber"/>
    </resultMap>

    <select id="selectResearchDevelopmentExpenseSummaryList" resultMap="ResearchDevelopmentExpenseSummaryResult">
        select companyName,companyCode,year,month,amount,costType,pipelineType,drugPipeline,projectNumber
        from
        (SELECT
        a.butxt AS 'companyName',
        a.rbukrs AS 'companyCode',
        a.gjahr AS 'year',
        a.monat AS 'month',
        a.ksl AS 'amount',
        a.zcbfl AS 'costType',
        a.zgxfl AS 'pipelineType',
        a.zypgx AS 'drugPipeline',
        a.ktext AS 'projectNumber'
        FROM
        research_development_expense_summary_table a) b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
            <if test="year !=null and year !=''">and year = #{year}</if>
            <if test="month !=null and month !=''">and month = #{month}</if>
            <if test="costType !=null and costType != ''">and costType = #{costType}</if>
            <if test="pipelineType !=null and pipelineType != ''">and pipelineType = #{pipelineType}</if>
            <if test="drugPipeline !=null and drugPipeline != ''">and drugPipeline = #{drugPipeline}</if>
            <if test="projectNumber !=null and projectNumber != ''">and projectNumber = #{projectNumber}</if>
            <if test="monthList !=null and monthList.size>0">
                and month in
                <foreach collection="monthList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="companyList !=null and companyList.size>0">
                and companyName in
                <foreach collection="companyList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>