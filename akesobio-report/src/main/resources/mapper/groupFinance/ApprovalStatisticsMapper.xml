<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ApprovalStatisticsMapper">

    <resultMap type="ApprovalStatistics" id="ApprovalStatisticsResult">
        <result property="dateCreated" column="dateCreated"/>
        <result property="processNumber" column="processNumber"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="projectName" column="projectName"/>
        <result property="applicant" column="applicant"/>
        <result property="department" column="department"/>
        <result property="subjectName" column="subjectName"/>
        <result property="supplier" column="supplier"/>
        <result property="supplierCode" column="supplierCode"/>
        <result property="expences" column="expences"/>
        <result property="companyEntity" column="companyEntity"/>
        <result property="contractNo" column="contractNo"/>
        <result property="contractLevel" column="contractLevel"/>
        <result property="businessType" column="businessType"/>
        <result property="contractType" column="contractType"/>
        <result property="paymentDescription" column="paymentDescription"/>
        <result property="settlementInstructions" column="settlementInstructions"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="currency" column="currency"/>
        <result property="originalContractAmount" column="originalContractAmount"/>
        <result property="settlementAmount" column="settlementAmount"/>
        <result property="estimate" column="estimate"/>
        <result property="transactionAmount" column="transactionAmount"/>
        <result property="paymentAmount" column="paymentAmount"/>
        <result property="paymentApplicationAmount" column="paymentApplicationAmount"/>
        <result property="amountIncludingTax" column="amountIncludingTax"/>
        <result property="paymentStatus" column="paymentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="approvalDate" column="approvalDate"/>
        <result property="fdID" column="fdID"/>
        <result property="formName" column="formName"/>
        <result property="cg06" column="cg06"/>
    </resultMap>

    <sql id="selectApprovalStatisticsVo">
        select dateCreated,
               processNumber,
               applicationSubject,
               projectName,
               applicant,
               department,
               subjectName,
               supplier,
               supplierCode,
               expences,
               companyEntity,
               contractNo,
               contractLevel,
               businessType,
               contractType,
               paymentDescription,
               settlementInstructions,
               projectNumber,
               currency,
               originalContractAmount,
               settlementAmount,
               estimate,
               transactionAmount,
               paymentAmount,
               paymentApplicationAmount,
               amountIncludingTax,
               paymentStatus,
               currentSession,
               approvalDate,
               fdID,
               formName,
               cg06
        from approval_statistics
    </sql>

    <select id="selectApprovalStatisticsList" parameterType="ApprovalStatistics" resultMap="ApprovalStatisticsResult">
        <include refid="selectApprovalStatisticsVo"/>
        <where>
            <if test="queryFormName != null and queryFormName != ''">
                <choose>
                    <when test="queryFormName.size() > 1">
                        <foreach collection="queryFormName" item="item" open="(" separator="or" close=")">
                            formName like concat('%', #{item},'%')
                        </foreach>
                    </when>
                    <otherwise>
                        <foreach item="item" index="index" collection="queryFormName">
                            formName like concat('%', #{item},'%')
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="queryCurrentSession != null and queryCurrentSession != ''">
                <choose>
                    <when test="queryCurrentSession.size() > 1">
                    and
                        <foreach collection="queryCurrentSession" item="item" open="(" separator="or" close=")">
                            currentSession like concat('%', #{item},'%')
                        </foreach>
                    </when>
                    <otherwise>
                        <foreach item="item" index="index" collection="queryCurrentSession">
                            and currentSession like concat('%', #{item},'%')
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="params.beginDateCreated != null and params.beginDateCreated != '' and params.endDateCreated != null and params.endDateCreated != ''">
                and dateCreated between #{params.beginDateCreated}+' 00:00:00' and #{params.endDateCreated}+' 23:59:59'
            </if>
            <if test="processNumber != null  and processNumber != ''">and processNumber like concat('%',
                #{processNumber}, '%')
            </if>
            <if test="applicationSubject != null  and applicationSubject != ''">and applicationSubject like concat('%',
                #{applicationSubject}, '%')
            </if>
            <if test="projectName != null  and projectName != ''">and projectName like concat('%', #{projectName},
                '%')
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="subjectName != null  and subjectName != ''">and subjectName like concat('%', #{subjectName},
                '%')
            </if>
            <if test="supplier != null  and supplier != ''">and supplier like concat('%', #{supplier}, '%')</if>
            <if test="supplierCode != null  and supplierCode != ''">and supplierCode like concat('%', #{supplierCode},
                '%')
            </if>
            <if test="expences != null  and expences != ''">and expences like concat('%', #{expences}, '%')</if>
            <if test="companyEntity != null  and companyEntity != ''">and companyEntity like concat('%',
                #{companyEntity}, '%')
            </if>
            <if test="contractNo != null  and contractNo != ''">and contractNo like concat('%', #{contractNo}, '%')</if>
            <if test="contractLevel != null  and contractLevel != ''">and contractLevel like concat('%',
                #{contractLevel}, '%')
            </if>
            <if test="businessType != null  and businessType != ''">and businessType like concat('%', #{businessType},
                '%')
            </if>
            <if test="contractType != null  and contractType != ''">and contractType like concat('%', #{contractType},
                '%')
            </if>
            <if test="paymentDescription != null  and paymentDescription != ''">and paymentDescription like concat('%',
                #{paymentDescription}, '%')
            </if>
            <if test="settlementInstructions != null  and settlementInstructions != ''">and settlementInstructions like
                concat('%', #{settlementInstructions}, '%')
            </if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="currency != null  and currency != ''">and currency = #{currency}</if>
            <if test="originalContractAmount != null ">and originalContractAmount = #{originalContractAmount}</if>
            <if test="settlementAmount != null ">and settlementAmount = #{settlementAmount}</if>
            <if test="estimate != null ">and estimate = #{estimate}</if>
            <if test="transactionAmount != null ">and transactionAmount = #{transactionAmount}</if>
            <if test="paymentAmount != null ">and paymentAmount = #{paymentAmount}</if>
            <if test="paymentApplicationAmount != null ">and paymentApplicationAmount = #{paymentApplicationAmount}</if>
            <if test="amountIncludingTax != null ">and amountIncludingTax = #{amountIncludingTax}</if>
            <if test="paymentStatus != null  and paymentStatus != ''">and paymentStatus like concat('%',
                #{paymentStatus}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="cg06 != null  and cg06 != ''">and cg06 like concat('%',
                #{cg06}, '%')
            </if>
            <if test="params.beginApprovalDate != null and params.beginApprovalDate != '' and params.endApprovalDate != null and params.endApprovalDate != ''">
                and approvalDate between #{params.beginApprovalDate}+' 00:00:00' and #{params.endApprovalDate}+' 23:59:59'
            </if>
        </where>
        order by dateCreated desc,approvalDate desc
    </select>

</mapper>