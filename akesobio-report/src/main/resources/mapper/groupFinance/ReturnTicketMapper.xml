<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ReturnTicketMapper">
    
    <resultMap type="ReturnTicket" id="ReturnTicketResult">
        <result property="applicationDate"    column="applicationDate"    />
        <result property="oANumber"    column="oANumber"    />
        <result property="applicationSubject"    column="applicationSubject"    />
        <result property="reimbursementPerson"    column="reimbursementPerson"    />
        <result property="department"    column="department"    />
        <result property="costBearing"    column="costBearing"    />
        <result property="receivingUnit"    column="receivingUnit"    />
        <result property="lostTicketReturnTicket"    column="lostTicketReturnTicket"    />
        <result property="invoiceType"    column="invoiceType"    />
        <result property="specialTicketType"    column="specialTicketType"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="invoiceValue"    column="invoiceValue"    />
        <result property="invoiceNumber"    column="invoiceNumber"    />
        <result property="differenceInvoice"    column="differenceInvoice"    />
        <result property="reason"    column="reason"    />
        <result property="associatedApprovalForm"    column="associatedApprovalForm"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
    </resultMap>

    <sql id="selectReturnTicketVo">
        select applicationDate, oANumber, applicationSubject, reimbursementPerson, department, costBearing, receivingUnit, lostTicketReturnTicket, invoiceType, specialTicketType, projectNumber, invoiceValue, invoiceNumber, differenceInvoice, reason, associatedApprovalForm, documentStatus, currentSession from return_ticket
    </sql>

    <select id="selectReturnTicketList" parameterType="ReturnTicket" resultMap="ReturnTicketResult">
        <include refid="selectReturnTicketVo"/>
        <where>  
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="oANumber != null  and oANumber != ''"> and oANumber like concat('%', #{oANumber}, '%')</if>
            <if test="applicationSubject != null  and applicationSubject != ''"> and applicationSubject like concat('%', #{applicationSubject}, '%')</if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''"> and reimbursementPerson like concat('%', #{reimbursementPerson}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="costBearing != null  and costBearing != ''"> and costBearing like concat('%', #{costBearing}, '%')</if>
            <if test="receivingUnit != null  and receivingUnit != ''"> and receivingUnit like concat('%', #{receivingUnit}, '%')</if>
            <if test="lostTicketReturnTicket != null  and lostTicketReturnTicket != ''"> and lostTicketReturnTicket like concat('%', #{lostTicketReturnTicket}, '%')</if>
            <if test="invoiceType != null  and invoiceType != ''"> and invoiceType like concat('%', #{invoiceType}, '%')</if>
            <if test="specialTicketType != null  and specialTicketType != ''"> and specialTicketType like concat('%', #{specialTicketType}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="invoiceValue != null  and invoiceValue != ''"> and invoiceValue = #{invoiceValue}</if>
            <if test="invoiceNumber != null  and invoiceNumber != ''"> and invoiceNumber like concat('%', #{invoiceNumber}, '%')</if>
            <if test="differenceInvoice != null  and differenceInvoice != ''"> and differenceInvoice = #{differenceInvoice}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="associatedApprovalForm != null  and associatedApprovalForm != ''"> and associatedApprovalForm like concat('%', #{associatedApprovalForm}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
        </where>
        order by applicationDate desc
    </select>
</mapper>