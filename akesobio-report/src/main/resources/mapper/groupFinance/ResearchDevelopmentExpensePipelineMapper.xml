<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ResearchDevelopmentExpensePipelineMapper">

    <resultMap type="ResearchDevelopmentExpensePipeline" id="ResearchDevelopmentExpensePipelineResult">
        <result property="id" column="id"/>
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="yearMonth" column="yearMonth"/>
        <result property="amount" column="amount"/>
        <result property="costType" column="costType"/>
        <result property="pipelineType" column="pipelineType"/>
        <result property="drugPipeline" column="drugPipeline"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="deleteStatus" column="deleteStatus"/>
    </resultMap>

    <sql id="selectResearchDevelopmentExpensePipelineVo">
        select id,
               companyName,
               companyCode, year, month, yearMonth, amount, costType, pipelineType, drugPipeline, projectNumber, deleteStatus
        from research_development_expense_pipeline
    </sql>

    <select id="selectResearchDevelopmentExpensePipelineList" parameterType="ResearchDevelopmentExpensePipeline"
            resultMap="ResearchDevelopmentExpensePipelineResult">
        <include refid="selectResearchDevelopmentExpensePipelineVo"/>
        <where>
            <if test="companyName != null  and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode != null  and companyCode != ''">and companyCode = #{companyCode}</if>
            <if test="year != null ">and year = #{year}</if>
            <if test="month != null  and month != ''">and month = #{month}</if>
            <if test="yearMonth != null  and yearMonth != ''">and yearMonth = #{yearMonth}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="costType != null  and costType != ''">and costType = #{costType}</if>
            <if test="pipelineType != null  and pipelineType != ''">and pipelineType = #{pipelineType}</if>
            <if test="drugPipeline != null  and drugPipeline != ''">and drugPipeline = #{drugPipeline}</if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber = #{projectNumber}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="pipelineList !=null and pipelineList.size>0">
                and drugPipeline in
                <foreach collection="pipelineList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="projectList !=null and projectList.size>0">
                and projectNumber in
                <foreach collection="projectList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectResearchDevelopmentExpensePipelineById" parameterType="Integer"
            resultMap="ResearchDevelopmentExpensePipelineResult">
        <include refid="selectResearchDevelopmentExpensePipelineVo"/>
        where id = #{id}
    </select>

    <insert id="insertResearchDevelopmentExpensePipeline" parameterType="ResearchDevelopmentExpensePipeline">
        insert into research_development_expense_pipeline
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="year != null">year,</if>
            <if test="month != null">month,</if>
            <if test="yearMonth != null">yearMonth,</if>
            <if test="amount != null">amount,</if>
            <if test="costType != null">costType,</if>
            <if test="pipelineType != null">pipelineType,</if>
            <if test="drugPipeline != null">drugPipeline,</if>
            <if test="projectNumber != null">projectNumber,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="year != null">#{year},</if>
            <if test="month != null">#{month},</if>
            <if test="yearMonth != null">#{yearMonth},</if>
            <if test="amount != null">#{amount},</if>
            <if test="costType != null">#{costType},</if>
            <if test="pipelineType != null">#{pipelineType},</if>
            <if test="drugPipeline != null">#{drugPipeline},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
        </trim>
    </insert>

    <update id="updateResearchDevelopmentExpensePipeline" parameterType="ResearchDevelopmentExpensePipeline">
        update research_development_expense_pipeline
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="yearMonth != null">yearMonth = #{yearMonth},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="costType != null">costType = #{costType},</if>
            <if test="pipelineType != null">pipelineType = #{pipelineType},</if>
            <if test="drugPipeline != null">drugPipeline = #{drugPipeline},</if>
            <if test="projectNumber != null">projectNumber = #{projectNumber},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResearchDevelopmentExpensePipelineById" parameterType="Integer">
        delete
        from research_development_expense_pipeline
        where id = #{id}
    </delete>

    <delete id="deleteResearchDevelopmentExpensePipelineByIds" parameterType="String">
        delete from research_development_expense_pipeline where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAllResearchDevelopmentExpensePipeline">
        delete
        from research_development_expense_pipeline
    </delete>
</mapper>