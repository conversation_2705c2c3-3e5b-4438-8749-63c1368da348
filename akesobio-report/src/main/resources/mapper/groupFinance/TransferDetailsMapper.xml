<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.TransferDetailsMapper">
    
    <resultMap type="TransferDetails" id="TransferDetailsResult">
        <result property="applicationDate"    column="applicationDate"    />
        <result property="oANumber"    column="oANumber"    />
        <result property="applicationSubject"    column="applicationSubject"    />
        <result property="reimbursementPerson"    column="reimbursementPerson"    />
        <result property="department"    column="department"    />
        <result property="receivingUnit"    column="receivingUnit"    />
        <result property="supplierCode"    column="supplierCode"    />
        <result property="transferOutSubject"    column="transferOutSubject"    />
        <result property="transferOutProjectNumber"    column="transferOutProjectNumber"    />
        <result property="transferOutCostCenter"    column="transferOutCostCenter"    />
        <result property="changeIntoSubject"    column="changeIntoSubject"    />
        <result property="changeIntoProjectNumber"    column="changeIntoProjectNumber"    />
        <result property="changeIntoCostCenter"    column="changeIntoCostCenter"    />
        <result property="excludingTaxPaymentAmount"    column="excludingTaxPaymentAmount"    />
        <result property="taxAmount"    column="taxAmount"    />
        <result property="includingTaxPaymentAmount"    column="includingTaxPaymentAmount"    />
        <result property="invoice"    column="invoice"    />
        <result property="invoiceNumber"    column="invoiceNumber"    />
        <result property="payableVoucherNumber"    column="payableVoucherNumber"    />
        <result property="voucherPushTime"    column="voucherPushTime"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
    </resultMap>

    <sql id="selectTransferDetailsVo">
        select applicationDate, oANumber, applicationSubject, reimbursementPerson, department, receivingUnit, supplierCode, transferOutSubject, transferOutProjectNumber, transferOutCostCenter, changeIntoSubject, changeIntoProjectNumber, changeIntoCostCenter, excludingTaxPaymentAmount, taxAmount, includingTaxPaymentAmount, invoice, invoiceNumber, payableVoucherNumber, voucherPushTime, documentStatus, currentSession from transfer_details
    </sql>

    <select id="selectTransferDetailsList" parameterType="TransferDetails" resultMap="TransferDetailsResult">
        <include refid="selectTransferDetailsVo"/>
        <where>  
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="oANumber != null  and oANumber != ''"> and oANumber like concat('%', #{oANumber}, '%')</if>
            <if test="applicationSubject != null  and applicationSubject != ''"> and applicationSubject like concat('%', #{applicationSubject}, '%')</if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''"> and reimbursementPerson like concat('%', #{reimbursementPerson}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="receivingUnit != null  and receivingUnit != ''"> and receivingUnit like concat('%', #{receivingUnit}, '%')</if>
            <if test="supplierCode != null  and supplierCode != ''"> and supplierCode like concat('%', #{supplierCode}, '%')</if>
            <if test="transferOutSubject != null  and transferOutSubject != ''"> and transferOutSubject like concat('%', #{transferOutSubject}, '%')</if>
            <if test="transferOutProjectNumber != null  and transferOutProjectNumber != ''"> and transferOutProjectNumber like concat('%', #{transferOutProjectNumber}, '%')</if>
            <if test="transferOutCostCenter != null  and transferOutCostCenter != ''"> and transferOutCostCenter like concat('%', #{transferOutCostCenter}, '%')</if>
            <if test="changeIntoSubject != null  and changeIntoSubject != ''"> and changeIntoSubject like concat('%', #{changeIntoSubject}, '%')</if>
            <if test="changeIntoProjectNumber != null  and changeIntoProjectNumber != ''"> and changeIntoProjectNumber like concat('%', #{changeIntoProjectNumber}, '%')</if>
            <if test="changeIntoCostCenter != null  and changeIntoCostCenter != ''"> and changeIntoCostCenter like concat('%', #{changeIntoCostCenter}, '%')</if>
            <if test="excludingTaxPaymentAmount != null  and excludingTaxPaymentAmount != ''"> and excludingTaxPaymentAmount = #{excludingTaxPaymentAmount}</if>
            <if test="taxAmount != null  and taxAmount != ''"> and taxAmount = #{taxAmount}</if>
            <if test="includingTaxPaymentAmount != null  and includingTaxPaymentAmount != ''"> and includingTaxPaymentAmount = #{includingTaxPaymentAmount}</if>
            <if test="invoice != null  and invoice != ''"> and invoice = #{invoice}</if>
            <if test="invoiceNumber != null  and invoiceNumber != ''"> and invoiceNumber like concat('%', #{invoiceNumber}, '%')</if>
            <if test="payableVoucherNumber != null  and payableVoucherNumber != ''"> and payableVoucherNumber like concat('%', #{payableVoucherNumber}, '%')</if>
            <if test="voucherPushTime != null  and voucherPushTime != ''"> and voucherPushTime = #{voucherPushTime}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
        </where>
        order by applicationDate desc
    </select>
</mapper>