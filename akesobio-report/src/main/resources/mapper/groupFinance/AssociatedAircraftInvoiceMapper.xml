<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.AssociatedAircraftInvoiceMapper">
    
    <resultMap type="AssociatedAircraftInvoice" id="AssociatedAircraftInvoiceResult">
        <result property="userName"    column="user_name"    />
        <result property="userAccount"    column="user_account"    />
        <result property="createTime"    column="create_time"    />
        <result property="invoiceNo"    column="invoice_no"    />
        <result property="invoiceDate"    column="invoice_date"    />
        <result property="drawer"    column="drawer"    />
        <result property="idNum"    column="id_num"    />
        <result property="checkCode"    column="check_code"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="civilAviationFund"    column="civil_aviation_fund"    />
        <result property="amountTax"    column="amount_tax"    />
        <result property="fromStation"    column="from_station"    />
        <result property="toStation"    column="to_station"    />
        <result property="flightNo"    column="flight_no"    />
        <result property="travelDate"    column="travel_date"    />
        <result property="travelTime"    column="travel_time"    />
        <result property="seatLevel"    column="seat_level"    />
        <result property="carrier"    column="carrier"    />
        <result property="isDeduct"    column="is_deduct"    />
        <result property="deductTax"    column="deduct_tax"    />
        <result property="updateTime"    column="update_time"    />
        <result property="orgId"    column="org_id"    />
        <result property="orgName"    column="org_name"    />
        <result property="signStatus"    column="sign_status"    />
        <result property="fileAddress"    column="file_address"    />
        <result property="accounttingNo"    column="accountting_no"    />
    </resultMap>

    <sql id="selectAssociatedAircraftInvoiceVo">
        select i.user_name, i.user_account, i.create_time, i.invoice_no, i.invoice_date, i.drawer, i.id_num, i.check_code, i.total_amount, i.civil_aviation_fund, i.amount_tax, itl.from_station, itl.to_station, itl.flight_no, itl.travel_date, itl.travel_time, itl.seat_level, itl.carrier, i.is_deduct, i.deduct_tax, i.update_time, i.org_id, i.org_name, i.sign_status, i.file_address, qrml.accountting_no from invoice AS i
            LEFT JOIN query_reimbursement_main_list AS qrml ON i.invoice_no = qrml.inv_num
            LEFT JOIN invoice_travel_list AS itl ON i.id = itl.invoice_id
            WHERE i.reimburse_status = 2 AND i.invoice_type = '1003'
    </sql>

    <select id="selectAssociatedAircraftInvoiceList" parameterType="AssociatedAircraftInvoice" resultMap="AssociatedAircraftInvoiceResult">
        <include refid="selectAssociatedAircraftInvoiceVo"/>
        <if test="userName != null  and userName != ''"> and i.user_name like concat('%', #{userName}, '%')</if>
        <if test="userAccount != null  and userAccount != ''"> and i.user_account = #{userAccount}</if>
        <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and i.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        <if test="invoiceNo != null  and invoiceNo != ''"> and i.invoice_no = #{invoiceNo}</if>
        <if test="invoiceDate != null  and invoiceDate != ''"> and i.invoice_date = #{invoiceDate}</if>
        <if test="drawer != null  and drawer != ''"> and i.drawer = #{drawer}</if>
        <if test="idNum != null  and idNum != ''"> and i.id_num = #{idNum}</if>
        <if test="checkCode != null  and checkCode != ''"> and i.check_code = #{checkCode}</if>
        <if test="totalAmount != null "> and i.total_amount = #{totalAmount}</if>
        <if test="civilAviationFund != null "> and i.civil_aviation_fund = #{civilAviationFund}</if>
        <if test="amountTax != null "> and i.amount_tax = #{amountTax}</if>
        <if test="fromStation != null  and fromStation != ''"> and itl.from_station = #{fromStation}</if>
        <if test="toStation != null  and toStation != ''"> and itl.to_station = #{toStation}</if>
        <if test="flightNo != null  and flightNo != ''"> and itl.flight_no = #{flightNo}</if>
        <if test="travelDate != null  and travelDate != ''"> and itl.travel_date = #{travelDate}</if>
        <if test="travelTime != null  and travelTime != ''"> and itl.travel_time = #{travelTime}</if>
        <if test="seatLevel != null  and seatLevel != ''"> and itl.seat_level = #{seatLevel}</if>
        <if test="carrier != null  and carrier != ''"> and itl.carrier = #{carrier}</if>
        <if test="isDeduct != null "> and i.is_deduct = #{isDeduct}</if>
        <if test="deductTax != null "> and i.deduct_tax = #{deductTax}</if>
        <if test="updateTime != null  and updateTime != ''"> and i.update_time = #{updateTime}</if>
        <if test="orgId != null  and orgId != ''"> and i.org_id = #{orgId}</if>
        <if test="orgName != null  and orgName != ''"> and i.org_name like concat('%', #{orgName}, '%')</if>
        <if test="signStatus != null "> and i.sign_status = #{signStatus}</if>
        <if test="fileAddress != null  and fileAddress != ''"> and i.file_address = #{fileAddress}</if>
        <if test="accounttingNo != null  and accounttingNo != ''"> and qrml.accountting_no = #{accounttingNo}</if>
        order by i.create_time desc
    </select>
    
    <select id="selectAssociatedAircraftInvoiceByUserName" parameterType="String" resultMap="AssociatedAircraftInvoiceResult">
        <include refid="selectAssociatedAircraftInvoiceVo"/>
        where userName = #{userName}
    </select>
        
    <insert id="insertAssociatedAircraftInvoice" parameterType="AssociatedAircraftInvoice">
        insert into associated_aircraft_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">userName,</if>
            <if test="userAccount != null">userAccount,</if>
            <if test="createTime != null">createTime,</if>
            <if test="invoiceNo != null">invoiceNo,</if>
            <if test="invoiceDate != null">invoiceDate,</if>
            <if test="drawer != null">drawer,</if>
            <if test="idNum != null">idNum,</if>
            <if test="checkCode != null">checkCode,</if>
            <if test="totalAmount != null">totalAmount,</if>
            <if test="civilAviationFund != null">civilAviationFund,</if>
            <if test="amountTax != null">amountTax,</if>
            <if test="fromStation != null">fromStation,</if>
            <if test="toStation != null">toStation,</if>
            <if test="flightNo != null">flightNo,</if>
            <if test="travelDate != null">travelDate,</if>
            <if test="travelTime != null">travelTime,</if>
            <if test="seatLevel != null">seatLevel,</if>
            <if test="carrier != null">carrier,</if>
            <if test="isDeduct != null">isDeduct,</if>
            <if test="deductTax != null">deductTax,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="orgId != null">orgId,</if>
            <if test="orgName != null">orgName,</if>
            <if test="signStatus != null">signStatus,</if>
            <if test="fileAddress != null">fileAddress,</if>
            <if test="accounttingNo != null">accounttingNo,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="userAccount != null">#{userAccount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="invoiceNo != null">#{invoiceNo},</if>
            <if test="invoiceDate != null">#{invoiceDate},</if>
            <if test="drawer != null">#{drawer},</if>
            <if test="idNum != null">#{idNum},</if>
            <if test="checkCode != null">#{checkCode},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="civilAviationFund != null">#{civilAviationFund},</if>
            <if test="amountTax != null">#{amountTax},</if>
            <if test="fromStation != null">#{fromStation},</if>
            <if test="toStation != null">#{toStation},</if>
            <if test="flightNo != null">#{flightNo},</if>
            <if test="travelDate != null">#{travelDate},</if>
            <if test="travelTime != null">#{travelTime},</if>
            <if test="seatLevel != null">#{seatLevel},</if>
            <if test="carrier != null">#{carrier},</if>
            <if test="isDeduct != null">#{isDeduct},</if>
            <if test="deductTax != null">#{deductTax},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="signStatus != null">#{signStatus},</if>
            <if test="fileAddress != null">#{fileAddress},</if>
            <if test="accounttingNo != null">#{accounttingNo},</if>
         </trim>
    </insert>

    <update id="updateAssociatedAircraftInvoice" parameterType="AssociatedAircraftInvoice">
        update associated_aircraft_invoice
        <trim prefix="SET" suffixOverrides=",">
            <if test="userAccount != null">userAccount = #{userAccount},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="invoiceNo != null">invoiceNo = #{invoiceNo},</if>
            <if test="invoiceDate != null">invoiceDate = #{invoiceDate},</if>
            <if test="drawer != null">drawer = #{drawer},</if>
            <if test="idNum != null">idNum = #{idNum},</if>
            <if test="checkCode != null">checkCode = #{checkCode},</if>
            <if test="totalAmount != null">totalAmount = #{totalAmount},</if>
            <if test="civilAviationFund != null">civilAviationFund = #{civilAviationFund},</if>
            <if test="amountTax != null">amountTax = #{amountTax},</if>
            <if test="fromStation != null">fromStation = #{fromStation},</if>
            <if test="toStation != null">toStation = #{toStation},</if>
            <if test="flightNo != null">flightNo = #{flightNo},</if>
            <if test="travelDate != null">travelDate = #{travelDate},</if>
            <if test="travelTime != null">travelTime = #{travelTime},</if>
            <if test="seatLevel != null">seatLevel = #{seatLevel},</if>
            <if test="carrier != null">carrier = #{carrier},</if>
            <if test="isDeduct != null">isDeduct = #{isDeduct},</if>
            <if test="deductTax != null">deductTax = #{deductTax},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="orgId != null">orgId = #{orgId},</if>
            <if test="orgName != null">orgName = #{orgName},</if>
            <if test="signStatus != null">signStatus = #{signStatus},</if>
            <if test="fileAddress != null">fileAddress = #{fileAddress},</if>
            <if test="accounttingNo != null">accounttingNo = #{accounttingNo},</if>
        </trim>
        where userName = #{userName}
    </update>

    <delete id="deleteAssociatedAircraftInvoiceByUserName" parameterType="String">
        delete from associated_aircraft_invoice where userName = #{userName}
    </delete>

    <delete id="deleteAssociatedAircraftInvoiceByUserNames" parameterType="String">
        delete from associated_aircraft_invoice where userName in 
        <foreach item="userName" collection="array" open="(" separator="," close=")">
            #{userName}
        </foreach>
    </delete>
</mapper>