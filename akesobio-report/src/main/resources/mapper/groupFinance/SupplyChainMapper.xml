<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.SupplyChainMapper">

    <resultMap type="SupplyChain" id="SupplyChainResult">
        <result property="fromName"    column="fromName"    />
        <result property="OANumber"    column="OANumber"    />
        <result property="costBearingCompany"    column="costBearingCompany"    />
        <result property="paymentApplicationDate"    column="paymentApplicationDate"    />
        <result property="applicant"    column="applicant"    />
        <result property="department"    column="department"    />
        <result property="contractNo"    column="contractNo"    />
        <result property="costCenterCode"    column="costCenterCode"    />
        <result property="costCenter"    column="costCenter"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="receivingUnit"    column="receivingUnit"    />
        <result property="contractType"    column="contractType"    />
        <result property="paymentDescription"    column="paymentDescription"    />
        <result property="accountCode"    column="accountCode"    />
        <result property="suject"    column="suject"    />
        <result property="paymentMethod"    column="paymentMethod"    />
        <result property="settlementPeriod"    column="settlementPeriod"    />
        <result property="currency"    column="currency"    />
        <result property="exchangeRate"    column="exchangeRate"    />
        <result property="amountWithoutTax"    column="amountWithoutTax"    />
        <result property="specialTax"    column="specialTax"    />
        <result property="amount"    column="amount"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="sapCredentialPushDate"    column="sapCredentialPushDate"    />
        <result property="sapDocumentStatus"    column="sapDocumentStatus"    />
    </resultMap>

    <sql id="selectSupplyChainVo">
        select fromName, OANumber, costBearingCompany, paymentApplicationDate, applicant, department, contractNo, costCenterCode, costCenter, projectNumber, receivingUnit, contractType, paymentDescription, accountCode, suject, paymentMethod, settlementPeriod, currency, exchangeRate, amountWithoutTax, specialTax, amount, documentStatus, currentSession, sapCredentialPushDate, sapDocumentStatus from supply_spending
    </sql>

    <select id="selectSupplyChainList" parameterType="SupplyChain" resultMap="SupplyChainResult">
        <include refid="selectSupplyChainVo"/>
        <where>
            <if test="fromName != null  and fromName != ''"> and fromName like concat('%', #{fromName}, '%')</if>
            <if test="OANumber != null  and OANumber != ''"> and OANumber like concat('%', #{OANumber}, '%')</if>
            <if test="costBearingCompany != null  and costBearingCompany != ''"> and costBearingCompany like concat('%', #{costBearingCompany}, '%')</if>
            <if test="params.beginPaymentApplicationDate != null and params.beginPaymentApplicationDate != '' and params.endPaymentApplicationDate != null and params.endPaymentApplicationDate != ''"> and paymentApplicationDate between #{params.beginPaymentApplicationDate} and #{params.endPaymentApplicationDate}</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="contractNo != null  and contractNo != ''"> and contractNo like concat('%', #{contractNo}, '%')</if>
            <if test="costCenterCode != null  and costCenterCode != ''"> and costCenterCode like concat('%', #{costCenterCode}, '%')</if>
            <if test="costCenter != null  and costCenter != ''"> and costCenter like concat('%', #{costCenter}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="receivingUnit != null  and receivingUnit != ''"> and receivingUnit like concat('%', #{receivingUnit}, '%')</if>
            <if test="contractType != null  and contractType != ''"> and contractType like concat('%', #{contractType}, '%')</if>
            <if test="paymentDescription != null  and paymentDescription != ''"> and paymentDescription like concat('%', #{paymentDescription}, '%')</if>
            <if test="accountCode != null  and accountCode != ''"> and accountCode like concat('%', #{accountCode}, '%')</if>
            <if test="suject != null  and suject != ''"> and suject like concat('%', #{suject}, '%')</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and paymentMethod = #{paymentMethod}</if>
            <if test="settlementPeriod != null  and settlementPeriod != ''"> and settlementPeriod = #{settlementPeriod}</if>
            <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
            <if test="exchangeRate != null  and exchangeRate != ''"> and exchangeRate = #{exchangeRate}</if>
            <if test="amountWithoutTax != null  and amountWithoutTax != ''"> and amountWithoutTax = #{amountWithoutTax}</if>
            <if test="specialTax != null  and specialTax != ''"> and specialTax = #{specialTax}</if>
            <if test="amount != null  and amount != ''"> and amount = #{amount}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="sapCredentialPushDate != null  and sapCredentialPushDate != ''"> and sapCredentialPushDate like concat('%', #{sapCredentialPushDate}, '%')</if>
            <if test="sapDocumentStatus != null  and sapDocumentStatus != ''"> and sapDocumentStatus like concat('%', #{sapDocumentStatus}, '%')</if>
        </where>
        order by paymentApplicationDate desc
    </select>

</mapper>
