<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.AssociatedTaxiInvoiceMapper">
    
    <resultMap type="AssociatedTaxiInvoice" id="AssociatedTaxiInvoiceResult">
        <result property="userName"    column="user_name"    />
        <result property="userAccount"    column="user_account"    />
        <result property="createTime"    column="create_time"    />
        <result property="invoiceNo"    column="invoice_no"    />
        <result property="invoiceCode"    column="invoice_code"    />
        <result property="invoiceDate"    column="invoice_date"    />
        <result property="leaveTime"    column="leave_time"    />
        <result property="arriveTime"    column="arrive_time"    />
        <result property="amountTax"    column="amount_tax"    />
        <result property="mileage"    column="mileage"    />
        <result property="isDeduct"    column="is_deduct"    />
        <result property="deductTax"    column="deduct_tax"    />
        <result property="updateTime"    column="update_time"    />
        <result property="orgId"    column="org_id"    />
        <result property="orgName"    column="org_name"    />
        <result property="signStatus"    column="sign_status"    />
        <result property="fileAddress"    column="file_address"    />
        <result property="accounttingNo"    column="accountting_no"    />
    </resultMap>

    <sql id="selectAssociatedTaxiInvoiceVo">
        select i.user_name, i.user_account, i.create_time, i.invoice_no, i.invoice_code, i.invoice_date, i.leave_time, i.arrive_time, i.amount_tax, i.mileage, i.is_deduct, i.deduct_tax, i.update_time, i.org_id, i.org_name, i.sign_status, i.file_address, qrml.accountting_no
        from
             invoice AS i
                LEFT JOIN query_reimbursement_main_list AS qrml ON i.invoice_no = qrml.inv_num
        WHERE i.reimburse_status = 2 AND i.invoice_type = '1004'
    </sql>

    <select id="selectAssociatedTaxiInvoiceList" parameterType="AssociatedTaxiInvoice" resultMap="AssociatedTaxiInvoiceResult">
        <include refid="selectAssociatedTaxiInvoiceVo"/>
        <if test="userName != null  and userName != ''"> and i.user_name like concat('%', #{userName}, '%')</if>
        <if test="userAccount != null  and userAccount != ''"> and i.user_account = #{userAccount}</if>
        <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and i.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        <if test="invoiceNo != null  and invoiceNo != ''"> and i.invoice_no = #{invoiceNo}</if>
        <if test="invoiceCode != null  and invoiceCode != ''"> and i.invoice_code = #{invoiceCode}</if>
        <if test="invoiceDate != null  and invoiceDate != ''"> and i.invoice_date = #{invoiceDate}</if>
        <if test="leaveTime != null  and leaveTime != ''"> and i.leave_time = #{leaveTime}</if>
        <if test="arriveTime != null  and arriveTime != ''"> and i.arrive_time = #{arriveTime}</if>
        <if test="amountTax != null "> and i.amount_tax = #{amountTax}</if>
        <if test="mileage != null  and mileage != ''"> and i.mileage = #{mileage}</if>
        <if test="isDeduct != null "> and i.is_deduct = #{isDeduct}</if>
        <if test="deductTax != null "> and i.deduct_tax = #{deductTax}</if>
        <if test="updateTime != null  and updateTime != ''"> and i.update_time = #{updateTime}</if>
        <if test="orgId != null  and orgId != ''"> and i.org_id = #{orgId}</if>
        <if test="orgName != null  and orgName != ''"> and i.org_name like concat('%', #{orgName}, '%')</if>
        <if test="signStatus != null "> and i.sign_status = #{signStatus}</if>
        <if test="fileAddress != null  and fileAddress != ''"> and i.file_address = #{fileAddress}</if>
        <if test="accounttingNo != null  and accounttingNo != ''"> and qrml.accountting_no = #{accounttingNo}</if>
        order by i.create_time desc
    </select>
    
    <select id="selectAssociatedTaxiInvoiceByUserName" parameterType="String" resultMap="AssociatedTaxiInvoiceResult">
        <include refid="selectAssociatedTaxiInvoiceVo"/>
        where userName = #{userName}
    </select>
        
    <insert id="insertAssociatedTaxiInvoice" parameterType="AssociatedTaxiInvoice">
        insert into associated_taxi_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">userName,</if>
            <if test="userAccount != null">userAccount,</if>
            <if test="createTime != null">createTime,</if>
            <if test="invoiceNo != null">invoiceNo,</if>
            <if test="invoiceCode != null">invoiceCode,</if>
            <if test="invoiceDate != null">invoiceDate,</if>
            <if test="leaveTime != null">leaveTime,</if>
            <if test="arriveTime != null">arriveTime,</if>
            <if test="amountTax != null">amountTax,</if>
            <if test="mileage != null">mileage,</if>
            <if test="isDeduct != null">isDeduct,</if>
            <if test="deductTax != null">deductTax,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="orgId != null">orgId,</if>
            <if test="orgName != null">orgName,</if>
            <if test="signStatus != null">signStatus,</if>
            <if test="fileAddress != null">fileAddress,</if>
            <if test="accounttingNo != null">accounttingNo,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="userAccount != null">#{userAccount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="invoiceNo != null">#{invoiceNo},</if>
            <if test="invoiceCode != null">#{invoiceCode},</if>
            <if test="invoiceDate != null">#{invoiceDate},</if>
            <if test="leaveTime != null">#{leaveTime},</if>
            <if test="arriveTime != null">#{arriveTime},</if>
            <if test="amountTax != null">#{amountTax},</if>
            <if test="mileage != null">#{mileage},</if>
            <if test="isDeduct != null">#{isDeduct},</if>
            <if test="deductTax != null">#{deductTax},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="signStatus != null">#{signStatus},</if>
            <if test="fileAddress != null">#{fileAddress},</if>
            <if test="accounttingNo != null">#{accounttingNo},</if>
         </trim>
    </insert>

    <update id="updateAssociatedTaxiInvoice" parameterType="AssociatedTaxiInvoice">
        update associated_taxi_invoice
        <trim prefix="SET" suffixOverrides=",">
            <if test="userAccount != null">userAccount = #{userAccount},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="invoiceNo != null">invoiceNo = #{invoiceNo},</if>
            <if test="invoiceCode != null">invoiceCode = #{invoiceCode},</if>
            <if test="invoiceDate != null">invoiceDate = #{invoiceDate},</if>
            <if test="leaveTime != null">leaveTime = #{leaveTime},</if>
            <if test="arriveTime != null">arriveTime = #{arriveTime},</if>
            <if test="amountTax != null">amountTax = #{amountTax},</if>
            <if test="mileage != null">mileage = #{mileage},</if>
            <if test="isDeduct != null">isDeduct = #{isDeduct},</if>
            <if test="deductTax != null">deductTax = #{deductTax},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="orgId != null">orgId = #{orgId},</if>
            <if test="orgName != null">orgName = #{orgName},</if>
            <if test="signStatus != null">signStatus = #{signStatus},</if>
            <if test="fileAddress != null">fileAddress = #{fileAddress},</if>
            <if test="accounttingNo != null">accounttingNo = #{accounttingNo},</if>
        </trim>
        where userName = #{userName}
    </update>

    <delete id="deleteAssociatedTaxiInvoiceByUserName" parameterType="String">
        delete from associated_taxi_invoice where userName = #{userName}
    </delete>

    <delete id="deleteAssociatedTaxiInvoiceByUserNames" parameterType="String">
        delete from associated_taxi_invoice where userName in 
        <foreach item="userName" collection="array" open="(" separator="," close=")">
            #{userName}
        </foreach>
    </delete>
</mapper>