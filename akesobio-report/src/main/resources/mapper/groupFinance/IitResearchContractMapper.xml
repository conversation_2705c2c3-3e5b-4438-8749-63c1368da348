<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.IitResearchContractMapper">
    
    <resultMap type="IitResearchContract" id="IitResearchContractResult">
        <result property="creationDate"    column="creationDate"    />
        <result property="singleNumber"    column="singleNumber"    />
        <result property="applicant"    column="applicant"    />
        <result property="department"    column="department"    />
        <result property="contractNo"    column="contractNo"    />
        <result property="applicationSubject"    column="applicationSubject"    />
        <result property="expences"    column="expences"    />
        <result property="cooperateUnit"    column="cooperateUnit"    />
        <result property="centerNo"    column="centerNo"    />
        <result property="centerName"    column="centerName"    />
        <result property="transactionContent"    column="transactionContent"    />
        <result property="feeDescription"    column="feeDescription"    />
        <result property="contractLevel"    column="contractLevel"    />
        <result property="businessType"    column="businessType"    />
        <result property="contractType"    column="contractType"    />
        <result property="outsourcingSubdivision"    column="outsourcingSubdivision"    />
        <result property="projectType"    column="projectType"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="studyMedicationName"    column="studyMedicationName"    />
        <result property="studyMedicationQuantity"    column="studyMedicationQuantity"    />
        <result property="casesNumber"    column="casesNumber"    />
        <result property="casePrice"    column="casePrice"    />
        <result property="invoiceType"    column="invoiceType"    />
        <result property="taxRate"    column="taxRate"    />
        <result property="transactionAmount"    column="transactionAmount"    />
        <result property="paymentMilestones"    column="paymentMilestones"    />
        <result property="milestoneAmount"    column="milestoneAmount"    />
        <result property="paymentProportion"    column="paymentProportion"    />
        <result property="regionalManager"    column="regionalManager"    />
        <result property="salesAreaDirector"    column="salesAreaDirector"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
    </resultMap>

    <sql id="selectIitResearchContractVo">
        select creationDate, singleNumber, applicant, department, contractNo, applicationSubject, expences, cooperateUnit, centerNo, centerName, transactionContent, feeDescription, contractLevel, businessType, contractType, outsourcingSubdivision, projectType, projectNumber, studyMedicationName, studyMedicationQuantity, casesNumber, casePrice, invoiceType, taxRate, transactionAmount, paymentMilestones, milestoneAmount, paymentProportion, regionalManager, salesAreaDirector, documentStatus, currentSession from IIT_research_contract
    </sql>

    <select id="selectIitResearchContractList" parameterType="IitResearchContract" resultMap="IitResearchContractResult">
        <include refid="selectIitResearchContractVo"/>
        <where>  
            <if test="params.beginCreationDate != null and params.beginCreationDate != '' and params.endCreationDate != null and params.endCreationDate != ''"> and creationDate between #{params.beginCreationDate} and #{params.endCreationDate}</if>
            <if test="singleNumber != null  and singleNumber != ''"> and singleNumber like concat('%', #{singleNumber}, '%')</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="contractNo != null  and contractNo != ''"> and contractNo like concat('%', #{contractNo}, '%')</if>
            <if test="applicationSubject != null  and applicationSubject != ''"> and applicationSubject like concat('%', #{applicationSubject}, '%')</if>
            <if test="expences != null  and expences != ''"> and expences like concat('%', #{expences}, '%')</if>
            <if test="cooperateUnit != null  and cooperateUnit != ''"> and cooperateUnit like concat('%', #{cooperateUnit}, '%')</if>
            <if test="centerNo != null  and centerNo != ''"> and centerNo like concat('%', #{centerNo}, '%')</if>
            <if test="centerName != null  and centerName != ''"> and centerName like concat('%', #{centerName}, '%')</if>
            <if test="transactionContent != null  and transactionContent != ''"> and transactionContent like concat('%', #{transactionContent}, '%')</if>
            <if test="feeDescription != null  and feeDescription != ''"> and feeDescription like concat('%', #{feeDescription}, '%')</if>
            <if test="contractLevel != null  and contractLevel != ''"> and contractLevel like concat('%', #{contractLevel}, '%')</if>
            <if test="businessType != null  and businessType != ''"> and businessType like concat('%', #{businessType}, '%')</if>
            <if test="contractType != null  and contractType != ''"> and contractType like concat('%', #{contractType}, '%')</if>
            <if test="outsourcingSubdivision != null  and outsourcingSubdivision != ''"> and outsourcingSubdivision like concat('%', #{outsourcingSubdivision}, '%')</if>
            <if test="projectType != null  and projectType != ''"> and projectType like concat('%', #{projectType}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="studyMedicationName != null  and studyMedicationName != ''"> and studyMedicationName like concat('%', #{studyMedicationName}, '%')</if>
            <if test="studyMedicationQuantity != null  and studyMedicationQuantity != ''"> and studyMedicationQuantity = #{studyMedicationQuantity}</if>
            <if test="casesNumber != null  and casesNumber != ''"> and casesNumber = #{casesNumber}</if>
            <if test="casePrice != null  and casePrice != ''"> and casePrice = #{casePrice}</if>
            <if test="invoiceType != null  and invoiceType != ''"> and invoiceType like concat('%', #{invoiceType}, '%')</if>
            <if test="taxRate != null  and taxRate != ''"> and taxRate = #{taxRate}</if>
            <if test="transactionAmount != null  and transactionAmount != ''"> and transactionAmount = #{transactionAmount}</if>
            <if test="paymentMilestones != null  and paymentMilestones != ''"> and paymentMilestones = #{paymentMilestones}</if>
            <if test="milestoneAmount != null  and milestoneAmount != ''"> and milestoneAmount = #{milestoneAmount}</if>
            <if test="paymentProportion != null  and paymentProportion != ''"> and paymentProportion = #{paymentProportion}</if>
            <if test="regionalManager != null  and regionalManager != ''"> and regionalManager = #{regionalManager}</if>
            <if test="salesAreaDirector != null  and salesAreaDirector != ''"> and salesAreaDirector = #{salesAreaDirector}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
        </where>
        order by creationDate desc
    </select>

</mapper>