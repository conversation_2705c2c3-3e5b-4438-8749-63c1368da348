<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.DataTableMapper">
    <!-- 实体类  -->
    <resultMap type="DataTable" id="DataTableResult">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectDataTableList">
        SELECT
            id,
            data_name,
            table_name,
            flag
        FROM data_table
    </sql>
    
    <!-- 方法 -->
    <select id="queryDataTableList" parameterType="DataTable" resultMap="DataTableResult">
        <include refid="selectDataTableList"/>
        <where>  
            <if test="dataName != null and dataName != ''"> and data_name like concat('%', #{dataName}, '%')</if>
            <if test="tableName != null and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
            <if test="flag != null and flag != ''"> and flag = #{flag}</if>
        </where>
    </select>

    <select id="queryDataTableDISTINCT" resultType="String">
        SELECT
            DISTINCT
            table_name
        FROM data_table
    </select>
    <select id="queryDataTableByDataName" parameterType="String" resultType="String">
        SELECT
            table_name
        FROM data_table
        WHERE data_name = #{dataName}
    </select>
</mapper>