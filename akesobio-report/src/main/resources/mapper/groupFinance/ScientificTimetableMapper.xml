<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ScientificTimetableMapper">
    
    <resultMap type="ScientificTimetable" id="ScientificTimetableResult">
        <result property="oddNumbers"    column="oddNumbers"    />
        <result property="applicationDate"    column="applicationDate"    />
        <result property="companyCode"    column="companyCode"    />
        <result property="applicant"    column="applicant"    />
        <result property="jobNumber"    column="jobNumber"    />
        <result property="teamLeader"    column="teamLeader"    />
        <result property="costCenter"    column="costCenter"    />
        <result property="costCenterName"    column="costCenterName"    />
        <result property="remarks"    column="remarks"    />
        <result property="workDate"    column="workDate"    />
        <result property="costCenterDetail"    column="costCenterDetail"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="scientificTime"    column="scientificTime"    />
        <result property="documentStatus"    column="documentStatus"    />
    </resultMap>

    <sql id="selectScientificTimetableVo">
        select oddNumbers, applicationDate, companyCode, applicant, jobNumber, teamLeader, costCenter, costCenterName, remarks, workDate, costCenterDetail, projectNumber, scientificTime, documentStatus from scientific_timetable
    </sql>

    <select id="selectScientificTimetableList" parameterType="ScientificTimetable" resultMap="ScientificTimetableResult">
        <include refid="selectScientificTimetableVo"/>
        <where>  
            <if test="oddNumbers != null  and oddNumbers != ''"> and oddNumbers like concat('%', #{oddNumbers}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="companyCode != null  and companyCode != ''"> and companyCode like concat('%', #{companyCode}, '%')</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="jobNumber != null  and jobNumber != ''"> and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="teamLeader != null  and teamLeader != ''"> and teamLeader like concat('%', #{teamLeader}, '%')</if>
            <if test="costCenter != null  and costCenter != ''"> and costCenter like concat('%', #{costCenter}, '%')</if>
            <if test="costCenterName != null  and costCenterName != ''"> and costCenterName like concat('%', #{costCenterName}, '%')</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="params.beginWorkDate != null and params.beginWorkDate != '' and params.endWorkDate != null and params.endWorkDate != ''"> and workDate between #{params.beginWorkDate} and #{params.endWorkDate}</if>
            <if test="costCenterDetail != null  and costCenterDetail != ''"> and costCenterDetail like concat('%', #{costCenterDetail}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="scientificTime != null "> and scientificTime = #{scientificTime}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
        </where>
    </select>
</mapper>