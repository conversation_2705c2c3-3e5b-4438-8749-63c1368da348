<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ExistingInventoryMapper">
    <resultMap type="com.akesobio.report.groupFinance.domain.ExistingInventory" id="ExistingInventoryResult">
        <result property="factory" column="factory"/>
        <result property="materialNo" column="materialNo"/>
        <result property="materialDescription" column="materialDescription"/>
        <result property="expiredQuantity" column="expiredQuantity"/>
        <result property="expiredAmount" column="expiredAmount"/>
        <result property="inventoryDate" column="inventoryDate"/>
    </resultMap>
    <select id="selectExistingInventoryList" resultMap="ExistingInventoryResult">
        select factory,materialNo,materialDescription,expiredQuantity,expiredAmount,inventoryDate
        from (
        select
        a.werks 'factory',
        a.matnr 'materialNo',
        a.maktx 'materialDescription',
        a.exp_qty 'expiredQuantity',
        a.exp_amount 'expiredAmount',
        a.zdate 'inventoryDate'
        from ExistingStocksReport a) b
        <where>
            <if test="factory != null and factory !=''">and factory = #{factory}</if>
            <if test="materialNo != null and materialNo !=''">and materialNo like concat('%',#{materialNo},'%')</if>
            <if test="materialDescription != null and materialDescription !=''">and materialDescription like concat('%',#{materialDescription},'%')</if>
            <if test="inventoryDate != null and inventoryDate !=''">and inventoryDate = #{inventoryDate}</if>
        </where>
    </select>
</mapper>