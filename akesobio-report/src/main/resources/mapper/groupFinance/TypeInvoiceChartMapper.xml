<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.TypeInvoiceChartMapper">

    <resultMap type="com.akesobio.report.groupFinance.vo.TypeInvoiceChartVo" id="typeInvoiceChartVo">
        <result property="invoiceType" column="invoice_type"/>
        <result property="count" column="typeCount"/>
    </resultMap>

    <select id="selectInvoiceTypes" resultType="string">
        select DISTINCT invoice_type FROM invoice
    </select>

    <select id="selectInvoiceCountByInvoiceType" resultMap="typeInvoiceChartVo">
    SELECT  invoice_type,COUNT(*) AS typeCount FROM invoice
    WHERE
        reimburse_status = 2 GROUP BY invoice_type
    </select>

</mapper>