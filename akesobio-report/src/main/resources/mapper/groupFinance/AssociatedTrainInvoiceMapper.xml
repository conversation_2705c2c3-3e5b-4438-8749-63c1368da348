<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.AssociatedTrainInvoiceMapper">
    
    <resultMap type="AssociatedTrainInvoice" id="AssociatedTrainInvoiceResult">
        <result property="userName"    column="user_name"    />
        <result property="userAccount"    column="user_account"    />
        <result property="createTime"    column="create_time"    />
        <result property="invoiceNo"    column="invoice_no"    />
        <result property="invoiceDate"    column="invoice_date"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="deductTax"    column="deduct_tax"    />
        <result property="amountTax"    column="amount_tax"    />
        <result property="drawer"    column="drawer"    />
        <result property="trainNumber"    column="train_number"    />
        <result property="trainSeat"    column="train_seat"    />
        <result property="leaveCity"    column="leave_city"    />
        <result property="arriveCity"    column="arrive_city"    />
        <result property="isDeduct"    column="is_deduct"    />
        <result property="updateTime"    column="update_time"    />
        <result property="orgId"    column="org_id"    />
        <result property="orgName"    column="org_name"    />
        <result property="signStatus"    column="sign_status"    />
        <result property="fileAddress"    column="file_address"    />
        <result property="accounttingNo"    column="accountting_no"    />
    </resultMap>

    <sql id="selectAssociatedTrainInvoiceVo">
        select i.user_name, i.user_account, i.create_time, i.invoice_no, i.invoice_date, i.invoice_type, i.deduct_tax, i.amount_tax, i.drawer, i.train_number, i.train_seat, i.leave_city, i.arrive_city, i.is_deduct, i.update_time, i.org_id, i.org_name, i.sign_status, i.file_address, qrml.accountting_no
        from
            invoice AS i
                LEFT JOIN query_reimbursement_main_list AS qrml ON i.invoice_no = qrml.inv_num
        WHERE i.reimburse_status = 2 AND (i.invoice_type = '1002' OR i.invoice_type = '1010')
    </sql>

    <select id="selectAssociatedTrainInvoiceList" parameterType="AssociatedTrainInvoice" resultMap="AssociatedTrainInvoiceResult">
        <include refid="selectAssociatedTrainInvoiceVo"/>
        <if test="userName != null  and userName != ''"> and i.user_name like concat('%', #{userName}, '%')</if>
        <if test="userAccount != null  and userAccount != ''"> and i.user_account = #{userAccount}</if>
        <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and i.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        <if test="invoiceNo != null  and invoiceNo != ''"> and i.invoice_no = #{invoiceNo}</if>
        <if test="invoiceDate != null  and invoiceDate != ''"> and i.invoice_date = #{invoiceDate}</if>
        <if test="invoiceType != null  and invoiceType != ''"> and i.invoice_type = #{invoiceType}</if>
        <if test="deductTax != null "> and i.deduct_tax = #{deductTax}</if>
        <if test="amountTax != null "> and i.amount_tax = #{amountTax}</if>
        <if test="drawer != null  and drawer != ''"> and i.drawer = #{drawer}</if>
        <if test="trainNumber != null  and trainNumber != ''"> and i.train_number = #{trainNumber}</if>
        <if test="trainSeat != null  and trainSeat != ''"> and i.train_seat = #{trainSeat}</if>
        <if test="leaveCity != null  and leaveCity != ''"> and i.leave_city = #{leaveCity}</if>
        <if test="arriveCity != null  and arriveCity != ''"> and i.arrive_city = #{arriveCity}</if>
        <if test="isDeduct != null "> and i.is_deduct = #{isDeduct}</if>
        <if test="updateTime != null  and updateTime != ''"> and i.update_time = #{updateTime}</if>
        <if test="orgId != null  and orgId != ''"> and i.org_id = #{orgId}</if>
        <if test="orgName != null  and orgName != ''"> and i.org_name like concat('%', #{orgName}, '%')</if>
        <if test="signStatus != null "> and i.sign_status = #{signStatus}</if>
        <if test="fileAddress != null  and fileAddress != ''"> and i.file_address = #{fileAddress}</if>
        <if test="accounttingNo != null  and accounttingNo != ''"> and qrml.accountting_no = #{accounttingNo}</if>
        order by i.create_time desc
    </select>
    
    <select id="selectAssociatedTrainInvoiceByUserName" parameterType="String" resultMap="AssociatedTrainInvoiceResult">
        <include refid="selectAssociatedTrainInvoiceVo"/>
        where userName = #{userName}
    </select>
        
    <insert id="insertAssociatedTrainInvoice" parameterType="AssociatedTrainInvoice">
        insert into associated_train_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">userName,</if>
            <if test="userAccount != null">userAccount,</if>
            <if test="createTime != null">createTime,</if>
            <if test="invoiceNo != null">invoiceNo,</if>
            <if test="invoiceDate != null">invoiceDate,</if>
            <if test="invoiceType != null">invoiceType,</if>
            <if test="deductTax != null">deductTax,</if>
            <if test="amountTax != null">amountTax,</if>
            <if test="drawer != null">drawer,</if>
            <if test="trainNumber != null">trainNumber,</if>
            <if test="trainSeat != null">trainSeat,</if>
            <if test="leaveCity != null">leaveCity,</if>
            <if test="arriveCity != null">arriveCity,</if>
            <if test="isDeduct != null">isDeduct,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="orgId != null">orgId,</if>
            <if test="orgName != null">orgName,</if>
            <if test="signStatus != null">signStatus,</if>
            <if test="fileAddress != null">fileAddress,</if>
            <if test="accounttingNo != null">accounttingNo,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="userAccount != null">#{userAccount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="invoiceNo != null">#{invoiceNo},</if>
            <if test="invoiceDate != null">#{invoiceDate},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="deductTax != null">#{deductTax},</if>
            <if test="amountTax != null">#{amountTax},</if>
            <if test="drawer != null">#{drawer},</if>
            <if test="trainNumber != null">#{trainNumber},</if>
            <if test="trainSeat != null">#{trainSeat},</if>
            <if test="leaveCity != null">#{leaveCity},</if>
            <if test="arriveCity != null">#{arriveCity},</if>
            <if test="isDeduct != null">#{isDeduct},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="signStatus != null">#{signStatus},</if>
            <if test="fileAddress != null">#{fileAddress},</if>
            <if test="accounttingNo != null">#{accounttingNo},</if>
         </trim>
    </insert>

    <update id="updateAssociatedTrainInvoice" parameterType="AssociatedTrainInvoice">
        update associated_train_invoice
        <trim prefix="SET" suffixOverrides=",">
            <if test="userAccount != null">userAccount = #{userAccount},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="invoiceNo != null">invoiceNo = #{invoiceNo},</if>
            <if test="invoiceDate != null">invoiceDate = #{invoiceDate},</if>
            <if test="invoiceType != null">invoiceType = #{invoiceType},</if>
            <if test="deductTax != null">deductTax = #{deductTax},</if>
            <if test="amountTax != null">amountTax = #{amountTax},</if>
            <if test="drawer != null">drawer = #{drawer},</if>
            <if test="trainNumber != null">trainNumber = #{trainNumber},</if>
            <if test="trainSeat != null">trainSeat = #{trainSeat},</if>
            <if test="leaveCity != null">leaveCity = #{leaveCity},</if>
            <if test="arriveCity != null">arriveCity = #{arriveCity},</if>
            <if test="isDeduct != null">isDeduct = #{isDeduct},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="orgId != null">orgId = #{orgId},</if>
            <if test="orgName != null">orgName = #{orgName},</if>
            <if test="signStatus != null">signStatus = #{signStatus},</if>
            <if test="fileAddress != null">fileAddress = #{fileAddress},</if>
            <if test="accounttingNo != null">accounttingNo = #{accounttingNo},</if>
        </trim>
        where userName = #{userName}
    </update>

    <delete id="deleteAssociatedTrainInvoiceByUserName" parameterType="String">
        delete from associated_train_invoice where userName = #{userName}
    </delete>

    <delete id="deleteAssociatedTrainInvoiceByUserNames" parameterType="String">
        delete from associated_train_invoice where userName in 
        <foreach item="userName" collection="array" open="(" separator="," close=")">
            #{userName}
        </foreach>
    </delete>
</mapper>