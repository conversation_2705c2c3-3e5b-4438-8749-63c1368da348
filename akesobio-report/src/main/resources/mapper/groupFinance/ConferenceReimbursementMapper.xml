<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ConferenceReimbursementMapper">
    
    <resultMap type="ConferenceReimbursement" id="ConferenceReimbursementResult">
        <result property="oANumber"    column="oANumber"    />
        <result property="createdDate"    column="createdDate"    />
        <result property="reimbursementCategory"    column="reimbursementCategory"    />
        <result property="companyCode"    column="companyCode"    />
        <result property="companyName"    column="companyName"    />
        <result property="reimbursementPerson"    column="reimbursementPerson"    />
        <result property="reimbursementPersonNumber"    column="reimbursementPersonNumber"    />
        <result property="department"    column="department"    />
        <result property="relatedMeetingApplication"    column="relatedMeetingApplication"    />
        <result property="costCenterCode"    column="costCenterCode"    />
        <result property="costCenterName"    column="costCenterName"    />
        <result property="businessOccurrenceDate"    column="businessOccurrenceDate"    />
        <result property="businessDescription"    column="businessDescription"    />
        <result property="startDate"    column="startDate"    />
        <result property="endDate"    column="endDate"    />
        <result property="meetingObjective"    column="meetingObjective"    />
        <result property="meetingType"    column="meetingType"    />
        <result property="meetingCategory"    column="meetingCategory"    />
        <result property="meetingFormat"    column="meetingFormat"    />
        <result property="companyRole"    column="companyRole"    />
        <result property="objectOriented"    column="objectOriented"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="coverNumberPeople"    column="coverNumberPeople"    />
        <result property="signNumber"    column="signNumber"    />
        <result property="expertsNumber"    column="expertsNumber"    />
        <result property="invitation"    column="invitation"    />
        <result property="schedule"    column="schedule"    />
        <result property="attendanceSheet"    column="attendanceSheet"    />
        <result property="photo"    column="photo"    />
        <result property="paymentRecipient"    column="paymentRecipient"    />
        <result property="accountCode"    column="accountCode"    />
        <result property="expenseAccountName"    column="expenseAccountName"    />
        <result property="invoiceNo"    column="invoiceNo"    />
        <result property="amountWithoutTax"    column="amountWithoutTax"    />
        <result property="specialTax"    column="specialTax"    />
        <result property="reimbursementAmount"    column="reimbursementAmount"    />
        <result property="currency"    column="currency"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="centerName"    column="centerName"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="sapCredentialPushDate"    column="sapCredentialPushDate"    />
        <result property="sapPayNumber"    column="sapPayNumber"    />
        <result property="sapDocumentStatus"    column="sapDocumentStatus"    />
    </resultMap>

    <sql id="selectConferenceReimbursementVo">
        select oANumber, createdDate, reimbursementCategory, companyCode, companyName, reimbursementPerson, reimbursementPersonNumber, department, relatedMeetingApplication, costCenterCode, costCenterName, businessOccurrenceDate, businessDescription, startDate, endDate, meetingObjective, meetingType, meetingCategory, meetingFormat, companyRole, objectOriented, province, city, coverNumberPeople, signNumber, expertsNumber, invitation, schedule, attendanceSheet, photo, paymentRecipient, accountCode, expenseAccountName, invoiceNo, amountWithoutTax, specialTax, reimbursementAmount, currency, projectNumber, centerName, documentStatus, currentSession, sapCredentialPushDate, sapPayNumber, sapDocumentStatus from conference_reimbursement
    </sql>

    <select id="selectConferenceReimbursementList" parameterType="ConferenceReimbursement" resultMap="ConferenceReimbursementResult">
        <include refid="selectConferenceReimbursementVo"/>
        <where>  
            <if test="oANumber != null  and oANumber != ''"> and oANumber like concat('%', #{oANumber}, '%')</if>
            <if test="params.beginCreatedDate != null and params.beginCreatedDate != '' and params.endCreatedDate != null and params.endCreatedDate != ''"> and createdDate between #{params.beginCreatedDate} and #{params.endCreatedDate}</if>
            <if test="reimbursementCategory != null  and reimbursementCategory != ''"> and reimbursementCategory like concat('%', #{reimbursementCategory}, '%')</if>
            <if test="companyCode != null  and companyCode != ''"> and companyCode = #{companyCode}</if>
            <if test="companyName != null  and companyName != ''"> and companyName like concat('%', #{companyName}, '%')</if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''"> and reimbursementPerson like concat('%', #{reimbursementPerson}, '%')</if>
            <if test="reimbursementPersonNumber != null  and reimbursementPersonNumber != ''"> and reimbursementPersonNumber like concat('%', #{reimbursementPersonNumber}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="relatedMeetingApplication != null  and relatedMeetingApplication != ''"> and relatedMeetingApplication = #{relatedMeetingApplication}</if>
            <if test="costCenterCode != null  and costCenterCode != ''"> and costCenterCode = #{costCenterCode}</if>
            <if test="costCenterName != null  and costCenterName != ''"> and costCenterName like concat('%', #{costCenterName}, '%')</if>
            <if test="businessOccurrenceDate != null  and businessOccurrenceDate != ''"> and businessOccurrenceDate = #{businessOccurrenceDate}</if>
            <if test="businessDescription != null  and businessDescription != ''"> and businessDescription = #{businessDescription}</if>
            <if test="startDate != null "> and startDate = #{startDate}</if>
            <if test="endDate != null "> and endDate = #{endDate}</if>
            <if test="meetingObjective != null  and meetingObjective != ''"> and meetingObjective = #{meetingObjective}</if>
            <if test="meetingType != null  and meetingType != ''"> and meetingType = #{meetingType}</if>
            <if test="meetingCategory != null  and meetingCategory != ''"> and meetingCategory = #{meetingCategory}</if>
            <if test="meetingFormat != null  and meetingFormat != ''"> and meetingFormat = #{meetingFormat}</if>
            <if test="companyRole != null  and companyRole != ''"> and companyRole = #{companyRole}</if>
            <if test="objectOriented != null  and objectOriented != ''"> and objectOriented = #{objectOriented}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="coverNumberPeople != null  and coverNumberPeople != ''"> and coverNumberPeople = #{coverNumberPeople}</if>
            <if test="signNumber != null  and signNumber != ''"> and signNumber = #{signNumber}</if>
            <if test="expertsNumber != null  and expertsNumber != ''"> and expertsNumber = #{expertsNumber}</if>
            <if test="invitation != null  and invitation != ''"> and invitation = #{invitation}</if>
            <if test="schedule != null  and schedule != ''"> and schedule = #{schedule}</if>
            <if test="attendanceSheet != null  and attendanceSheet != ''"> and attendanceSheet = #{attendanceSheet}</if>
            <if test="photo != null  and photo != ''"> and photo = #{photo}</if>
            <if test="paymentRecipient != null  and paymentRecipient != ''"> and paymentRecipient = #{paymentRecipient}</if>
            <if test="accountCode != null  and accountCode != ''"> and accountCode = #{accountCode}</if>
            <if test="expenseAccountName != null  and expenseAccountName != ''"> and expenseAccountName like concat('%', #{expenseAccountName}, '%')</if>
            <if test="invoiceNo != null  and invoiceNo != ''"> and invoiceNo = #{invoiceNo}</if>
            <if test="amountWithoutTax != null  and amountWithoutTax != ''"> and amountWithoutTax = #{amountWithoutTax}</if>
            <if test="specialTax != null  and specialTax != ''"> and specialTax = #{specialTax}</if>
            <if test="reimbursementAmount != null  and reimbursementAmount != ''"> and reimbursementAmount = #{reimbursementAmount}</if>
            <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="centerName != null  and centerName != ''"> and centerName like concat('%', #{centerName}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="sapCredentialPushDate != null  and sapCredentialPushDate != ''"> and sapCredentialPushDate = #{sapCredentialPushDate}</if>
            <if test="sapPayNumber != null  and sapPayNumber != ''"> and sapPayNumber like concat('%', #{sapPayNumber}, '%')</if>
            <if test="sapDocumentStatus != null  and sapDocumentStatus != ''"> and sapDocumentStatus like concat('%', #{sapDocumentStatus}, '%')</if>
        </where>
        order by createdDate desc
    </select>
</mapper>