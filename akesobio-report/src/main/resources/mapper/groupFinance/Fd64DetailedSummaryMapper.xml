<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.Fd64DetailedSummaryMapper">

    <resultMap type="Fd64DetailedSummary" id="Fd64DetailedSummaryResult">
        <result property="oddNumbers" column="oddNumbers"/>
        <result property="applicationDate" column="applicationDate"/>
        <result property="applicant" column="applicant"/>
        <result property="belongingCompany" column="belongingCompany"/>
        <result property="budgetMonth" column="budgetMonth"/>
        <result property="budgetProject" column="budgetProject"/>
        <result property="supplier" column="supplier"/>
        <result property="contractOrder" column="contractOrder"/>
        <result property="projectCategory" column="projectCategory"/>
        <result property="paymentProgress" column="paymentProgress"/>
        <result property="currency" column="currency"/>
        <result property="upper" column="upper"/>
        <result property="centre" column="centre"/>
        <result property="below" column="below"/>
        <result property="subtotal" column="subtotal"/>
        <result property="remarks" column="remarks"/>
        <result property="currentSession" column="currentSession"/>
        <result property="department" column="department"/>
    </resultMap>

    <sql id="selectFd64DetailedSummaryVo">
        select oddNumbers,
               applicationDate,
               applicant,
               belongingCompany,
               budgetMonth,
               supplier,
               contractOrder,
               projectCategory,
               paymentProgress,
               currency,
               upper,
               centre,
               below,
               subtotal,
               remarks,
               currentSession,
               budgetProject,
               department
        from fd64_detailed_summary
    </sql>

    <select id="selectFd64DetailedSummaryList" parameterType="Fd64DetailedSummary"
            resultMap="Fd64DetailedSummaryResult">
        <include refid="selectFd64DetailedSummaryVo"/>
        <where>
            <if test="oddNumbers != null  and oddNumbers != ''">and oddNumbers like concat('%', #{oddNumbers}, '%')</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''">
                and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="belongingCompany != null  and belongingCompany != ''">and belongingCompany like concat('%',
                #{belongingCompany}, '%')
            </if>
            <if test="budgetMonth != null  and budgetMonth != ''">and budgetMonth like concat('%', #{budgetMonth},
                '%')
            </if>
            <if test="supplier != null  and supplier != ''">and supplier like concat('%', #{supplier}, '%')</if>
            <if test="contractOrder != null  and contractOrder != ''">and contractOrder like concat('%',
                #{contractOrder}, '%')
            </if>
            <if test="projectCategory != null  and projectCategory != ''">and projectCategory like concat('%',
                #{projectCategory}, '%')
            </if>
            <if test="paymentProgress != null  and paymentProgress != ''">and paymentProgress like concat('%',
                #{paymentProgress}, '%')
            </if>
            <if test="currency != null  and currency != ''">and currency like concat('%', #{currency}, '%')</if>
            <if test="upper != null ">and upper = #{upper}</if>
            <if test="centre != null ">and centre = #{centre}</if>
            <if test="below != null ">and below = #{below}</if>
            <if test="subtotal != null ">and subtotal = #{subtotal}</if>
            <if test="remarks != null  and remarks != ''">and remarks like concat('%', #{remarks}, '%')</if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="budgetProject != null  and budgetProject != ''">and budgetProject like concat('%',
                #{budgetProject}, '%')
            </if>
            <if test="department != null  and department != ''">and department like concat('%',
                #{department}, '%')
            </if>
        </where>
    </select>
</mapper>