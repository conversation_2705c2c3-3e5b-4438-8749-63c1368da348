<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ClinicalTotalExpensesMapper">

    <resultMap type="ClinicalTotalExpenses" id="ClinicalTotalExpensesResult">
        <result property="OANumber"    column="OANumber"    />
        <result property="contractNo"    column="contractNo"    />
        <result property="paymentApplicationDate"    column="paymentApplicationDate"    />
        <result property="applicant"    column="applicant"    />
        <result property="department"    column="department"    />
        <result property="costBearingCompany"    column="costBearingCompany"    />
        <result property="projectNumber"    column="projectNumber"    />
        <result property="feeType"    column="feeType"    />
        <result property="paymentDescription"    column="paymentDescription"    />
        <result property="suject"    column="suject"    />
        <result property="settlementPeriod"    column="settlementPeriod"    />
        <result property="casesNumber"    column="casesNumber"    />
        <result property="amount"    column="amount"    />
        <result property="currency"    column="currency"    />
        <result property="exchangeRate"    column="exchangeRate"    />
        <result property="receivingUnit"    column="receivingUnit"    />
        <result property="contractType"    column="contractType"    />
        <result property="costCenterCode"    column="costCenterCode"    />
        <result property="costCenter"    column="costCenter"    />
        <result property="accountCode"    column="accountCode"    />
        <result property="amountWithoutTax"    column="amountWithoutTax"    />
        <result property="specialTax"    column="specialTax"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="sapDocumentStatus"    column="sapDocumentStatus"    />
        <result property="sapCredentialPushDate"    column="sapCredentialPushDate"    />
        <result property="applicationSubject"    column="applicationSubject"    />
        <result property="sapPayNumber"    column="sapPayNumber"    />
        <result property="invoiceType"    column="invoiceType"    />
        <result property="invoiceNumber"    column="invoiceNumber"    />
        <result property="invoice"    column="invoice"    />
    </resultMap>

    <sql id="selectClinicalTotalExpensesVo">
        select OANumber, contractNo, paymentApplicationDate, applicant, department, costBearingCompany, projectNumber, feeType, paymentDescription, suject, settlementPeriod, casesNumber, amount, currency, exchangeRate, receivingUnit, contractType, costCenterCode, costCenter, accountCode, amountWithoutTax, specialTax, currentSession, documentStatus, sapDocumentStatus, sapCredentialPushDate, applicationSubject, sapPayNumber, invoiceType,invoiceNumber,invoice from clinical_spending
    </sql>

    <select id="selectClinicalTotalExpensesList" parameterType="ClinicalTotalExpenses" resultMap="ClinicalTotalExpensesResult">
        <include refid="selectClinicalTotalExpensesVo"/>
        <where>
            <if test="OANumber != null  and OANumber != ''"> and OANumber like concat('%', #{OANumber}, '%')</if>
            <if test="contractNo != null  and contractNo != ''"> and contractNo like concat('%', #{contractNo}, '%')</if>
            <if test="params.beginPaymentApplicationDate != null and params.beginPaymentApplicationDate != '' and params.endPaymentApplicationDate != null and params.endPaymentApplicationDate != ''"> and paymentApplicationDate between #{params.beginPaymentApplicationDate} and #{params.endPaymentApplicationDate}</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="costBearingCompany != null  and costBearingCompany != ''"> and costBearingCompany like concat('%', #{costBearingCompany}, '%')</if>
            <if test="projectNumber != null  and projectNumber != ''"> and projectNumber like concat('%', #{projectNumber}, '%')</if>
            <if test="feeType != null  and feeType != ''"> and feeType like concat('%', #{feeType}, '%')</if>
            <if test="paymentDescription != null  and paymentDescription != ''"> and paymentDescription like concat('%', #{paymentDescription}, '%')</if>
            <if test="suject != null  and suject != ''"> and suject like concat('%', #{suject}, '%')</if>
            <if test="settlementPeriod != null  and settlementPeriod != ''"> and settlementPeriod like concat('%', #{settlementPeriod}, '%')</if>
            <if test="casesNumber != null  and casesNumber != ''"> and casesNumber = #{casesNumber}</if>
            <if test="amount != null  and amount != ''"> and amount = #{amount}</if>
            <if test="currency != null  and currency != ''"> and currency like concat('%', #{currency}, '%')</if>
            <if test="exchangeRate != null  and exchangeRate != ''"> and exchangeRate = #{exchangeRate}</if>
            <if test="receivingUnit != null  and receivingUnit != ''"> and receivingUnit like concat('%', #{receivingUnit}, '%')</if>
            <if test="contractType != null  and contractType != ''"> and contractType like concat('%', #{contractType}, '%')</if>
            <if test="costCenterCode != null  and costCenterCode != ''"> and costCenterCode like concat('%', #{costCenterCode}, '%')</if>
            <if test="costCenter != null  and costCenter != ''"> and costCenter like concat('%', #{costCenter}, '%')</if>
            <if test="accountCode != null  and accountCode != ''"> and accountCode like concat('%', #{accountCode}, '%')</if>
            <if test="amountWithoutTax != null  and amountWithoutTax != ''"> and amountWithoutTax = #{amountWithoutTax}</if>
            <if test="specialTax != null  and specialTax != ''"> and specialTax = #{specialTax}</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="sapDocumentStatus != null  and sapDocumentStatus != ''"> and sapDocumentStatus like concat('%', #{sapDocumentStatus}, '%')</if>
            <if test="sapCredentialPushDate != null  and sapCredentialPushDate != ''"> and sapCredentialPushDate like concat('%', #{sapCredentialPushDate}, '%')</if>
            <if test="applicationSubject != null  and applicationSubject != ''"> and applicationSubject like concat('%', #{applicationSubject}, '%')</if>
            <if test="sapPayNumber != null  and sapPayNumber != ''"> and sapPayNumber like concat('%', #{sapPayNumber}, '%')</if>
        </where>
        order by paymentApplicationDate desc
    </select>

    <select id="selectClinicalTotalExpensesCart"  parameterType="ClinicalTotalExpenses" resultMap="ClinicalTotalExpensesResult" >
        SELECT costBearingCompany, SUM(amount) AS total
        FROM clinical_spending_copy1
        WHERE YEAR(paymentApplicationDate) = #{year} and  costBearingCompany is not null
        GROUP BY costBearingCompany
    </select>

</mapper>
