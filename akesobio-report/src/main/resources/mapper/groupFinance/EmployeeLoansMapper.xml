<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.EmployeeLoansMapper">
    <resultMap type="com.akesobio.report.groupFinance.domain.EmployeeLoans" id="EmployeeLoansResult">
        <result property="companyCode" column="companyCode"/>
        <result property="department" column="department"/>
        <result property="yearMonth" column="yearMonth"/>
        <result property="money" column="money"/>
    </resultMap>
    <select id="selectEmployeeLoansList" resultMap="EmployeeLoansResult">
        select companyCode,department,yearMonth,money
        from (
        select
        a.rbukrs as 'companyCode',
        a.departmentlevel1 as 'department',
        a.fis<PERSON>ear<PERSON> as 'yearMonth',
        a.hsl as 'money'
        from EmployeeLoanReport a) b
        <where>
            <if test="companyCode != null and companyCode !=''">and companyCode = #{companyCode}</if>
            <if test="department != null and department !=''">and department = #{department}</if>
            <if test="yearMonth != null and yearMonth !=''">and yearMonth like concat('%',#{yearMonth},'%')</if>
            <if test="yearMonthList !=null and yearMonthList.size>0">
                and yearMonth in
                <foreach collection="yearMonthList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>