<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.MaterialServiceProcurementRequestMapper">
    <resultMap type="com.akesobio.report.groupFinance.domain.MaterialServiceProcurementRequest"
               id="MaterialServiceProcurementRequestResult">
        <result property="companyEntity" column="companyEntity"/>
        <result property="factory" column="factory"/>
        <result property="orderNumber" column="orderNumber"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentProcessor" column="currentProcessor"/>
        <result property="currentSession" column="currentSession"/>
        <result property="purchaseOrderNumber" column="purchaseOrderNumber"/>
        <result property="applicationDate" column="applicationDate"/>
        <result property="expenseYear" column="expenseYear"/>
        <result property="expenseMonth" column="expenseMonth"/>
        <result property="applicant" column="applicant"/>
        <result property="supplierCode" column="supplierCode"/>
        <result property="supplierName" column="supplierName"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="internalOrderNumber" column="internalOrderNumber"/>
        <result property="firstLevelClassification" column="firstLevelClassification"/>
        <result property="secondaryClassification" column="secondaryClassification"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="serviceName" column="serviceName"/>
        <result property="accountingSubjectCode" column="accountingSubjectCode"/>
        <result property="accountingSubjectName" column="accountingSubjectName"/>
        <result property="functionalScope" column="functionalScope"/>
        <result property="functionalScopeName" column="functionalScopeName"/>
        <result property="quantity" column="quantity"/>
        <result property="unitPrice" column="unitPrice"/>
        <result property="money" column="money"/>
        <result property="taxRate" column="taxRate"/>
        <result property="remarks" column="remarks"/>
        <result property="completionDate" column="completionDate"/>
    </resultMap>
    <select id="selectMaterialServiceProcurementRequestList" resultMap="MaterialServiceProcurementRequestResult">
        select companyEntity,
               factory,
               orderNumber,
               documentStatus,
               currentProcessor,
               currentSession,
               purchaseOrderNumber,
               applicationDate,
               expenseYear,
               expenseMonth,
               applicant,
               supplierCode,
               supplierName,
               costCenterCode,
               costCenterName,
               projectNumber,
               internalOrderNumber,
               firstLevelClassification,
               secondaryClassification,
               serviceCode,
               serviceName,
               accountingSubjectCode,
               accountingSubjectName,
               functionalScope,
               functionalScopeName,
               quantity,
               unitPrice,
               money,
               taxRate,
               remarks,
               completionDate
        from materialServiceProcurementRequest
    </select>

</mapper>