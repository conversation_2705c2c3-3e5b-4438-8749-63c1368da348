<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.PresidentOfficeBusinessCostCenterMapper">
    <resultMap type="com.akesobio.report.groupFinance.vo.PresidentOfficeBusinessCostCenterVo" id="PresidentOfficeBusinessCostCenterVo">
        <result property="year"    column="year" />
        <result property="month"   column="month"  />
        <result property="total"   column="total"  />
        <result property="total1"  column="total1" />
    </resultMap>
    <select id="costCenterLists"  resultMap="PresidentOfficeBusinessCostCenterVo">
        select Year(paymentApplicationDate) year,Month(paymentApplicationDate) month,SUM(CONVERT(DECIMAL(13,2),ISNULL(amount, 0))) total,(SUM(CONVERT(DECIMAL(13,2),ISNULL(amount, 0)))/2) total1
        FROM president_business
        <where>
            <if test="costCenter !=null and costCenter != ''">and costCenterCode=#{costCenter}</if>
            <if test="date !=null and date !=''">and paymentApplicationDate >=#{date}</if>
        </where>
        Group by Year(paymentApplicationDate),Month(paymentApplicationDate)
        order by month ASC
    </select>
</mapper>
