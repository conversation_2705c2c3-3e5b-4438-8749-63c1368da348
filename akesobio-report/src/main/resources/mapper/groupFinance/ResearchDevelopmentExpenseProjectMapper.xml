<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ResearchDevelopmentExpenseProjectMapper">

    <resultMap type="ResearchDevelopmentExpenseProject" id="ResearchDevelopmentExpenseProjectResult">
        <result property="id" column="id"/>
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="yearMonth" column="yearMonth"/>
        <result property="amount" column="amount"/>
        <result property="costType" column="costType"/>
        <result property="pipelineType" column="pipelineType"/>
        <result property="drugPipeline" column="drugPipeline"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="deleteStatus" column="deleteStatus"/>
    </resultMap>

    <sql id="selectResearchDevelopmentExpenseProjectVo">
        select id,
               companyName,
               companyCode, year, month, yearMonth, amount, costType, pipelineType, drugPipeline, projectNumber, deleteStatus
        from research_development_expense_project
    </sql>

    <select id="selectResearchDevelopmentExpenseProjectList" parameterType="ResearchDevelopmentExpenseProject"
            resultMap="ResearchDevelopmentExpenseProjectResult">
        <include refid="selectResearchDevelopmentExpenseProjectVo"/>
        <where>
            <if test="companyName != null  and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode != null  and companyCode != ''">and companyCode = #{companyCode}</if>
            <if test="year != null ">and year = #{year}</if>
            <if test="month != null  and month != ''">and month = #{month}</if>
            <if test="yearMonth != null  and yearMonth != ''">and yearMonth = #{yearMonth}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="costType != null  and costType != ''">and costType = #{costType}</if>
            <if test="pipelineType != null  and pipelineType != ''">and pipelineType = #{pipelineType}</if>
            <if test="drugPipeline != null  and drugPipeline != ''">and drugPipeline = #{drugPipeline}</if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber = #{projectNumber}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
        </where>
    </select>

    <select id="selectResearchDevelopmentExpenseProjectById" parameterType="Integer"
            resultMap="ResearchDevelopmentExpenseProjectResult">
        <include refid="selectResearchDevelopmentExpenseProjectVo"/>
        where id = #{id}
    </select>

    <insert id="insertResearchDevelopmentExpenseProject" parameterType="ResearchDevelopmentExpenseProject">
        insert into research_development_expense_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="year != null">year,</if>
            <if test="month != null">month,</if>
            <if test="yearMonth != null">yearMonth,</if>
            <if test="amount != null">amount,</if>
            <if test="costType != null">costType,</if>
            <if test="pipelineType != null">pipelineType,</if>
            <if test="drugPipeline != null">drugPipeline,</if>
            <if test="projectNumber != null">projectNumber,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="year != null">#{year},</if>
            <if test="month != null">#{month},</if>
            <if test="yearMonth != null">#{yearMonth},</if>
            <if test="amount != null">#{amount},</if>
            <if test="costType != null">#{costType},</if>
            <if test="pipelineType != null">#{pipelineType},</if>
            <if test="drugPipeline != null">#{drugPipeline},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
        </trim>
    </insert>

    <update id="updateResearchDevelopmentExpenseProject" parameterType="ResearchDevelopmentExpenseProject">
        update research_development_expense_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="yearMonth != null">yearMonth = #{yearMonth},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="costType != null">costType = #{costType},</if>
            <if test="pipelineType != null">pipelineType = #{pipelineType},</if>
            <if test="drugPipeline != null">drugPipeline = #{drugPipeline},</if>
            <if test="projectNumber != null">projectNumber = #{projectNumber},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResearchDevelopmentExpenseProjectById" parameterType="Integer">
        delete
        from research_development_expense_project
        where id = #{id}
    </delete>

    <delete id="deleteResearchDevelopmentExpenseProjectByIds" parameterType="String">
        delete from research_development_expense_project where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAllResearchDevelopmentExpenseProject">
        delete
        from research_development_expense_project
    </delete>
</mapper>