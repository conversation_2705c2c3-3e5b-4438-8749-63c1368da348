<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.IitResearchTotalExpenditureMapper">

    <resultMap type="IitResearchTotalExpenditure" id="IitResearchTotalExpenditureResult">
        <result property="paymentApplicationDate" column="paymentApplicationDate"/>
        <result property="singleNumber" column="singleNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="department" column="department"/>
        <result property="contractNo" column="contractNo"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="costBearingCompany" column="costBearingCompany"/>
        <result property="receivingUnit" column="receivingUnit"/>
        <result property="centerNo" column="centerNo"/>
        <result property="centerName" column="centerName"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="projectType" column="projectType"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="contractType" column="contractType"/>
        <result property="outsourcingSubdivision" column="outsourcingSubdivision"/>
        <result property="paymentDescription" column="paymentDescription"/>
        <result property="accountCode" column="accountCode"/>
        <result property="suject" column="suject"/>
        <result property="settlementPeriod" column="settlementPeriod"/>
        <result property="invoiceType" column="invoiceType"/>
        <result property="invoiceNo" column="invoiceNo"/>
        <result property="taxRate" column="taxRate"/>
        <result property="amountWithoutTax" column="amountWithoutTax"/>
        <result property="specialTax" column="specialTax"/>
        <result property="amount" column="amount"/>
        <result property="ascriptionDepartment" column="ascriptionDepartment"/>
        <result property="amountIncludingTax" column="amountIncludingTax"/>
        <result property="regionalManager" column="regionalManager"/>
        <result property="salesAreaDirector" column="salesAreaDirector"/>
        <result property="remark" column="remark"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="invoice" column="invoice"/>
        <result property="sapPayNumber" column="sapPayNumber"/>
        <result property="tableName" column="tableName"/>
        <result property="applicantJobNumber" column="applicantJobNumber"/>
        <result property="reimbursementPerson" column="reimbursementPerson"/>
        <result property="meetingCategory" column="meetingCategory"/>
        <result property="businessType" column="businessType"/>
        <result property="thirdLevelDepartment" column="thirdLevelDepartment"/>
        <result property="secondaryDepartment" column="secondaryDepartment"/>
        <result property="endLevelDepartment" column="endLevelDepartment"/>
        <result property="costDescription" column="costDescription"/>
        <result property="remarks" column="remarks"/>
        <result property="systemProjectNumber" column="systemProjectNumber"/>
        <result property="collectionProjectNumber" column="collectionProjectNumber"/>
        <result property="departmentAllocationAmount" column="departmentAllocationAmount"/>
    </resultMap>

    <sql id="selectIitResearchTotalExpenditureVo">
        select paymentApplicationDate,
               singleNumber,
               applicant,
               department,
               contractNo,
               applicationSubject,
               costBearingCompany,
               receivingUnit,
               centerNo,
               centerName,
               costCenterCode,
               costCenterName,
               projectType,
               projectNumber,
               contractType,
               outsourcingSubdivision,
               paymentDescription,
               accountCode,
               suject,
               settlementPeriod,
               invoiceType,
               invoiceNo,
               invoice,
               taxRate,
               amountWithoutTax,
               specialTax,
               amount,
               ascriptionDepartment,
               amountIncludingTax,
               regionalManager,
               salesAreaDirector,
               remark,
               documentStatus,
               currentSession,
               sapPayNumber,
               tableName,
               applicantJobNumber,
               reimbursementPerson,
               meetingCategory,
               businessType,
               thirdLevelDepartment,
               secondaryDepartment,
               endLevelDepartment,
               costDescription,
               remarks,
               systemProjectNumber,
               collectionProjectNumber,
               departmentAllocationAmount
        from IIT_research_business
    </sql>

    <select id="selectIitResearchTotalExpenditureList" parameterType="IitResearchTotalExpenditure"
            resultMap="IitResearchTotalExpenditureResult">
        <include refid="selectIitResearchTotalExpenditureVo"/>
        <where>
            <if test="params.beginPaymentApplicationDate != null and params.beginPaymentApplicationDate != '' and params.endPaymentApplicationDate != null and params.endPaymentApplicationDate != ''">
                and paymentApplicationDate between #{params.beginPaymentApplicationDate} and
                #{params.endPaymentApplicationDate}
            </if>
            <if test="singleNumber != null  and singleNumber != ''">and singleNumber like concat('%', #{singleNumber},
                '%')
            </if>
            <if test="applicant != null  and applicant != ''">and applicant like concat('%', #{applicant}, '%')</if>
            <if test="department != null  and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="contractNo != null  and contractNo != ''">and contractNo like concat('%', #{contractNo}, '%')</if>
            <if test="applicationSubject != null  and applicationSubject != ''">and applicationSubject like concat('%',
                #{applicationSubject}, '%')
            </if>
            <if test="costBearingCompany != null  and costBearingCompany != ''">and costBearingCompany like concat('%',
                #{costBearingCompany}, '%')
            </if>
            <if test="receivingUnit != null  and receivingUnit != ''">and receivingUnit like concat('%',
                #{receivingUnit}, '%')
            </if>
            <if test="centerNo != null  and centerNo != ''">and centerNo like concat('%', #{centerNo}, '%')</if>
            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="costCenterCode != null  and costCenterCode != ''">and costCenterCode like concat('%',
                #{costCenterCode}, '%')
            </if>
            <if test="costCenterName != null  and costCenterName != ''">and costCenterName like concat('%',
                #{costCenterName}, '%')
            </if>
            <if test="projectType != null  and projectType != ''">and projectType like concat('%', #{projectType},
                '%')
            </if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="contractType != null  and contractType != ''">and contractType like concat('%', #{contractType},
                '%')
            </if>
            <if test="outsourcingSubdivision != null  and outsourcingSubdivision != ''">and outsourcingSubdivision like
                concat('%', #{outsourcingSubdivision}, '%')
            </if>
            <if test="paymentDescription != null  and paymentDescription != ''">and paymentDescription like concat('%',
                #{paymentDescription}, '%')
            </if>
            <if test="accountCode != null  and accountCode != ''">and accountCode like concat('%', #{accountCode},
                '%')
            </if>
            <if test="suject != null  and suject != ''">and suject like concat('%', #{suject}, '%')</if>
            <if test="settlementPeriod != null  and settlementPeriod != ''">and settlementPeriod like concat('%',
                #{settlementPeriod}, '%')
            </if>
            <if test="invoiceType != null  and invoiceType != ''">and invoiceType like concat('%', #{invoiceType},
                '%')
            </if>
            <if test="invoiceNo != null  and invoiceNo != ''">and invoiceNo like concat('%', #{invoiceNo}, '%')</if>
            <if test="taxRate != null  and taxRate != ''">and taxRate = #{taxRate}</if>
            <if test="amountWithoutTax != null  and amountWithoutTax != ''">and amountWithoutTax = #{amountWithoutTax}
            </if>
            <if test="specialTax != null  and specialTax != ''">and specialTax = #{specialTax}</if>
            <if test="amount != null  and amount != ''">and amount = #{amount}</if>
            <if test="ascriptionDepartment != null  and ascriptionDepartment != ''">and ascriptionDepartment like
                concat('%', #{ascriptionDepartment}, '%')
            </if>
            <if test="amountIncludingTax != null  and amountIncludingTax != ''">and amountIncludingTax =
                #{amountIncludingTax}
            </if>
            <if test="regionalManager != null  and regionalManager != ''">and regionalManager = #{regionalManager}</if>
            <if test="salesAreaDirector != null  and salesAreaDirector != ''">and salesAreaDirector =
                #{salesAreaDirector}
            </if>
            <if test="documentStatus != null  and documentStatus != ''">and documentStatus like concat('%',
                #{documentStatus}, '%')
            </if>
            <if test="currentSession != null  and currentSession != ''">and currentSession like concat('%',
                #{currentSession}, '%')
            </if>
            <if test="tableName != null  and tableName != ''">and tableName like concat('%', #{tableName}, '%')</if>
            <if test="applicantJobNumber != null  and applicantJobNumber != ''">and applicantJobNumber like concat('%',
                #{applicantJobNumber}, '%')
            </if>
            <if test="reimbursementPerson != null  and reimbursementPerson != ''">and reimbursementPerson like
                concat('%', #{reimbursementPerson}, '%')
            </if>
            <if test="meetingCategory != null  and meetingCategory != ''">and meetingCategory like concat('%',
                #{meetingCategory}, '%')
            </if>
            <if test="businessType != null  and businessType != ''">and businessType like concat('%', #{businessType},
                '%')
            </if>
            <if test="thirdLevelDepartment != null  and thirdLevelDepartment != ''">and thirdLevelDepartment like
                concat('%', #{thirdLevelDepartment}, '%')
            </if>
            <if test="secondaryDepartment != null  and secondaryDepartment != ''">and secondaryDepartment like
                concat('%', #{secondaryDepartment}, '%')
            </if>
            <if test="endLevelDepartment != null  and endLevelDepartment != ''">and endLevelDepartment like concat('%',
                #{endLevelDepartment}, '%')
            </if>
            <if test="costDescription != null  and costDescription != ''">and costDescription like concat('%',
                #{costDescription}, '%')
            </if>
            <if test="remarks != null  and remarks != ''">and remarks like concat('%', #{remarks}, '%')</if>
            <if test="systemProjectNumber != null  and systemProjectNumber != ''">and systemProjectNumber like
                concat('%', #{systemProjectNumber}, '%')
            </if>
            <if test="collectionProjectNumber != null  and collectionProjectNumber != ''">and collectionProjectNumber
                like concat('%', #{collectionProjectNumber}, '%')
            </if>
            <if test="departmentAllocationAmount != null ">and departmentAllocationAmount =
                #{departmentAllocationAmount}
            </if>
        </where>
        order by paymentApplicationDate desc
    </select>

</mapper>