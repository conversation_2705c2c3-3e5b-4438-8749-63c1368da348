<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.LoanMapper">
    <resultMap type="Loan" id="LoanResult">
        <result property="costBearing"    column="costBearing"    />
        <result property="companyCode"    column="companyCode"    />
        <result property="applicationDate"    column="applicationDate"    />
        <result property="oANumber"    column="oANumber"    />
        <result property="applicantName"    column="applicantName"    />
        <result property="applicationNumber"    column="applicationNumber"    />
        <result property="department"    column="department"    />
        <result property="applicantPosition"    column="applicantPosition"    />
        <result property="payee"    column="payee"    />
        <result property="loanAmount"    column="loanAmount"    />
        <result property="currency"    column="currency"    />
        <result property="repaymentDate"    column="repaymentDate"    />
        <result property="loanType"    column="loanType"    />
        <result property="loanUse"    column="loanUse"    />
        <result property="costCenter"    column="costCenter"    />
        <result property="bankName"    column="bankName"    />
        <result property="bankAccount"    column="bankAccount"    />
        <result property="currentSession"    column="currentSession"    />
        <result property="documentStatus"    column="documentStatus"    />
        <result property="sapVoucherPushTime"    column="sapVoucherPushTime"    />
        <result property="sapVoucherNumber"    column="sapVoucherNumber"    />
        <result property="cbsPaymentVoucherStatus"    column="cbsPaymentVoucherStatus"    />
    </resultMap>

    <sql id="selectLoanVo">
        select costBearing, companyCode, applicationDate, oANumber, applicantName, applicationNumber, department, applicantPosition, payee, loanAmount, currency, repaymentDate, loanType, loanUse, costCenter, bankName, bankAccount, currentSession, documentStatus, sapVoucherPushTime, sapVoucherNumber, cbsPaymentVoucherStatus from loan
    </sql>

    <select id="selectLoanList" parameterType="Loan" resultMap="LoanResult">
        <include refid="selectLoanVo"/>
        <where>  
            <if test="costBearing != null  and costBearing != ''"> and costBearing like concat('%', #{costBearing}, '%')</if>
            <if test="companyCode != null  and companyCode != ''"> and companyCode = #{companyCode}</if>
            <if test="params.beginApplicationDate != null and params.beginApplicationDate != '' and params.endApplicationDate != null and params.endApplicationDate != ''"> and applicationDate between #{params.beginApplicationDate} and #{params.endApplicationDate}</if>
            <if test="oANumber != null  and oANumber != ''"> and oANumber like concat('%', #{oANumber}, '%')</if>
            <if test="applicantName != null  and applicantName != ''"> and applicantName like concat('%', #{applicantName}, '%')</if>
            <if test="applicationNumber != null  and applicationNumber != ''"> and applicationNumber like concat('%', #{applicationNumber}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="applicantPosition != null  and applicantPosition != ''"> and applicantPosition = #{applicantPosition}</if>
            <if test="payee != null  and payee != ''"> and payee like concat('%', #{payee}, '%')</if>
            <if test="loanAmount != null  and loanAmount != ''"> and loanAmount = #{loanAmount}</if>
            <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
            <if test="params.beginRepaymentDate != null and params.beginRepaymentDate != '' and params.endRepaymentDate != null and params.endRepaymentDate != ''"> and repaymentDate between #{params.beginRepaymentDate} and #{params.endRepaymentDate}</if>
            <if test="params.beginLoanType != null and params.beginLoanType != '' and params.endLoanType != null and params.endLoanType != ''"> and loanType between #{params.beginLoanType} and #{params.endLoanType}</if>
            <if test="loanUse != null  and loanUse != ''"> and loanUse = #{loanUse}</if>
            <if test="costCenter != null  and costCenter != ''"> and costCenter like concat('%', #{costCenter}, '%')</if>
            <if test="bankName != null  and bankName != ''"> and bankName = #{bankName}</if>
            <if test="bankAccount != null  and bankAccount != ''"> and bankAccount = #{bankAccount}</if>
            <if test="currentSession != null  and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>
            <if test="documentStatus != null  and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="params.beginSapVoucherPushTime != null and params.beginSapVoucherPushTime != '' and params.endSapVoucherPushTime != null and params.endSapVoucherPushTime != ''"> and sapVoucherPushTime between #{params.beginSapVoucherPushTime} and #{params.endSapVoucherPushTime}</if>
            <if test="sapVoucherNumber != null  and sapVoucherNumber != ''"> and sapVoucherNumber like concat('%', #{sapVoucherNumber}, '%')</if>
            <if test="cbsPaymentVoucherStatus != null  and cbsPaymentVoucherStatus != ''"> and cbsPaymentVoucherStatus like concat('%', #{cbsPaymentVoucherStatus}, '%')</if>
        </where>
    </select>
</mapper>