<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.FinancialVoucherInformationMapper">
    <resultMap type="com.akesobio.report.groupFinance.domain.FinancialVoucherInformation" id="FinancialVoucherInformationResult">
        <result property="belnr" column="belnr"/>
        <result property="xblnr" column="xblnr"/>
        <result property="zuonr" column="zuonr"/>
        <result property="hsl" column="hsl"/>
        <result property="racct" column="racct"/>
    </resultMap>
    <select id="selectFinancialVoucherInformationList" resultMap="FinancialVoucherInformationResult">
        select belnr,xblnr,zuonr,hsl from FinancialVoucherInformation
        <where>
            <if test="xblnr != null and xblnr !=''">and xblnr = #{xblnr}</if>
            <if test="belnr != null and belnr !=''">and belnr = #{belnr} </if>
            <if test="zuonr != null and zuonr !=''">and zuonr = #{zuonr} </if>
            <if test="racct != null and racct !=''">and racct = #{racct} </if>
        </where>
    </select>
</mapper>