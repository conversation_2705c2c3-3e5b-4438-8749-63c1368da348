<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.LogisticsCostSummaryMapper">

    <resultMap type="LogisticsCostSummary" id="LogisticsCostSummaryResult">
        <result property="id" column="id"/>
        <result property="companyEntity" column="companyEntity"/>
        <result property="expenseYear" column="expenseYear"/>
        <result property="expenseMonth" column="expenseMonth"/>
        <result property="accountingSubjectCode" column="accountingSubjectCode"/>
        <result property="accountingSubjectName" column="accountingSubjectName"/>
        <result property="internalOrderNumber" column="internalOrderNumber"/>
        <result property="firstLevelClassification" column="firstLevelClassification"/>
        <result property="secondaryClassification" column="secondaryClassification"/>
        <result property="functionalScope" column="functionalScope"/>
        <result property="functionalScopeName" column="functionalScopeName"/>
        <result property="money" column="money"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="costCenterCode" column="costCenterCode"/>
    </resultMap>

    <sql id="selectLogisticsCostSummaryVo">
        select id,
               companyEntity,
               expenseYear,
               expenseMonth,
               accountingSubjectCode,
               accountingSubjectName,
               internalOrderNumber,
               firstLevelClassification,
               secondaryClassification,
               functionalScope,
               functionalScopeName,
               money,
               deleteStatus,
               costCenterCode
        from logistics_cost_summary
    </sql>

    <select id="selectLogisticsCostSummaryList" parameterType="LogisticsCostSummary"
            resultMap="LogisticsCostSummaryResult">
        <include refid="selectLogisticsCostSummaryVo"/>
        <where>
            <if test="companyEntity != null  and companyEntity != ''">and companyEntity like concat('%',
                #{companyEntity}, '%')
            </if>
            <if test="expenseYear != null ">and expenseYear = #{expenseYear}</if>
            <if test="expenseMonth != null ">and expenseMonth = #{expenseMonth}</if>
            <if test="accountingSubjectCode != null  and accountingSubjectCode != ''">and accountingSubjectCode like
                concat('%', #{accountingSubjectCode}, '%')
            </if>
            <if test="accountingSubjectName != null  and accountingSubjectName != ''">and accountingSubjectName like
                concat('%', #{accountingSubjectName}, '%')
            </if>
            <if test="internalOrderNumber != null  and internalOrderNumber != ''">and internalOrderNumber like
                concat('%', #{internalOrderNumber}, '%')
            </if>
            <if test="firstLevelClassification != null  and firstLevelClassification != ''">and firstLevelClassification
                like concat('%', #{firstLevelClassification}, '%')
            </if>
            <if test="secondaryClassification != null  and secondaryClassification != ''">and secondaryClassification
                like concat('%', #{secondaryClassification}, '%')
            </if>
            <if test="functionalScope != null  and functionalScope != ''">and functionalScope like concat('%',
                #{functionalScope}, '%')
            </if>
            <if test="functionalScopeName != null  and functionalScopeName != ''">and functionalScopeName like
                concat('%', #{functionalScopeName}, '%')
            </if>
            <if test="money != null ">and money = #{money}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="costCenterCode != null  and costCenterCode != ''"> and costCenterCode like concat('%', #{costCenterCode}, '%')</if>
            and money > 0
        </where>
    </select>

    <select id="selectLogisticsCostSummaryById" parameterType="Integer" resultMap="LogisticsCostSummaryResult">
        <include refid="selectLogisticsCostSummaryVo"/>
        where id = #{id}
    </select>

    <insert id="insertLogisticsCostSummary" parameterType="LogisticsCostSummary">
        insert into logistics_cost_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyEntity != null">companyEntity,</if>
            <if test="expenseYear != null">expenseYear,</if>
            <if test="expenseMonth != null">expenseMonth,</if>
            <if test="accountingSubjectCode != null">accountingSubjectCode,</if>
            <if test="accountingSubjectName != null">accountingSubjectName,</if>
            <if test="internalOrderNumber != null">internalOrderNumber,</if>
            <if test="firstLevelClassification != null">firstLevelClassification,</if>
            <if test="secondaryClassification != null">secondaryClassification,</if>
            <if test="functionalScope != null">functionalScope,</if>
            <if test="functionalScopeName != null">functionalScopeName,</if>
            <if test="money != null">money,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="costCenterCode != null">costCenterCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyEntity != null">#{companyEntity},</if>
            <if test="expenseYear != null">#{expenseYear},</if>
            <if test="expenseMonth != null">#{expenseMonth},</if>
            <if test="accountingSubjectCode != null">#{accountingSubjectCode},</if>
            <if test="accountingSubjectName != null">#{accountingSubjectName},</if>
            <if test="internalOrderNumber != null">#{internalOrderNumber},</if>
            <if test="firstLevelClassification != null">#{firstLevelClassification},</if>
            <if test="secondaryClassification != null">#{secondaryClassification},</if>
            <if test="functionalScope != null">#{functionalScope},</if>
            <if test="functionalScopeName != null">#{functionalScopeName},</if>
            <if test="money != null">#{money},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="costCenterCode != null">#{costCenterCode},</if>
        </trim>
    </insert>

    <update id="updateLogisticsCostSummary" parameterType="LogisticsCostSummary">
        update logistics_cost_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyEntity != null">companyEntity = #{companyEntity},</if>
            <if test="expenseYear != null">expenseYear = #{expenseYear},</if>
            <if test="expenseMonth != null">expenseMonth = #{expenseMonth},</if>
            <if test="accountingSubjectCode != null">accountingSubjectCode = #{accountingSubjectCode},</if>
            <if test="accountingSubjectName != null">accountingSubjectName = #{accountingSubjectName},</if>
            <if test="internalOrderNumber != null">internalOrderNumber = #{internalOrderNumber},</if>
            <if test="firstLevelClassification != null">firstLevelClassification = #{firstLevelClassification},</if>
            <if test="secondaryClassification != null">secondaryClassification = #{secondaryClassification},</if>
            <if test="functionalScope != null">functionalScope = #{functionalScope},</if>
            <if test="functionalScopeName != null">functionalScopeName = #{functionalScopeName},</if>
            <if test="money != null">money = #{money},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
            <if test="costCenterCode != null">costCenterCode = #{costCenterCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLogisticsCostSummaryById" parameterType="Integer">
        delete
        from logistics_cost_summary
        where id = #{id}
    </delete>

    <delete id="deleteLogisticsCostSummaryByIds" parameterType="String">
        delete from logistics_cost_summary where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAllLogisticsCostSummary">
        delete
        from logistics_cost_summary
    </delete>
</mapper>