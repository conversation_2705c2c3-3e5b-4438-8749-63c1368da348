<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ReportCollection2Mapper">
    <!-- 实体类  -->
    <resultMap type="ReportCollection2" id="ReportCollection2Result">
    </resultMap>
    
    <!-- sql语句 -->
    <sql id="selectReportCollection2List">
        SELECT
            id,
            filePath,
            suffix,
            tableName,
            oddNumber,
            applicationDate,
            docSubject,
            expenseEntity,
            department,
            expenseAmount,
            applicationName,
            documentStatus,
            currentSession,
            currentProcessor,
            nodeArrivalTime
        FROM report_collection_s
    </sql>
    
    <!-- 方法 -->
    <select id="queryReportCollection2List" parameterType="ReportCollection2" resultMap="ReportCollection2Result">
        <include refid="selectReportCollection2List"/>
        <where>
            <if test="tableName != null and tableName != ''"> and tableName like concat('%', #{tableName}, '%')</if>
            <if test="docSubject != null and docSubject != ''"> and docSubject like concat('%', #{docSubject}, '%')</if>
            <if test="department != null and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="expenseAmount != null and expenseAmount != ''"> and expenseAmount like concat('%', #{expenseAmount}, '%')</if>
            <if test="applicationName != null and applicationName != ''"> and applicationName like concat('%', #{applicationName}, '%')</if>
            <if test="oddNumber != null and oddNumber != ''"> and oddNumber like concat('%', #{oddNumber}, '%')</if>
            <if test="documentStatus != null and documentStatus != ''"> and documentStatus like concat('%', #{documentStatus}, '%')</if>
            <if test="currentSession != null and currentSession != ''"> and currentSession like concat('%', #{currentSession}, '%')</if>

            <if test="startDate != null and startDate != ''">and applicationDate &gt;= #{startDate}</if>
            <if test="endDate != null and endDate != ''">and applicationDate &lt;= #{endDate}</if>
        </where>
    </select>
</mapper>