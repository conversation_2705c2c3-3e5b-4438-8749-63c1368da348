<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.MonthlyInventoryRingRatioChangeMapper">
    <resultMap type="com.akesobio.report.groupFinance.domain.MonthlyInventoryRingRatioChange"
               id="MonthlyInventoryRingRatioChangeResult">
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="year" column="year"/>
        <result property="subject" column="subject"/>
        <result property="month1" column="month1"/>
        <result property="month2" column="month2"/>
        <result property="month3" column="month3"/>
        <result property="month4" column="month4"/>
        <result property="month5" column="month5"/>
        <result property="month6" column="month6"/>
        <result property="month7" column="month7"/>
        <result property="month8" column="month8"/>
        <result property="month9" column="month9"/>
        <result property="month10" column="month10"/>
        <result property="month11" column="month11"/>
        <result property="month12" column="month12"/>
    </resultMap>

    <select id="selectMonthlyInventoryRingRatioChangeList" resultMap="MonthlyInventoryRingRatioChangeResult">
        select
        companyName,companyCode,year,subject,month1,month2,month3,month4,
        month5, month6,month7,month8,month9,month10,month11,month12
        from
        (select
        a.butxt as 'companyName',
        a.rbukrs as 'companyCode',
        a.ryear as 'year',
        a.txt20 as 'subject',
        a.hsl01 as 'month1',
        a.hsl02 as 'month2',
        a.hsl03 as 'month3',
        a.hsl04 as 'month4',
        a.hsl05 as 'month5',
        a.hsl06 as 'month6',
        a.hsl07 as 'month7',
        a.hsl08 as 'month8',
        a.hsl09 as 'month9',
        a.hsl10 as 'month10',
        a.hsl11 as 'month11',
        a.hsl12 as 'month12'
        from monthly_inventory_ring_ratio_change_table a) b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
            <if test="year !=null and year !=''">and year = #{year}</if>
            <if test="subject !=null and subject != ''">and subject = #{subject}</if>

            <if test="companyList !=null and companyList.size>0">
                and companyName in
                <foreach collection="companyList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="subjectList !=null and subjectList.size>0">
                and subject in
                <foreach collection="subjectList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>