<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.CapitalBudgetingPlanMapper">

    <resultMap type="CapitalBudgetingPlan" id="CapitalBudgetingPlanResult">

    </resultMap>
    <resultMap type="CapitalBudgetingPlans" id="CapitalBudgetingPlansResult">

    </resultMap>

    <sql id="selectCapitalBudgetingPlan">
        SELECT a.fd_id             AS id,
               p.fd_name           AS name,
               a.fd_3c61010627130a AS applicationDate,
               a.fd_3c6cc6e3a220fa_text AS company,
               a.fd_3c6cc9a73ebc0e      AS companyId,
               a.fd_3c5eafccfe1c48 AS budgetMonth,
               a.fd_3c6100ea27d500 AS department,
               a.fd_3c5f4421b947b4 AS cnyTotal,
               a.fd_3c5f5ef285f7ca AS cnyFinanceTotal,
               a.fd_3c64155aead410 AS usdTotal,
               a.fd_3c642cdbf645dc AS usdFinanceTotal,
               pe.fd_name          AS applicant,
               l.fd_create_time    AS createTime,
               l.doc_subject       AS subject,
               (
                   CASE
                       l.doc_status
                       WHEN '10' THEN
                           '草稿'
                       WHEN '20' THEN
                           '待审'
                       WHEN '11' THEN
                           '驳回'
                       WHEN '00' THEN
                           '废弃'
                       WHEN '30' THEN
                           '结束'
                       WHEN '31' THEN
                           '已反馈'
                       ELSE '不对劲'
                       END
                   ) AS documentStatus,
               n.fd_fact_node_name AS currentSession,
               de.fd_name          AS handlePerson
        FROM ekp_zjys a
                 LEFT JOIN hr_staff_person_info p ON p.fd_id = a.fd_3c6037daa1c9dc
                 LEFT JOIN lbpm_process l ON l.fd_id = a.fd_id
                 LEFT JOIN lbpm_node n ON n.fd_process_id = l.fd_id
                 LEFT JOIN lbpm_workitem d ON d.fd_process_id = l.fd_id
                 LEFT JOIN hr_staff_person_info de ON de.fd_id = d.fd_expected_id
                 LEFT JOIN hr_staff_person_info pe ON pe.fd_id = l.fd_creator_id
    </sql>

    <select id="queryCapitalBudgetingPlan" parameterType="CapitalBudgetingPlan" resultMap="CapitalBudgetingPlanResult">
        <include refid="selectCapitalBudgetingPlan"/>
        <where>
            <if test="queryDepartment != null and queryDepartment != ''">
                <choose>
                    <when test="queryDepartment.size() > 1">
                        <foreach collection="queryDepartment" item="item" open="(" separator="or" close=")">
                            a.fd_3c6100ea27d500 like concat('%', #{item},'%')
                        </foreach>
                    </when>
                    <otherwise>
                        <foreach item="item" index="index" collection="queryDepartment">
                            and a.fd_3c6100ea27d500 like concat('%', #{item},'%')
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="queryCompany != null and queryCompany != ''">
                <choose>
                    <when test="queryCompany.size() > 1">
                        and
                        <foreach collection="queryCompany" item="item" open="(" separator="or" close=")">
                            a.fd_3c6cc6e3a220fa_text like concat('%', #{item}, '%')
                        </foreach>
                    </when>
                    <otherwise>
                        <foreach item="item" index="index" collection="queryCompany">
                            and a.fd_3c6cc6e3a220fa_text like concat('%', #{item}, '%')
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="queryBudgetMonth != null and queryBudgetMonth != ''">
                <choose>
                    <when test="queryBudgetMonth.size() > 1">
                        and
                        <foreach collection="queryBudgetMonth" item="item" open="(" separator="or" close=")">
                            a.fd_3c5eafccfe1c48 like concat('%',#{item}, '%')
                        </foreach>
                    </when>
                    <otherwise>
                        <foreach item="item" index="index" collection="queryBudgetMonth">
                            and a.fd_3c5eafccfe1c48 like concat('%',#{item}, '%')
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="startDate != null  and startDate != ''">and a.fd_3c61010627130a &gt;= #{startDate}</if>
            <if test="endDate != null  and endDate != ''">and a.fd_3c61010627130a &lt;= #{endDate}</if>
        </where>
    </select>

    <select id="queryCapitalBudgetingPlans" parameterType="CapitalBudgetingPlans"
            resultMap="CapitalBudgetingPlansResult">
        SELECT
        a.fd_3c6cc6e3a220fa_text AS company,
        MAX(it.fd_3c5f4421b947b4) AS it,
        MAX(engineering.fd_3c5f4421b947b4) AS engineering,
        MAX(administration.fd_3c5f4421b947b4) AS administration,
        MAX(facilities.fd_3c5f4421b947b4) AS facilities,
        MAX(procure.fd_3c5f4421b947b4) AS procure,
        MAX(secure.fd_3c5f4421b947b4) AS secure
        FROM ekp_zjys a
        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c5f4421b947b4),2),0) AS fd_3c5f4421b947b4 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = 'IT部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS it ON it.fd_3c6cc6e3a220fa_text = a.fd_3c6cc6e3a220fa_text

        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c5f4421b947b4),2),0) AS fd_3c5f4421b947b4 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = '工程部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS engineering ON engineering.fd_3c6cc6e3a220fa_text = a.fd_3c6cc6e3a220fa_text

        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c5f4421b947b4),2),0) AS fd_3c5f4421b947b4 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = '行政部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS administration ON administration.fd_3c6cc6e3a220fa_text =
        a.fd_3c6cc6e3a220fa_text

        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c5f4421b947b4),2),0) AS fd_3c5f4421b947b4 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = '设施运维部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS facilities ON facilities.fd_3c6cc6e3a220fa_text = a.fd_3c6cc6e3a220fa_text

        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c5f4421b947b4),2),0) AS fd_3c5f4421b947b4 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = '物流采购部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS procure ON procure.fd_3c6cc6e3a220fa_text = a.fd_3c6cc6e3a220fa_text

        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c5f4421b947b4),2),0) AS fd_3c5f4421b947b4 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = '职业安全部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS secure ON secure.fd_3c6cc6e3a220fa_text = a.fd_3c6cc6e3a220fa_text
        GROUP BY a.fd_3c6cc6e3a220fa_text
    </select>

    <select id="queryCapitalBudgetingPlansUSD" parameterType="CapitalBudgetingPlans"
            resultMap="CapitalBudgetingPlansResult">
        SELECT
        a.fd_3c6cc6e3a220fa_text AS company,
        MAX(it.fd_3c64155aead410) AS it,
        MAX(engineering.fd_3c64155aead410) AS engineering,
        MAX(administration.fd_3c64155aead410) AS administration,
        MAX(facilities.fd_3c64155aead410) AS facilities,
        MAX(procure.fd_3c64155aead410) AS procure,
        MAX(secure.fd_3c64155aead410) AS secure
        FROM ekp_zjys a
        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c64155aead410),2),0) AS fd_3c64155aead410 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = 'IT部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS it ON it.fd_3c6cc6e3a220fa_text = a.fd_3c6cc6e3a220fa_text

        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c64155aead410),2),0) AS fd_3c64155aead410 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = '工程部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS engineering ON engineering.fd_3c6cc6e3a220fa_text = a.fd_3c6cc6e3a220fa_text

        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c64155aead410),2),0) AS fd_3c64155aead410 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = '行政部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS administration ON administration.fd_3c6cc6e3a220fa_text =
        a.fd_3c6cc6e3a220fa_text

        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c64155aead410),2),0) AS fd_3c64155aead410 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = '设施运维部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS facilities ON facilities.fd_3c6cc6e3a220fa_text = a.fd_3c6cc6e3a220fa_text

        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c64155aead410),2),0) AS fd_3c64155aead410 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = '物流采购部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS procure ON procure.fd_3c6cc6e3a220fa_text = a.fd_3c6cc6e3a220fa_text

        LEFT JOIN (SELECT fd_3c6cc6e3a220fa_text,COALESCE(ROUND(SUM(fd_3c64155aead410),2),0) AS fd_3c64155aead410 FROM
        ekp_zjys
        WHERE fd_3c6100ea27d500 = '职业安全部'
        <choose>
            <when test="months != null and months != ''">
                AND fd_3c5eafccfe1c48 = #{months}
            </when>
        </choose>
        GROUP BY fd_3c6cc6e3a220fa_text) AS secure ON secure.fd_3c6cc6e3a220fa_text = a.fd_3c6cc6e3a220fa_text
        GROUP BY a.fd_3c6cc6e3a220fa_text
    </select>

    <select id="queryDepartment" resultType="String">
        SELECT a.dept_name_a
        FROM dept_name_all_v a
        GROUP BY a.dept_name_a
    </select>
    <select id="queryCompany" resultType="String">
        SELECT fd_39ac14eca6116e
        FROM ekp_fyzt
        WHERE fd_Status = '1'
    </select>

</mapper>