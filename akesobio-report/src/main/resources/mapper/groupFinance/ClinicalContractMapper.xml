<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ClinicalContractMapper">

    <resultMap type="ClinicalContract" id="ClinicalContractResult">
        <result property="dateCreated" column="dateCreated"/>
        <result property="OATrackingNumber" column="OATrackingNumber"/>
        <result property="applicationSubject" column="applicationSubject"/>
        <result property="applicant" column="applicant"/>
        <result property="department" column="department"/>
        <result property="contractNo" column="contractNo"/>
        <result property="cooperateUnit" column="cooperateUnit"/>
        <result property="expences" column="expences"/>
        <result property="centerName" column="centerName"/>
        <result property="transactionContent" column="transactionContent"/>
        <result property="contractLevel" column="contractLevel"/>
        <result property="masterAgreement" column="masterAgreement"/>
        <result property="businessType" column="businessType"/>
        <result property="contractType" column="contractType"/>
        <result property="outsourcingSubdivision" column="outsourcingSubdivision"/>
        <result property="currency" column="currency"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="casesNumber" column="casesNumber"/>
        <result property="casePrice" column="casePrice"/>
        <result property="taxRate" column="taxRate"/>
        <result property="transactionAmount" column="transactionAmount"/>
        <result property="warranty" column="warranty"/>
        <result property="laborFee" column="laborFee"/>
        <result property="formState" column="formState"/>
        <result property="currentSession" column="currentSession"/>
        <result property="paymentMilestones" column="paymentMilestones"/>
        <result property="milestoneAmount" column="milestoneAmount"/>
        <result property="mainAgreementAmount" column="mainAgreementAmount"/>
        <result property="contractTaxAmount" column="contractTaxAmount"/>
    </resultMap>

    <sql id="selectClinicalContractVo">
        select dateCreated,
               OATrackingNumber,
               applicationSubject,
               applicant,
               department,
               contractNo,
               cooperateUnit,
               expences,
               centerName,
               transactionContent,
               contractLevel,
               masterAgreement,
               businessType,
               contractType,
               outsourcingSubdivision,
               currency,
               projectNumber,
               casesNumber,
               casePrice,
               taxRate,
               transactionAmount,
               warranty,
               laborFee,
               formState,
               currentSession,
               paymentMilestones,
               milestoneAmount,
               mainAgreementAmount,
               contractTaxAmount
        from clinical_contract
    </sql>

    <select id="selectClinicalContractList" parameterType="ClinicalContract" resultMap="ClinicalContractResult">
        <include refid="selectClinicalContractVo"/>
        <where>
            <if test="params.beginDateCreated != null and params.beginDateCreated != '' and params.endDateCreated != null and params.endDateCreated != ''">
                and dateCreated between #{params.beginDateCreated} and #{params.endDateCreated}
            </if>
            <if test="OATrackingNumber != null  and OATrackingNumber != ''">and OATrackingNumber = #{OATrackingNumber}
            </if>
            <if test="applicationSubject != null  and applicationSubject != ''">and applicationSubject =
                #{applicationSubject}
            </if>
            <if test="applicant != null  and applicant != ''">and applicant = #{applicant}</if>
            <if test="department != null  and department != ''">and department = #{department}</if>
            <if test="contractNo != null  and contractNo != ''">and contractNo like concat('%', #{contractNo}, '%')</if>
            <if test="cooperateUnit != null  and cooperateUnit != ''">and cooperateUnit like concat('%',
                #{cooperateUnit}, '%')
            </if>
            <if test="expences != null  and expences != ''">and expences like concat('%', #{expences}, '%')</if>
            <if test="centerName != null  and centerName != ''">and centerName like concat('%', #{centerName}, '%')</if>
            <if test="transactionContent != null  and transactionContent != ''">and transactionContent =
                #{transactionContent}
            </if>
            <if test="contractLevel != null  and contractLevel != ''">and contractLevel = #{contractLevel}</if>
            <if test="masterAgreement != null  and masterAgreement != ''">and masterAgreement = #{masterAgreement}</if>
            <if test="businessType != null  and businessType != ''">and businessType = #{businessType}</if>
            <if test="contractType != null  and contractType != ''">and contractType = #{contractType}</if>
            <if test="outsourcingSubdivision != null  and outsourcingSubdivision != ''">and outsourcingSubdivision =
                #{outsourcingSubdivision}
            </if>
            <if test="currency != null  and currency != ''">and currency = #{currency}</if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber like concat('%',
                #{projectNumber}, '%')
            </if>
            <if test="casesNumber != null  and casesNumber != ''">and casesNumber = #{casesNumber}</if>
            <if test="casePrice != null  and casePrice != ''">and casePrice = #{casePrice}</if>
            <if test="taxRate != null  and taxRate != ''">and taxRate = #{taxRate}</if>
            <if test="transactionAmount != null  and transactionAmount != ''">and transactionAmount =
                #{transactionAmount}
            </if>
            <if test="warranty != null  and warranty != ''">and warranty = #{warranty}</if>
            <if test="laborFee != null  and laborFee != ''">and laborFee = #{laborFee}</if>
            <if test="formState != null  and formState != ''">and formState = #{formState}</if>
            <if test="currentSession != null  and currentSession != ''">and currentSession = #{currentSession}</if>
            <if test="paymentMilestones != null  and paymentMilestones != ''">and paymentMilestones =
                #{paymentMilestones}
            </if>
            <if test="milestoneAmount != null  and milestoneAmount != ''">and milestoneAmount = #{milestoneAmount}</if>
        </where>
        order by dateCreated desc
    </select>

</mapper>