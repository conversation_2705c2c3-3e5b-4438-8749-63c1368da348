<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.FinanceExpensesSummaryMapper">
    <resultMap type="com.akesobio.report.groupFinance.domain.FinanceExpensesSummary" id="FinanceExpensesSummaryResult">
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="subject" column="subject"/>
        <result property="amount" column="amount"/>
    </resultMap>

    <select id="selectFinanceExpensesSummaryList" resultMap="FinanceExpensesSummaryResult">
        select companyName,companyCode,year,month,subject,amount
        from
        (select
        a.butxt as 'companyName',
        a.rbukrs as 'companyCode',
        a.gjahr as 'year',
        a.monat as 'month',
        a.txt20 as 'subject',
        a.ksl as 'amount'
        from financial_expense_summary_table a) b
        <where>
            <if test="companyName !=null and companyName != ''">and companyName = #{companyName}</if>
            <if test="companyCode !=null and companyCode != ''">and companyCode = #{companyCode}</if>
            <if test="year !=null and year !=''">and year = #{year}</if>
            <if test="month !=null and month !=''">and month = #{month}</if>
            <if test="subject !=null and subject != ''">and subject = #{subject}</if>
            <if test="monthList !=null and monthList.size>0">
                and month in
                <foreach collection="monthList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="companyList !=null and companyList.size>0">
                and companyName in
                <foreach collection="companyList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>