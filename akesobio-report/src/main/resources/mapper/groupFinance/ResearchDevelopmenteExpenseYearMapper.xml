<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.groupFinance.mapper.ResearchDevelopmenteExpenseYearMapper">

    <resultMap type="ResearchDevelopmenteExpenseYear" id="ResearchDevelopmenteExpenseYearResult">
        <result property="id" column="id"/>
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="amount" column="amount"/>
        <result property="costType" column="costType"/>
        <result property="pipelineType" column="pipelineType"/>
        <result property="drugPipeline" column="drugPipeline"/>
        <result property="projectNumber" column="projectNumber"/>
        <result property="deleteStatus" column="deleteStatus"/>
    </resultMap>

    <sql id="selectResearchDevelopmenteExpenseYearVo">
        select id,
               companyName,
               companyCode, year, month, amount, costType, pipelineType, drugPipeline, projectNumber, deleteStatus
        from research_developmentE_expense_year
    </sql>

    <select id="selectResearchDevelopmenteExpenseYearList" parameterType="ResearchDevelopmenteExpenseYear"
            resultMap="ResearchDevelopmenteExpenseYearResult">
        <include refid="selectResearchDevelopmenteExpenseYearVo"/>
        <where>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="companyCode != null  and companyCode != ''">and companyCode like concat('%', #{companyCode},
                '%')
            </if>
            <if test="year != null ">and year = #{year}</if>
            <if test="month != null  and month != ''">and month = #{month}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="costType != null  and costType != ''">and costType = #{costType}</if>
            <if test="pipelineType != null  and pipelineType != ''">and pipelineType = #{pipelineType}</if>
            <if test="drugPipeline != null  and drugPipeline != ''">and drugPipeline = #{drugPipeline}</if>
            <if test="projectNumber != null  and projectNumber != ''">and projectNumber = #{projectNumber}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="monthList !=null and monthList.size>0">
                and month in
                <foreach collection="monthList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="companyList !=null and companyList.size>0">
                and companyName in
                <foreach collection="companyList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and deleteStatus='0'
        </where>
    </select>

    <select id="selectResearchDevelopmenteExpenseYearById" parameterType="Integer"
            resultMap="ResearchDevelopmenteExpenseYearResult">
        <include refid="selectResearchDevelopmenteExpenseYearVo"/>
        where id = #{id}
    </select>

    <insert id="insertResearchDevelopmenteExpenseYear" parameterType="ResearchDevelopmenteExpenseYear">
        insert into research_developmentE_expense_year
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="year != null">year,</if>
            <if test="month != null">month,</if>
            <if test="amount != null">amount,</if>
            <if test="costType != null">costType,</if>
            <if test="pipelineType != null">pipelineType,</if>
            <if test="drugPipeline != null">drugPipeline,</if>
            <if test="projectNumber != null">projectNumber,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="year != null">#{year},</if>
            <if test="month != null">#{month},</if>
            <if test="amount != null">#{amount},</if>
            <if test="costType != null">#{costType},</if>
            <if test="pipelineType != null">#{pipelineType},</if>
            <if test="drugPipeline != null">#{drugPipeline},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
        </trim>
    </insert>

    <update id="updateResearchDevelopmenteExpenseYear" parameterType="ResearchDevelopmenteExpenseYear">
        update research_developmentE_expense_year
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="costType != null">costType = #{costType},</if>
            <if test="pipelineType != null">pipelineType = #{pipelineType},</if>
            <if test="drugPipeline != null">drugPipeline = #{drugPipeline},</if>
            <if test="projectNumber != null">projectNumber = #{projectNumber},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResearchDevelopmenteExpenseYearById" parameterType="Integer">
        delete
        from research_developmentE_expense_year
        where id = #{id}
    </delete>

    <delete id="deleteResearchDevelopmenteExpenseYearByIds" parameterType="String">
        delete from research_developmentE_expense_year where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateAllResearchDevelopmenteExpenseYear">
        UPDATE research_developmentE_expense_year
        SET deleteStatus = 1
    </update>

    <delete id="deleteAllResearchDevelopmenteExpenseYear" parameterType="String">
        delete
        from research_developmentE_expense_year
    </delete>
</mapper>