<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sfe.mapper.SfeKtnMapper">
    
    <resultMap type="SfeKtn" id="SfeKtnResult">
        <result property="id"    column="id"    />
        <result property="date"    column="date"    />
        <result property="directorRegion"    column="director_region"    />
        <result property="region"    column="region"    />
        <result property="drugType"    column="drug_type"    />
        <result property="cervicalCancer1lNp"    column="cervical_cancer_1l_np"    />
        <result property="cervicalCancer2lNp"    column="cervical_cancer_2l_np"    />
        <result property="stomachCancer1lNp"    column="stomach_cancer_1l_np"    />
        <result property="stomachCancer2lPlusNp"    column="stomach_cancer_2l_plus_np"    />
        <result property="liverCancerHccNp"    column="liver_cancer_hcc_np"    />
        <result property="otherTumorTypesNp"    column="other_tumor_types_np"    />
        <result property="opTotal"    column="op_total"    />
        <result property="prescriptionTotalUnits"    column="prescription_total_units"    />
    </resultMap>

    <sql id="selectSfeKtnVo">
        select id, date, director_region, region, drug_type, cervical_cancer_1l_np, cervical_cancer_2l_np, stomach_cancer_1l_np, stomach_cancer_2l_plus_np, liver_cancer_hcc_np, other_tumor_types_np, op_total, prescription_total_units from sfe_ktn
    </sql>

    <select id="selectSfeKtnList" parameterType="SfeKtn" resultMap="SfeKtnResult">
        <include refid="selectSfeKtnVo"/>
        <where>  
            <if test="date != null "> and date = #{date}</if>
            <if test="directorRegion != null  and directorRegion != ''"> and director_region = #{directorRegion}</if>
            <if test="region != null  and region != ''"> and region = #{region}</if>
            <if test="drugType != null  and drugType != ''"> and drug_type = #{drugType}</if>
            <if test="cervicalCancer1lNp != null "> and cervical_cancer_1l_np = #{cervicalCancer1lNp}</if>
            <if test="cervicalCancer2lNp != null "> and cervical_cancer_2l_np = #{cervicalCancer2lNp}</if>
            <if test="stomachCancer1lNp != null "> and stomach_cancer_1l_np = #{stomachCancer1lNp}</if>
            <if test="stomachCancer2lPlusNp != null "> and stomach_cancer_2l_plus_np = #{stomachCancer2lPlusNp}</if>
            <if test="liverCancerHccNp != null "> and liver_cancer_hcc_np = #{liverCancerHccNp}</if>
            <if test="otherTumorTypesNp != null "> and other_tumor_types_np = #{otherTumorTypesNp}</if>
            <if test="opTotal != null "> and op_total = #{opTotal}</if>
            <if test="prescriptionTotalUnits != null "> and prescription_total_units = #{prescriptionTotalUnits}</if>
        </where>
    </select>
    
    <select id="selectSfeKtnById" parameterType="Long" resultMap="SfeKtnResult">
        <include refid="selectSfeKtnVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSfeKtn" parameterType="SfeKtn">
        insert into sfe_ktn
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="date != null">date,</if>
            <if test="directorRegion != null">director_region,</if>
            <if test="region != null">region,</if>
            <if test="drugType != null">drug_type,</if>
            <if test="cervicalCancer1lNp != null">cervical_cancer_1l_np,</if>
            <if test="cervicalCancer2lNp != null">cervical_cancer_2l_np,</if>
            <if test="stomachCancer1lNp != null">stomach_cancer_1l_np,</if>
            <if test="stomachCancer2lPlusNp != null">stomach_cancer_2l_plus_np,</if>
            <if test="liverCancerHccNp != null">liver_cancer_hcc_np,</if>
            <if test="otherTumorTypesNp != null">other_tumor_types_np,</if>
            <if test="opTotal != null">op_total,</if>
            <if test="prescriptionTotalUnits != null">prescription_total_units,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="date != null">#{date},</if>
            <if test="directorRegion != null">#{directorRegion},</if>
            <if test="region != null">#{region},</if>
            <if test="drugType != null">#{drugType},</if>
            <if test="cervicalCancer1lNp != null">#{cervicalCancer1lNp},</if>
            <if test="cervicalCancer2lNp != null">#{cervicalCancer2lNp},</if>
            <if test="stomachCancer1lNp != null">#{stomachCancer1lNp},</if>
            <if test="stomachCancer2lPlusNp != null">#{stomachCancer2lPlusNp},</if>
            <if test="liverCancerHccNp != null">#{liverCancerHccNp},</if>
            <if test="otherTumorTypesNp != null">#{otherTumorTypesNp},</if>
            <if test="opTotal != null">#{opTotal},</if>
            <if test="prescriptionTotalUnits != null">#{prescriptionTotalUnits},</if>
         </trim>
    </insert>

    <update id="updateSfeKtn" parameterType="SfeKtn">
        update sfe_ktn
        <trim prefix="SET" suffixOverrides=",">
            <if test="date != null">date = #{date},</if>
            <if test="directorRegion != null">director_region = #{directorRegion},</if>
            <if test="region != null">region = #{region},</if>
            <if test="drugType != null">drug_type = #{drugType},</if>
            <if test="cervicalCancer1lNp != null">cervical_cancer_1l_np = #{cervicalCancer1lNp},</if>
            <if test="cervicalCancer2lNp != null">cervical_cancer_2l_np = #{cervicalCancer2lNp},</if>
            <if test="stomachCancer1lNp != null">stomach_cancer_1l_np = #{stomachCancer1lNp},</if>
            <if test="stomachCancer2lPlusNp != null">stomach_cancer_2l_plus_np = #{stomachCancer2lPlusNp},</if>
            <if test="liverCancerHccNp != null">liver_cancer_hcc_np = #{liverCancerHccNp},</if>
            <if test="otherTumorTypesNp != null">other_tumor_types_np = #{otherTumorTypesNp},</if>
            <if test="opTotal != null">op_total = #{opTotal},</if>
            <if test="prescriptionTotalUnits != null">prescription_total_units = #{prescriptionTotalUnits},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSfeKtnById" parameterType="Long">
        delete from sfe_ktn where id = #{id}
    </delete>

    <delete id="deleteSfeKtnByIds" parameterType="String">
        delete from sfe_ktn where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>