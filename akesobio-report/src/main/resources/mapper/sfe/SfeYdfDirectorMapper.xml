<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sfe.mapper.SfeYdfDirectorMapper">
    <resultMap type="SfeYdfDirector" id="SfeYdfDirectorResult">
        <result property="id" column="id"/>
        <result property="yearMonth" column="yearMonth"/>
        <result property="quarter" column="quarter"/>
        <result property="executionDate" column="executionDate"/>
        <result property="directorArea" column="directorArea"/>
        <result property="region" column="region"/>
        <result property="egfrDay1" column="egfrDay1"/>
        <result property="egfrDay2" column="egfrDay2"/>
        <result property="faDay1" column="faDay1"/>
        <result property="faDay2" column="faDay2"/>
        <result property="otherDay" column="otherDay"/>
        <result property="totalDayNp" column="totalDayNp"/>
        <result property="totalDayOp" column="totalDayOp"/>
        <result property="prescriptionCountDay" column="prescriptionCountDay"/>
        <result property="dailyCountRanking" column="dailyCountRanking"/>
        <result property="regionMonth" column="regionMonth"/>
        <result property="npMonth" column="npMonth"/>
        <result property="opMonth" column="opMonth"/>
        <result property="prescriptionCountMonth" column="prescriptionCountMonth"/>
        <result property="monthlyAchievementRate" column="monthlyAchievementRate"/>
        <result property="monthlyAchievementRateRanking" column="monthlyAchievementRateRanking"/>
        <result property="regionQuarter" column="regionQuarter"/>
        <result property="npQuarter" column="npQuarter"/>
        <result property="opQuarter" column="opQuarter"/>
        <result property="prescriptionCountQuarter" column="prescriptionCountQuarter"/>
        <result property="quarterAchievementRate" column="quarterAchievementRate"/>
        <result property="quarterAchievementRateRanking" column="quarterAchievementRateRanking"/>
        <result property="deleteStatus" column="deleteStatus"/>
    </resultMap>

    <sql id="selectSfeYdfDirectorVo">
        select id,
               yearMonth,
               quarter,
               executionDate,
               directorArea,
               region,
               egfrDay1,
               egfrDay2,
               faDay1,
               faDay2,
               otherDay,
               totalDayNp,
               totalDayOp,
               prescriptionCountDay,
               dailyCountRanking,
               regionMonth,
               npMonth,
               opMonth,
               prescriptionCountMonth,
               monthlyAchievementRate,
               monthlyAchievementRateRanking,
               regionQuarter,
               npQuarter,
               opQuarter,
               prescriptionCountQuarter,
               quarterAchievementRate,
               quarterAchievementRateRanking,
               deleteStatus
        from sfe_ydf_director
    </sql>

    <select id="selectSfeYdfDirectorList" parameterType="SfeYdfDirector" resultMap="SfeYdfDirectorResult">
        <include refid="selectSfeYdfDirectorVo"/>
        <where>
            <if test="yearMonth != null  and yearMonth != ''">and yearMonth like concat('%', #{yearMonth}, '%')</if>
            <if test="quarter != null  and quarter != ''">and quarter like concat('%', #{quarter}, '%')</if>
            <if test="executionDate != null ">and executionDate = #{executionDate}</if>
            <if test="directorArea != null  and directorArea != ''">and directorArea like concat('%', #{directorArea},
                '%')
            </if>
            <if test="region != null  and region != ''">and region like concat('%', #{region}, '%')</if>
            <if test="egfrDay1 != null ">and egfrDay1 = #{egfrDay1}</if>
            <if test="egfrDay2 != null ">and egfrDay2 = #{egfrDay2}</if>
            <if test="faDay1 != null ">and faDay1 = #{faDay1}</if>
            <if test="faDay2 != null ">and faDay2 = #{faDay2}</if>
            <if test="otherDay != null ">and otherDay = #{otherDay}</if>
            <if test="totalDayNp != null ">and totalDayNp = #{totalDayNp}</if>
            <if test="totalDayOp != null ">and totalDayOp = #{totalDayOp}</if>
            <if test="prescriptionCountDay != null ">and prescriptionCountDay = #{prescriptionCountDay}</if>
            <if test="dailyCountRanking != null ">and dailyCountRanking = #{dailyCountRanking}</if>
            <if test="regionMonth != null  and regionMonth != ''">and regionMonth like concat('%', #{regionMonth},
                '%')
            </if>
            <if test="npMonth != null ">and npMonth = #{npMonth}</if>
            <if test="opMonth != null ">and opMonth = #{opMonth}</if>
            <if test="prescriptionCountMonth != null ">and prescriptionCountMonth = #{prescriptionCountMonth}</if>
            <if test="monthlyAchievementRate != null  and monthlyAchievementRate != ''">and monthlyAchievementRate =
                #{monthlyAchievementRate}
            </if>
            <if test="monthlyAchievementRateRanking != null ">and monthlyAchievementRateRanking =
                #{monthlyAchievementRateRanking}
            </if>
            <if test="regionQuarter != null  and regionQuarter != ''">and regionQuarter = #{regionQuarter}</if>
            <if test="npQuarter != null ">and npQuarter = #{npQuarter}</if>
            <if test="opQuarter != null ">and opQuarter = #{opQuarter}</if>
            <if test="prescriptionCountQuarter != null ">and prescriptionCountQuarter = #{prescriptionCountQuarter}</if>
            <if test="quarterAchievementRate != null  and quarterAchievementRate != ''">and quarterAchievementRate =
                #{quarterAchievementRate}
            </if>
            <if test="quarterAchievementRateRanking != null ">and quarterAchievementRateRanking =
                #{quarterAchievementRateRanking}
            </if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
        </where>
    </select>

    <select id="selectSfeYdfDirectorById" parameterType="Integer" resultMap="SfeYdfDirectorResult">
        <include refid="selectSfeYdfDirectorVo"/>
        where id = #{id}
    </select>

    <insert id="insertSfeYdfDirector" parameterType="SfeYdfDirector">
        insert into sfe_ydf_director
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="yearMonth != null">yearMonth,</if>
            <if test="quarter != null">quarter,</if>
            <if test="executionDate != null">executionDate,</if>
            <if test="directorArea != null">directorArea,</if>
            <if test="region != null">region,</if>
            <if test="egfrDay1 != null">egfrDay1,</if>
            <if test="egfrDay2 != null">egfrDay2,</if>
            <if test="faDay1 != null">faDay1,</if>
            <if test="faDay2 != null">faDay2,</if>
            <if test="otherDay != null">otherDay,</if>
            <if test="totalDayNp != null">totalDayNp,</if>
            <if test="totalDayOp != null">totalDayOp,</if>
            <if test="prescriptionCountDay != null">prescriptionCountDay,</if>
            <if test="dailyCountRanking != null">dailyCountRanking,</if>
            <if test="regionMonth != null">regionMonth,</if>
            <if test="npMonth != null">npMonth,</if>
            <if test="opMonth != null">opMonth,</if>
            <if test="prescriptionCountMonth != null">prescriptionCountMonth,</if>
            <if test="monthlyAchievementRate != null">monthlyAchievementRate,</if>
            <if test="monthlyAchievementRateRanking != null">monthlyAchievementRateRanking,</if>
            <if test="regionQuarter != null">regionQuarter,</if>
            <if test="npQuarter != null">npQuarter,</if>
            <if test="opQuarter != null">opQuarter,</if>
            <if test="prescriptionCountQuarter != null">prescriptionCountQuarter,</if>
            <if test="quarterAchievementRate != null">quarterAchievementRate,</if>
            <if test="quarterAchievementRateRanking != null">quarterAchievementRateRanking,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="yearMonth != null">#{yearMonth},</if>
            <if test="quarter != null">#{quarter},</if>
            <if test="executionDate != null">#{executionDate},</if>
            <if test="directorArea != null">#{directorArea},</if>
            <if test="region != null">#{region},</if>
            <if test="egfrDay1 != null">#{egfrDay1},</if>
            <if test="egfrDay2 != null">#{egfrDay2},</if>
            <if test="faDay1 != null">#{faDay1},</if>
            <if test="faDay2 != null">#{faDay2},</if>
            <if test="otherDay != null">#{otherDay},</if>
            <if test="totalDayNp != null">#{totalDayNp},</if>
            <if test="totalDayOp != null">#{totalDayOp},</if>
            <if test="prescriptionCountDay != null">#{prescriptionCountDay},</if>
            <if test="dailyCountRanking != null">#{dailyCountRanking},</if>
            <if test="regionMonth != null">#{regionMonth},</if>
            <if test="npMonth != null">#{npMonth},</if>
            <if test="opMonth != null">#{opMonth},</if>
            <if test="prescriptionCountMonth != null">#{prescriptionCountMonth},</if>
            <if test="monthlyAchievementRate != null">#{monthlyAchievementRate},</if>
            <if test="monthlyAchievementRateRanking != null">#{monthlyAchievementRateRanking},</if>
            <if test="regionQuarter != null">#{regionQuarter},</if>
            <if test="npQuarter != null">#{npQuarter},</if>
            <if test="opQuarter != null">#{opQuarter},</if>
            <if test="prescriptionCountQuarter != null">#{prescriptionCountQuarter},</if>
            <if test="quarterAchievementRate != null">#{quarterAchievementRate},</if>
            <if test="quarterAchievementRateRanking != null">#{quarterAchievementRateRanking},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
        </trim>
    </insert>

    <update id="updateSfeYdfDirector" parameterType="SfeYdfDirector">
        update sfe_ydf_director
        <trim prefix="SET" suffixOverrides=",">
            <if test="yearMonth != null">yearMonth = #{yearMonth},</if>
            <if test="quarter != null">quarter = #{quarter},</if>
            <if test="executionDate != null">executionDate = #{executionDate},</if>
            <if test="directorArea != null">directorArea = #{directorArea},</if>
            <if test="region != null">region = #{region},</if>
            <if test="egfrDay1 != null">egfrDay1 = #{egfrDay1},</if>
            <if test="egfrDay2 != null">egfrDay2 = #{egfrDay2},</if>
            <if test="faDay1 != null">faDay1 = #{faDay1},</if>
            <if test="faDay2 != null">faDay2 = #{faDay2},</if>
            <if test="otherDay != null">otherDay = #{otherDay},</if>
            <if test="totalDayNp != null">totalDayNp = #{totalDayNp},</if>
            <if test="totalDayOp != null">totalDayOp = #{totalDayOp},</if>
            <if test="prescriptionCountDay != null">prescriptionCountDay = #{prescriptionCountDay},</if>
            <if test="dailyCountRanking != null">dailyCountRanking = #{dailyCountRanking},</if>
            <if test="regionMonth != null">regionMonth = #{regionMonth},</if>
            <if test="npMonth != null">npMonth = #{npMonth},</if>
            <if test="opMonth != null">opMonth = #{opMonth},</if>
            <if test="prescriptionCountMonth != null">prescriptionCountMonth = #{prescriptionCountMonth},</if>
            <if test="monthlyAchievementRate != null">monthlyAchievementRate = #{monthlyAchievementRate},</if>
            <if test="monthlyAchievementRateRanking != null">monthlyAchievementRateRanking =
                #{monthlyAchievementRateRanking},
            </if>
            <if test="regionQuarter != null">regionQuarter = #{regionQuarter},</if>
            <if test="npQuarter != null">npQuarter = #{npQuarter},</if>
            <if test="opQuarter != null">opQuarter = #{opQuarter},</if>
            <if test="prescriptionCountQuarter != null">prescriptionCountQuarter = #{prescriptionCountQuarter},</if>
            <if test="quarterAchievementRate != null">quarterAchievementRate = #{quarterAchievementRate},</if>
            <if test="quarterAchievementRateRanking != null">quarterAchievementRateRanking =
                #{quarterAchievementRateRanking},
            </if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSfeYdfDirectorById" parameterType="Integer">
        delete
        from sfe_ydf_director
        where id = #{id}
    </delete>

    <delete id="deleteSfeYdfDirectorByIds" parameterType="String">
        delete from sfe_ydf_director where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>