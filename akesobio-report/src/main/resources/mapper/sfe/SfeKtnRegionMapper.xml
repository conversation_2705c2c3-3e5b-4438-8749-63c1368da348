<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sfe.mapper.SfeKtnRegionMapper">

    <resultMap type="SfeKtnRegion" id="SfeKtnRegionResult">
        <result property="id" column="id"/>
        <result property="yearMonth" column="yearMonth"/>
        <result property="executionDate" column="executionDate"/>
        <result property="directorArea" column="directorArea"/>
        <result property="region" column="region"/>
        <result property="ggDay1" column="ggDay1"/>
        <result property="ggDay2" column="ggDay2"/>
        <result property="waDay1" column="waDay1"/>
        <result property="waDay2" column="waDay2"/>
        <result property="gaDay1" column="gaDay1"/>
        <result property="otherDay" column="otherDay"/>
        <result property="totalDay" column="totalDay"/>
        <result property="ggMonth1" column="ggMonth1"/>
        <result property="ggMonth2" column="ggMonth2"/>
        <result property="waMonth1" column="waMonth1"/>
        <result property="waMonth2" column="waMonth2"/>
        <result property="gaMonth1" column="gaMonth1"/>
        <result property="otherMonth" column="otherMonth"/>
        <result property="totalMonth" column="totalMonth"/>
        <result property="monthlyRanking" column="monthlyRanking"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="regionMonth" column="regionMonth"/>
        <result property="quarter" column="quarter"/>
        <result property="regionQuarter" column="regionQuarter"/>
        <result property="ggQuarter1" column="ggQuarter1"/>
        <result property="ggQuarter2" column="ggQuarter2"/>
        <result property="waQuarter1" column="waQuarter1"/>
        <result property="waQuarter2" column="waQuarter2"/>
        <result property="gaQuarter1" column="gaQuarter1"/>
        <result property="otherQuarter" column="otherQuarter"/>
        <result property="totalQuarter" column="totalQuarter"/>
        <result property="quarterRanking" column="quarterRanking"/>
    </resultMap>

    <sql id="selectSfeKtnRegionVo">
        select id,
               yearMonth,
               executionDate,
               directorArea,
               region,
               ggDay1,
               ggDay2,
               waDay1,
               waDay2,
               gaDay1,
               otherDay,
               totalDay,
               ggMonth1,
               ggMonth2,
               waMonth1,
               waMonth2,
               gaMonth1,
               otherMonth,
               totalMonth,
               monthlyRanking,
               deleteStatus,
               regionMonth,
               quarter,
               regionQuarter,
               ggQuarter1,
               ggQuarter2,
               waQuarter1,
               waQuarter2,
               gaQuarter1,
               otherQuarter,
               totalQuarter,
               quarterRanking
        from sfe_ktn_region
    </sql>

    <select id="selectSfeKtnRegionList" parameterType="SfeKtnRegion" resultMap="SfeKtnRegionResult">
        <include refid="selectSfeKtnRegionVo"/>
        <where>
            <if test="yearMonth != null  and yearMonth != ''">and yearMonth like concat('%', #{yearMonth}, '%')</if>
            <if test="executionDate != null ">and executionDate = #{executionDate}</if>
            <if test="directorArea != null  and directorArea != ''">and directorArea like concat('%', #{directorArea},
                '%')
            </if>
            <if test="region != null  and region != ''">and region like concat('%', #{region}, '%')</if>
            <if test="ggDay1 != null ">and ggDay1 = #{ggDay1}</if>
            <if test="ggDay2 != null ">and ggDay2 = #{ggDay2}</if>
            <if test="waDay1 != null ">and waDay1 = #{waDay1}</if>
            <if test="waDay2 != null ">and waDay2 = #{waDay2}</if>
            <if test="gaDay1 != null ">and gaDay1 = #{gaDay1}</if>
            <if test="otherDay != null ">and otherDay = #{otherDay}</if>
            <if test="totalDay != null ">and totalDay = #{totalDay}</if>
            <if test="ggMonth1 != null ">and ggMonth1 = #{ggMonth1}</if>
            <if test="ggMonth2 != null ">and ggMonth2 = #{ggMonth2}</if>
            <if test="waMonth1 != null ">and waMonth1 = #{waMonth1}</if>
            <if test="waMonth2 != null ">and waMonth2 = #{waMonth2}</if>
            <if test="gaMonth1 != null ">and gaMonth1 = #{gaMonth1}</if>
            <if test="otherMonth != null ">and otherMonth = #{otherMonth}</if>
            <if test="totalMonth != null ">and totalMonth = #{totalMonth}</if>
            <if test="monthlyRanking != null ">and monthlyRanking = #{monthlyRanking}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="regionMonth != null  and regionMonth != ''">and regionMonth like concat('%', #{regionMonth},
                '%')
            </if>
            <if test="quarter != null  and quarter != ''">and quarter = #{quarter}</if>
            <if test="regionQuarter != null  and regionQuarter != ''">and regionQuarter = #{regionQuarter}</if>
            <if test="ggQuarter1 != null ">and ggQuarter1 = #{ggQuarter1}</if>
            <if test="ggQuarter2 != null ">and ggQuarter2 = #{ggQuarter2}</if>
            <if test="waQuarter1 != null ">and waQuarter1 = #{waQuarter1}</if>
            <if test="waQuarter2 != null ">and waQuarter2 = #{waQuarter2}</if>
            <if test="gaQuarter1 != null ">and gaQuarter1 = #{gaQuarter1}</if>
            <if test="otherQuarter != null ">and otherQuarter = #{otherQuarter}</if>
            <if test="totalQuarter != null ">and totalQuarter = #{totalQuarter}</if>
            <if test="quarterRanking != null ">and quarterRanking = #{quarterRanking}</if>
        </where>
    </select>

    <select id="selectSfeKtnRegionById" parameterType="Long" resultMap="SfeKtnRegionResult">
        <include refid="selectSfeKtnRegionVo"/>
        where id = #{id}
    </select>

    <insert id="insertSfeKtnRegion" parameterType="SfeKtnRegion">
        insert into sfe_ktn_region
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="yearMonth != null">yearMonth,</if>
            <if test="executionDate != null">executionDate,</if>
            <if test="directorArea != null">directorArea,</if>
            <if test="region != null">region,</if>
            <if test="ggDay1 != null">ggDay1,</if>
            <if test="ggDay2 != null">ggDay2,</if>
            <if test="waDay1 != null">waDay1,</if>
            <if test="waDay2 != null">waDay2,</if>
            <if test="gaDay1 != null">gaDay1,</if>
            <if test="otherDay != null">otherDay,</if>
            <if test="totalDay != null">totalDay,</if>
            <if test="ggMonth1 != null">ggMonth1,</if>
            <if test="ggMonth2 != null">ggMonth2,</if>
            <if test="waMonth1 != null">waMonth1,</if>
            <if test="waMonth2 != null">waMonth2,</if>
            <if test="gaMonth1 != null">gaMonth1,</if>
            <if test="otherMonth != null">otherMonth,</if>
            <if test="totalMonth != null">totalMonth,</if>
            <if test="monthlyRanking != null">monthlyRanking,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="regionMonth != null">regionMonth,</if>
            <if test="quarter != null">quarter,</if>
            <if test="regionQuarter != null">regionQuarter,</if>
            <if test="ggQuarter1 != null">ggQuarter1,</if>
            <if test="ggQuarter2 != null">ggQuarter2,</if>
            <if test="waQuarter1 != null">waQuarter1,</if>
            <if test="waQuarter2 != null">waQuarter2,</if>
            <if test="gaQuarter1 != null">gaQuarter1,</if>
            <if test="otherQuarter != null">otherQuarter,</if>
            <if test="totalQuarter != null">totalQuarter,</if>
            <if test="quarterRanking != null">quarterRanking,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="yearMonth != null">#{yearMonth},</if>
            <if test="executionDate != null">#{executionDate},</if>
            <if test="directorArea != null">#{directorArea},</if>
            <if test="region != null">#{region},</if>
            <if test="ggDay1 != null">#{ggDay1},</if>
            <if test="ggDay2 != null">#{ggDay2},</if>
            <if test="waDay1 != null">#{waDay1},</if>
            <if test="waDay2 != null">#{waDay2},</if>
            <if test="gaDay1 != null">#{gaDay1},</if>
            <if test="otherDay != null">#{otherDay},</if>
            <if test="totalDay != null">#{totalDay},</if>
            <if test="ggMonth1 != null">#{ggMonth1},</if>
            <if test="ggMonth2 != null">#{ggMonth2},</if>
            <if test="waMonth1 != null">#{waMonth1},</if>
            <if test="waMonth2 != null">#{waMonth2},</if>
            <if test="gaMonth1 != null">#{gaMonth1},</if>
            <if test="otherMonth != null">#{otherMonth},</if>
            <if test="totalMonth != null">#{totalMonth},</if>
            <if test="monthlyRanking != null">#{monthlyRanking},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="regionMonth != null">#{regionMonth},</if>
            <if test="quarter != null">#{quarter},</if>
            <if test="regionQuarter != null">#{regionQuarter},</if>
            <if test="ggQuarter1 != null">#{ggQuarter1},</if>
            <if test="ggQuarter2 != null">#{ggQuarter2},</if>
            <if test="waQuarter1 != null">#{waQuarter1},</if>
            <if test="waQuarter2 != null">#{waQuarter2},</if>
            <if test="gaQuarter1 != null">#{gaQuarter1},</if>
            <if test="otherQuarter != null">#{otherQuarter},</if>
            <if test="totalQuarter != null">#{totalQuarter},</if>
            <if test="quarterRanking != null">#{quarterRanking},</if>
        </trim>
    </insert>

    <update id="updateSfeKtnRegion" parameterType="SfeKtnRegion">
        update sfe_ktn_region
        <trim prefix="SET" suffixOverrides=",">
            <if test="yearMonth != null">yearMonth = #{yearMonth},</if>
            <if test="executionDate != null">executionDate = #{executionDate},</if>
            <if test="directorArea != null">directorArea = #{directorArea},</if>
            <if test="region != null">region = #{region},</if>
            <if test="ggDay1 != null">ggDay1 = #{ggDay1},</if>
            <if test="ggDay2 != null">ggDay2 = #{ggDay2},</if>
            <if test="waDay1 != null">waDay1 = #{waDay1},</if>
            <if test="waDay2 != null">waDay2 = #{waDay2},</if>
            <if test="gaDay1 != null">gaDay1 = #{gaDay1},</if>
            <if test="otherDay != null">otherDay = #{otherDay},</if>
            <if test="totalDay != null">totalDay = #{totalDay},</if>
            <if test="ggMonth1 != null">ggMonth1 = #{ggMonth1},</if>
            <if test="ggMonth2 != null">ggMonth2 = #{ggMonth2},</if>
            <if test="waMonth1 != null">waMonth1 = #{waMonth1},</if>
            <if test="waMonth2 != null">waMonth2 = #{waMonth2},</if>
            <if test="gaMonth1 != null">gaMonth1 = #{gaMonth1},</if>
            <if test="otherMonth != null">otherMonth = #{otherMonth},</if>
            <if test="totalMonth != null">totalMonth = #{totalMonth},</if>
            <if test="monthlyRanking != null">monthlyRanking = #{monthlyRanking},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
            <if test="regionMonth != null">regionMonth = #{regionMonth},</if>
            <if test="quarter != null">quarter = #{quarter},</if>
            <if test="regionQuarter != null">regionQuarter = #{regionQuarter},</if>
            <if test="ggQuarter1 != null">ggQuarter1 = #{ggQuarter1},</if>
            <if test="ggQuarter2 != null">ggQuarter2 = #{ggQuarter2},</if>
            <if test="waQuarter1 != null">waQuarter1 = #{waQuarter1},</if>
            <if test="waQuarter2 != null">waQuarter2 = #{waQuarter2},</if>
            <if test="gaQuarter1 != null">gaQuarter1 = #{gaQuarter1},</if>
            <if test="otherQuarter != null">otherQuarter = #{otherQuarter},</if>
            <if test="totalQuarter != null">totalQuarter = #{totalQuarter},</if>
            <if test="quarterRanking != null">quarterRanking = #{quarterRanking},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSfeKtnRegionById" parameterType="Long">
        delete
        from sfe_ktn_region
        where id = #{id}
    </delete>

    <delete id="deleteSfeKtnRegionByIds" parameterType="String">
        delete from sfe_ktn_region where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>