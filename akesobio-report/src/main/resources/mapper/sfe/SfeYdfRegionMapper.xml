<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sfe.mapper.SfeYdfRegionMapper">
    <resultMap type="SfeYdfRegion" id="SfeYdfRegionResult">
        <result property="id" column="id"/>
        <result property="yearMonth" column="yearMonth"/>
        <result property="executionDate" column="executionDate"/>
        <result property="directorArea" column="directorArea"/>
        <result property="region" column="region"/>
        <result property="egfrDay" column="egfrDay"/>
        <result property="egfrDay1" column="egfrDay1"/>
        <result property="lcDay" column="lcDay"/>
        <result property="lcDay1" column="lcDay1"/>
        <result property="otherDay" column="otherDay"/>
        <result property="totalDay" column="totalDay"/>
        <result property="regionMonth" column="regionMonth"/>
        <result property="egfrMonth" column="egfrMonth"/>
        <result property="egfrMonth1" column="egfrMonth1"/>
        <result property="lcMonth" column="lcMonth"/>
        <result property="lcMonth1" column="lcMonth1"/>
        <result property="otherMonth" column="otherMonth"/>
        <result property="totalMonth" column="totalMonth"/>
        <result property="monthlyRanking" column="monthlyRanking"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="quarter" column="quarter"/>
        <result property="regionQuarter" column="regionQuarter"/>
        <result property="egfrQuarter" column="egfrQuarter"/>
        <result property="egfrQuarter1" column="egfrQuarter1"/>
        <result property="lcQuarter" column="lcQuarter"/>
        <result property="lcQuarter1" column="lcQuarter1"/>
        <result property="otherQuarter" column="otherQuarter"/>
        <result property="totalQuarter" column="totalQuarter"/>
        <result property="quarterRanking" column="quarterRanking"/>
    </resultMap>

    <sql id="selectSfeYdfRegionVo">
        select id,
               yearMonth,
               executionDate,
               directorArea,
               region,
               egfrDay,
               egfrDay1,
               lcDay,
               lcDay1,
               otherDay,
               totalDay,
               regionMonth,
               egfrMonth,
               egfrMonth1,
               lcMonth,
               lcMonth1,
               otherMonth,
               totalMonth,
               monthlyRanking,
               deleteStatus,
               quarter,
               regionQuarter,
               egfrQuarter,
               egfrQuarter1,
               lcQuarter,
               lcQuarter1,
               otherQuarter,
               totalQuarter,
               quarterRanking
        from sfe_ydf_region
    </sql>

    <select id="selectSfeYdfRegionList" parameterType="SfeYdfRegion" resultMap="SfeYdfRegionResult">
        <include refid="selectSfeYdfRegionVo"/>
        <where>
            <if test="yearMonth != null  and yearMonth != ''">and yearMonth like concat('%', #{yearMonth}, '%')</if>
            <if test="executionDate != null ">and executionDate = #{executionDate}</if>
            <if test="directorArea != null  and directorArea != ''">and directorArea like concat('%', #{directorArea},
                '%')
            </if>
            <if test="region != null  and region != ''">and region like concat('%', #{region}, '%')</if>
            <if test="egfrDay != null ">and egfrDay = #{egfrDay}</if>
            <if test="egfrDay1 != null ">and egfrDay1 = #{egfrDay1}</if>
            <if test="lcDay != null ">and lcDay = #{lcDay}</if>
            <if test="lcDay1 != null ">and lcDay1 = #{lcDay1}</if>
            <if test="otherDay != null ">and otherDay = #{otherDay}</if>
            <if test="totalDay != null ">and totalDay = #{totalDay}</if>
            <if test="regionMonth != null  and regionMonth != ''">and regionMonth = #{regionMonth}</if>
            <if test="egfrMonth != null ">and egfrMonth = #{egfrMonth}</if>
            <if test="egfrMonth1 != null ">and egfrMonth1 = #{egfrMonth1}</if>
            <if test="lcMonth != null ">and lcMonth = #{lcMonth}</if>
            <if test="lcMonth1 != null ">and lcMonth1 = #{lcMonth1}</if>
            <if test="otherMonth != null ">and otherMonth = #{otherMonth}</if>
            <if test="totalMonth != null ">and totalMonth = #{totalMonth}</if>
            <if test="monthlyRanking != null ">and monthlyRanking = #{monthlyRanking}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="quarter != null  and quarter != ''">and quarter = #{quarter}</if>
            <if test="regionQuarter != null  and regionQuarter != ''">and regionQuarter = #{regionQuarter}</if>
            <if test="egfrQuarter != null ">and egfrQuarter = #{egfrQuarter}</if>
            <if test="egfrQuarter1 != null ">and egfrQuarter1 = #{egfrQuarter1}</if>
            <if test="lcQuarter != null ">and lcQuarter = #{lcQuarter}</if>
            <if test="lcQuarter1 != null ">and lcQuarter1 = #{lcQuarter1}</if>
            <if test="otherQuarter != null ">and otherQuarter = #{otherQuarter}</if>
            <if test="totalQuarter != null ">and totalQuarter = #{totalQuarter}</if>
            <if test="quarterRanking != null ">and quarterRanking = #{quarterRanking}</if>
        </where>
    </select>

    <select id="selectSfeYdfRegionById" parameterType="Long" resultMap="SfeYdfRegionResult">
        <include refid="selectSfeYdfRegionVo"/>
        where id = #{id}
    </select>

    <insert id="insertSfeYdfRegion" parameterType="SfeYdfRegion">
        insert into sfe_ydf_region
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="yearMonth != null">yearMonth,</if>
            <if test="executionDate != null">executionDate,</if>
            <if test="directorArea != null">directorArea,</if>
            <if test="region != null">region,</if>
            <if test="egfrDay != null">egfrDay,</if>
            <if test="egfrDay1 != null">egfrDay1,</if>
            <if test="lcDay != null">lcDay,</if>
            <if test="lcDay1 != null">lcDay1,</if>
            <if test="otherDay != null">otherDay,</if>
            <if test="totalDay != null">totalDay,</if>
            <if test="regionMonth != null">regionMonth,</if>
            <if test="egfrMonth != null">egfrMonth,</if>
            <if test="egfrMonth1 != null">egfrMonth1,</if>
            <if test="lcMonth != null">lcMonth,</if>
            <if test="lcMonth1 != null">lcMonth1,</if>
            <if test="otherMonth != null">otherMonth,</if>
            <if test="totalMonth != null">totalMonth,</if>
            <if test="monthlyRanking != null">monthlyRanking,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="quarter != null">quarter,</if>
            <if test="regionQuarter != null">regionQuarter,</if>
            <if test="egfrQuarter != null">egfrQuarter,</if>
            <if test="egfrQuarter1 != null">egfrQuarter1,</if>
            <if test="lcQuarter != null">lcQuarter,</if>
            <if test="lcQuarter1 != null">lcQuarter1,</if>
            <if test="otherQuarter != null">otherQuarter,</if>
            <if test="totalQuarter != null">totalQuarter,</if>
            <if test="quarterRanking != null">quarterRanking,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="yearMonth != null">#{yearMonth},</if>
            <if test="executionDate != null">#{executionDate},</if>
            <if test="directorArea != null">#{directorArea},</if>
            <if test="region != null">#{region},</if>
            <if test="egfrDay != null">#{egfrDay},</if>
            <if test="egfrDay1 != null">#{egfrDay1},</if>
            <if test="lcDay != null">#{lcDay},</if>
            <if test="lcDay1 != null">#{lcDay1},</if>
            <if test="otherDay != null">#{otherDay},</if>
            <if test="totalDay != null">#{totalDay},</if>
            <if test="regionMonth != null">#{regionMonth},</if>
            <if test="egfrMonth != null">#{egfrMonth},</if>
            <if test="egfrMonth1 != null">#{egfrMonth1},</if>
            <if test="lcMonth != null">#{lcMonth},</if>
            <if test="lcMonth1 != null">#{lcMonth1},</if>
            <if test="otherMonth != null">#{otherMonth},</if>
            <if test="totalMonth != null">#{totalMonth},</if>
            <if test="monthlyRanking != null">#{monthlyRanking},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="quarter != null">#{quarter},</if>
            <if test="regionQuarter != null">#{regionQuarter},</if>
            <if test="egfrQuarter != null">#{egfrQuarter},</if>
            <if test="egfrQuarter1 != null">#{egfrQuarter1},</if>
            <if test="lcQuarter != null">#{lcQuarter},</if>
            <if test="lcQuarter1 != null">#{lcQuarter1},</if>
            <if test="otherQuarter != null">#{otherQuarter},</if>
            <if test="totalQuarter != null">#{totalQuarter},</if>
            <if test="quarterRanking != null">#{quarterRanking},</if>
        </trim>
    </insert>

    <update id="updateSfeYdfRegion" parameterType="SfeYdfRegion">
        update sfe_ydf_region
        <trim prefix="SET" suffixOverrides=",">
            <if test="yearMonth != null">yearMonth = #{yearMonth},</if>
            <if test="executionDate != null">executionDate = #{executionDate},</if>
            <if test="directorArea != null">directorArea = #{directorArea},</if>
            <if test="region != null">region = #{region},</if>
            <if test="egfrDay != null">egfrDay = #{egfrDay},</if>
            <if test="egfrDay1 != null">egfrDay1 = #{egfrDay1},</if>
            <if test="lcDay != null">lcDay = #{lcDay},</if>
            <if test="lcDay1 != null">lcDay1 = #{lcDay1},</if>
            <if test="otherDay != null">otherDay = #{otherDay},</if>
            <if test="totalDay != null">totalDay = #{totalDay},</if>
            <if test="regionMonth != null">regionMonth = #{regionMonth},</if>
            <if test="egfrMonth != null">egfrMonth = #{egfrMonth},</if>
            <if test="egfrMonth1 != null">egfrMonth1 = #{egfrMonth1},</if>
            <if test="lcMonth != null">lcMonth = #{lcMonth},</if>
            <if test="lcMonth1 != null">lcMonth1 = #{lcMonth1},</if>
            <if test="otherMonth != null">otherMonth = #{otherMonth},</if>
            <if test="totalMonth != null">totalMonth = #{totalMonth},</if>
            <if test="monthlyRanking != null">monthlyRanking = #{monthlyRanking},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
            <if test="quarter != null">quarter = #{quarter},</if>
            <if test="regionQuarter != null">regionQuarter = #{regionQuarter},</if>
            <if test="egfrQuarter != null">egfrQuarter = #{egfrQuarter},</if>
            <if test="egfrQuarter1 != null">egfrQuarter1 = #{egfrQuarter1},</if>
            <if test="lcQuarter != null">lcQuarter = #{lcQuarter},</if>
            <if test="lcQuarter1 != null">lcQuarter1 = #{lcQuarter1},</if>
            <if test="otherQuarter != null">otherQuarter = #{otherQuarter},</if>
            <if test="totalQuarter != null">totalQuarter = #{totalQuarter},</if>
            <if test="quarterRanking != null">quarterRanking = #{quarterRanking},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSfeYdfRegionById" parameterType="Long">
        delete
        from sfe_ydf_region
        where id = #{id}
    </delete>

    <delete id="deleteSfeYdfRegionByIds" parameterType="String">
        delete from sfe_ydf_region where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>