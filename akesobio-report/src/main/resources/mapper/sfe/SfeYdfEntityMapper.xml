<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sfe.mapper.SfeYdfEntityMapper">
    <resultMap type="SfeYdfEntity" id="SfeYdfEntityResult">
        <result property="executionDate" column="executionDate"/>
        <result property="directorArea" column="directorArea"/>
        <result property="region" column="region"/>
        <result property="egfrDay" column="egfrDay"/>
        <result property="egfrDay1" column="egfrDay1"/>
        <result property="lcDay" column="lcDay"/>
        <result property="lcDay1" column="lcDay1"/>
        <result property="otherDay" column="otherDay"/>
        <result property="totalOp" column="totalOp"/>
        <result property="totalNumber" column="totalNumber"/>
    </resultMap>

    <select id="selectSfeYdfList" parameterType="SfeYdfEntity" resultMap="SfeYdfEntityResult">
        SELECT
        executionDate,
        directorArea,
        region,
        egfrDay,
        egfrDay1,
        lcDay,
        lcDay1,
        otherDay,
        totalOp,
        totalNumber
        FROM
        (
        SELECT
        a.date 'executionDate',
        a.director_region 'directorArea',
        a.region 'region',
        a.egfr_tki2l_np 'egfrDay',
        a.egfr_tki_hl_np 'egfrDay1',
        a.lung_cancer_wild1l_np 'lcDay',
        a.lung_cancer_other_np 'lcDay1',
        a.other_tumor_types_np 'otherDay',
        a.op_total 'totalOp',
        a.prescription_total_units 'totalNumber'
        FROM
        sfe_ydf a
        WHERE
        enabled = 'Y'
        ) b
        <where>
            <if test="executionDate != null  and executionDate != ''">and executionDate like concat('%',
                #{executionDate}, '%')
            </if>
            <if test="directorArea != null  and directorArea != ''">and directorArea =#{directorArea}</if>
            <if test="region != null  and region != ''">and region =#{region}</if>
        </where>
    </select>

    <select id="selectSfeYdfMonthList" parameterType="SfeYdfEntity" resultMap="SfeYdfEntityResult">
        SELECT
        executionDate,
        directorArea,
        region,
        egfrDay,
        egfrDay1,
        lcDay,
        lcDay1,
        otherDay,
        totalOp,
        totalNumber
        FROM
        (
        SELECT
        a.date 'executionDate',
        a.director_region 'directorArea',
        a.region 'region',
        a.egfr_tki2l_np 'egfrDay',
        a.egfr_tki_hl_np 'egfrDay1',
        a.lung_cancer_wild1l_np 'lcDay',
        a.lung_cancer_other_np 'lcDay1',
        a.other_tumor_types_np 'otherDay',
        a.op_total 'totalOp',
        a.prescription_total_units 'totalNumber'
        FROM
        sfe_ydf a
        WHERE
        enabled = 'Y'
        ) b
        <where>
            <if test="executionDate != null  and executionDate != ''">and executionDate like concat('%',
                #{executionDate}, '%')
            </if>
            <if test="directorArea != null  and directorArea != ''">and directorArea =#{directorArea}</if>
            <if test="region != null  and region != ''">and region =#{region}</if>
        </where>
    </select>

    <select id="selectSfeYdfQuarterList" parameterType="SfeYdfEntity" resultMap="SfeYdfEntityResult">
        SELECT
        executionDate,
        directorArea,
        region,
        egfrDay,
        egfrDay1,
        lcDay,
        lcDay1,
        otherDay,
        totalOp,
        totalNumber
        FROM
        (
        SELECT
        a.date 'executionDate',
        a.director_region 'directorArea',
        a.region 'region',
        a.egfr_tki2l_np 'egfrDay',
        a.egfr_tki_hl_np 'egfrDay1',
        a.lung_cancer_wild1l_np 'lcDay',
        a.lung_cancer_other_np 'lcDay1',
        a.other_tumor_types_np 'otherDay',
        a.op_total 'totalOp',
        a.prescription_total_units 'totalNumber'
        FROM
        sfe_ydf a
        WHERE
        enabled = 'Y'
        ) b
        <where>
            <if test="params.beginQuarterDate != null and params.beginQuarterDate != '' and params.endQuarterDate != null and params.endQuarterDate != ''">
                and executionDate between #{params.beginQuarterDate} and #{params.endQuarterDate}
            </if>
            <if test="directorArea != null  and directorArea != ''">and directorArea =#{directorArea}</if>
            <if test="region != null  and region != ''">and region =#{region}</if>
        </where>
    </select>

</mapper>