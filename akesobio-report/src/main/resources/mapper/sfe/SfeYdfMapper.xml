<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sfe.mapper.SfeYdfMapper">
    
    <resultMap type="SfeYdf" id="SfeYdfResult">
        <result property="id"    column="id"    />
        <result property="date"    column="date"    />
        <result property="directorRegion"    column="director_region"    />
        <result property="region"    column="region"    />
        <result property="drugType"    column="drug_type"    />
        <result property="egfrTki2lNp"    column="egfr_tki_2l_np"    />
        <result property="egfrTkiHlNp"    column="egfr_tki_hl_np"    />
        <result property="lungCancerWild1lNp"    column="lung_cancer_wild_1l_np"    />
        <result property="lungCancerOtherNp"    column="lung_cancer_other_np"    />
        <result property="otherTumorTypesNp"    column="other_tumor_types_np"    />
        <result property="opTotal"    column="op_total"    />
        <result property="prescriptionTotalUnits"    column="prescription_total_units"    />
    </resultMap>

    <sql id="selectSfeYdfVo">
        select id, date, director_region, region, drug_type, egfr_tki_2l_np, egfr_tki_hl_np, lung_cancer_wild_1l_np, lung_cancer_other_np, other_tumor_types_np, op_total, prescription_total_units from sfe_ydf
    </sql>

    <select id="selectSfeYdfList" parameterType="SfeYdf" resultMap="SfeYdfResult">
        <include refid="selectSfeYdfVo"/>
        <where>  
            <if test="date != null "> and date = #{date}</if>
            <if test="directorRegion != null  and directorRegion != ''"> and director_region = #{directorRegion}</if>
            <if test="region != null  and region != ''"> and region = #{region}</if>
            <if test="drugType != null  and drugType != ''"> and drug_type = #{drugType}</if>
            <if test="egfrTki2lNp != null "> and egfr_tki_2l_np = #{egfrTki2lNp}</if>
            <if test="egfrTkiHlNp != null "> and egfr_tki_hl_np = #{egfrTkiHlNp}</if>
            <if test="lungCancerWild1lNp != null "> and lung_cancer_wild_1l_np = #{lungCancerWild1lNp}</if>
            <if test="lungCancerOtherNp != null "> and lung_cancer_other_np = #{lungCancerOtherNp}</if>
            <if test="otherTumorTypesNp != null "> and other_tumor_types_np = #{otherTumorTypesNp}</if>
            <if test="opTotal != null "> and op_total = #{opTotal}</if>
            <if test="prescriptionTotalUnits != null "> and prescription_total_units = #{prescriptionTotalUnits}</if>
        </where>
    </select>
    
    <select id="selectSfeYdfById" parameterType="Long" resultMap="SfeYdfResult">
        <include refid="selectSfeYdfVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSfeYdf" parameterType="SfeYdf">
        insert into sfe_ydf
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="date != null">date,</if>
            <if test="directorRegion != null">director_region,</if>
            <if test="region != null">region,</if>
            <if test="drugType != null">drug_type,</if>
            <if test="egfrTki2lNp != null">egfr_tki_2l_np,</if>
            <if test="egfrTkiHlNp != null">egfr_tki_hl_np,</if>
            <if test="lungCancerWild1lNp != null">lung_cancer_wild_1l_np,</if>
            <if test="lungCancerOtherNp != null">lung_cancer_other_np,</if>
            <if test="otherTumorTypesNp != null">other_tumor_types_np,</if>
            <if test="opTotal != null">op_total,</if>
            <if test="prescriptionTotalUnits != null">prescription_total_units,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="date != null">#{date},</if>
            <if test="directorRegion != null">#{directorRegion},</if>
            <if test="region != null">#{region},</if>
            <if test="drugType != null">#{drugType},</if>
            <if test="egfrTki2lNp != null">#{egfrTki2lNp},</if>
            <if test="egfrTkiHlNp != null">#{egfrTkiHlNp},</if>
            <if test="lungCancerWild1lNp != null">#{lungCancerWild1lNp},</if>
            <if test="lungCancerOtherNp != null">#{lungCancerOtherNp},</if>
            <if test="otherTumorTypesNp != null">#{otherTumorTypesNp},</if>
            <if test="opTotal != null">#{opTotal},</if>
            <if test="prescriptionTotalUnits != null">#{prescriptionTotalUnits},</if>
         </trim>
    </insert>

    <update id="updateSfeYdf" parameterType="SfeYdf">
        update sfe_ydf
        <trim prefix="SET" suffixOverrides=",">
            <if test="date != null">date = #{date},</if>
            <if test="directorRegion != null">director_region = #{directorRegion},</if>
            <if test="region != null">region = #{region},</if>
            <if test="drugType != null">drug_type = #{drugType},</if>
            <if test="egfrTki2lNp != null">egfr_tki_2l_np = #{egfrTki2lNp},</if>
            <if test="egfrTkiHlNp != null">egfr_tki_hl_np = #{egfrTkiHlNp},</if>
            <if test="lungCancerWild1lNp != null">lung_cancer_wild_1l_np = #{lungCancerWild1lNp},</if>
            <if test="lungCancerOtherNp != null">lung_cancer_other_np = #{lungCancerOtherNp},</if>
            <if test="otherTumorTypesNp != null">other_tumor_types_np = #{otherTumorTypesNp},</if>
            <if test="opTotal != null">op_total = #{opTotal},</if>
            <if test="prescriptionTotalUnits != null">prescription_total_units = #{prescriptionTotalUnits},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSfeYdfById" parameterType="Long">
        delete from sfe_ydf where id = #{id}
    </delete>

    <delete id="deleteSfeYdfByIds" parameterType="String">
        delete from sfe_ydf where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>