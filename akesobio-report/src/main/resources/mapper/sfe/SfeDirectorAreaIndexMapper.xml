<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sfe.mapper.SfeDirectorAreaIndexMapper">

    <resultMap type="SfeDirectorAreaIndex" id="SfeDirectorAreaIndexResult">
        <result property="id" column="id"/>
        <result property="directorArea" column="directorArea"/>
        <result property="productName" column="productName"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="monthlyIndicatorCount" column="monthlyIndicatorCount"/>
        <result property="monthlyIndicatorAmount" column="monthlyIndicatorAmount"/>
        <result property="deleteStatus" column="deleteStatus"/>
    </resultMap>

    <sql id="selectSfeDirectorAreaIndexVo">
        select id, directorArea, productName, year, month, monthlyIndicatorCount, monthlyIndicatorAmount, deleteStatus
        from sfe_director_area_index
    </sql>

    <select id="selectSfeDirectorAreaIndexList" parameterType="SfeDirectorAreaIndex"
            resultMap="SfeDirectorAreaIndexResult">
        <include refid="selectSfeDirectorAreaIndexVo"/>
        <where>
            <if test="directorArea != null  and directorArea != ''">and directorArea like concat('%', #{directorArea},
                '%')
            </if>
            <if test="productName != null  and productName != ''">and productName like concat('%', #{productName},
                '%')
            </if>
            <if test="year != null ">and year = #{year}</if>
            <if test="month != null ">and month = #{month}</if>
            <if test="monthlyIndicatorCount != null ">and monthlyIndicatorCount = #{monthlyIndicatorCount}</if>
            <if test="monthlyIndicatorAmount != null ">and monthlyIndicatorAmount = #{monthlyIndicatorAmount}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
        </where>
    </select>

    <select id="selectSfeDirectorAreaIndexById" parameterType="Integer" resultMap="SfeDirectorAreaIndexResult">
        <include refid="selectSfeDirectorAreaIndexVo"/>
        where id = #{id}
    </select>

    <insert id="insertSfeDirectorAreaIndex" parameterType="SfeDirectorAreaIndex">
        insert into sfe_director_area_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="directorArea != null">directorArea,</if>
            <if test="productName != null">productName,</if>
            <if test="year != null">year,</if>
            <if test="month != null">month,</if>
            <if test="monthlyIndicatorCount != null">monthlyIndicatorCount,</if>
            <if test="monthlyIndicatorAmount != null">monthlyIndicatorAmount,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="directorArea != null">#{directorArea},</if>
            <if test="productName != null">#{productName},</if>
            <if test="year != null">#{year},</if>
            <if test="month != null">#{month},</if>
            <if test="monthlyIndicatorCount != null">#{monthlyIndicatorCount},</if>
            <if test="monthlyIndicatorAmount != null">#{monthlyIndicatorAmount},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
        </trim>
    </insert>

    <update id="updateSfeDirectorAreaIndex" parameterType="SfeDirectorAreaIndex">
        update sfe_director_area_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="directorArea != null">directorArea = #{directorArea},</if>
            <if test="productName != null">productName = #{productName},</if>
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="monthlyIndicatorCount != null">monthlyIndicatorCount = #{monthlyIndicatorCount},</if>
            <if test="monthlyIndicatorAmount != null">monthlyIndicatorAmount = #{monthlyIndicatorAmount},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSfeDirectorAreaIndexById" parameterType="Integer">
        delete
        from sfe_director_area_index
        where id = #{id}
    </delete>

    <delete id="deleteSfeDirectorAreaIndexByIds" parameterType="String">
        delete from sfe_director_area_index where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>