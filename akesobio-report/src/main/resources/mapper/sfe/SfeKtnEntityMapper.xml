<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sfe.mapper.SfeKtnEntityMapper">
    <resultMap type="SfeKtnEntity" id="SfeKtnEntityResult">
        <result property="executionDate" column="executionDate"/>
        <result property="directorArea" column="directorArea"/>
        <result property="region" column="region"/>
        <result property="ggDay1" column="ggDay1"/>
        <result property="ggDay2" column="ggDay2"/>
        <result property="waDay1" column="waDay1"/>
        <result property="waDay2" column="waDay2"/>
        <result property="gaDay1" column="gaDay1"/>
        <result property="otherDay" column="otherDay"/>
        <result property="totalOp" column="totalOp"/>
        <result property="totalNumber" column="totalNumber"/>
    </resultMap>

    <select id="selectSfeKtnList" parameterType="SfeKtnEntity" resultMap="SfeKtnEntityResult">
        SELECT
        executionDate,
        directorArea,
        region,
        ggDay1,
        ggDay2,
        waDay1,
        waDay2,
        gaDay1,
        totalOp,
        totalNumber
        FROM
        (
        SELECT
        a.date 'executionDate',
        a.director_region 'directorArea',
        a.region 'region',
        a.cervical_cancer1l_np 'ggDay1',
        a.cervical_cancer2l_np 'ggDay2',
        a.stomach_cancer1l_np 'waDay1',
        a.stomach_cancer2l_plus_np 'waDay2',
        a.liver_cancer_hcc_np 'gaDay1',
        a.other_tumor_types_np 'otherDay',
        a.op_total 'totalOp',
        a.prescription_total_units 'totalNumber'
        FROM
        sfe_ktn a
        WHERE
        enabled = 'Y'
        ) b
        <where>
            <if test="executionDate != null  and executionDate != ''">and executionDate like concat('%',
                #{executionDate}, '%')
            </if>
            <if test="directorArea != null  and directorArea != ''">and directorArea =#{directorArea}</if>
            <if test="region != null  and region != ''">and region =#{region}</if>
        </where>
    </select>

    <select id="selectSfeKtnMonthList" parameterType="SfeKtnEntity" resultMap="SfeKtnEntityResult">
        SELECT
        executionDate,
        directorArea,
        region,
        ggDay1,
        ggDay2,
        waDay1,
        waDay2,
        gaDay1,
        totalOp,
        totalNumber
        FROM
        (
        SELECT
        a.date 'executionDate',
        a.director_region 'directorArea',
        a.region 'region',
        a.cervical_cancer1l_np 'ggDay1',
        a.cervical_cancer2l_np 'ggDay2',
        a.stomach_cancer1l_np 'waDay1',
        a.stomach_cancer2l_plus_np 'waDay2',
        a.liver_cancer_hcc_np 'gaDay1',
        a.other_tumor_types_np 'otherDay',
        a.op_total 'totalOp',
        a.prescription_total_units 'totalNumber'
        FROM
        sfe_ktn a
        WHERE
        enabled = 'Y'
        ) b
        <where>
            <if test="executionDate != null  and executionDate != ''">and executionDate like concat('%',
                #{executionDate}, '%')
            </if>
            <if test="directorArea != null  and directorArea != ''">and directorArea =#{directorArea}</if>
            <if test="region != null  and region != ''">and region =#{region}</if>
        </where>
    </select>

    <select id="selectSfeKtnQuarterList" parameterType="SfeKtnEntity" resultMap="SfeKtnEntityResult">
        SELECT
        executionDate,
        directorArea,
        region,
        ggDay1,
        ggDay2,
        waDay1,
        waDay2,
        gaDay1,
        totalOp,
        totalNumber
        FROM
        (
        SELECT
        a.date 'executionDate',
        a.director_region 'directorArea',
        a.region 'region',
        a.cervical_cancer1l_np 'ggDay1',
        a.cervical_cancer2l_np 'ggDay2',
        a.stomach_cancer1l_np 'waDay1',
        a.stomach_cancer2l_plus_np 'waDay2',
        a.liver_cancer_hcc_np 'gaDay1',
        a.other_tumor_types_np 'otherDay',
        a.op_total 'totalOp',
        a.prescription_total_units 'totalNumber'
        FROM
        sfe_ktn a
        WHERE
        enabled = 'Y'
        ) b
        <where>
            <if test="params.beginQuarterDate != null and params.beginQuarterDate != '' and params.endQuarterDate != null and params.endQuarterDate != ''">
                and executionDate between #{params.beginQuarterDate} and #{params.endQuarterDate}
            </if>
            <if test="directorArea != null  and directorArea != ''">and directorArea =#{directorArea}</if>
            <if test="region != null  and region != ''">and region =#{region}</if>
        </where>
    </select>

</mapper>