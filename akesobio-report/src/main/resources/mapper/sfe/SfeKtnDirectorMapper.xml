<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sfe.mapper.SfeKtnDirectorMapper">
    <resultMap type="SfeKtnDirector" id="SfeKtnDirectorResult">
        <result property="id" column="id"/>
        <result property="yearMonth" column="yearMonth"/>
        <result property="quarter" column="quarter"/>
        <result property="executionDate" column="executionDate"/>
        <result property="directorArea" column="directorArea"/>
        <result property="region" column="region"/>
        <result property="gjDay1" column="gjDay1"/>
        <result property="gjDay2" column="gjDay2"/>
        <result property="waDay1" column="waDay1"/>
        <result property="waDay2" column="waDay2"/>
        <result property="gaDay" column="gaDay"/>
        <result property="otherDay" column="otherDay"/>
        <result property="totalDayNp" column="totalDayNp"/>
        <result property="totalDayOp" column="totalDayOp"/>
        <result property="prescriptionCountDay" column="prescriptionCountDay"/>
        <result property="dailyCountRanking" column="dailyCountRanking"/>
        <result property="regionMonth" column="regionMonth"/>
        <result property="npMonth" column="npMonth"/>
        <result property="opMonth" column="opMonth"/>
        <result property="prescriptionCountMonth" column="prescriptionCountMonth"/>
        <result property="monthlyAchievementRate" column="monthlyAchievementRate"/>
        <result property="monthlyAchievementRateRanking" column="monthlyAchievementRateRanking"/>
        <result property="regionQuarter" column="regionQuarter"/>
        <result property="npQuarter" column="npQuarter"/>
        <result property="opQuarter" column="opQuarter"/>
        <result property="prescriptionCountQuarter" column="prescriptionCountQuarter"/>
        <result property="quarterAchievementRate" column="quarterAchievementRate"/>
        <result property="quarterAchievementRateRanking" column="quarterAchievementRateRanking"/>
        <result property="deleteStatus" column="deleteStatus"/>
    </resultMap>

    <sql id="selectSfeKtnDirectorVo">
        select id,
               yearMonth,
               quarter,
               executionDate,
               directorArea,
               region,
               gjDay1,
               gjDay2,
               waDay1,
               waDay2,
               gaDay,
               otherDay,
               totalDayNp,
               totalDayOp,
               prescriptionCountDay,
               dailyCountRanking,
               regionMonth,
               npMonth,
               opMonth,
               prescriptionCountMonth,
               monthlyAchievementRate,
               monthlyAchievementRateRanking,
               regionQuarter,
               npQuarter,
               opQuarter,
               prescriptionCountQuarter,
               quarterAchievementRate,
               quarterAchievementRateRanking,
               deleteStatus
        from sfe_ktn_director
    </sql>

    <select id="selectSfeKtnDirectorList" parameterType="SfeKtnDirector" resultMap="SfeKtnDirectorResult">
        <include refid="selectSfeKtnDirectorVo"/>
        <where>
            <if test="yearMonth != null  and yearMonth != ''">and yearMonth like concat('%', #{yearMonth}, '%')</if>
            <if test="quarter != null  and quarter != ''">and quarter like concat('%', #{quarter}, '%')</if>
            <if test="executionDate != null ">and executionDate = #{executionDate}</if>
            <if test="directorArea != null  and directorArea != ''">and directorArea like concat('%', #{directorArea},
                '%')
            </if>
            <if test="region != null  and region != ''">and region like concat('%', #{region}, '%')</if>
            <if test="gjDay1 != null ">and gjDay1 = #{gjDay1}</if>
            <if test="gjDay2 != null ">and gjDay2 = #{gjDay2}</if>
            <if test="waDay1 != null ">and waDay1 = #{waDay1}</if>
            <if test="waDay2 != null ">and waDay2 = #{waDay2}</if>
            <if test="gaDay != null ">and gaDay = #{gaDay}</if>
            <if test="otherDay != null ">and otherDay = #{otherDay}</if>
            <if test="totalDayNp != null ">and totalDayNp = #{totalDayNp}</if>
            <if test="totalDayOp != null ">and totalDayOp = #{totalDayOp}</if>
            <if test="prescriptionCountDay != null ">and prescriptionCountDay = #{prescriptionCountDay}</if>
            <if test="dailyCountRanking != null ">and dailyCountRanking = #{dailyCountRanking}</if>
            <if test="regionMonth != null  and regionMonth != ''">and regionMonth = #{regionMonth}</if>
            <if test="npMonth != null ">and npMonth = #{npMonth}</if>
            <if test="opMonth != null ">and opMonth = #{opMonth}</if>
            <if test="prescriptionCountMonth != null ">and prescriptionCountMonth = #{prescriptionCountMonth}</if>
            <if test="monthlyAchievementRate != null  and monthlyAchievementRate != ''">and monthlyAchievementRate =
                #{monthlyAchievementRate}
            </if>
            <if test="monthlyAchievementRateRanking != null ">and monthlyAchievementRateRanking =
                #{monthlyAchievementRateRanking}
            </if>
            <if test="regionQuarter != null  and regionQuarter != ''">and regionQuarter = #{regionQuarter}</if>
            <if test="npQuarter != null ">and npQuarter = #{npQuarter}</if>
            <if test="opQuarter != null ">and opQuarter = #{opQuarter}</if>
            <if test="prescriptionCountQuarter != null ">and prescriptionCountQuarter = #{prescriptionCountQuarter}</if>
            <if test="quarterAchievementRate != null  and quarterAchievementRate != ''">and quarterAchievementRate =
                #{quarterAchievementRate}
            </if>
            <if test="quarterAchievementRateRanking != null ">and quarterAchievementRateRanking =
                #{quarterAchievementRateRanking}
            </if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
        </where>
    </select>

    <select id="selectSfeKtnDirectorById" parameterType="Integer" resultMap="SfeKtnDirectorResult">
        <include refid="selectSfeKtnDirectorVo"/>
        where id = #{id}
    </select>

    <insert id="insertSfeKtnDirector" parameterType="SfeKtnDirector">
        insert into sfe_ktn_director
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="yearMonth != null">yearMonth,</if>
            <if test="quarter != null">quarter,</if>
            <if test="executionDate != null">executionDate,</if>
            <if test="directorArea != null">directorArea,</if>
            <if test="region != null">region,</if>
            <if test="gjDay1 != null">gjDay1,</if>
            <if test="gjDay2 != null">gjDay2,</if>
            <if test="waDay1 != null">waDay1,</if>
            <if test="waDay2 != null">waDay2,</if>
            <if test="gaDay != null">gaDay,</if>
            <if test="otherDay != null">otherDay,</if>
            <if test="totalDayNp != null">totalDayNp,</if>
            <if test="totalDayOp != null">totalDayOp,</if>
            <if test="prescriptionCountDay != null">prescriptionCountDay,</if>
            <if test="dailyCountRanking != null">dailyCountRanking,</if>
            <if test="regionMonth != null">regionMonth,</if>
            <if test="npMonth != null">npMonth,</if>
            <if test="opMonth != null">opMonth,</if>
            <if test="prescriptionCountMonth != null">prescriptionCountMonth,</if>
            <if test="monthlyAchievementRate != null">monthlyAchievementRate,</if>
            <if test="monthlyAchievementRateRanking != null">monthlyAchievementRateRanking,</if>
            <if test="regionQuarter != null">regionQuarter,</if>
            <if test="npQuarter != null">npQuarter,</if>
            <if test="opQuarter != null">opQuarter,</if>
            <if test="prescriptionCountQuarter != null">prescriptionCountQuarter,</if>
            <if test="quarterAchievementRate != null">quarterAchievementRate,</if>
            <if test="quarterAchievementRateRanking != null">quarterAchievementRateRanking,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="yearMonth != null">#{yearMonth},</if>
            <if test="quarter != null">#{quarter},</if>
            <if test="executionDate != null">#{executionDate},</if>
            <if test="directorArea != null">#{directorArea},</if>
            <if test="region != null">#{region},</if>
            <if test="gjDay1 != null">#{gjDay1},</if>
            <if test="gjDay2 != null">#{gjDay2},</if>
            <if test="waDay1 != null">#{waDay1},</if>
            <if test="waDay2 != null">#{waDay2},</if>
            <if test="gaDay != null">#{gaDay},</if>
            <if test="otherDay != null">#{otherDay},</if>
            <if test="totalDayNp != null">#{totalDayNp},</if>
            <if test="totalDayOp != null">#{totalDayOp},</if>
            <if test="prescriptionCountDay != null">#{prescriptionCountDay},</if>
            <if test="dailyCountRanking != null">#{dailyCountRanking},</if>
            <if test="regionMonth != null">#{regionMonth},</if>
            <if test="npMonth != null">#{npMonth},</if>
            <if test="opMonth != null">#{opMonth},</if>
            <if test="prescriptionCountMonth != null">#{prescriptionCountMonth},</if>
            <if test="monthlyAchievementRate != null">#{monthlyAchievementRate},</if>
            <if test="monthlyAchievementRateRanking != null">#{monthlyAchievementRateRanking},</if>
            <if test="regionQuarter != null">#{regionQuarter},</if>
            <if test="npQuarter != null">#{npQuarter},</if>
            <if test="opQuarter != null">#{opQuarter},</if>
            <if test="prescriptionCountQuarter != null">#{prescriptionCountQuarter},</if>
            <if test="quarterAchievementRate != null">#{quarterAchievementRate},</if>
            <if test="quarterAchievementRateRanking != null">#{quarterAchievementRateRanking},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
        </trim>
    </insert>

    <update id="updateSfeKtnDirector" parameterType="SfeKtnDirector">
        update sfe_ktn_director
        <trim prefix="SET" suffixOverrides=",">
            <if test="yearMonth != null">yearMonth = #{yearMonth},</if>
            <if test="quarter != null">quarter = #{quarter},</if>
            <if test="executionDate != null">executionDate = #{executionDate},</if>
            <if test="directorArea != null">directorArea = #{directorArea},</if>
            <if test="region != null">region = #{region},</if>
            <if test="gjDay1 != null">gjDay1 = #{gjDay1},</if>
            <if test="gjDay2 != null">gjDay2 = #{gjDay2},</if>
            <if test="waDay1 != null">waDay1 = #{waDay1},</if>
            <if test="waDay2 != null">waDay2 = #{waDay2},</if>
            <if test="gaDay != null">gaDay = #{gaDay},</if>
            <if test="otherDay != null">otherDay = #{otherDay},</if>
            <if test="totalDayNp != null">totalDayNp = #{totalDayNp},</if>
            <if test="totalDayOp != null">totalDayOp = #{totalDayOp},</if>
            <if test="prescriptionCountDay != null">prescriptionCountDay = #{prescriptionCountDay},</if>
            <if test="dailyCountRanking != null">dailyCountRanking = #{dailyCountRanking},</if>
            <if test="regionMonth != null">regionMonth = #{regionMonth},</if>
            <if test="npMonth != null">npMonth = #{npMonth},</if>
            <if test="opMonth != null">opMonth = #{opMonth},</if>
            <if test="prescriptionCountMonth != null">prescriptionCountMonth = #{prescriptionCountMonth},</if>
            <if test="monthlyAchievementRate != null">monthlyAchievementRate = #{monthlyAchievementRate},</if>
            <if test="monthlyAchievementRateRanking != null">monthlyAchievementRateRanking =
                #{monthlyAchievementRateRanking},
            </if>
            <if test="regionQuarter != null">regionQuarter = #{regionQuarter},</if>
            <if test="npQuarter != null">npQuarter = #{npQuarter},</if>
            <if test="opQuarter != null">opQuarter = #{opQuarter},</if>
            <if test="prescriptionCountQuarter != null">prescriptionCountQuarter = #{prescriptionCountQuarter},</if>
            <if test="quarterAchievementRate != null">quarterAchievementRate = #{quarterAchievementRate},</if>
            <if test="quarterAchievementRateRanking != null">quarterAchievementRateRanking =
                #{quarterAchievementRateRanking},
            </if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSfeKtnDirectorById" parameterType="Integer">
        delete
        from sfe_ktn_director
        where id = #{id}
    </delete>

    <delete id="deleteSfeKtnDirectorByIds" parameterType="String">
        delete from sfe_ktn_director where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>