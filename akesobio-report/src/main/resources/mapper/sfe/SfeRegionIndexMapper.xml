<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sfe.mapper.SfeRegionIndexMapper">

    <resultMap type="SfeRegionIndex" id="SfeRegionIndexResult">
        <result property="id" column="id"/>
        <result property="directorArea" column="directorArea"/>
        <result property="region" column="region"/>
        <result property="productName" column="productName"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="monthlyIndicatorCount" column="monthlyIndicatorCount"/>
        <result property="monthlyIndicatorAmount" column="monthlyIndicatorAmount"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="quarter" column="quarter"/>
    </resultMap>

    <sql id="selectSfeRegionIndexVo">
        select id,
               directorArea,
               region,
               productName, year, month, monthlyIndicatorCount, monthlyIndicatorAmount, deleteStatus, quarter
        from sfe_region_index
    </sql>

    <select id="selectSfeRegionIndexList" parameterType="SfeRegionIndex" resultMap="SfeRegionIndexResult">
        <include refid="selectSfeRegionIndexVo"/>
        <where>
            <if test="directorArea != null  and directorArea != ''">and directorArea like concat('%', #{directorArea},
                '%')
            </if>
            <if test="region != null  and region != ''">and region like concat('%', #{region}, '%')</if>
            <if test="productName != null  and productName != ''">and productName like concat('%', #{productName},
                '%')
            </if>
            <if test="year != null ">and year = #{year}</if>
            <if test="month != null ">and month = #{month}</if>
            <if test="monthlyIndicatorCount != null ">and monthlyIndicatorCount = #{monthlyIndicatorCount}</if>
            <if test="monthlyIndicatorAmount != null ">and monthlyIndicatorAmount = #{monthlyIndicatorAmount}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="quarter != null ">and quarter = #{quarter}</if>
        </where>
    </select>

    <select id="selectSfeRegionIndexById" parameterType="Integer" resultMap="SfeRegionIndexResult">
        <include refid="selectSfeRegionIndexVo"/>
        where id = #{id}
    </select>

    <insert id="insertSfeRegionIndex" parameterType="SfeRegionIndex">
        insert into sfe_region_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="directorArea != null">directorArea,</if>
            <if test="region != null">region,</if>
            <if test="productName != null">productName,</if>
            <if test="year != null">year,</if>
            <if test="month != null">month,</if>
            <if test="monthlyIndicatorCount != null">monthlyIndicatorCount,</if>
            <if test="monthlyIndicatorAmount != null">monthlyIndicatorAmount,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="quarter != null">quarter,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="directorArea != null">#{directorArea},</if>
            <if test="region != null">#{region},</if>
            <if test="productName != null">#{productName},</if>
            <if test="year != null">#{year},</if>
            <if test="month != null">#{month},</if>
            <if test="monthlyIndicatorCount != null">#{monthlyIndicatorCount},</if>
            <if test="monthlyIndicatorAmount != null">#{monthlyIndicatorAmount},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="quarter != null">#{quarter},</if>
        </trim>
    </insert>

    <update id="updateSfeRegionIndex" parameterType="SfeRegionIndex">
        update sfe_region_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="directorArea != null">directorArea = #{directorArea},</if>
            <if test="region != null">region = #{region},</if>
            <if test="productName != null">productName = #{productName},</if>
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="monthlyIndicatorCount != null">monthlyIndicatorCount = #{monthlyIndicatorCount},</if>
            <if test="monthlyIndicatorAmount != null">monthlyIndicatorAmount = #{monthlyIndicatorAmount},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
            <if test="quarter != null">quarter = #{quarter},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSfeRegionIndexById" parameterType="Integer">
        delete
        from sfe_region_index
        where id = #{id}
    </delete>

    <delete id="deleteSfeRegionIndexByIds" parameterType="String">
        delete from sfe_region_index where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>