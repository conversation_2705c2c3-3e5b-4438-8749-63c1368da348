<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.sfe.mapper.SfeDirectorAreaMapper">
    <resultMap type="SfeDirectorArea" id="SfeDirectorAreaResult">
        <result property="id" column="id"/>
        <result property="yearMonth" column="yearMonth"/>
        <result property="executionDate" column="executionDate"/>
        <result property="directorArea" column="directorArea"/>
        <result property="ktn" column="ktn"/>
        <result property="ktnMonthlyCount" column="ktnMonthlyCount"/>
        <result property="ktnMonthlyAmount" column="ktnMonthlyAmount"/>
        <result property="ktnMonthlyIndicators" column="ktnMonthlyIndicators"/>
        <result property="ydf" column="ydf"/>
        <result property="ydfMonthlyCount" column="ydfMonthlyCount"/>
        <result property="ydfMonthlyAmount" column="ydfMonthlyAmount"/>
        <result property="ydfMonthlyIndicators" column="ydfMonthlyIndicators"/>
        <result property="hj" column="hj"/>
        <result property="hjMonthlyCount" column="hjMonthlyCount"/>
        <result property="hjMonthlyIndicators" column="hjMonthlyIndicators"/>
        <result property="deleteStatus" column="deleteStatus"/>
    </resultMap>

    <sql id="selectSfeDirectorAreaVo">
        select id,
               yearMonth,
               executionDate,
               directorArea,
               ktn,
               ktnMonthlyCount,
               ktnMonthlyAmount,
               ktnMonthlyIndicators,
               ydf,
               ydfMonthlyCount,
               ydfMonthlyAmount,
               ydfMonthlyIndicators,
               hj,
               hjMonthlyCount,
               hjMonthlyIndicators,
               deleteStatus
        from sfe_director_area
    </sql>

    <select id="selectSfeDirectorAreaList" parameterType="SfeDirectorArea" resultMap="SfeDirectorAreaResult">
        <include refid="selectSfeDirectorAreaVo"/>
        <where>
            <if test="yearMonth != null  and yearMonth != ''">and yearMonth like concat('%', #{yearMonth}, '%')</if>
            <if test="executionDate != null ">and executionDate = #{executionDate}</if>
            <if test="directorArea != null  and directorArea != ''">and directorArea like concat('%', #{directorArea},
                '%')
            </if>
            <if test="ktn != null  and ktn != ''">and ktn like concat('%', #{ktn}, '%')</if>
            <if test="ktnMonthlyCount != null ">and ktnMonthlyCount = #{ktnMonthlyCount}</if>
            <if test="ktnMonthlyAmount != null ">and ktnMonthlyAmount = #{ktnMonthlyAmount}</if>
            <if test="ktnMonthlyIndicators != null  and ktnMonthlyIndicators != ''">and ktnMonthlyIndicators =
                #{ktnMonthlyIndicators}
            </if>
            <if test="ydf != null  and ydf != ''">and ydf = #{ydf}</if>
            <if test="ydfMonthlyCount != null ">and ydfMonthlyCount = #{ydfMonthlyCount}</if>
            <if test="ydfMonthlyAmount != null ">and ydfMonthlyAmount = #{ydfMonthlyAmount}</if>
            <if test="ydfMonthlyIndicators != null  and ydfMonthlyIndicators != ''">and ydfMonthlyIndicators =
                #{ydfMonthlyIndicators}
            </if>
            <if test="hj != null  and hj != ''">and hj = #{hj}</if>
            <if test="hjMonthlyCount != null ">and hjMonthlyCount = #{hjMonthlyCount}</if>
            <if test="hjMonthlyIndicators != null  and hjMonthlyIndicators != ''">and hjMonthlyIndicators =
                #{hjMonthlyIndicators}
            </if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
        </where>
    </select>

    <select id="selectSfeDirectorAreaById" parameterType="Integer" resultMap="SfeDirectorAreaResult">
        <include refid="selectSfeDirectorAreaVo"/>
        where id = #{id}
    </select>

    <insert id="insertSfeDirectorArea" parameterType="SfeDirectorArea">
        insert into sfe_director_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="yearMonth != null">yearMonth,</if>
            <if test="executionDate != null">executionDate,</if>
            <if test="directorArea != null">directorArea,</if>
            <if test="ktn != null">ktn,</if>
            <if test="ktnMonthlyCount != null">ktnMonthlyCount,</if>
            <if test="ktnMonthlyAmount != null">ktnMonthlyAmount,</if>
            <if test="ktnMonthlyIndicators != null">ktnMonthlyIndicators,</if>
            <if test="ydf != null">ydf,</if>
            <if test="ydfMonthlyCount != null">ydfMonthlyCount,</if>
            <if test="ydfMonthlyAmount != null">ydfMonthlyAmount,</if>
            <if test="ydfMonthlyIndicators != null">ydfMonthlyIndicators,</if>
            <if test="hj != null">hj,</if>
            <if test="hjMonthlyCount != null">hjMonthlyCount,</if>
            <if test="hjMonthlyIndicators != null">hjMonthlyIndicators,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="yearMonth != null">#{yearMonth},</if>
            <if test="executionDate != null">#{executionDate},</if>
            <if test="directorArea != null">#{directorArea},</if>
            <if test="ktn != null">#{ktn},</if>
            <if test="ktnMonthlyCount != null">#{ktnMonthlyCount},</if>
            <if test="ktnMonthlyAmount != null">#{ktnMonthlyAmount},</if>
            <if test="ktnMonthlyIndicators != null">#{ktnMonthlyIndicators},</if>
            <if test="ydf != null">#{ydf},</if>
            <if test="ydfMonthlyCount != null">#{ydfMonthlyCount},</if>
            <if test="ydfMonthlyAmount != null">#{ydfMonthlyAmount},</if>
            <if test="ydfMonthlyIndicators != null">#{ydfMonthlyIndicators},</if>
            <if test="hj != null">#{hj},</if>
            <if test="hjMonthlyCount != null">#{hjMonthlyCount},</if>
            <if test="hjMonthlyIndicators != null">#{hjMonthlyIndicators},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
        </trim>
    </insert>

    <update id="updateSfeDirectorArea" parameterType="SfeDirectorArea">
        update sfe_director_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="yearMonth != null">yearMonth = #{yearMonth},</if>
            <if test="executionDate != null">executionDate = #{executionDate},</if>
            <if test="directorArea != null">directorArea = #{directorArea},</if>
            <if test="ktn != null">ktn = #{ktn},</if>
            <if test="ktnMonthlyCount != null">ktnMonthlyCount = #{ktnMonthlyCount},</if>
            <if test="ktnMonthlyAmount != null">ktnMonthlyAmount = #{ktnMonthlyAmount},</if>
            <if test="ktnMonthlyIndicators != null">ktnMonthlyIndicators = #{ktnMonthlyIndicators},</if>
            <if test="ydf != null">ydf = #{ydf},</if>
            <if test="ydfMonthlyCount != null">ydfMonthlyCount = #{ydfMonthlyCount},</if>
            <if test="ydfMonthlyAmount != null">ydfMonthlyAmount = #{ydfMonthlyAmount},</if>
            <if test="ydfMonthlyIndicators != null">ydfMonthlyIndicators = #{ydfMonthlyIndicators},</if>
            <if test="hj != null">hj = #{hj},</if>
            <if test="hjMonthlyCount != null">hjMonthlyCount = #{hjMonthlyCount},</if>
            <if test="hjMonthlyIndicators != null">hjMonthlyIndicators = #{hjMonthlyIndicators},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSfeDirectorAreaById" parameterType="Integer">
        delete
        from sfe_director_area
        where id = #{id}
    </delete>

    <delete id="deleteSfeDirectorAreaByIds" parameterType="String">
        delete from sfe_director_area where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>