<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.oaApi.mapper.MutualFundMapper">

    <resultMap type="com.akesobio.report.oaApi.domain.MutualFund" id="MutualFundResult">
        <result property="yearNew" column="yearNew"/>
        <result property="oddNumbers" column="oddNumbers"/>
        <result property="jobNumber" column="jobNumber"/>
        <result property="documentStatus" column="documentStatus"/>
    </resultMap>

    <select id="selectMutualFundList" resultMap="MutualFundResult">
        SELECT
        yearNew,
        oddNumbers,
        jobNumber,
        documentStatus
        FROM
        (
        SELECT YEAR
        ( a.fd_shenQingRiQi ) AS 'yearNew',
        a.fd_danHao AS 'oddNumbers',
        a.fd_gongHao AS 'jobNumber',
        (
        CASE
        f.doc_status
        WHEN '10' THEN
        '草稿'
        WHEN '20' THEN
        '待审'
        WHEN '11' THEN
        '驳回'
        WHEN '00' THEN
        '废弃'
        WHEN '30' THEN
        '结束'
        WHEN '31' THEN
        '已反馈' ELSE '不对劲'
        END
        ) AS 'documentStatus'
        FROM
        ekp_1803a8164f9c21c3e7fd a
        LEFT JOIN [dbo].[km_review_main] f ON a.fd_id= f.fd_id
        WHERE
        f.doc_status IN ( '20', '11', '30' )
        ) b
        <where>
            <if test="yearNew != null">and yearNew = #{yearNew}</if>
            <if test="jobNumber != null and jobNumber != ''">and jobNumber = #{jobNumber}</if>
        </where>
    </select>
</mapper>