<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.oaApi.mapper.UserInfoMapper">

    <resultMap type="com.akesobio.report.oaApi.domain.UserInfo" id="UserInfoResult">
        <result property="fdId" column="fdId"/>
        <result property="jobNumber" column="jobNumber"/>
        <result property="name" column="name"/>
        <result property="department" column="department"/>
        <result property="rank" column="rank"/>
        <result property="post" column="post"/>
    </resultMap>

    <select id="selectUserInfoList" resultMap="UserInfoResult">
        SELECT
        fdId,
        jobNumber,
        name,
        department,
        rank,
        post,
        email
        FROM
        (
        SELECT
        a.id 'fdId',
        a.job_number 'jobNumber',
        a.name 'name',
        a.dept_name 'department',
        a.job_grade 'rank',
        a.post 'post',
        a.email 'email'
        FROM
        hr_employee_info a
        WHERE
        a.employee_status= '在职'
        ) b
        <where>
            <if test="fdId != null">and fdId like concat('%', #{fdId}, '%')</if>
            <if test="jobNumber != null and jobNumber != ''">and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="name != null">and name like concat('%', #{name}, '%')</if>
        </where>
    </select>
</mapper>