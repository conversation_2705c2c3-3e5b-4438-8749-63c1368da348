<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.administration.mapper.MealConsumeMapper">

    <resultMap id="BaseResultMap" type="MealConsume">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="jobNumber" column="job_number" jdbcType="VARCHAR"/>
        <result property="employeeName" column="employee_name" jdbcType="VARCHAR"/>
        <result property="mealDate" column="meal_date" jdbcType="DATE"/>
        <result property="mealType" column="meal_type" jdbcType="TINYINT" typeHandler="com.akesobio.report.administration.enums.handler.MealTypeEnumTypeHandler"/>
        <result property="applyTime" column="apply_time" jdbcType="TIMESTAMP"/>
        <result property="mealTime" column="meal_time" jdbcType="TIMESTAMP"/>
        <result property="mealLocation" column="meal_location" jdbcType="TINYINT" typeHandler="com.akesobio.report.administration.enums.handler.MealLocationEnumTypeHandler"/>
        <result property="company" column="company" jdbcType="VARCHAR"/>
        <result property="department" column="department" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="select">
        select id,
               job_number,
               employee_name,
               meal_date,
               meal_type,
               apply_time,
               meal_time,
               meal_location,
               company,
               department,
               create_time
        from ad_meal_consume
    </sql>
    <select id="selectList" resultMap="BaseResultMap">
        <include refid="select"/>
        <if test="query != null">
            where status = 1
            <if test=" query.jobNumber != null and query.jobNumber != ''">
                AND job_number like concat('%', #{query.jobNumber}, '%')
            </if>
            <if test=" query.employeeName != null and query.employeeName != ''">
                AND employee_name like concat('%', #{query.employeeName}, '%')
            </if>
            <if test="query.jobNumbers != null and query.jobNumbers.length > 0 and query.jobNumbers.length &lt; 2000">
                AND job_number IN
                <foreach item="jobNumber" collection="query.jobNumbers" open="(" separator="," close=")">
                    #{jobNumber}
                </foreach>
            </if>
            <if test=" query.mealType != null ">and meal_type = #{query.mealType.code}</if>
            <if test=" query.mealLocation != null ">and meal_location = #{query.mealLocation.code}</if>
            <if test=" query.company != null and query.company != ''">
                AND company like concat('%',#{query.company},'%')
            </if>
            <if test=" query.department != null and query.department != ''">
                AND department like concat('%',#{query.department},'%')
            </if>
            <if test="query.mealDate != null and query.mealDate.length == 2">
                AND meal_date BETWEEN #{query.mealDate[0]} and #{query.mealDate[1]}
            </if>
        </if>
        order by meal_date asc, job_number asc, meal_type asc
    </select>
    <select id="selectRealConsumeJobNumberList" resultType="java.lang.String">
        select job_number from (
        <include refid="select"/>
        where meal_time is not null and status = 1
        <if test="query != null">
            <if test=" query.jobNumber != null and query.jobNumber != ''">and job_number = #{query.jobNumber}</if>
            <if test="query.jobNumbers != null and query.jobNumbers.size > 0 and query.jobNumbers.size &lt; 2000">
                AND job_number IN
                <foreach item="jobNumber" collection="query.jobNumbers" open="(" separator="," close=")">
                    #{jobNumber}
                </foreach>
            </if>
            <if test=" query.mealType != null ">and meal_type = #{query.mealType.code}</if>
            <if test=" query.company != null and query.company != ''">
                and company like concat('%',#{query.company},'%')
            </if>
            <if test=" query.department != null and query.department != ''">
                and department like concat('%',#{query.department},'%')
            </if>
            <if test="query.mealDate != null and query.mealDate.length == 2">
                and meal_date BETWEEN #{query.mealDate[0]} and #{query.mealDate[1]}
            </if>
        </if>
        )t1 group by job_number;
    </select>
</mapper>
