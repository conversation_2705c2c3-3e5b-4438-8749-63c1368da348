<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.administration.mapper.CateringDetailsSummaryMapper">
    
    <resultMap type="CateringDetailsSummary" id="CateringDetailsSummaryResult">
        <result property="name"    column="name"    />
        <result property="jobNumber"    column="jobNumber"    />
        <result property="department"    column="department"    />
        <result property="mealDate"    column="mealDate"    />
        <result property="threeMealsDay"    column="threeMealsDay"    />
        <result property="address"    column="address"    />
    </resultMap>

    <sql id="selectCateringDetailsSummaryVo">
        select name, jobNumber, department, mealDate, threeMealsDay, address from catering_details_summary
    </sql>

    <select id="selectCateringDetailsSummaryList" parameterType="CateringDetailsSummary" resultMap="CateringDetailsSummaryResult">
        <include refid="selectCateringDetailsSummaryVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="jobNumber != null  and jobNumber != ''"> and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="params.beginMealDate != null and params.beginMealDate != '' and params.endMealDate != null and params.endMealDate != ''"> and mealDate between #{params.beginMealDate} and #{params.endMealDate}</if>
            <if test="threeMealsDay != null  and threeMealsDay != ''"> and threeMealsDay like concat('%', #{threeMealsDay}, '%')</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
        </where>
        order by mealDate desc
    </select>
</mapper>