# 云简文件上传API升级总结

## 升级概述

本次升级将云简文件上传API从v2版本升级到v3版本，主要解决了之前遇到的403错误问题，并提供了更好的Content-Type处理策略。

## 主要变更

### 1. API接口变更

**接口路径变更：**
- 旧版本：`/common/files/v2/uploadUrl`
- 新版本：`/common/files/v3/uploadUrl`

**请求参数变更：**
```json
// v2版本请求体
{
  "data": ["file1.pdf", "file2.jpg"]
}

// v3版本请求体
{
  "bizId": "2c1ece50-3cab-4f1c-a5d9-fd297afdd1e2",
  "timestamp": 1744252563000,
  "data": [
    {
      "file_name": "file1.pdf",
      "content_type": "application/pdf"
    },
    {
      "file_name": "file2.jpg", 
      "content_type": "image/jpeg"
    }
  ]
}
```

**响应格式变更：**
```json
// v2版本响应
{
  "resCode": 200000,
  "data": [
    {
      "fileName": "test.pdf",
      "fileURL": "https://xxx/upload-url"
    }
  ]
}

// v3版本响应
{
  "resCode": 200000,
  "data": [
    {
      "file_name": "test.pdf",
      "upload_url": "https://xxx/upload-url",
      "attachment_url": "cc28cda9-836c-493f-81c8-7e23d78cdf2f.pdf"
    }
  ]
}
```

### 2. 代码变更

#### 2.1 CloudpenseApiUtils.java 主要变更

1. **getUploadUrl方法升级：**
   - 添加了bizId和timestamp字段
   - 请求体中的data数组现在包含file_name和content_type
   - 响应解析适配新的字段名（file_name, upload_url, attachment_url）

2. **新增getContentTypeByFileName方法：**
   - 根据文件名自动识别Content-Type
   - 支持常见文件格式（PDF、图片、Office文档等）

3. **保持uploadFile方法兼容性：**
   - 继续使用智能Content-Type设置策略
   - 避免与预签名URL签名冲突

#### 2.2 配置文件更新

更新了以下配置文件中的API URL：
- `application-develop.yml`
- `application-dev.yml` 
- `application-prod.yml`

#### 2.3 调用方代码更新

1. **OaFormUploadService.java：**
   - 更新uploadFileToCloudpense方法
   - 直接使用v3 API返回的attachment_url，无需再从upload_url中提取

2. **测试文件更新：**
   - `Test.java`：适配新的响应字段
   - `FileUploadDemo.java`：完整升级到v3 API格式

### 3. 新特性和优势

#### 3.1 解决的问题
- **403错误修复：** 通过在请求时明确指定content_type，避免了上传时的Content-Type冲突
- **更好的错误处理：** v3 API提供了更详细的错误信息
- **简化附件URL获取：** 直接返回attachment_url，无需手动提取

#### 3.2 新增功能
- **业务追踪：** 通过bizId可以追踪每次请求
- **时间戳支持：** 便于日志分析和问题排查
- **直接附件URL：** 简化了文件上传后的URL处理流程

### 4. 兼容性说明

#### 4.1 向后兼容
- 保持了原有的方法签名不变
- 返回的Map结构保持兼容（使用相同的key名称）
- 文件上传逻辑保持不变

#### 4.2 破坏性变更
- 配置文件中的URL需要更新到v3版本
- 内部实现逻辑有较大变化，但对外接口保持兼容

### 5. 测试建议

#### 5.1 功能测试
1. 测试各种文件类型的上传（PDF、图片、Office文档等）
2. 验证Content-Type自动识别功能
3. 测试大文件上传（接近10MB限制）
4. 验证批量文件上传（最多100个文件）

#### 5.2 错误场景测试
1. 测试无效文件名
2. 测试超过文件数量限制
3. 测试网络异常情况
4. 验证403错误是否已解决

### 6. 部署注意事项

1. **配置更新：** 确保所有环境的配置文件都已更新到v3 API
2. **日志监控：** 关注升级后的API调用日志，确保没有异常
3. **回滚准备：** 保留v2版本的配置作为备份，以便必要时快速回滚

### 7. 后续优化建议

1. **缓存优化：** 可以考虑缓存Content-Type映射关系
2. **异步上传：** 对于大文件可以考虑异步上传机制
3. **重试机制：** 添加上传失败的自动重试逻辑
4. **监控告警：** 添加上传成功率监控和告警

## 总结

本次升级成功解决了之前的403错误问题，提供了更稳定和功能丰富的文件上传能力。通过保持API兼容性，最小化了对现有代码的影响，同时为未来的功能扩展奠定了基础。
