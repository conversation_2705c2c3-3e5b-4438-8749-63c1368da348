# 数据源配置
spring:
  redis:
    # 地址
    host: **********
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 12
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
    druid:
      # 主库数据源
      master:
        url: **************************************************************************************
        username: sa
        password: Sa123456@
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      ekp:
        enabled: true
        url: **********************************************************************
        username: sa
        password: Sa123456@
      ekpdev:
        enabled: true
        url: **********************************************************************
        username: sa
        password: Sa12345678@
      # 考勤数据源
      attendance:
        # 从数据源开关/默认关闭
        enabled: true
        url: ********************************************************************************
        username: sa
        password: Sa123456@
      #百望发票数据源
      baiwang:
        # 从数据源开关/默认关闭
        enabled: true
        url: *************************************************************************
        username: sa
        password: Sa123456@

        #百望发票视图数据源
        #      baiwangdev:
        #        # 从数据源开关/默认关闭
        #        enabled: true
        #        url: *************************************************************************_dev
        #        username: sa
        #        password: Sa123456@

        #SAP数据源
      sap:
        # 从数据源开关/默认关闭
        enabled: true
        url: jdbc:sqlserver://**********:1433;SelectMethod=cursor;DatabaseName=SapServerData
        username: sa
        password: Sa123456@

        #行政报餐数据源
      mealreporting:
        # 从数据源开关/默认关闭
        enabled: true
        url: jdbc:sqlserver://**********:1433;SelectMethod=cursor;DatabaseName=order_a_meal
        username: sa
        password: Sa123456@

      # 考勤数据源
      cleanarea:
        # 从数据源开关/默认关闭
        enabled: true
        url: jdbc:sqlserver://**********:1433;SelectMethod=cursor;DatabaseName=CleanAreaAttendData
        username: sa
        password: Sa123456@

      oahr:
        enabled: true
        url: jdbc:sqlserver://**********:1433;SelectMethod=cursor;DatabaseName=self_service
        username: sa
        password: Sa123456@

      abutmentdata:
        enabled: true
        url: jdbc:sqlserver://**********:1433;SelectMethod=cursor;DatabaseName=AbutmentData
        username: sa
        password: Sa123456@

      ddidata:
        enabled: true
        url: **************************************************************************
        username: sa
        password: Sa123456@


      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 900000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 900000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: akesobio
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  #    secret: abcdefghijklmnopqrstuvwxyz
  secret: dingdd1a815d7913bddd4ac5d6980864d335

  # 令牌有效期（默认30分钟）
  expireTime: 720
server:
  servlet:
    # 应用的访问路径
    context-path: /dev-api
  ssl:
    key-alias: das.akesobio.com
    key-store: classpath:server.jks
    key-store-type: JKS
    key-store-password: Akesobio:9966
    enabled: true