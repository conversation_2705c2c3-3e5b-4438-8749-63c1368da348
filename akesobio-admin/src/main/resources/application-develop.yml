server:
  servlet:
    # 应用的访问路径
    context-path: /dev-api
# 数据源配置
spring:
  redis:
    # 地址
    host: **********
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 12
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
    druid:
      # 主库数据源
      master:
        url: **************************************************************************************
        username: sa
        password: Sa123456@
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      ekp:
        enabled: true
        url: **********************************************************************
        username: sa
        password: Sa123456@
      ekpdev:
        enabled: true
        url: **********************************************************************
        username: sa
        password: Sa12345678@
      # 考勤数据源
      attendance:
        # 从数据源开关/默认关闭
        enabled: true
        url: ********************************************************************************
        username: sa
        password: Sa123456@
      #百望发票数据源
      baiwang:
        # 从数据源开关/默认关闭
        enabled: true
        url: *************************************************************************
        username: sa
        password: Sa123456@

        #SAP数据源
      sap:
        # 从数据源开关/默认关闭
        enabled: true
        url: *******************************************************************************
        username: sa
        password: Sa123456@

        #行政报餐数据源
      mealreporting:
        # 从数据源开关/默认关闭
        enabled: true
        url: ******************************************************************************
        username: sa
        password: Sa123456@

      # 考勤数据源
      cleanarea:
        # 从数据源开关/默认关闭
        enabled: true
        url: *************************************************************************************
        username: sa
        password: Sa123456@

      oahr:
        enabled: true
        url: ******************************************************************************
        username: sa
        password: Sa123456@
      #      oahr:
      #        enabled: true
      #        url: ******************************************************************************
      #        username: admin.dev
      #        password: CJjzyNiMDp9Z

      abutmentdata:
        enabled: true
        url: ******************************************************************************
        username: sa
        password: Sa123456@

      ddidata:
        enabled: true
        url: **************************************************************************
        username: sa
        password: Sa123456@

      sfe:
        enabled: true
        url: *********************************************************************
        username: sa
        password: Sa123456@
        validationQuery: SELECT 1

      mdmData:
        enabled: true
        url: *********************************************************************
        username: sa
        password: Sa123456@
        validationQuery: SELECT 1
      # CPKFSW MySQL数据源
      cpkfsw:
        enabled: true
        url: **********************************************************************
        username: readonly_user
        password: 123456
        driverClassName: com.mysql.cj.jdbc.Driver
        validationQuery: SELECT 1


      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 300000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: akesobio
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  #    secret: abcdefghijklmnopqrstuvwxyz
  secret: dingdd1a815d7913bddd4ac5d6980864d335

  # 令牌有效期（默认30分钟）
  expireTime: 720
#费控系统测试相关参数
costControl:
  baseUrl: https://taliopenapi.cloudpense.com
  #上传费控单据
  documentUrl: https://taliopenapi.cloudpense.com/common/document
  #上传文件
  upFilesUrl: https://taliopenapi.cloudpense.com/common/files/url

  remoteFileUrl: https://taliopenapi.cloudpense.com/common/files/v2/remote
  #值列表
  lovUrl: https://taliopenapi.cloudpense.com/common/lovs/v2/batch
  #同步部门及成本中心主数据url
  costCenter:
    url: https://taliopenapi.cloudpense.com/common/departments/v2/batch
  #同步职位
  position:
    url: https://taliopenapi.cloudpense.com/common/positions/batch
  #同步人员信息
  personInfo:
    url: https://taliopenapi.cloudpense.com/common/users/v2/batch
  #同步员工账号
  employeeAccount:
    url: https://taliopenapi.cloudpense.com/common/userAccounts/v2/batch
  #同步供应商主数据
  supplier:
    url: https://taliopenapi.cloudpense.com/common/suppliers/v2/batch
  #同步供应商银行账号主数据
  supplierBankAccount:
    url: https://taliopenapi.cloudpense.com/common/supplieraccounts/v2/batch
  #同步SAP成本中心主数据
  sapCostCenterData:
    url: https://taliopenapi.cloudpense.com/common/lovs/v2/batch
  #同步OA人员离职信息
  oaResignationData:
    url: https://taliopenapi.cloudpense.com/common/lovs/v2/batch
  #同步OA会计科目主数据
  oaLedgerAccount:
    url: https://taliopenapi.cloudpense.com/common/glAccounts/v2/batch
  #人员成本中心管控主数据
  costCenterUsers:
    url: https://taliopenapi.cloudpense.com/common/costCenterUsers/batch
  #身份证信息
  userCardBatchUrl:
    url: https://taliopenapi.cloudpense.com/common/userCard/batch

  #token
  token:
    token_url: https://taliopenapi.cloudpense.com/common/unAuth/tokens/get
    grant_type: client_credentials
    client_id: cloudpense2483631363b
    client_secret: 44fd9f67faf8bf5f44b73494f8c00c7848f0b5c1474314c7e2164d68afc3fccbde45ae98

  #获取文件上传地址 (升级到v3版本)
  getUploadUrl:
    url: https://aliopenapi.cloudpense.com/common/files/v3/uploadUrl
  #审批流批量修改 https://gphn.cloudpense.com/qaapidoc/workflow/batch.html
  workflowBatch: https://aliopenapi.cloudpense.com/common/workflow/batch
  #更新费用报销头部信息
  updateExpClaimHeaderUrl: https://taliopenapi.cloudpense.com/cproxy/claim/updateExpClaimHeader
  costCenterDepartmentBatchUrl: https://taliopenapi.cloudpense.com/common/costCenterDepartments/batch

#费控内部接口url
fk:
  #值列表查询
  selectValueUrl: https://taliopenapi.cloudpense.com/cproxy/lov/findByPage

#会议系统配置文件
client:
  auth:
    token: 343074a2-7387-4336-a068-b462f95430c7
    IP: https://akesobiouat.recloud.com.cn
    GetToken: /t/akesobiouat/token
    grant_type: password
    username: demo2
    password: Jcyv2ori#

    #    IP: https://akesobiouat.recloud.com.cn
    #    GetToken: /t/akesobiouat/token

    user: /api/akesobiocsd/user/info/save
    company: /api/akesobiocsd/company/save
    number: /api/akesobiocsd/project/number/save
    department: /api/akesobiocsd/business/unit/save
    position: /api/akesobiocsd/position/save
    product: /api/akesobiocsd/product/save
    category: /api/akesobiocsd/product/category/save
    center: /api/akesobiocsd/cost/center/save
    supplier: /api/akesobiocsd/supplier/save
    pipeline: /api/akesobiocsd/product/pipeline/save
    area: /api/akesobiocsd/owning/area/save
    costDepartment: /api/akesobiocsd/costDepartment/save

    #新增采购订单（单条）
    purchaseOrderAdd: /api/akesobiocsd/salesOrder/save
    #获取信用额度
    purchaseOrderLimit: /api/akesobiocsd/account/getBalance
    #获取资质效期
    qualificationValidityPeriod: /api/akesobiocsd/account/getQualificationPeriod
    #更新销售订单签核状态（批量）
    purchaseOrderStatus: /api/akesobiocsd/salesOrder/approve
    #更新采购订单（单条）
    purchaseOrderUpdate: /api/akesobiocsd/salesOrder/update
    #更新年度经销合同签核状态（批量）
    annualDistributionContractStatus: /api/akesobiocsd/annualDistributionContract/approve
    annualDistributionContractUploadFile: /api/akesobiocsd/openApiAnnualAgreement/updateFile
    #更新经销商开户签核状态（批量）
    businessPartnerStatus: /api/akesobiocsd/accountApp/approve
    #新增 OA 签核流程（批量）
    approvalRecordInfo: /api/akesobiocsd/approve/save
    #新增采购订单（2000 条）
    purchaseOrderList: /api/akesobiocsd/salesOrder/initSave

    createReturnOrder: /api/akesobiocsd/returnOrder/save
    updateReturnOrder: /api/akesobiocsd/returnOrder/update
    approveReturnOrder: /api/akesobiocsd/returnOrder/approve

    updateDealer: /api/akesobiocsd/accountApp/update

    dataBaseurl: **********************************************************************
    ekpUser: sa
    ekpPassword: Sa12345678@

#oa系统配置文件
ekp:
  filePath: \\***********\kmss\resource

#数据分析系统配置文件
analysis:
  baseUrl: https://das.akesobio.com/prod-api

ding-talk:
  sfe:
    key: dingedubo6fjypz94ulg
    secret: P3ZqSnMm35Jl4YCcdTY4aPs7pZJhthpOl4R_p3ePFV7NIz12Ht7zS8MiMRl-QzOi

minio:
  url: http://**********:9090  # Minio服务地址
  accessKey: 49Si7Eur1fsNt7tc     # 用户名
  secretKey: Yl3fXPxRXc3BVIYQzDZIaVnLpg7bZ1ui     # 密码
  bucketName: cloudpense   # 存储桶名称

# DMS OpenAPI Configuration
dms:
  openapi:
    host: https://akesobiouat.recloud.com.cn  # Replace with actual DMS API host e.g., https://dms.example.com
    tenant-code: akesobiouat # Replace with actual DMS tenant code
    username: demo2 # Replace with actual DMS API username
    password: Jcyv2ori# # Replace with actual DMS API password

#倍通
bt:
  auth:
    url: https://akesobio-uat.sinoeyes.com:48888/webapi/api/akesobio/getToken
    sysCode: akesobioOA
    secret: akesobioOA@Test250527

    insertOrgMaster: https://akesobio-uat.sinoeyes.com:48888/webapi/api/akesobio/insertOrgMaster/1

