# CloudpenseApiUtils.addDocumentAttachments 方法使用说明

## 概述

在 `CloudpenseApiUtils` 类中新增了 `addDocumentAttachments` 方法，用于调用云简费控系统的单据附件添加接口。该功能仅支持添加单据头部附件，不支持删除或修改现有附件。

## 接口规格

- **接口路径**: `common/document/addAttachments`
- **HTTP方法**: POST
- **数据格式**: JSON
- **功能**: 为云简系统单据添加附件（仅支持添加，不支持删除/修改）
- **限制**: 目前只允许添加单据头部附件

## 方法签名

```java
// 完整版本，支持多个附件和指定语言环境
public JSONObject addDocumentAttachments(String externalId, List<Map<String, String>> attachments, String locale) throws Exception

// 简化版本，使用默认中文语言环境
public JSONObject addDocumentAttachments(String externalId, List<Map<String, String>> attachments) throws Exception

// 单个附件便捷方法，支持指定语言环境
public JSONObject addDocumentAttachment(String externalId, String fileName, String attachmentUrl, String attachmentTypeCode, String locale) throws Exception

// 单个附件便捷方法，使用默认中文语言环境
public JSONObject addDocumentAttachment(String externalId, String fileName, String attachmentUrl, String attachmentTypeCode) throws Exception
```

## 参数说明

### externalId (必填)
- **类型**: String
- **说明**: 外部系统单据号或云简系统单据号
- **限制**: 最大64字符
- **示例**: "EXP202407100001"

### attachments (必填)
- **类型**: List<Map<String, String>>
- **说明**: 附件对象列表，最多支持50个附件
- **每个附件对象包含以下可选字段**:
  - `file_name`: 文件名（可选，最大255字节）
  - `attachment_url`: 文件地址（可选，需通过文件上传接口预先获取）
  - `attachment_type_code`: 附件类型编码（可选）

### locale (可选)
- **类型**: String
- **说明**: 语言环境代码
- **支持值**: 
  - `zh_CN` - 简体中文（默认）
  - `zh_TW` - 繁体中文
  - `en_US` - 英语
  - `ja_JP` - 日语
- **默认值**: "zh_CN"

## 返回值

返回 `JSONObject` 对象，包含以下主要字段：

### 响应结构
```json
{
  "resCode": 200000,
  "resMsg": "成功",
  "bizId": "业务唯一标识",
  "data": {
    // 附件添加结果相关数据
  }
}
```

### 响应状态码
- **200000**: 添加成功
- **其他**: 添加失败，具体错误信息见 `resMsg` 字段

## 使用示例

### 示例1: 添加多个附件
```java
@Autowired
private CloudpenseApiUtils cloudpenseApiUtils;

public void addMultipleAttachments() {
    try {
        String externalId = "EXP202407100001";
        
        // 构建附件列表
        List<Map<String, String>> attachments = new ArrayList<>();
        
        // 第一个附件
        Map<String, String> attachment1 = new HashMap<>();
        attachment1.put("file_name", "发票.pdf");
        attachment1.put("attachment_url", "cpkfsw/16c6095b-2ba4-4cad-9b22-4f7791be2027.pdf");
        attachment1.put("attachment_type_code", "INVOICE");
        attachments.add(attachment1);
        
        // 第二个附件
        Map<String, String> attachment2 = new HashMap<>();
        attachment2.put("file_name", "收据.jpg");
        attachment2.put("attachment_url", "cpkfsw/28d7195c-3cb5-5dbe-ac33-5f8802cf3038.jpg");
        attachment2.put("attachment_type_code", "RECEIPT");
        attachments.add(attachment2);
        
        // 调用添加附件方法
        JSONObject result = cloudpenseApiUtils.addDocumentAttachments(externalId, attachments, "zh_CN");
        
        // 检查结果
        if (result.getIntValue("resCode") == 200000) {
            System.out.println("附件添加成功: " + result.getString("resMsg"));
        } else {
            System.err.println("附件添加失败: " + result.getString("resMsg"));
        }
        
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

### 示例2: 添加单个附件（便捷方法）
```java
public void addSingleAttachment() {
    try {
        String externalId = "EXP202407100001";
        String fileName = "合同.pdf";
        String attachmentUrl = "cpkfsw/39e8206d-4dc6-6ecf-bd44-6f9913dg4049.pdf";
        String attachmentTypeCode = "CONTRACT";
        
        // 调用单个附件添加方法
        JSONObject result = cloudpenseApiUtils.addDocumentAttachment(
            externalId, fileName, attachmentUrl, attachmentTypeCode);
        
        // 检查结果
        if (result.getIntValue("resCode") == 200000) {
            System.out.println("附件添加成功: " + result.getString("resMsg"));
        } else {
            System.err.println("附件添加失败: " + result.getString("resMsg"));
        }
        
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

### 示例3: 只提供文件名的附件
```java
public void addAttachmentWithNameOnly() {
    try {
        String externalId = "EXP202407100001";
        
        List<Map<String, String>> attachments = new ArrayList<>();
        Map<String, String> attachment = new HashMap<>();
        attachment.put("file_name", "备注文档.txt");
        // 不提供attachment_url和attachment_type_code
        attachments.add(attachment);
        
        JSONObject result = cloudpenseApiUtils.addDocumentAttachments(externalId, attachments);
        
        if (result.getIntValue("resCode") == 200000) {
            System.out.println("附件添加成功");
        }
        
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

### 示例4: 结合文件上传使用
```java
public void uploadAndAddAttachment() {
    try {
        String externalId = "EXP202407100001";
        String filePath = "D:/documents/invoice.pdf";
        
        // 1. 先上传文件获取附件URL
        String attachmentUrl = cloudpenseApiUtils.uploadFile(filePath);
        
        if (attachmentUrl != null) {
            // 2. 添加附件到单据
            JSONObject result = cloudpenseApiUtils.addDocumentAttachment(
                externalId, "发票.pdf", attachmentUrl, "INVOICE");
            
            if (result.getIntValue("resCode") == 200000) {
                System.out.println("文件上传并添加附件成功");
            }
        }
        
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

## 注意事项

1. **附件数量限制**: 最多支持50个附件（attachments到attachments50）
2. **文件名长度**: 文件名最大255字节
3. **单据号长度**: externalId最大64字符
4. **附件URL**: 需要通过文件上传接口预先获取
5. **仅支持添加**: 该接口只支持添加附件，不支持删除或修改现有附件
6. **头部附件**: 目前只允许添加单据头部附件
7. **可选字段**: 所有附件字段都是可选的，但至少需要提供一个字段

## 错误处理

方法会抛出以下异常：
- `IllegalArgumentException`: 参数验证失败
- `RuntimeException`: API调用失败或响应解析失败
- `Exception`: 其他异常情况

建议在调用时使用try-catch块进行异常处理。

## 相关方法

- `uploadFile(String filePath)`: 上传本地文件获取附件URL
- `uploadRemoteFiles(String fileName, String url)`: 上传远程文件获取附件URL
- `getDocumentDetail(String externalId)`: 查询单据详情（可查看现有附件）
