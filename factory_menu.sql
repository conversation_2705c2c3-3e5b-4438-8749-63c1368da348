-- 工厂管理菜单权限SQL脚本
-- 注意：执行前需要先确定自动台账模块的parent_id，这里假设为某个具体的ID值

-- 工厂管理主菜单 (需要根据实际情况修改parent_id)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('工厂管理', NULL, '6', 'factory', 'autoacct/factory/index', 1, 0, 'C', '0', '0', 'autoacct:factory:list', 'el-icon-office-building', 'admin', getdate(), '', null, '工厂管理菜单');

-- 获取刚插入的菜单ID
DECLARE @parentId BIGINT;
SELECT @parentId = SCOPE_IDENTITY();

-- 工厂查询权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('工厂查询', @parentId, '1', '#', '', 1, 0, 'F', '0', '0', 'autoacct:factory:query', '#', 'admin', getdate(), '', null, '');

-- 工厂新增权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('工厂新增', @parentId, '2', '#', '', 1, 0, 'F', '0', '0', 'autoacct:factory:add', '#', 'admin', getdate(), '', null, '');

-- 工厂修改权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('工厂修改', @parentId, '3', '#', '', 1, 0, 'F', '0', '0', 'autoacct:factory:edit', '#', 'admin', getdate(), '', null, '');

-- 工厂删除权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('工厂删除', @parentId, '4', '#', '', 1, 0, 'F', '0', '0', 'autoacct:factory:remove', '#', 'admin', getdate(), '', null, '');

-- 工厂导出权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('工厂导出', @parentId, '5', '#', '', 1, 0, 'F', '0', '0', 'autoacct:factory:export', '#', 'admin', getdate(), '', null, '');

-- 使用说明：
-- 1. 执行前请先查询自动台账模块的菜单ID：
--    SELECT menu_id FROM sys_menu WHERE menu_name = '自动台账' OR path = 'autoacct';
-- 2. 将上面查询到的menu_id替换第一个INSERT语句中的parent_id的NULL值
-- 3. 如果数据库不是SQL Server，请调整getdate()函数为对应数据库的当前时间函数
--    MySQL: NOW()
--    PostgreSQL: NOW()
--    Oracle: SYSDATE