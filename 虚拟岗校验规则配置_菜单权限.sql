-- =====================================================
-- 虚拟岗校验规则配置功能 - 菜单权限SQL脚本
-- 功能名称：虚拟岗校验规则配置
-- 所属模块：成本控制 (costControl)
-- 创建日期：2025-01-31
-- 说明：基于mq_config表的虚拟岗校验规则配置管理功能
-- =====================================================

-- 查询现有成本控制模块ID（如果不存在需要先创建）
-- SELECT menu_id, menu_name FROM sys_menu WHERE menu_name LIKE '%成本控制%' OR path = 'costControl';

-- 如果成本控制模块不存在，先创建模块菜单
-- INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- VALUES('成本控制', 0, '5', 'costControl', NULL, 1, 0, 'M', '0', '0', NULL, 'el-icon-money', 'admin', getdate(), '', null, '成本控制模块');

-- =====================================================
-- 主菜单：虚拟岗校验规则配置
-- =====================================================
INSERT INTO sys_menu (
    menu_name, 
    parent_id, 
    order_num, 
    path, 
    component, 
    is_frame, 
    is_cache, 
    menu_type, 
    visible, 
    status, 
    perms, 
    icon, 
    create_by, 
    create_time, 
    update_by, 
    update_time, 
    remark
)
VALUES(
    '虚拟岗校验规则配置',                                    -- 菜单名称
    (SELECT menu_id FROM sys_menu WHERE path = 'costControl' AND menu_type = 'M'),  -- 父菜单ID（成本控制模块）
    '3',                                                    -- 排序号
    'virtualPositionValidation',                            -- 路由路径
    'system/virtualPositionValidation/index',               -- 组件路径
    1,                                                      -- 非外链
    0,                                                      -- 不缓存
    'C',                                                    -- 菜单类型：组件
    '0',                                                    -- 显示
    '0',                                                    -- 正常状态
    'costControl:mqconfig:list',                            -- 权限标识
    'el-icon-setting',                                      -- 图标
    'admin',                                                -- 创建者
    getdate(),                                              -- 创建时间
    '',                                                     -- 更新者
    null,                                                   -- 更新时间
    '虚拟岗校验规则配置菜单'                                  -- 备注
);

-- 获取刚插入的主菜单ID
DECLARE @parentId BIGINT;
SELECT @parentId = SCOPE_IDENTITY();

-- =====================================================
-- 功能按钮权限
-- =====================================================

-- 1. 查询权限
INSERT INTO sys_menu (
    menu_name, parent_id, order_num, path, component, is_frame, is_cache, 
    menu_type, visible, status, perms, icon, create_by, create_time, 
    update_by, update_time, remark
)
VALUES(
    '虚拟岗配置查询', @parentId, '1', '#', '', 1, 0, 'F', '0', '0', 
    'costControl:mqconfig:query', '#', 'admin', getdate(), '', null, ''
);

-- 2. 新增权限
INSERT INTO sys_menu (
    menu_name, parent_id, order_num, path, component, is_frame, is_cache, 
    menu_type, visible, status, perms, icon, create_by, create_time, 
    update_by, update_time, remark
)
VALUES(
    '虚拟岗配置新增', @parentId, '2', '#', '', 1, 0, 'F', '0', '0', 
    'costControl:mqconfig:add', '#', 'admin', getdate(), '', null, ''
);

-- 3. 修改权限
INSERT INTO sys_menu (
    menu_name, parent_id, order_num, path, component, is_frame, is_cache, 
    menu_type, visible, status, perms, icon, create_by, create_time, 
    update_by, update_time, remark
)
VALUES(
    '虚拟岗配置修改', @parentId, '3', '#', '', 1, 0, 'F', '0', '0', 
    'costControl:mqconfig:edit', '#', 'admin', getdate(), '', null, ''
);

-- 4. 删除权限
INSERT INTO sys_menu (
    menu_name, parent_id, order_num, path, component, is_frame, is_cache, 
    menu_type, visible, status, perms, icon, create_by, create_time, 
    update_by, update_time, remark
)
VALUES(
    '虚拟岗配置删除', @parentId, '4', '#', '', 1, 0, 'F', '0', '0', 
    'costControl:mqconfig:remove', '#', 'admin', getdate(), '', null, ''
);

-- 5. 导出权限
INSERT INTO sys_menu (
    menu_name, parent_id, order_num, path, component, is_frame, is_cache, 
    menu_type, visible, status, perms, icon, create_by, create_time, 
    update_by, update_time, remark
)
VALUES(
    '虚拟岗配置导出', @parentId, '5', '#', '', 1, 0, 'F', '0', '0', 
    'costControl:mqconfig:export', '#', 'admin', getdate(), '', null, ''
);

-- =====================================================
-- 验证SQL - 查询创建的菜单
-- =====================================================
-- 查询主菜单
SELECT menu_id, menu_name, parent_id, path, component, perms, icon 
FROM sys_menu 
WHERE menu_name = '虚拟岗校验规则配置';

-- 查询功能按钮
SELECT m.menu_id, m.menu_name, m.perms, m.menu_type
FROM sys_menu m
WHERE m.parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '虚拟岗校验规则配置')
ORDER BY m.order_num;

-- =====================================================
-- 使用说明
-- =====================================================
/*
1. 执行前请确认：
   - 成本控制模块是否存在，如不存在请先创建
   - 路由路径 'virtualPositionValidation' 是否与现有菜单冲突
   - 权限标识 'costControl:mqconfig:*' 是否符合项目规范

2. 权限说明：
   - costControl:mqconfig:list   - 列表查询（主菜单权限）
   - costControl:mqconfig:query  - 详细查询
   - costControl:mqconfig:add    - 新增配置
   - costControl:mqconfig:edit   - 修改配置
   - costControl:mqconfig:remove - 删除配置
   - costControl:mqconfig:export - 导出Excel

3. 前端路由配置：
   - 路由路径：/virtualPositionValidation
   - 组件路径：system/virtualPositionValidation/index
   - 对应Vue文件：ruoyi-ui/src/views/system/virtualPositionValidation/index.vue

4. 如需调整：
   - 修改 order_num 调整菜单排序
   - 修改 parent_id 调整菜单层级
   - 修改 icon 更换菜单图标
*/
