# CloudpenseApiUtils.getDocumentDetail 方法使用说明

## 概述

在 `CloudpenseApiUtils` 类中新增了 `getDocumentDetail` 方法，用于调用云简费控系统的单据详情查询接口。

## 接口规格

- **接口路径**: `common/document/unique`
- **HTTP方法**: GET
- **请求参数**: `externalId`（String类型，外部系统单据号或云简单据号，必填）
- **响应格式**: JSON

## 方法签名

```java
// 完整版本，支持指定语言环境
public JSONObject getDocumentDetail(String externalId, String locale) throws Exception

// 简化版本，使用默认中文语言环境
public JSONObject getDocumentDetail(String externalId) throws Exception
```

## 参数说明

### externalId (必填)
- **类型**: String
- **说明**: 外部系统单据号或云简单据号
- **示例**: "EXP202407100001"

### locale (可选)
- **类型**: String
- **说明**: 语言环境代码
- **支持值**: 
  - `zh_CN` - 简体中文（默认）
  - `zh_TW` - 繁体中文
  - `en_US` - 英语
  - `ja_JP` - 日语
- **默认值**: "zh_CN"

## 返回值

返回 `JSONObject` 对象，包含以下主要字段：

### 响应结构
```json
{
  "resCode": 200000,
  "resMsg": "成功",
  "bizId": "业务唯一标识",
  "data": {
    "header": {
      "document_num": "单据号",
      "status": "单据状态",
      "employee_name": "申请人姓名",
      "total_amount": "总金额"
    },
    "header_links": [],
    "header_back_links": [],
    "claim_lines": [
      {
        "line_num": "行号",
        "expense_type_name": "费用类型",
        "amount": "金额",
        "description": "描述"
      }
    ],
    "custom_line": [],
    "budget_line": [],
    "budget_adjustment_line": [],
    "budget_transfer_line": [],
    "workflow_paths": [
      {
        "sequence_num": "序号",
        "employee_name": "审批人",
        "status": "审批状态",
        "note": "审批意见"
      }
    ]
  }
}
```

### 响应状态码
- **200000**: 查询成功
- **206001**: 单据不存在
- **其他**: 查询失败，具体错误信息见 `resMsg` 字段

## 使用示例

### 基本用法
```java
@Autowired
private CloudpenseApiUtils cloudpenseApiUtils;

public void queryDocument() {
    try {
        // 使用默认中文语言环境
        JSONObject response = cloudpenseApiUtils.getDocumentDetail("EXP202407100001");
        
        if (response.getInteger("resCode") == 200000) {
            JSONObject data = response.getJSONObject("data");
            JSONObject header = data.getJSONObject("header");
            
            System.out.println("单据号: " + header.getString("document_num"));
            System.out.println("状态: " + header.getString("status"));
            System.out.println("申请人: " + header.getString("employee_name"));
        } else if (response.getInteger("resCode") == 206001) {
            System.out.println("单据不存在");
        } else {
            System.out.println("查询失败: " + response.getString("resMsg"));
        }
        
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

### 指定语言环境
```java
public void queryDocumentWithLocale() {
    try {
        // 使用英文语言环境
        JSONObject response = cloudpenseApiUtils.getDocumentDetail("EXP202407100001", "en_US");
        
        // 处理响应...
        
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

### 获取费用行信息
```java
public void getExpenseLines() {
    try {
        JSONObject response = cloudpenseApiUtils.getDocumentDetail("EXP202407100001");
        
        if (response.getInteger("resCode") == 200000) {
            JSONObject data = response.getJSONObject("data");
            JSONArray claimLines = data.getJSONArray("claim_lines");
            
            if (claimLines != null) {
                for (int i = 0; i < claimLines.size(); i++) {
                    JSONObject line = claimLines.getJSONObject(i);
                    System.out.println("费用行 " + (i + 1) + ": " + 
                        line.getString("expense_type_name") + " - " + 
                        line.getBigDecimal("amount"));
                }
            }
        }
        
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

### 获取工作流信息
```java
public void getWorkflowInfo() {
    try {
        JSONObject response = cloudpenseApiUtils.getDocumentDetail("EXP202407100001");
        
        if (response.getInteger("resCode") == 200000) {
            JSONObject data = response.getJSONObject("data");
            JSONArray workflowPaths = data.getJSONArray("workflow_paths");
            
            if (workflowPaths != null) {
                for (int i = 0; i < workflowPaths.size(); i++) {
                    JSONObject workflow = workflowPaths.getJSONObject(i);
                    System.out.println("审批节点 " + workflow.getInteger("sequence_num") + ": " +
                        workflow.getString("employee_name") + " - " + 
                        workflow.getString("status"));
                }
            }
        }
        
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

## 异常处理

### 参数验证异常
```java
try {
    cloudpenseApiUtils.getDocumentDetail(""); // 空字符串
} catch (IllegalArgumentException e) {
    System.out.println("参数错误: " + e.getMessage());
}

try {
    cloudpenseApiUtils.getDocumentDetail("TEST001", "invalid_locale"); // 不支持的语言
} catch (IllegalArgumentException e) {
    System.out.println("语言环境不支持: " + e.getMessage());
}
```

### 网络异常处理
```java
try {
    JSONObject response = cloudpenseApiUtils.getDocumentDetail("EXP202407100001");
    // 处理响应...
} catch (Exception e) {
    System.out.println("查询异常: " + e.getMessage());
    // 记录日志或进行其他错误处理
}
```

## 注意事项

1. **参数验证**: `externalId` 不能为空或空字符串
2. **语言环境**: 仅支持 zh_CN、zh_TW、en_US、ja_JP 四种语言环境
3. **异常处理**: 方法可能抛出 `Exception`，需要适当处理
4. **响应解析**: 建议先检查 `resCode` 再处理 `data` 字段
5. **日志记录**: 方法内部已包含详细的日志记录，便于调试和监控
6. **性能考虑**: 每次调用都会获取新的访问令牌，批量查询时注意控制频率

## 完整示例

参考 `DocumentDetailExample.java` 文件中的完整示例代码，包含：
- 基本用法示例
- 多语言支持示例  
- 错误处理示例
- 批量查询示例
