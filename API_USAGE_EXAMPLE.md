# 动态数据查询API使用说明

## 接口概述

新增的动态数据查询API允许根据用户提供的参数动态查询主表和子表数据，并返回JSON格式的结果。

## 接口信息

- **URL**: `/costControl/YXHTUploadController/queryDynamicData`
- **方法**: POST
- **Content-Type**: application/json

## 请求参数

```json
{
    "documentNumber": "YXHT-20250409001",
    "mainTableName": "ekp_flwhtsp",
    "childTableNames": [
        "ekp_kp_flwhtsp_77be2bc29a",
        "ekp_kp_flwhtsp_a4e5a433e2"
    ]
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| documentNumber | String | 是 | 单号，如：'YXHT-20250409001' |
| mainTableName | String | 是 | 主表名称，如：'ekp_flwhtsp' |
| childTableNames | Array | 是 | 子表名称列表，如：['ekp_kp_flwhtsp_77be2bc29a', 'ekp_kp_flwhtsp_a4e5a433e2'] |

## 响应格式

### 成功响应

```json
{
    "code": 200,
    "msg": "数据查询成功",
    "data": "[{\"fd_id\":\"123\",\"fd_name\":\"测试数据\",\"fd_39f277be2bc29a\":[{\"fd_parent_id\":\"123\",\"field1\":\"value1\"}],\"fd_3b52a4e5a433e2\":[{\"fd_parent_id\":\"123\",\"field2\":\"value2\"}],\"kf_fdId\":\"123\"}]"
}
```

### 错误响应

```json
{
    "code": 500,
    "msg": "参数错误: 单号不能为空"
}
```

## 使用示例

### cURL 示例

```bash
curl -X POST "http://localhost:8080/costControl/YXHTUploadController/queryDynamicData" \
  -H "Content-Type: application/json" \
  -d '{
    "documentNumber": "YXHT-20250409001",
    "mainTableName": "ekp_flwhtsp",
    "childTableNames": [
      "ekp_kp_flwhtsp_77be2bc29a",
      "ekp_kp_flwhtsp_a4e5a433e2"
    ]
  }'
```

### JavaScript 示例

```javascript
const requestData = {
    documentNumber: "YXHT-20250409001",
    mainTableName: "ekp_flwhtsp",
    childTableNames: [
        "ekp_kp_flwhtsp_77be2bc29a",
        "ekp_kp_flwhtsp_a4e5a433e2"
    ]
};

fetch('/costControl/YXHTUploadController/queryDynamicData', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestData)
})
.then(response => response.json())
.then(data => {
    console.log('查询结果:', data);
    if (data.code === 200) {
        const jsonData = JSON.parse(data.data);
        console.log('解析后的数据:', jsonData);
    }
})
.catch(error => {
    console.error('请求失败:', error);
});
```

## 返回数据结构说明

返回的JSON数据是一个数组，每个元素代表一条主表记录，包含：

1. **主表字段**: 所有主表的原始字段
2. **子表数据**: 以特定key存储的子表数据数组
   - `fd_39f277be2bc29a`: 第一个子表的数据
   - `fd_3b52a4e5a433e2`: 第二个子表的数据
   - 更多子表使用 `child_table_N` 格式
3. **kf_fdId**: 主表记录ID的副本

## 错误处理

API会返回以下类型的错误：

- **参数错误** (400): 必填参数缺失或格式不正确
- **查询失败** (500): 数据库查询异常
- **系统错误** (500): 其他未知错误

## 安全注意事项

1. **SQL注入防护**: 表名会进行格式验证，只允许字母、数字和下划线
2. **参数验证**: 所有输入参数都会进行严格验证
3. **日志记录**: 所有请求和错误都会记录到日志中

## 性能考虑

1. 建议在数据库表的关联字段上建立索引
2. 对于大量数据的查询，考虑添加分页功能
3. 可以根据需要添加缓存机制

## 测试建议

1. 使用提供的测试类进行单元测试
2. 验证不同参数组合的查询结果
3. 测试异常情况的处理
