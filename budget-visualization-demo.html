<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>部门预算可视化系统 - 演示</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#3B82F6',
            secondary: '#6366F1',
            success: '#10B981',
            warning: '#F59E0B',
            danger: '#EF4444',
            neutral: '#6B7280',
            'neutral-light': '#F3F4F6',
            'neutral-dark': '#1F2937'
          },
          fontFamily: {
            inter: ['Inter', 'sans-serif'],
          },
        },
      }
    }
  </script>
  
  <style>
    body {
      font-family: 'Inter', sans-serif;
    }
    
    .tree-line {
      position: relative;
    }
    
    .tree-line::before {
      content: '';
      position: absolute;
      left: -20px;
      top: 0;
      height: 100%;
      width: 1px;
      background-color: #e5e7eb;
    }
    
    .tree-line::after {
      content: '';
      position: absolute;
      left: -20px;
      top: 24px;
      width: 16px;
      height: 1px;
      background-color: #e5e7eb;
    }
    
    .tree-item:last-child > .tree-line::before {
      height: 24px;
    }
    
    .card-hover {
      transition: all 0.3s ease;
    }
    
    .card-hover:hover {
      transform: translateY(-4px);
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    }
    
    .fade-in {
      animation: fadeIn 0.5s ease-in-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .progress-bar {
      height: 6px;
      background-color: #e5e7eb;
      border-radius: 3px;
      overflow: hidden;
      position: relative;
    }
    
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #3B82F6, #10B981);
      border-radius: 3px;
      transition: width 0.3s ease;
    }
    
    .block-item {
      position: relative;
      border-radius: 8px;
      margin: 4px;
      padding: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: white;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      min-height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    
    .block-item:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }
    
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #10B981;
      z-index: 1000;
      animation: slideIn 0.3s ease-out;
    }
    
    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>

<body class="bg-gray-50 text-gray-800 min-h-screen">
  <!-- 头部导航 -->
  <header class="bg-white shadow-sm sticky top-0 z-50">
    <div class="container mx-auto px-4 py-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-3">
          <i class="fa fa-line-chart text-primary text-2xl"></i>
          <h1 class="text-xl font-bold text-gray-800">部门预算可视化系统</h1>
          <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">演示版</span>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <i class="fa fa-calendar text-gray-500"></i>
            <select id="yearSelect" class="border rounded px-3 py-1 text-sm">
              <option value="2024">2024年</option>
              <option value="2023">2023年</option>
              <option value="2022">2022年</option>
            </select>
          </div>
          <button id="refreshBtn" class="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm flex items-center space-x-2">
            <i class="fa fa-refresh"></i>
            <span>刷新</span>
          </button>
          <button id="viewToggle" class="px-4 py-2 rounded-md bg-primary text-white flex items-center space-x-2 hover:bg-blue-600 transition-colors">
            <i class="fa fa-exchange"></i>
            <span>切换视图</span>
          </button>
        </div>
      </div>
    </div>
  </header>

  <main class="container mx-auto px-4 py-8">
    <!-- 树状视图 -->
    <div id="treeView" class="fade-in">
      <div class="bg-white rounded-xl shadow-md p-6 mb-8">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold flex items-center">
            <i class="fa fa-sitemap text-primary mr-2"></i>
            部门预算树状结构
          </h2>
          <div class="flex items-center space-x-4 text-sm text-gray-600">
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-blue-500 rounded"></div>
              <span>一级部门</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-green-500 rounded"></div>
              <span>二级部门</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-yellow-500 rounded"></div>
              <span>三级部门</span>
            </div>
          </div>
        </div>
        <div class="overflow-x-auto">
          <div id="budgetTree" class="pl-4">
            <!-- 树状图内容将由JS动态生成 -->
          </div>
        </div>
      </div>
    </div>

    <!-- 方块占比视图 -->
    <div id="blockView" class="fade-in hidden">
      <div class="bg-white rounded-xl shadow-md p-6 mb-8">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold flex items-center">
            <i class="fa fa-th-large text-primary mr-2"></i>
            部门预算占比可视化
          </h2>
          <div class="text-sm text-gray-600">
            <span>方块大小代表预算金额占比</span>
          </div>
        </div>
        <div id="blockChart" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- 方块占比图将由JS动态生成 -->
        </div>
      </div>
    </div>

    <!-- 预算详情卡片 -->
    <div id="detailCard" class="bg-white rounded-xl shadow-md p-6 mt-8 hidden fade-in">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-semibold flex items-center">
          <i class="fa fa-info-circle text-primary mr-2"></i>
          预算详情
          <span id="detailTitle" class="ml-2 text-gray-500"></span>
        </h2>
        <button id="closeDetail" class="text-gray-400 hover:text-gray-600 text-xl">
          <i class="fa fa-times"></i>
        </button>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
          <p class="text-sm opacity-90 mb-1">总预算</p>
          <p id="detailTotal" class="text-2xl font-bold">¥0</p>
        </div>
        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
          <p class="text-sm opacity-90 mb-1">本级预算</p>
          <p id="detailCurrent" class="text-2xl font-bold">¥0</p>
        </div>
        <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-4 text-white">
          <p class="text-sm opacity-90 mb-1">下级汇总</p>
          <p id="detailChildren" class="text-2xl font-bold">¥0</p>
        </div>
        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
          <p class="text-sm opacity-90 mb-1">预算执行率</p>
          <p id="detailUsage" class="text-2xl font-bold">0%</p>
        </div>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg font-semibold mb-3">预算构成</h3>
          <div id="detailChart" style="height: 300px;"></div>
        </div>
        <div>
          <h3 class="text-lg font-semibold mb-3">部门信息</h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-gray-600">部门编码:</span>
              <span id="detailCode" class="font-medium">-</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">部门层级:</span>
              <span id="detailLevel" class="font-medium">-</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">所属片区:</span>
              <span id="detailArea" class="font-medium">-</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">是否末级:</span>
              <span id="detailIsLeaf" class="font-medium">-</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">部门路径:</span>
              <span id="detailPath" class="font-medium text-sm">-</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- 页脚 -->
  <footer class="bg-gray-800 text-white py-8 mt-12">
    <div class="container mx-auto px-4">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="mb-4 md:mb-0">
          <p class="text-gray-400">© 2024 部门预算可视化系统 - 演示版本</p>
        </div>
        <div class="flex space-x-6">
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <i class="fa fa-question-circle mr-1"></i> 帮助文档
          </a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <i class="fa fa-envelope mr-1"></i> 技术支持
          </a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <i class="fa fa-github mr-1"></i> 源代码
          </a>
        </div>
      </div>
    </div>
  </footer>

  <script>
    // 预算数据
    const budgetData = {
      name: "总预算",
      departmentCode: "ROOT",
      departmentName: "总预算",
      level: 0,
      totalBudget: 3000000000,
      currentBudget: 0,
      childrenBudget: 3000000000,
      isLeaf: "N",
      area: "总部",
      departmentPath: "ROOT",
      departmentNamePath: "总预算",
      children: [
        {
          name: "商业运营部",
          departmentCode: "202",
          departmentName: "商业运营部",
          level: 1,
          totalBudget: 1598090183.40,
          currentBudget: 130643816.00,
          childrenBudget: 1467446367.40,
          isLeaf: "N",
          area: "YX-17商业运营",
          departmentPath: "202",
          departmentNamePath: "商业运营部",
          children: [
            {
              name: "市场中心",
              departmentCode: "16",
              departmentName: "市场中心",
              level: 2,
              totalBudget: 601910790.00,
              currentBudget: 0.00,
              childrenBudget: 601910790.00,
              isLeaf: "N",
              area: "",
              departmentPath: "202/16",
              departmentNamePath: "商业运营部 / 市场中心",
              children: [
                {
                  name: "品牌推广组",
                  departmentCode: "1601",
                  departmentName: "品牌推广组",
                  level: 3,
                  totalBudget: 300000000.00,
                  currentBudget: 300000000.00,
                  childrenBudget: 0.00,
                  isLeaf: "Y",
                  area: "",
                  departmentPath: "202/16/1601",
                  departmentNamePath: "商业运营部 / 市场中心 / 品牌推广组",
                  children: []
                },
                {
                  name: "市场分析组",
                  departmentCode: "1602",
                  departmentName: "市场分析组",
                  level: 3,
                  totalBudget: 301910790.00,
                  currentBudget: 301910790.00,
                  childrenBudget: 0.00,
                  isLeaf: "Y",
                  area: "",
                  departmentPath: "202/16/1602",
                  departmentNamePath: "商业运营部 / 市场中心 / 市场分析组",
                  children: []
                }
              ]
            },
            {
              name: "销售中心",
              departmentCode: "17",
              departmentName: "销售中心",
              level: 2,
              totalBudget: 865535577.40,
              currentBudget: 0.00,
              childrenBudget: 865535577.40,
              isLeaf: "N",
              area: "",
              departmentPath: "202/17",
              departmentNamePath: "商业运营部 / 销售中心",
              children: [
                {
                  name: "华北销售区",
                  departmentCode: "1701",
                  departmentName: "华北销售区",
                  level: 3,
                  totalBudget: 432767788.70,
                  currentBudget: 432767788.70,
                  childrenBudget: 0.00,
                  isLeaf: "Y",
                  area: "华北",
                  departmentPath: "202/17/1701",
                  departmentNamePath: "商业运营部 / 销售中心 / 华北销售区",
                  children: []
                },
                {
                  name: "华南销售区",
                  departmentCode: "1702",
                  departmentName: "华南销售区",
                  level: 3,
                  totalBudget: 432767788.70,
                  currentBudget: 432767788.70,
                  childrenBudget: 0.00,
                  isLeaf: "Y",
                  area: "华南",
                  departmentPath: "202/17/1702",
                  departmentNamePath: "商业运营部 / 销售中心 / 华南销售区",
                  children: []
                }
              ]
            }
          ]
        },
        {
          name: "研发中心",
          departmentCode: "203",
          departmentName: "研发中心",
          level: 1,
          totalBudget: 800000000.00,
          currentBudget: 50000000.00,
          childrenBudget: 750000000.00,
          isLeaf: "N",
          area: "研发园区",
          departmentPath: "203",
          departmentNamePath: "研发中心",
          children: [
            {
              name: "药物研发部",
              departmentCode: "2031",
              departmentName: "药物研发部",
              level: 2,
              totalBudget: 500000000.00,
              currentBudget: 500000000.00,
              childrenBudget: 0.00,
              isLeaf: "Y",
              area: "",
              departmentPath: "203/2031",
              departmentNamePath: "研发中心 / 药物研发部",
              children: []
            },
            {
              name: "临床试验部",
              departmentCode: "2032",
              departmentName: "临床试验部",
              level: 2,
              totalBudget: 250000000.00,
              currentBudget: 250000000.00,
              childrenBudget: 0.00,
              isLeaf: "Y",
              area: "",
              departmentPath: "203/2032",
              departmentNamePath: "研发中心 / 临床试验部",
              children: []
            }
          ]
        },
        {
          name: "财务中心",
          departmentCode: "204",
          departmentName: "财务中心",
          level: 1,
          totalBudget: 300000000.00,
          currentBudget: 100000000.00,
          childrenBudget: 200000000.00,
          isLeaf: "N",
          area: "总部",
          departmentPath: "204",
          departmentNamePath: "财务中心",
          children: [
            {
              name: "会计部",
              departmentCode: "2041",
              departmentName: "会计部",
              level: 2,
              totalBudget: 120000000.00,
              currentBudget: 120000000.00,
              childrenBudget: 0.00,
              isLeaf: "Y",
              area: "",
              departmentPath: "204/2041",
              departmentNamePath: "财务中心 / 会计部",
              children: []
            },
            {
              name: "审计部",
              departmentCode: "2042",
              departmentName: "审计部",
              level: 2,
              totalBudget: 80000000.00,
              currentBudget: 80000000.00,
              childrenBudget: 0.00,
              isLeaf: "Y",
              area: "",
              departmentPath: "204/2042",
              departmentNamePath: "财务中心 / 审计部",
              children: []
            }
          ]
        },
        {
          name: "人力资源部",
          departmentCode: "205",
          departmentName: "人力资源部",
          level: 1,
          totalBudget: 201909816.60,
          currentBudget: 101909816.60,
          childrenBudget: 100000000.00,
          isLeaf: "N",
          area: "总部",
          departmentPath: "205",
          departmentNamePath: "人力资源部",
          children: [
            {
              name: "招聘培训部",
              departmentCode: "2051",
              departmentName: "招聘培训部",
              level: 2,
              totalBudget: 100000000.00,
              currentBudget: 100000000.00,
              childrenBudget: 0.00,
              isLeaf: "Y",
              area: "",
              departmentPath: "205/2051",
              departmentNamePath: "人力资源部 / 招聘培训部",
              children: []
            }
          ]
        },
        {
          name: "IT中心",
          departmentCode: "206",
          departmentName: "IT中心",
          level: 1,
          totalBudget: 100000000.00,
          currentBudget: 30000000.00,
          childrenBudget: 70000000.00,
          isLeaf: "N",
          area: "总部",
          departmentPath: "206",
          departmentNamePath: "IT中心",
          children: [
            {
              name: "系统开发部",
              departmentCode: "2061",
              departmentName: "系统开发部",
              level: 2,
              totalBudget: 70000000.00,
              currentBudget: 70000000.00,
              childrenBudget: 0.00,
              isLeaf: "Y",
              area: "",
              departmentPath: "206/2061",
              departmentNamePath: "IT中心 / 系统开发部",
              children: []
            }
          ]
        }
      ]
    };

    // 当前选中的部门
    let selectedDepartment = null;
    let detailChart = null;

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      initializeApp();
    });

    function initializeApp() {
      renderTree(budgetData, document.getElementById('budgetTree'));
      bindEvents();
      showNotification('系统初始化完成', 'success');
    }

    function bindEvents() {
      // 视图切换
      document.getElementById('viewToggle').addEventListener('click', toggleView);
      
      // 刷新按钮
      document.getElementById('refreshBtn').addEventListener('click', refreshData);
      
      // 关闭详情
      document.getElementById('closeDetail').addEventListener('click', closeDetail);
      
      // 年度选择
      document.getElementById('yearSelect').addEventListener('change', function() {
        showNotification(`切换到${this.value}年度数据`, 'info');
        refreshData();
      });
    }

    function toggleView() {
      const treeView = document.getElementById('treeView');
      const blockView = document.getElementById('blockView');
      const toggleBtn = document.getElementById('viewToggle');
      
      if (treeView.classList.contains('hidden')) {
        // 切换到树状视图
        treeView.classList.remove('hidden');
        blockView.classList.add('hidden');
        toggleBtn.innerHTML = '<i class="fa fa-th-large"></i> <span>方块视图</span>';
        renderTree(budgetData, document.getElementById('budgetTree'));
      } else {
        // 切换到方块视图
        treeView.classList.add('hidden');
        blockView.classList.remove('hidden');
        toggleBtn.innerHTML = '<i class="fa fa-sitemap"></i> <span>树状视图</span>';
        renderBlockChart(budgetData);
      }
    }

    function refreshData() {
      const refreshBtn = document.getElementById('refreshBtn');
      const originalHTML = refreshBtn.innerHTML;
      
      refreshBtn.innerHTML = '<div class="loading"></div><span>刷新中...</span>';
      refreshBtn.disabled = true;
      
      // 模拟数据刷新
      setTimeout(() => {
        const treeView = document.getElementById('treeView');
        const blockView = document.getElementById('blockView');
        
        if (!treeView.classList.contains('hidden')) {
          renderTree(budgetData, document.getElementById('budgetTree'));
        } else {
          renderBlockChart(budgetData);
        }
        
        refreshBtn.innerHTML = originalHTML;
        refreshBtn.disabled = false;
        showNotification('数据刷新完成', 'success');
      }, 1000);
    }

    // 渲染树状图
    function renderTree(data, container, level = 0) {
      container.innerHTML = '';
      
      if (data.children && data.children.length > 0) {
        data.children.forEach(child => {
          const treeItem = document.createElement('div');
          treeItem.className = 'mb-4 tree-item';
          
          const itemContent = document.createElement('div');
          itemContent.className = `flex items-center p-4 rounded-lg cursor-pointer transition-all card-hover ${getBackgroundColor(child.level)}`;
          itemContent.style.marginLeft = `${level * 20}px`;
          
          // 展开/折叠按钮
          if (child.children && child.children.length > 0) {
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'mr-3 text-gray-500 hover:text-primary transition-colors';
            toggleBtn.innerHTML = '<i class="fa fa-chevron-down"></i>';
            toggleBtn.addEventListener('click', function(e) {
              e.stopPropagation();
              const icon = this.querySelector('i');
              const childContainer = this.parentElement.nextElementSibling;
              
              if (icon.classList.contains('fa-chevron-down')) {
                icon.classList.replace('fa-chevron-down', 'fa-chevron-right');
                childContainer.style.display = 'none';
              } else {
                icon.classList.replace('fa-chevron-right', 'fa-chevron-down');
                childContainer.style.display = 'block';
              }
            });
            itemContent.appendChild(toggleBtn);
          } else {
            const emptySpace = document.createElement('div');
            emptySpace.className = 'w-8 h-8 mr-3';
            itemContent.appendChild(emptySpace);
          }
          
          // 部门信息
          const deptInfo = document.createElement('div');
          deptInfo.className = 'flex-1 min-w-0';
          deptInfo.innerHTML = `
            <div class="flex items-center space-x-2">
              <span class="font-semibold text-gray-800">${child.departmentName}</span>
              <span class="px-2 py-1 text-xs rounded-full ${getLevelBadgeColor(child.level)}">${child.departmentCode}</span>
              <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">L${child.level}</span>
            </div>
            <div class="text-sm text-gray-600 mt-1">${child.departmentNamePath}</div>
          `;
          itemContent.appendChild(deptInfo);
          
          // 预算信息
          const budgetInfo = document.createElement('div');
          budgetInfo.className = 'flex items-center space-x-6';
          
          const budgetDetails = document.createElement('div');
          budgetDetails.className = 'text-right';
          budgetDetails.innerHTML = `
            <div class="text-sm text-gray-600">总预算: <span class="font-semibold text-blue-600">${formatCurrency(child.totalBudget)}</span></div>
            <div class="text-sm text-gray-600">本级: <span class="font-medium">${formatCurrency(child.currentBudget)}</span> | 下级: <span class="font-medium">${formatCurrency(child.childrenBudget)}</span></div>
          `;
          
          const usagePercent = Math.round(Math.random() * 80 + 20);
          const progressContainer = document.createElement('div');
          progressContainer.className = 'flex items-center space-x-2';
          progressContainer.innerHTML = `
            <div class="w-24 progress-bar">
              <div class="progress-fill" style="width: ${usagePercent}%"></div>
            </div>
            <span class="text-sm font-medium text-gray-600">${usagePercent}%</span>
          `;
          
          budgetInfo.appendChild(budgetDetails);
          budgetInfo.appendChild(progressContainer);
          itemContent.appendChild(budgetInfo);
          
          // 点击事件
          itemContent.addEventListener('click', function() {
            selectDepartment(child);
          });
          
          treeItem.appendChild(itemContent);
          
          // 子部门容器
          if (child.children && child.children.length > 0) {
            const childrenContainer = document.createElement('div');
            childrenContainer.className = 'ml-6 mt-2 tree-line';
            renderTree(child, childrenContainer, level + 1);
            treeItem.appendChild(childrenContainer);
          }
          
          container.appendChild(treeItem);
        });
      }
    }

    // 渲染方块占比图
    function renderBlockChart(data) {
      const container = document.getElementById('blockChart');
      container.innerHTML = '';
      
      if (data.children && data.children.length > 0) {
        data.children.forEach(child => {
          const blockItem = document.createElement('div');
          blockItem.className = 'block-item';
          
          const percentage = Math.round((child.totalBudget / data.totalBudget) * 100);
          const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];
          const color = colors[(child.level - 1) % colors.length];
          
          blockItem.style.backgroundColor = color;
          blockItem.style.minHeight = `${Math.max(150, percentage * 3)}px`;
          
          const usagePercent = Math.round(Math.random() * 80 + 20);
          
          blockItem.innerHTML = `
            <div>
              <h3 class="font-bold text-lg mb-1">${child.departmentName}</h3>
              <p class="text-sm opacity-90">${child.departmentCode}</p>
            </div>
            <div>
              <div class="text-xl font-bold">${formatCurrency(child.totalBudget)}</div>
              <div class="text-sm opacity-90">占比: ${percentage}%</div>
              <div class="mt-2">
                <div class="w-full bg-white bg-opacity-30 rounded-full h-2">
                  <div class="bg-white h-2 rounded-full" style="width: ${usagePercent}%"></div>
                </div>
                <div class="text-xs mt-1 opacity-90">执行率: ${usagePercent}%</div>
              </div>
            </div>
          `;
          
          blockItem.addEventListener('click', function() {
            selectDepartment(child);
          });
          
          container.appendChild(blockItem);
        });
      }
    }

    // 选择部门并显示详情
    function selectDepartment(department) {
      selectedDepartment = department;
      
      // 更新详情卡片
      document.getElementById('detailTitle').textContent = `- ${department.departmentName}`;
      document.getElementById('detailTotal').textContent = formatCurrency(department.totalBudget);
      document.getElementById('detailCurrent').textContent = formatCurrency(department.currentBudget);
      document.getElementById('detailChildren').textContent = formatCurrency(department.childrenBudget);
      
      // 模拟执行率
      const usageRate = Math.round(Math.random() * 40 + 40);
      document.getElementById('detailUsage').textContent = `${usageRate}%`;
      
      // 更新部门信息
      document.getElementById('detailCode').textContent = department.departmentCode;
      document.getElementById('detailLevel').textContent = `第${department.level}级`;
      document.getElementById('detailArea').textContent = department.area || '无';
      document.getElementById('detailIsLeaf').textContent = department.isLeaf === 'Y' ? '是' : '否';
      document.getElementById('detailPath').textContent = department.departmentNamePath;
      
      // 显示详情卡片
      const detailCard = document.getElementById('detailCard');
      detailCard.classList.remove('hidden');
      
      // 滚动到详情卡片
      detailCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
      
      // 渲染详情图表
      renderDetailChart(department);
      
      showNotification(`已选择部门: ${department.departmentName}`, 'info');
    }

    // 渲染详情图表
    function renderDetailChart(department) {
      const chartContainer = document.getElementById('detailChart');
      
      if (detailChart) {
        detailChart.dispose();
      }
      
      detailChart = echarts.init(chartContainer);
      
      const option = {
        title: {
          text: '预算构成分析',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['本级预算', '下级汇总']
        },
        series: [
          {
            name: '预算构成',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '20',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { 
                value: department.currentBudget, 
                name: '本级预算',
                itemStyle: { color: '#3B82F6' }
              },
              { 
                value: department.childrenBudget, 
                name: '下级汇总',
                itemStyle: { color: '#10B981' }
              }
            ]
          }
        ]
      };
      
      detailChart.setOption(option);
    }

    // 关闭详情卡片
    function closeDetail() {
      selectedDepartment = null;
      document.getElementById('detailCard').classList.add('hidden');
      
      if (detailChart) {
        detailChart.dispose();
        detailChart = null;
      }
    }

    // 辅助函数
    function formatCurrency(value) {
      if (value === null || value === undefined) return '¥0';
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    }

    function getBackgroundColor(level) {
      const colors = {
        1: 'bg-blue-50 hover:bg-blue-100',
        2: 'bg-green-50 hover:bg-green-100',
        3: 'bg-yellow-50 hover:bg-yellow-100',
        4: 'bg-purple-50 hover:bg-purple-100'
      };
      return colors[level] || 'bg-gray-50 hover:bg-gray-100';
    }

    function getLevelBadgeColor(level) {
      const colors = {
        1: 'bg-blue-100 text-blue-800',
        2: 'bg-green-100 text-green-800',
        3: 'bg-yellow-100 text-yellow-800',
        4: 'bg-purple-100 text-purple-800'
      };
      return colors[level] || 'bg-gray-100 text-gray-800';
    }

    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = 'notification';
      
      const colors = {
        success: 'border-green-500',
        info: 'border-blue-500',
        warning: 'border-yellow-500',
        error: 'border-red-500'
      };
      
      const icons = {
        success: 'fa-check-circle text-green-500',
        info: 'fa-info-circle text-blue-500',
        warning: 'fa-exclamation-triangle text-yellow-500',
        error: 'fa-times-circle text-red-500'
      };
      
      notification.classList.add(colors[type]);
      notification.innerHTML = `
        <div class="flex items-center space-x-2">
          <i class="fa ${icons[type]}"></i>
          <span>${message}</span>
        </div>
      `;
      
      document.body.appendChild(notification);
      
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // 窗口大小改变时重新渲染图表
    window.addEventListener('resize', function() {
      if (detailChart) {
        detailChart.resize();
      }
    });
  </script>
</body>
</html>