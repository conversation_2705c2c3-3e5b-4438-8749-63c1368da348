# 虚拟岗校验规则配置功能完整实现方案

## 原始提示词补全

**原始提示词：**
请基于 akesobio-report/src/main/java/com/akesobio/report/costControl/doc/eventSql 目录中的数据结构和字段信息，为"虚拟岗校验规则配置"功能创建一个完整的 Vue.js Web UI 界面。

**补全后的完整提示词：**

请基于 akesobio-report/src/main/java/com/akesobio/report/costControl/doc/eventSql 目录中的数据结构和字段信息，为"虚拟岗校验规则配置"功能创建一个完整的前后端实现。

### 数据结构分析

根据 `虚拟岗配置事件模板.txt` 和 `20250217-差旅申请单需对接考勤系统.sql` 文件，mq_config 表结构如下：

```sql
CREATE TABLE mq_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    company_id BIGINT NOT NULL COMMENT '公司ID',
    exchange VARCHAR(100) NOT NULL COMMENT '交换机',
    rooting_key VARCHAR(200) NOT NULL COMMENT '路由键',
    platform VARCHAR(50) NOT NULL COMMENT '平台',
    interface_name VARCHAR(100) NOT NULL COMMENT '接口名称',
    description TEXT COMMENT '描述',
    event_code VARCHAR(50) NOT NULL COMMENT '事件代码',
    header_type_code VARCHAR(50) NOT NULL COMMENT '单据类型代码',
    position_code VARCHAR(50) NOT NULL COMMENT '岗位编码',
    workflow_type VARCHAR(50) COMMENT '工作流类型',
    created_by VARCHAR(100) COMMENT '创建人',
    creation_date DATETIME COMMENT '创建时间',
    last_updated_by VARCHAR(100) COMMENT '最后更新人',
    last_update_date DATETIME COMMENT '最后更新时间',
    remark TEXT COMMENT '备注'
);
```

### 后端实现要求

#### 1. 实体类 (Domain)
- **文件位置**: `akesobio-report/src/main/java/com/akesobio/report/costControl/domain/MqConfig.java`
- **要求**:
  - 继承 BaseEntity
  - 包含完整的字段映射和验证注解
  - 支持 Excel 导出注解

#### 2. 数据访问层 (Mapper)
- **接口文件**: `akesobio-report/src/main/java/com/akesobio/report/costControl/mapper/MqConfigMapper.java`
- **XML文件**: `akesobio-report/src/main/resources/mapper/costControl/MqConfigMapper.xml`
- **要求**:
  - 继承 BaseMapper<MqConfig>
  - 实现分页查询、条件查询、唯一性校验
  - 支持模糊查询和日期范围查询

#### 3. 业务逻辑层 (Service)
- **接口文件**: `akesobio-report/src/main/java/com/akesobio/report/costControl/service/IMqConfigService.java`
- **实现文件**: `akesobio-report/src/main/java/com/akesobio/report/costControl/service/impl/MqConfigServiceImpl.java`
- **要求**:
  - 实现 CRUD 操作
  - 接口名称唯一性校验
  - 支持批量删除

#### 4. 控制器层 (Controller)
- **文件位置**: `akesobio-report/src/main/java/com/akesobio/report/costControl/controller/MqConfigController.java`
- **要求**:
  - 继承 BaseController
  - 实现 RESTful API
  - 包含权限控制注解
  - 支持导出功能
  - 遵循项目现有的 API 响应格式
  - API路径前缀: `/costControl/mqconfig`

### 前端实现要求

#### 1. API 接口文件
- **文件位置**: `ruoyi-ui/src/api/system/mqconfig.js`
- **要求**:
  - 封装所有后端 API 调用
  - 使用项目统一的 request 工具

#### 2. Vue 组件
- **文件位置**: `ruoyi-ui/src/views/system/virtualPositionValidation/index.vue`
- **功能需求**：
  - 查询功能：支持分页查询和条件筛选
  - 修改功能：支持编辑现有配置记录
  - 新增功能：支持添加新的校验规则配置
  - 不需要删除功能（按用户要求）

#### 3. 技术实现要求：
- 使用 Vue.js 框架（基于项目现有的技术栈）
- 遵循项目现有的代码风格和组件结构
- 参考 ruoyi-ui/src/views/system/config/index.vue 的实现模式
- 包含表格展示、搜索表单、新增/编辑弹窗等标准组件

#### 4. UI 设计要求：
- 遵循项目现有的 UI 设计规范
- 包含适当的表单验证
- 提供用户友好的操作反馈
- 支持响应式布局

### 字段说明和业务规则

1. **事件代码 (event_code)** 可选值：
   - `wfp`: 开始审批
   - `wfa`: 审批通过
   - `expsubmitted`: 单据提交
   - `expapproved`: 审批通过

2. **路由键 (rooting_key)** 规则：
   - 测试环境使用 `aliqa`
   - 生产环境使用 `aliprod`

3. **必填字段验证**：
   - 公司ID、交换机、路由键、平台、接口名称、事件代码、单据类型代码、岗位编码

4. **唯一性约束**：
   - 接口名称需要保证唯一性

### 权限配置

需要在系统中配置以下权限：
- `costControl:mqconfig:list` - 查询权限
- `costControl:mqconfig:query` - 详情查询权限
- `costControl:mqconfig:add` - 新增权限
- `costControl:mqconfig:edit` - 修改权限
- `costControl:mqconfig:export` - 导出权限

### 测试建议

1. **单元测试**：为 Service 层编写单元测试
2. **集成测试**：测试完整的 CRUD 操作流程
3. **前端测试**：验证表单验证、数据展示、交互功能
4. **权限测试**：验证各个权限点的控制效果

### 部署注意事项

1. 确保数据库中存在 mq_config 表
2. 配置相应的菜单和权限
3. 验证前后端 API 接口的连通性
4. 测试分页、搜索、导出等功能

这个完整的实现方案涵盖了从数据库设计到前端界面的所有层面，确保功能的完整性和可维护性。
