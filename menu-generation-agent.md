# Menu Generation Agent for Akesobio Reporting System

## Agent Purpose
This agent guides AI in generating SQL scripts for creating or updating menu items in the Akesobio Reporting System based on the project's established patterns and database structure.

## System Overview
The system uses a hierarchical menu structure stored in the `sys_menu` table with the following key characteristics:
- **Database**: SQL Server
- **Menu Types**: 
  - `M` = Module/Directory (parent menus)
  - `C` = Component (page menus)
  - `F` = Function (button permissions)
- **Permission Pattern**: `module:feature:action` (e.g., `autoacct:factory:list`)

## Menu Generation Template Analysis

### 1. Base SQL Template Structure (from sql.vm)
```sql
-- 主菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}', '${parentMenuId}', '1', '${businessName}', '${moduleName}/${businessName}/index', 1, 0, 'C', '0', '0', '${permissionPrefix}:list', '#', 'admin', getdate(), '', null, '${functionName}菜单');

-- 获取父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 功能按钮 SQL (查询、新增、修改、删除、导出)
```

### 2. Practical Implementation Example (from factory_menu.sql)
```sql
-- 工厂管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('工厂管理', NULL, '6', 'factory', 'autoacct/factory/index', 1, 0, 'C', '0', '0', 'autoacct:factory:list', 'el-icon-office-building', 'admin', getdate(), '', null, '工厂管理菜单');

-- 获取刚插入的菜单ID
DECLARE @parentId BIGINT;
SELECT @parentId = SCOPE_IDENTITY();

-- 功能权限按钮
INSERT INTO sys_menu (...) VALUES('工厂查询', @parentId, '1', '#', '', 1, 0, 'F', '0', '0', 'autoacct:factory:query', '#', 'admin', getdate(), '', null, '');
```

## Menu Generation Guidelines

### 1. Field Requirements Analysis

| Field | Description | Pattern/Rules |
|-------|-------------|---------------|
| `menu_name` | 显示名称 | 业务功能中文名，如"工厂管理"、"成本控制" |
| `parent_id` | 父菜单ID | NULL为顶级，或查询现有模块ID |
| `order_num` | 排序号 | 递增数字，同级菜单中唯一 |
| `path` | 路由路径 | 英文小写，如'factory'、'cost-control' |
| `component` | 组件路径 | `{module}/{feature}/index` 格式 |
| `is_frame` | 是否外链 | 1=否，0=是 |
| `is_cache` | 是否缓存 | 0=不缓存，1=缓存 |
| `menu_type` | 菜单类型 | 'M'=目录，'C'=菜单，'F'=按钮 |
| `visible` | 是否显示 | '0'=显示，'1'=隐藏 |
| `status` | 状态 | '0'=正常，'1'=停用 |
| `perms` | 权限标识 | `{module}:{feature}:{action}` |
| `icon` | 图标 | Element UI图标类名 |
| `create_by` | 创建者 | 固定'admin' |
| `create_time` | 创建时间 | `getdate()` (SQL Server) |
| `remark` | 备注 | 功能描述 |

### 2. Permission Naming Convention
- **Module Prefixes**: 
  - `system:` - 系统管理
  - `monitor:` - 系统监控
  - `autoacct:` - 自动台账
  - `clinical:` - 临床运营
  - `cost:` - 成本控制
  - `admin:` - 行政管理
  - `hr:` - 人力资源

- **Standard Actions**:
  - `:list` - 列表查询
  - `:query` - 详细查询
  - `:add` - 新增
  - `:edit` - 修改
  - `:remove` - 删除
  - `:export` - 导出
  - `:import` - 导入

### 3. Icon Selection Guidelines
Common icons used in the system:
- `el-icon-office-building` - 工厂/建筑相关
- `el-icon-goods` - 物料/商品
- `el-icon-document` - 文档/报表
- `el-icon-user` - 用户相关
- `el-icon-setting` - 配置/设置
- `el-icon-data-line` - 数据/统计
- `el-icon-money` - 财务/成本

## AI Prompt Template

When generating menu SQL, use this structure:

```
请为 {功能名称} 生成完整的菜单权限SQL脚本，要求：

**功能信息**：
- 功能名称：{中文名称}
- 所属模块：{模块名，如autoacct、clinical等}
- 父菜单：{父菜单名称或NULL}
- 路由路径：{英文路径}
- 组件路径：{module/feature/index}
- 权限前缀：{module:feature}

**生成要求**：
1. 包含主菜单和5个标准功能按钮（查询、新增、修改、删除、导出）
2. 使用SQL Server语法（getdate()、SCOPE_IDENTITY()）
3. 遵循项目命名规范和权限模式
4. 包含适当的图标和排序号
5. 添加必要的注释和使用说明

**标准功能按钮**：
- 查询权限：{prefix}:query
- 新增权限：{prefix}:add  
- 修改权限：{prefix}:edit
- 删除权限：{prefix}:remove
- 导出权限：{prefix}:export
```

## SQL Server Specific Notes
- Use `getdate()` instead of `NOW()`
- Use `SCOPE_IDENTITY()` instead of `LAST_INSERT_ID()`
- Use `DECLARE @var TYPE` for variables
- String values use single quotes

## Validation Checklist
Before executing generated SQL:
1. ✅ 确认父菜单ID存在或为NULL
2. ✅ 权限标识唯一且符合命名规范
3. ✅ 路由路径不与现有冲突
4. ✅ 排序号在同级菜单中合理
5. ✅ 图标类名有效
6. ✅ SQL语法适配SQL Server

## Usage Example

```sql
-- 查询现有模块ID
SELECT menu_id, menu_name FROM sys_menu WHERE menu_type = 'M' AND parent_id = 0;

-- 查询自动台账模块ID
SELECT menu_id FROM sys_menu WHERE menu_name = '自动台账' OR path = 'autoacct';

-- 执行生成的菜单SQL
-- [生成的SQL脚本]
```

This agent ensures consistent, compliant menu generation following the Akesobio Reporting System's established patterns and database structure.