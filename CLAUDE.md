# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is "康方报表管理系统" (Akesobio Reporting System), an enterprise reporting and management system built on the RuoYi framework. It's a multi-module Maven Spring Boot application with a Vue.js frontend, using SQL Server as the primary database.

## Common Commands

### Backend Development
- **Build**: `mvn clean package -Dspring.profiles.active=prod -Dmaven.test.skip=true`
- **Development Run**: `mvn spring-boot:run` (from akesobio-admin directory)
- **Production Deployment**: `./ry.sh start|stop|restart|status`
- **Test**: `mvn test`
- **Clean**: `mvn clean`

### Frontend Development (ruoyi-ui directory)
- **Development Server**: `npm run dev`
- **Production Build**: `npm run build:prod`
- **Development Build**: `npm run build:dev`
- **Staging Build**: `npm run build:stage`
- **Lint**: `npm run lint`

## Architecture

### Module Structure
The project follows a layered multi-module Maven architecture:

- **akesobio-admin**: Main application entry point, web controllers, and REST APIs
- **akesobio-framework**: Core framework configuration (Spring Security, MyBatis Plus, Redis, JWT)
- **akesobio-common**: Shared utilities, constants, annotations, and domain objects
- **akesobio-system**: System management (users, roles, permissions, departments)
- **akesobio-report**: Business reporting logic and domain-specific functionality
- **akesobio-quartz**: Scheduled task management using Quartz
- **akesobio-generator**: Code generation utilities
- **ruoyi-ui**: Vue.js frontend application

### Key Technologies
- **Backend**: Spring Boot 2.5.14, Spring Security + JWT, MyBatis Plus, SQL Server
- **Frontend**: Vue.js 2.6.12, Element UI, ECharts, Vuex
- **Infrastructure**: Redis caching, Quartz scheduling, MinIO file storage
- **Documentation**: Swagger 3.0 API docs

### Database Configuration
- Primary database: SQL Server (migrated from MySQL)
- PageHelper configured for SQL Server dialect
- MyBatis Plus with automatic table field mapping
- Connection pooling via Druid

## Configuration Files

### Key Configuration Locations
- **Main Config**: `akesobio-admin/src/main/resources/application.yml` (port 9966, profiles, MyBatis Plus)
- **Environment Configs**: `application-{env}.yml` files for different environments
- **Database**: Configured in environment-specific yml files
- **Frontend Config**: `ruoyi-ui/src/settings.js` and environment files

### Profiles
- `dev`: Development environment
- `prod`: Production environment  
- `test`: Testing environment
- Current active profile is set to `prod` in application.yml

## Business Domains

The system handles multiple business areas:
- **Clinical Operations**: Trial management, reimbursements, materials tracking
- **Cost Control**: Budget management, expense reporting
- **Administration**: Catering and meal management
- **Auto Accounting**: Material inventory, procurement tracking
- **HR Management**: Employee data, attendance systems
- **Finance**: Financial reporting and analysis
- **Procurement**: Purchase order management
- **Legal & Compliance**: Document management

## Development Guidelines

### Code Organization
- Controllers in `*Controller.java` follow REST conventions
- Service layer in `service/impl/*ServiceImpl.java` with interfaces in `service/I*Service.java`
- Domain objects in `domain/` directory
- Mappers in `mapper/` with corresponding XML files in `resources/mapper/`
- DTOs and VOs in appropriate packages under domain

### Database Operations
- Use MyBatis Plus for database operations
- Entity classes should extend BaseEntity when applicable
- Use QueryWrapperX and LambdaQueryWrapperX for complex queries
- Page queries use PageHelper for pagination

### Frontend Structure
- Components in `ruoyi-ui/src/components/`
- Views in `ruoyi-ui/src/views/` organized by business domain
- API calls in `ruoyi-ui/src/api/` matching backend controller structure
- Shared utilities in `ruoyi-ui/src/utils/`

### Security
- JWT-based authentication with configurable token expiration
- Role-based access control (RBAC) with data scope permissions
- XSS protection enabled for specified URL patterns
- File upload restrictions: 10MB per file, 20MB total request size

## Deployment

### Production Deployment
1. Build backend: `mvn clean package -Dspring.profiles.active=prod -Dmaven.test.skip=true`
2. Build frontend: `npm run build:prod` (from ruoyi-ui directory)
3. Deploy using: `./ry.sh start`
4. Monitor with: `./ry.sh status`

### JVM Configuration
- Heap: 512m-1024m
- Metaspace: 128m-512m
- GC: Parallel GC for both young and old generation
- Timezone: Asia/Shanghai

## External Integrations

The system integrates with multiple external systems:
- OA (Office Automation) systems for workflow management
- ERP systems for business data synchronization  
- Third-party APIs for various data sources
- Document management systems for file handling

## File Management

- Upload path configurable via `akesobio.profile` property
- MinIO support available for distributed file storage
- Excel import/export functionality using EasyExcel and Apache POI
- Image processing capabilities included