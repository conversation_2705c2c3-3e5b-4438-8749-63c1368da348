# AutoAcct模块技术规格文档

## 1. 模块概述

### 1.1 业务背景
AutoAcct（自动核算）模块是一个企业级物料管理和库存核算系统，主要用于：
- 物料需求规划与BOM管理
- 库存占用分析与缺口计算
- 生产计划与物料计划协调
- 多维度报表生成与数据分析
- 多工厂物料数据统一管理

### 1.2 核心功能
- **物料主数据管理**：物料基础信息、规格、计量单位等
- **BOM物料需求管理**：生产工序、批用量、替代组管理
- **库存核算分析**：实时库存、占用明细、缺口分析
- **生产计划管理**：MPM编码、生产计划、物料计划
- **报表生成**：横向汇总报表、月度发货计划、缺口分析报表
- **数据集成**：Excel导入导出、钉钉通知、多系统集成

### 1.3 技术架构
- **后端**：Spring Boot + MyBatis-Plus + Redis + 异步处理
- **前端**：Vue.js + Element UI + Vuex状态管理
- **数据库**：MySQL（多数据源支持）
- **集成**：钉钉机器人、Excel处理、文件管理

## 2. 后端技术规格

### 2.1 包结构设计

```
com.akesobio.report.autoacct/
├── controller/          # REST API控制器层
├── domain/             # 数据模型层
│   ├── excle/         # Excel导入导出模型
│   │   ├── export/    # 导出VO类
│   │   └── impot/     # 导入模型类  
│   └── vo/            # 视图对象类
├── mapper/            # MyBatis数据访问层
└── service/           # 业务逻辑层
    └── impl/          # 服务实现类
```

### 2.2 Controller层API规格

#### 2.2.1 核心控制器

**InventoryOccupationDetailController** - 库存占用明细API
```java
@RestController
@RequestMapping("/autoacct/inventoryOccupationDetail")
public class InventoryOccupationDetailController {
    
    // 基础CRUD操作
    @GetMapping("/list")                    // 查询库存占用明细列表
    @GetMapping("/{taskId}")               // 获取单个明细
    @PostMapping                           // 新增库存占用明细
    @PutMapping                            // 更新库存占用明细
    @DeleteMapping("/{taskIds}")           // 删除库存占用明细
    
    // 报表生成功能
    @PostMapping("/generateMonthlyShippingPlanReport")  // 生成月度发货计划报表
    @PostMapping("/generateMaterialRequirementReport")  // 生成物料需求横向汇总报表
    @PostMapping("/exportGap")                          // 导出缺口分析
    
    // 数据处理功能
    @PostMapping("/refreshInventoryOccupationDetail")   // 异步刷新库存占用明细
    @PostMapping("/import")                             // Excel数据导入
    @PostMapping("/export")                             // Excel数据导出
}
```

**BomMaterialRequireController** - BOM物料需求API
```java
@RestController
@RequestMapping("/autoacct/bomMaterialRequire")
public class BomMaterialRequireController {
    
    @GetMapping("/list")                    // 查询BOM物料需求列表（关联物料主数据）
    @PostMapping("/import")                 // Excel数据导入
    @PostMapping("/export")                 // Excel数据导出
    // 标准CRUD操作...
}
```

**AutoAccProjectMaterialController** - 项目物料API
```java
@RestController
@RequestMapping("/autoacct/projectMaterial")
public class AutoAccProjectMaterialController {
    
    @GetMapping("/list")                    // 查询项目物料列表
    @PostMapping("/import")                 // Excel数据导入
    @PostMapping("/export")                 // Excel数据导出
    // 标准CRUD操作...
}
```

#### 2.2.2 其他业务控制器

| 控制器 | 路径 | 主要功能 |
|--------|------|----------|
| AutoAccReceivedInventoryController | /autoacct/receivedInventory | 已领料库存管理 |
| AutoAccReceivedMaterialsController | /autoacct/receivedMaterials | 已领取物料管理 |
| AutoMaterialAcctInventoryController | /autoacct/inventory | 库存核算管理 |
| AutoMaterialAcctMaterialPlanController | /autoacct/materialPlan | 物料计划管理 |
| AutoMaterialAcctOtherRequirementController | /autoacct/otherRequirement | 其他需求管理 |
| CG04Controller | /autoacct/cg04 | CG04业务管理 |
| ConfigController | /autoacct/config | 配置管理 |
| MpmInfoController | /autoacct/mpmInfo | MPM编码信息管理 |
| ProdPlanController | /autoacct/prodPlan | 生产计划管理 |
| PurchaseNotreturnController | /autoacct/purchaseNotreturn | 采购未回管理 |

### 2.3 数据模型规格

#### 2.3.1 核心实体类

**InventoryOccupationDetail** - 库存占用明细
```java
@TableName("inventory_occupation_detail")
public class InventoryOccupationDetail extends BaseEntity {
    
    @TableId("task_id")
    private String taskId;                  // 任务编号
    
    private String accountingDate;          // 核算日期
    private String factoryCode;            // 工厂代码
    private String accountingCode;          // 核算编码
    private String materialCode;            // 物料代码
    
    private BigDecimal demandQuantity;      // 需求量
    private BigDecimal supplyQuantity;      // 供应量
    private BigDecimal batchRemaining;      // 批量剩余
    private BigDecimal inventoryRemaining;  // 库存剩余
    private BigDecimal qualifiedRemaining;  // 合格剩余
    private BigDecimal pendingInspection;   // 待验剩余
    private BigDecimal purchaseNotReturned; // 采购未回剩余
    private BigDecimal receivedMaterialQty; // 已领取物料数量
    private BigDecimal gap;                 // 缺口
    
    // 物料描述等辅助字段...
}
```

**MaterialMD** - 物料主数据
```java
@TableName("material_md")
public class MaterialMD extends BaseEntity {
    
    @TableId("matnr")
    private String matnr;                   // 物料编码
    
    private String maktx;                   // 物料描述
    private String zzbzmc;                  // 物料标准名称
    private String meins;                   // 基本计量单位
    private String matkl;                   // 物料组
    private String mtart;                   // 物料类型
    
    // 其他物料属性...
}
```

**BomMaterialRequire** - BOM物料需求
```java
@TableName("bom_material_require")
public class BomMaterialRequire extends BaseEntity {
    
    @TableId("id")
    private Long id;
    
    private String factoryCode;            // 工厂代码
    private String mpmCode;                // MPM编码
    private String mpmName;                // MPM名称
    private String productionProcess;      // 生产工序
    private String materialCode;           // 物料代码
    private BigDecimal batchUsage;         // 批用量
    private String alternativeGroup;       // 替代组
    private Integer priority;              // 优先级
    
    // 关联物料主数据获取描述信息...
}
```

#### 2.3.2 Excel模型类

**导出模型**
```java
// Excel库存占用明细导出
public class ExcelInventoryOccupationDetailVo {
    @Excel(name = "任务编号")
    private String taskId;
    
    @Excel(name = "核算日期")
    private String accountingDate;
    
    @Excel(name = "工厂代码")
    private String factoryCode;
    
    @Excel(name = "物料代码")
    private String materialCode;
    
    @Excel(name = "需求量")
    private BigDecimal demandQuantity;
    
    @Excel(name = "缺口")
    private BigDecimal gap;
    
    // 其他导出字段...
}

// Excel物料需求报表导出
public class ExcelMaterialRequirementReport {
    @Excel(name = "物料代码")
    private String materialCode;
    
    @Excel(name = "物料描述")
    private String materialDescription;
    
    @Excel(name = "项目需求汇总")
    private BigDecimal totalProjectRequirement;
    
    // 动态项目列...
}
```

**导入模型**
```java
// BOM物料需求导入
public class ExcelImportBomMaterialRequire {
    @Excel(name = "工厂代码")
    private String factoryCode;
    
    @Excel(name = "MPM编码")
    private String mpmCode;
    
    @Excel(name = "物料代码")
    private String materialCode;
    
    @Excel(name = "批用量")
    private BigDecimal batchUsage;
    
    // 其他导入字段...
}
```

### 2.4 Service层规格

#### 2.4.1 核心服务接口

**IInventoryOccupationDetailService**
```java
public interface IInventoryOccupationDetailService extends IService<InventoryOccupationDetail> {
    
    // 基础数据操作
    List<InventoryOccupationDetail> selectList(InventoryOccupationDetail query);
    
    // 报表生成功能
    List<MonthlyShipmentPlanVo> generateMonthlyShipmentPlan(String accountingDate, String factoryCode);
    List<ExcelMaterialRequirementReport> generateMaterialRequirementReport(String accountingDate, String factoryCode);
    List<InventoryOccupationDetailVo> exportGapList(String accountingDate, String factoryCode);
    
    // 数据处理功能
    void refreshInventoryOccupationDetail(String accountingDate, String factoryCode);
    String importData(List<ExcelInventoryOccupationDetailVo> list);
}
```

**IAutoAccProjectMaterialService**
```java
public interface IAutoAccProjectMaterialService extends IService<AutoAccProjectMaterial> {
    
    // 标准CRUD操作
    List<AutoAccProjectMaterial> selectList(AutoAccProjectMaterial query);
    
    // 数据导入功能
    String importData(List<ExcelImportAutoAccProjectMaterial> list);
}
```

#### 2.4.2 服务实现特性

**异步处理机制**
```java
@Service
public class InventoryOccupationDetailServiceImpl implements IInventoryOccupationDetailService {
    
    @Async("taskExecutor")
    public void refreshInventoryOccupationDetail(String accountingDate, String factoryCode) {
        try {
            // 1. 清理旧数据
            // 2. 重新计算库存占用
            // 3. 更新缓存
            // 4. 发送钉钉通知
            
            DingTalkUtils.sendMessage("库存占用明细计算完成");
        } catch (Exception e) {
            DingTalkUtils.sendMessage("库存占用明细计算失败: " + e.getMessage());
        }
    }
}
```

**Redis缓存集成**
```java
@Service
public class ConfigServiceImpl {
    
    @Autowired
    private RedisCache redisCache;
    
    private static final String CONFIG_KEY = "autoacct:config:";
    
    public void cacheConfig(String key, Object value) {
        redisCache.setCacheObject(CONFIG_KEY + key, value);
    }
    
    public <T> T getConfig(String key, Class<T> clazz) {
        return redisCache.getCacheObject(CONFIG_KEY + key);
    }
}
```

### 2.5 Mapper层规格

#### 2.5.1 基础Mapper接口

```java
@Mapper
public interface InventoryOccupationDetailMapper extends BaseMapper<InventoryOccupationDetail> {
    
    // 自定义查询方法
    List<String> selectTaskIds(@Param("accountingDate") String accountingDate, 
                              @Param("factoryCode") String factoryCode);
    
    // 复杂统计查询
    List<InventoryOccupationDetailVo> selectDetailWithMaterial(@Param("query") InventoryOccupationDetail query);
}

@Mapper
public interface MaterialMDMapper extends BaseMapper<MaterialMD> {
    
    // 获取所有物料主数据（用于关联查询）
    List<MaterialMD> getAllMaterials();
    
    // 根据物料代码批量查询
    List<MaterialMD> selectByMaterialCodes(@Param("materialCodes") List<String> materialCodes);
}
```

#### 2.5.2 MyBatis-Plus配置

```java
@Configuration
@MapperScan("com.akesobio.report.autoacct.mapper")
public class MyBatisConfig {
    
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        
        // 多租户插件（工厂隔离）
        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public Expression getTenantId() {
                // 根据当前用户获取工厂代码
                return new StringValue(getCurrentFactoryCode());
            }
        }));
        
        return interceptor;
    }
}
```

## 3. 前端技术规格

### 3.1 组件结构设计

```
ruoyi-ui/src/views/autoacct/
├── components/                    # 共享组件
│   ├── InventoryData.vue         # 库存数据选择器
│   ├── MpmData.vue              # MPM编码选择器
│   └── ProPlanData.vue          # 生产计划选择器
├── [业务模块]/                   # 20+个业务功能模块
│   └── index.vue                # 模块主页面
└── inventoryOccupationDetail/    # 核心模块
    ├── index.vue                # 主页面
    ├── exportGap/              # 缺口导出子模块
    └── horizontalReport/       # 横向报表子模块
```

### 3.2 核心组件规格

#### 3.2.1 库存占用明细组件

**inventoryOccupationDetail/index.vue**
```vue
<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="核算日期" prop="accountingDate">
        <el-date-picker v-model="queryParams.accountingDate" type="date"/>
      </el-form-item>
      <el-form-item label="工厂代码" prop="factoryCode">
        <el-select v-model="queryParams.factoryCode">
          <el-option label="康方生物" value="1000"/>
          <el-option label="康方药业" value="2000"/>
          <el-option label="康方赛诺" value="3000"/>
          <el-option label="康融广东" value="4000"/>
        </el-select>
      </el-form-item>
      <!-- 其他查询条件... -->
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" @click="handleRefresh">刷新计算</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" @click="handleExportGap">导出缺口</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" @click="handleGenerateReport">生成横向报表</el-button>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="detailList">
      <el-table-column prop="taskId" label="任务编号" width="150"/>
      <el-table-column prop="materialCode" label="物料代码" width="120"/>
      <el-table-column prop="materialDescription" label="物料描述" min-width="200"/>
      <el-table-column prop="demandQuantity" label="需求量" width="100"/>
      <el-table-column prop="gap" label="缺口" width="100">
        <template slot-scope="scope">
          <span :class="scope.row.gap > 0 ? 'text-danger' : ''">
            {{ scope.row.gap }}
          </span>
        </template>
      </el-table-column>
      <!-- 其他列... -->
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" 
                :limit.sync="queryParams.pageSize" @pagination="getList"/>
  </div>
</template>

<script>
import { listInventoryOccupationDetail, refreshDetail, exportGap } from "@/api/autoacct/inventoryOccupationDetail";

export default {
  name: "InventoryOccupationDetail",
  data() {
    return {
      loading: true,
      detailList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingDate: null,
        factoryCode: null
      }
    };
  },
  methods: {
    // 查询列表
    getList() {
      this.loading = true;
      listInventoryOccupationDetail(this.queryParams).then(response => {
        this.detailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    
    // 刷新计算（异步）
    handleRefresh() {
      this.$modal.confirm('是否确认刷新库存占用明细计算？').then(() => {
        return refreshDetail(this.queryParams.accountingDate, this.queryParams.factoryCode);
      }).then(() => {
        this.$modal.msgSuccess("刷新计算已启动，请稍后查看结果");
      });
    },
    
    // 导出缺口
    handleExportGap() {
      exportGap(this.queryParams).then(response => {
        this.$download.excel(response, "缺口分析.xlsx");
      });
    }
  }
};
</script>
```

#### 3.2.2 共享组件规格

**components/InventoryData.vue**
```vue
<template>
  <el-dialog title="选择库存数据" :visible.sync="visible" width="80%">
    <el-table :data="inventoryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"/>
      <el-table-column prop="materialCode" label="物料代码"/>
      <el-table-column prop="materialDescription" label="物料描述"/>
      <el-table-column prop="inventoryQuantity" label="库存数量"/>
      <!-- 其他列... -->
    </el-table>
    
    <div slot="footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirmSelection">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "InventoryData",
  props: {
    value: Boolean
  },
  data() {
    return {
      visible: this.value,
      inventoryList: [],
      selectedRows: []
    };
  },
  methods: {
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    
    confirmSelection() {
      this.$emit('selection-confirm', this.selectedRows);
      this.visible = false;
    }
  }
};
</script>
```

### 3.3 API调用规格

#### 3.3.1 API文件结构

**inventoryOccupationDetail.js**
```javascript
import request from '@/utils/request'

// 查询库存占用明细列表
export function listInventoryOccupationDetail(query) {
  return request({
    url: '/autoacct/inventoryOccupationDetail/list',
    method: 'get',
    params: query
  })
}

// 获取库存占用明细详细信息
export function getInventoryOccupationDetail(taskId) {
  return request({
    url: '/autoacct/inventoryOccupationDetail/' + taskId,
    method: 'get'
  })
}

// 新增库存占用明细
export function addInventoryOccupationDetail(data) {
  return request({
    url: '/autoacct/inventoryOccupationDetail',
    method: 'post',
    data: data
  })
}

// 修改库存占用明细
export function updateInventoryOccupationDetail(data) {
  return request({
    url: '/autoacct/inventoryOccupationDetail',
    method: 'put',
    data: data
  })
}

// 删除库存占用明细
export function delInventoryOccupationDetail(taskIds) {
  return request({
    url: '/autoacct/inventoryOccupationDetail/' + taskIds,
    method: 'delete'
  })
}

// 刷新库存占用明细计算
export function refreshDetail(accountingDate, factoryCode) {
  return request({
    url: '/autoacct/inventoryOccupationDetail/refreshInventoryOccupationDetail',
    method: 'post',
    data: { accountingDate, factoryCode }
  })
}

// 生成月度发货计划报表
export function generateMonthlyShippingPlan(accountingDate, factoryCode) {
  return request({
    url: '/autoacct/inventoryOccupationDetail/generateMonthlyShippingPlanReport',
    method: 'post',
    data: { accountingDate, factoryCode }
  })
}

// 生成物料需求横向汇总报表
export function generateMaterialRequirementReport(accountingDate, factoryCode) {
  return request({
    url: '/autoacct/inventoryOccupationDetail/generateMaterialRequirementReport',
    method: 'post',
    data: { accountingDate, factoryCode }
  })
}

// 导出缺口分析
export function exportGap(query) {
  return request({
    url: '/autoacct/inventoryOccupationDetail/exportGap',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}
```

#### 3.3.2 API调用模式

**标准CRUD模式**
```javascript
// 所有业务模块都遵循统一的API调用模式
const apiPattern = {
  list: (query) => request.get('/autoacct/{module}/list', { params: query }),
  get: (id) => request.get(`/autoacct/{module}/${id}`),
  add: (data) => request.post('/autoacct/{module}', { data }),
  update: (data) => request.put('/autoacct/{module}', { data }),
  delete: (ids) => request.delete(`/autoacct/{module}/${ids}`),
  export: (query) => request.post('/autoacct/{module}/export', { data: query, responseType: 'blob' }),
  import: (data) => request.post('/autoacct/{module}/import', { data })
};
```

### 3.4 状态管理规格

**store/modules/autoacct.js**
```javascript
const state = {
  // 当前选中的工厂
  currentFactory: '1000',
  
  // 物料主数据缓存
  materialMasterData: [],
  
  // MPM编码数据缓存
  mpmCodesData: [],
  
  // 配置信息缓存
  configData: {}
}

const mutations = {
  SET_CURRENT_FACTORY: (state, factory) => {
    state.currentFactory = factory
  },
  
  SET_MATERIAL_MASTER_DATA: (state, data) => {
    state.materialMasterData = data
  },
  
  SET_MPM_CODES_DATA: (state, data) => {
    state.mpmCodesData = data
  },
  
  SET_CONFIG_DATA: (state, data) => {
    state.configData = data
  }
}

const actions = {
  // 切换工厂
  changeFactory({ commit }, factory) {
    commit('SET_CURRENT_FACTORY', factory)
    // 清空缓存，重新加载数据
    commit('SET_MATERIAL_MASTER_DATA', [])
    commit('SET_MPM_CODES_DATA', [])
  },
  
  // 加载物料主数据
  async loadMaterialMasterData({ commit }) {
    try {
      const response = await request.get('/autoacct/material/masterData')
      commit('SET_MATERIAL_MASTER_DATA', response.data)
      return response.data
    } catch (error) {
      console.error('加载物料主数据失败:', error)
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
```

### 3.5 路由配置规格

虽然采用动态路由，但模块路由结构如下：

```javascript
// 动态路由配置示例
const autoacctRoutes = {
  path: '/autoacct',
  component: Layout,
  name: 'AutoAcct',
  meta: { title: '自动核算', icon: 'el-icon-s-data' },
  children: [
    {
      path: 'inventoryOccupationDetail',
      component: () => import('@/views/autoacct/inventoryOccupationDetail'),
      name: 'InventoryOccupationDetail',
      meta: { title: '库存占用明细', icon: 'el-icon-box' }
    },
    {
      path: 'bomrequire',
      component: () => import('@/views/autoacct/BOMrequire'),
      name: 'BOMRequire',
      meta: { title: 'BOM物料需求', icon: 'el-icon-menu' }
    },
    // 其他子路由...
  ]
}
```

## 4. 数据库设计规格

### 4.1 核心表结构

**库存占用明细表**
```sql
CREATE TABLE `inventory_occupation_detail` (
  `task_id` varchar(100) NOT NULL COMMENT '任务编号',
  `accounting_date` varchar(20) DEFAULT NULL COMMENT '核算日期',
  `factory_code` varchar(10) DEFAULT NULL COMMENT '工厂代码',
  `accounting_code` varchar(50) DEFAULT NULL COMMENT '核算编码',
  `material_code` varchar(50) DEFAULT NULL COMMENT '物料代码',
  `demand_quantity` decimal(15,3) DEFAULT NULL COMMENT '需求量',
  `supply_quantity` decimal(15,3) DEFAULT NULL COMMENT '供应量',
  `batch_remaining` decimal(15,3) DEFAULT NULL COMMENT '批量剩余',
  `inventory_remaining` decimal(15,3) DEFAULT NULL COMMENT '库存剩余',
  `qualified_remaining` decimal(15,3) DEFAULT NULL COMMENT '合格剩余',
  `pending_inspection` decimal(15,3) DEFAULT NULL COMMENT '待验剩余',
  `purchase_not_returned` decimal(15,3) DEFAULT NULL COMMENT '采购未回剩余',
  `received_material_qty` decimal(15,3) DEFAULT NULL COMMENT '已领取物料数量',
  `gap` decimal(15,3) DEFAULT NULL COMMENT '缺口',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`task_id`),
  KEY `idx_accounting_date` (`accounting_date`),
  KEY `idx_factory_code` (`factory_code`),
  KEY `idx_material_code` (`material_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存占用明细表';
```

**物料主数据表**
```sql
CREATE TABLE `material_md` (
  `matnr` varchar(50) NOT NULL COMMENT '物料编码',
  `maktx` varchar(200) DEFAULT NULL COMMENT '物料描述',
  `zzbzmc` varchar(200) DEFAULT NULL COMMENT '物料标准名称',
  `meins` varchar(10) DEFAULT NULL COMMENT '基本计量单位',
  `matkl` varchar(20) DEFAULT NULL COMMENT '物料组',
  `mtart` varchar(10) DEFAULT NULL COMMENT '物料类型',
  `zzgys` varchar(100) DEFAULT NULL COMMENT '供应商',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`matnr`),
  KEY `idx_matkl` (`matkl`),
  KEY `idx_mtart` (`mtart`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物料主数据表';
```

**BOM物料需求表**
```sql
CREATE TABLE `bom_material_require` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `factory_code` varchar(10) DEFAULT NULL COMMENT '工厂代码',
  `mpm_code` varchar(50) DEFAULT NULL COMMENT 'MPM编码',
  `mpm_name` varchar(200) DEFAULT NULL COMMENT 'MPM名称',
  `production_process` varchar(100) DEFAULT NULL COMMENT '生产工序',
  `material_code` varchar(50) DEFAULT NULL COMMENT '物料代码',
  `batch_usage` decimal(15,6) DEFAULT NULL COMMENT '批用量',
  `alternative_group` varchar(50) DEFAULT NULL COMMENT '替代组',
  `priority` int(11) DEFAULT NULL COMMENT '优先级',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_factory_code` (`factory_code`),
  KEY `idx_mpm_code` (`mpm_code`),
  KEY `idx_material_code` (`material_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM物料需求表';
```

### 4.2 索引设计

**性能优化索引**
```sql
-- 库存占用明细表复合索引
CREATE INDEX `idx_accounting_factory_material` ON `inventory_occupation_detail` 
(`accounting_date`, `factory_code`, `material_code`);

-- 物料主数据表文本索引
CREATE FULLTEXT INDEX `idx_material_description` ON `material_md` (`maktx`, `zzbzmc`);

-- BOM物料需求表复合索引
CREATE INDEX `idx_factory_mpm_material` ON `bom_material_require` 
(`factory_code`, `mpm_code`, `material_code`);
```

### 4.3 数据源配置

**多数据源支持**
```yaml
spring:
  datasource:
    # 主数据源（报表库）
    master:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *********************************************************************************************************
      username: ${DB_USERNAME:root}
      password: ${DB_PASSWORD:123456}
    
    # SAP数据源（物料主数据）
    sap:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ***************************************************************************************************
      username: ${SAP_DB_USERNAME:sap_user}
      password: ${SAP_DB_PASSWORD:sap_pass}
    
    # EKP数据源（工作流数据）
    ekp:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ***************************************************************************************************
      username: ${EKP_DB_USERNAME:ekp_user}
      password: ${EKP_DB_PASSWORD:ekp_pass}
```

## 5. 业务流程规格

### 5.1 库存核算流程

```mermaid
graph TD
    A[导入基础数据] --> B[设置核算参数]
    B --> C[触发库存核算计算]
    C --> D[清理历史数据]
    D --> E[加载BOM物料需求]
    E --> F[加载库存数据]
    F --> G[加载生产计划]
    G --> H[计算物料需求]
    H --> I[计算库存占用]
    I --> J[计算缺口分析]
    J --> K[生成占用明细]
    K --> L[发送钉钉通知]
    L --> M[缓存计算结果]
```

### 5.2 报表生成流程

```mermaid
graph TD
    A[选择报表类型] --> B{报表类型判断}
    B -->|月度发货计划| C[查询项目物料需求]
    B -->|横向汇总报表| D[查询物料需求明细]
    B -->|缺口分析报表| E[查询缺口数据]
    
    C --> F[按项目分组汇总]
    D --> G[按物料横向展开]
    E --> H[筛选有缺口物料]
    
    F --> I[生成Excel报表]
    G --> I
    H --> I
    
    I --> J[下载报表文件]
```

### 5.3 数据集成流程

```mermaid
graph TD
    A[Excel文件上传] --> B[文件格式校验]
    B --> C[数据格式校验]
    C --> D[业务规则校验]
    D --> E[关联物料主数据]
    E --> F[数据转换处理]
    F --> G[批量入库]
    G --> H[返回导入结果]
    
    I[定时任务] --> J[同步SAP物料主数据]
    J --> K[同步EKP工作流数据]
    K --> L[同步生产计划数据]
    L --> M[触发自动核算]
```

## 6. 集成与配置规格

### 6.1 钉钉机器人集成

**配置文件**
```yaml
dingtalk:
  robot:
    webhook: https://oapi.dingtalk.com/robot/send?access_token=${DINGTALK_TOKEN}
    secret: ${DINGTALK_SECRET}
    enabled: true
```

**通知服务**
```java
@Component
public class DingTalkNotificationService {
    
    @Value("${dingtalk.robot.webhook}")
    private String webhook;
    
    @Value("${dingtalk.robot.secret}")
    private String secret;
    
    public void sendTaskNotification(String taskName, String status, String message) {
        DingTalkMessage msg = DingTalkMessage.builder()
            .msgtype("text")
            .text(DingTalkText.builder()
                .content(String.format("【%s】%s\n状态：%s\n详情：%s", 
                    taskName, LocalDateTime.now(), status, message))
                .build())
            .build();
        
        sendMessage(msg);
    }
}
```

### 6.2 Redis缓存配置

**缓存策略**
```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory factory) {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(factory)
            .cacheDefaults(cacheConfiguration());
        
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1))  // 缓存1小时
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new FastJson2JsonRedisSerializer<>(Object.class)));
    }
}
```

### 6.3 异步处理配置

**线程池配置**
```java
@Configuration
@EnableAsync
public class AsyncConfig {
    
    @Bean("taskExecutor")
    public TaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("AutoAcct-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
```

### 6.4 Excel处理配置

**EasyExcel配置**
```java
@Configuration
public class ExcelConfig {
    
    @Bean
    public ExcelWriterBuilder excelWriterBuilder() {
        return EasyExcel.write()
            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
            .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20))
            .useDefaultStyle(false);
    }
}
```

## 7. 部署与运维规格

### 7.1 环境配置

**开发环境**
```yaml
# application-dev.yml
server:
  port: 8080

spring:
  datasource:
    master:
      url: ***********************************************
  
  redis:
    host: dev-redis
    port: 6379
    database: 0

logging:
  level:
    com.akesobio.report.autoacct: DEBUG
```

**生产环境**
```yaml
# application-prod.yml
server:
  port: 8080

spring:
  datasource:
    master:
      url: ********************************************
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
  
  redis:
    cluster:
      nodes: prod-redis-1:6379,prod-redis-2:6379,prod-redis-3:6379

logging:
  level:
    root: INFO
    com.akesobio.report.autoacct: INFO
```

### 7.2 监控配置

**Actuator监控**
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

**自定义监控指标**
```java
@Component
public class AutoAcctMetrics {
    
    private final Counter taskExecutionCounter;
    private final Timer taskExecutionTimer;
    
    public AutoAcctMetrics(MeterRegistry meterRegistry) {
        this.taskExecutionCounter = Counter.builder("autoacct.task.execution")
            .description("AutoAcct task execution count")
            .register(meterRegistry);
            
        this.taskExecutionTimer = Timer.builder("autoacct.task.duration")
            .description("AutoAcct task execution duration")
            .register(meterRegistry);
    }
}
```

### 7.3 性能优化

**数据库优化**
```sql
-- 定期分析表统计信息
ANALYZE TABLE inventory_occupation_detail;
ANALYZE TABLE material_md;
ANALYZE TABLE bom_material_require;

-- 清理历史数据（保留6个月）
DELETE FROM inventory_occupation_detail 
WHERE accounting_date < DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

**应用优化**
```java
// 批量处理优化
@Service
public class BatchProcessService {
    
    private static final int BATCH_SIZE = 1000;
    
    @Transactional
    public void batchInsert(List<InventoryOccupationDetail> list) {
        for (int i = 0; i < list.size(); i += BATCH_SIZE) {
            List<InventoryOccupationDetail> batch = list.subList(i, 
                Math.min(i + BATCH_SIZE, list.size()));
            inventoryMapper.batchInsert(batch);
        }
    }
}
```

## 8. 安全与权限规格

### 8.1 数据权限

**工厂数据隔离**
```java
@Component
public class FactoryDataPermissionHandler implements DataPermissionHandler {
    
    @Override
    public Expression getSqlSegment(PlainSelect plainSelect, String whereSegment) {
        // 获取当前用户的工厂权限
        List<String> factoryCodes = getCurrentUserFactoryCodes();
        
        if (CollectionUtils.isEmpty(factoryCodes)) {
            return null;
        }
        
        // 构建工厂代码过滤条件
        InExpression inExpression = new InExpression();
        inExpression.setLeftExpression(new Column("factory_code"));
        inExpression.setRightItemsList(new ExpressionList(
            factoryCodes.stream()
                .map(StringValue::new)
                .collect(Collectors.toList())
        ));
        
        return inExpression;
    }
}
```

### 8.2 操作权限

**权限注解**
```java
@RestController
public class InventoryOccupationDetailController {
    
    @PreAuthorize("@ss.hasPermi('autoacct:inventoryDetail:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryOccupationDetail query) {
        // 查询逻辑
    }
    
    @PreAuthorize("@ss.hasPermi('autoacct:inventoryDetail:refresh')")
    @PostMapping("/refresh")
    public AjaxResult refresh(String accountingDate, String factoryCode) {
        // 刷新逻辑
    }
}
```

### 8.3 数据脱敏

**敏感数据处理**
```java
@Configuration
public class DataMaskingConfig {
    
    @Bean
    public DataMaskingHandler dataMaskingHandler() {
        return new DataMaskingHandler() {
            @Override
            public String mask(String value, MaskType maskType) {
                switch (maskType) {
                    case MATERIAL_CODE:
                        return value.substring(0, 4) + "****";
                    case SUPPLIER_NAME:
                        return value.substring(0, 2) + "***";
                    default:
                        return value;
                }
            }
        };
    }
}
```

## 9. 测试规格

### 9.1 单元测试

**Service层测试**
```java
@SpringBootTest
@TestMethodOrder(OrderAnnotation.class)
class InventoryOccupationDetailServiceTest {
    
    @Autowired
    private IInventoryOccupationDetailService service;
    
    @Test
    @Order(1)
    void testRefreshInventoryOccupationDetail() {
        // 准备测试数据
        String accountingDate = "2023-12-01";
        String factoryCode = "1000";
        
        // 执行测试
        assertDoesNotThrow(() -> {
            service.refreshInventoryOccupationDetail(accountingDate, factoryCode);
        });
    }
    
    @Test
    @Order(2)
    void testGenerateMonthlyShipmentPlan() {
        // 测试月度发货计划生成
        List<MonthlyShipmentPlanVo> result = service.generateMonthlyShipmentPlan("2023-12-01", "1000");
        assertNotNull(result);
    }
}
```

### 9.2 集成测试

**Controller层测试**
```java
@SpringBootTest
@AutoConfigureTestDatabase
@Testcontainers
class InventoryOccupationDetailControllerTest {
    
    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8.0")
            .withDatabaseName("test_db")
            .withUsername("test")
            .withPassword("test");
    
    @Autowired
    private MockMvc mockMvc;
    
    @Test
    void testListInventoryOccupationDetail() throws Exception {
        mockMvc.perform(get("/autoacct/inventoryOccupationDetail/list")
                .param("accountingDate", "2023-12-01")
                .param("factoryCode", "1000"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
```

### 9.3 性能测试

**压力测试**
```java
@Component
public class AutoAcctPerformanceTest {
    
    @Autowired
    private IInventoryOccupationDetailService service;
    
    @Test
    @RepeatedTest(100)
    void testConcurrentRefresh() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        
        service.refreshInventoryOccupationDetail("2023-12-01", "1000");
        
        stopWatch.stop();
        long executionTime = stopWatch.getTotalTimeMillis();
        
        // 断言执行时间不超过30秒
        assertTrue(executionTime < 30000);
    }
}
```

## 10. 文档与维护

### 10.1 API文档

使用Swagger自动生成API文档：

```java
@Configuration
@EnableSwagger2
public class SwaggerConfig {
    
    @Bean
    public Docket autoAcctApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("AutoAcct模块")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.akesobio.report.autoacct.controller"))
                .paths(PathSelectors.any())
                .build();
    }
    
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("AutoAcct模块API文档")
                .description("自动核算模块的API接口文档")
                .version("1.0.0")
                .build();
    }
}
```

### 10.2 变更日志

**版本管理**
```markdown
# AutoAcct模块变更日志

## [1.2.0] - 2023-12-01
### 新增
- 新增横向汇总报表功能
- 新增钉钉机器人通知
- 新增Redis缓存优化

### 修改
- 优化库存占用计算性能
- 改进Excel导入导出功能
- 更新物料主数据同步机制

### 修复
- 修复缺口计算精度问题
- 修复并发场景下的数据一致性问题

## [1.1.0] - 2023-11-01
### 新增
- 新增BOM物料需求管理
- 新增多工厂数据支持
- 新增异步处理机制
```

### 10.3 运维手册

**常见问题处理**
```markdown
# AutoAcct模块运维手册

## 常见问题

### 1. 库存核算计算失败
**现象**：任务执行报错，钉钉收到失败通知
**排查步骤**：
1. 检查数据库连接状态
2. 检查基础数据完整性
3. 查看应用日志错误信息
4. 检查Redis缓存状态

### 2. 报表生成速度慢
**现象**：报表生成时间超过预期
**优化方案**：
1. 检查数据库索引是否有效
2. 优化SQL查询语句
3. 增加Redis缓存
4. 考虑数据分页处理

### 3. Excel导入失败
**现象**：文件上传后导入失败
**检查项目**：
1. 文件格式是否正确
2. 数据格式是否符合要求
3. 物料代码是否存在于主数据中
4. 必填字段是否完整
```

---

**文档版本**：v1.0.0  
**最后更新**：2023-12-01  
**维护团队**：AutoAcct开发组  
**联系方式**：<EMAIL>